@font-face{font-family:Geist<PERSON><PERSON>;src:url(../media/GeistMono_Variable.p.73882635.woff2)format("woff2");font-display:swap;font-weight:100 900}.geistmono_157ca88a-module__WVqpCG__className{font-family:GeistMono,ui-monospace,SFMono-Regular,Roboto Mono,Menlo,Monaco,Liberation Mono,DejaVu Sans Mono,Courier New,monospace}.geistmono_157ca88a-module__WVqpCG__variable{--font-geist-mono:"GeistMono",ui-monospace,SFMono-Regular,Roboto Mono,Menlo,Monaco,Liberation Mono,DejaVu Sans Mono,Courier New,monospace}
@font-face{font-family:GeistSans;src:url(../media/Geist_Variable-s.p.f19e4721.woff2)format("woff2");font-display:swap;font-weight:100 900}@font-face{font-family:GeistSans Fallback;src:local(Arial);ascent-override:85.83%;descent-override:20.53%;line-gap-override:9.33%;size-adjust:107.19%}.geistsans_d5a4f12f-module__tBZO7G__className{font-family:GeistSans,GeistSans Fallback}.geistsans_d5a4f12f-module__tBZO7G__variable{--font-geist-sans:"GeistSans","GeistSans Fallback"}
@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:transparent;--tw-gradient-via:transparent;--tw-gradient-to:transparent;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-shadow:0 0 transparent;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 transparent;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 transparent;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 transparent;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 transparent;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial;--tw-animation-delay:0s;--tw-animation-direction:normal;--tw-animation-duration:initial;--tw-animation-fill-mode:none;--tw-animation-iteration-count:1;--tw-enter-blur:0;--tw-enter-opacity:1;--tw-enter-rotate:0;--tw-enter-scale:1;--tw-enter-translate-x:0;--tw-enter-translate-y:0;--tw-exit-blur:0;--tw-exit-opacity:1;--tw-exit-rotate:0;--tw-exit-scale:1;--tw-exit-translate-x:0;--tw-exit-translate-y:0}::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:transparent;--tw-gradient-via:transparent;--tw-gradient-to:transparent;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-shadow:0 0 transparent;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 transparent;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 transparent;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 transparent;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 transparent;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial;--tw-animation-delay:0s;--tw-animation-direction:normal;--tw-animation-duration:initial;--tw-animation-fill-mode:none;--tw-animation-iteration-count:1;--tw-enter-blur:0;--tw-enter-opacity:1;--tw-enter-rotate:0;--tw-enter-scale:1;--tw-enter-translate-x:0;--tw-enter-translate-y:0;--tw-exit-blur:0;--tw-exit-opacity:1;--tw-exit-rotate:0;--tw-exit-scale:1;--tw-exit-translate-x:0;--tw-exit-translate-y:0}}}@layer theme{:root,:host{--color-blue-50:#eff6ff;--color-blue-500:#3080ff;--color-blue-600:#155dfc;--color-blue-700:#1447e6;--color-blue-800:#193cb8;--color-gray-50:#f9fafb;--color-gray-200:#e5e7eb;--color-gray-500:#6a7282;--color-gray-700:#364153;--color-gray-800:#1e2939;--color-black:#000;--color-white:#fff;--spacing:.25rem;--breakpoint-2xl:96rem;--container-xs:20rem;--container-sm:24rem;--container-lg:32rem;--container-xl:36rem;--container-2xl:42rem;--container-3xl:48rem;--container-4xl:56rem;--container-6xl:72rem;--container-7xl:80rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height:calc(1.5/1);--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--text-3xl:1.875rem;--text-3xl--line-height:calc(2.25/1.875);--text-4xl:2.25rem;--text-4xl--line-height:calc(2.5/2.25);--text-5xl:3rem;--text-5xl--line-height:1;--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--tracking-tighter:-.05em;--tracking-tight:-.025em;--tracking-normal:0em;--leading-tight:1.25;--radius-2xl:1rem;--ease-out:cubic-bezier(0,0,.2,1);--ease-in-out:cubic-bezier(.4,0,.2,1);--animate-spin:spin 1s linear infinite;--blur-md:12px;--blur-lg:16px;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-geist-sans);--default-mono-font-family:var(--font-geist-mono);--font-aeonik-bold:var(--font-aeonik-bold)}@supports (color:color(display-p3 0 0 0)){:root,:host{--color-blue-50:color(display-p3 .941826 .963151 .995385);--color-blue-500:color(display-p3 .266422 .491219 .988624);--color-blue-600:color(display-p3 .174493 .358974 .950247);--color-blue-700:color(display-p3 .1379 .274983 .867624);--color-blue-800:color(display-p3 .134023 .230647 .695537);--color-gray-50:color(display-p3 .977213 .98084 .985102);--color-gray-200:color(display-p3 .899787 .906171 .92106);--color-gray-500:color(display-p3 .421287 .446085 .504784);--color-gray-700:color(display-p3 .219968 .253721 .318679);--color-gray-800:color(display-p3 .125854 .159497 .216835)}}@supports (color:lab(0% 0 0)){:root,:host{--color-blue-50:lab(96.492% -1.14647 -5.11479);--color-blue-500:lab(54.1736% 13.3368 -74.6839);--color-blue-600:lab(44.0605% 29.0279 -86.0352);--color-blue-700:lab(36.9089% 35.0961 -85.6872);--color-blue-800:lab(30.2514% 27.7854 -70.2699);--color-gray-50:lab(98.2596% -.247031 -.706708);--color-gray-200:lab(91.6229% -.159085 -2.26791);--color-gray-500:lab(47.7841% -.393212 -10.0268);--color-gray-700:lab(27.1134% -.956401 -12.3224);--color-gray-800:lab(16.1051% -1.18239 -11.7533)}}}@layer base{*,:after,:before{box-sizing:border-box;border:0 solid;margin:0;padding:0}::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::-webkit-file-upload-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:transparent;border-radius:0}::-webkit-file-upload-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:transparent;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:transparent;border-radius:0}:where(select:-webkit-any([multiple],[size])) optgroup{font-weight:bolder}:where(select:-moz-any([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:-webkit-any([multiple],[size])) optgroup option:not(:-webkit-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))){padding-left:20px}:where(select:-moz-any([multiple],[size])) optgroup option:not(:-moz-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))){padding-left:20px}:where(select:is([multiple],[size])) optgroup option:not(:is(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))){padding-left:20px}:where(select:-webkit-any([multiple],[size])) optgroup option:-webkit-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)){padding-right:20px}:where(select:-moz-any([multiple],[size])) optgroup option:-moz-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)){padding-right:20px}:where(select:is([multiple],[size])) optgroup option:is(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)){padding-right:20px}:not(:-webkit-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)))::-webkit-file-upload-button{margin-right:4px}:not(:-moz-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)))::file-selector-button{margin-right:4px}:not(:is(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)))::file-selector-button{margin-right:4px}:-webkit-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))::-webkit-file-upload-button{margin-left:4px}:-moz-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))::file-selector-button{margin-left:4px}:is(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))::file-selector-button{margin-left:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab, red, red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-year-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-month-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-day-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-hour-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-minute-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-second-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-millisecond-field{padding-top:0;padding-bottom:0}::-webkit-datetime-edit-meridiem-field{padding-top:0;padding-bottom:0}::-webkit-calendar-picker-indicator{line-height:1}:-moz-ui-invalid{box-shadow:none}button{-webkit-appearance:button;-moz-appearance:button;appearance:button}input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-file-upload-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}*{border-color:var(--border)}html{scroll-behavior:smooth}body{overscroll-behavior:none;background-color:var(--background);color:var(--foreground);font-synthesis-weight:none;text-rendering:optimizeLegibility}::-webkit-scrollbar{width:5px}::-webkit-scrollbar-track{background:0 0}::-webkit-scrollbar-thumb{background:var(--border);border-radius:5px}*{scrollbar-width:thin;scrollbar-color:var(--border)#090909;border-color:var(--border);outline-color:var(--ring)}@supports (color:color-mix(in lab, red, red)){*{outline-color:color-mix(in oklab,var(--ring)50%,transparent)}}body{background-color:var(--background);color:var(--foreground)}}@layer components;@layer utilities{.\@container\/card-header{container:card-header/inline-size}.pointer-events-none{pointer-events:none}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.sticky{position:-webkit-sticky;position:sticky}.inset-0{inset:calc(var(--spacing)*0)}.inset-y-0{inset-block:calc(var(--spacing)*0)}.-top-12{top:calc(var(--spacing)*-12)}.top-0{top:calc(var(--spacing)*0)}.top-1\/2{top:50%}.top-4{top:calc(var(--spacing)*4)}.top-\[1px\]{top:1px}.top-\[60\%\]{top:60%}.top-full{top:100%}.-right-12{right:calc(var(--spacing)*-12)}.right-0{right:calc(var(--spacing)*0)}.-bottom-12{bottom:calc(var(--spacing)*-12)}.-left-12{left:calc(var(--spacing)*-12)}.left-0{left:calc(var(--spacing)*0)}.left-1\/2{left:50%}.left-\[200px\]{left:200px}.isolate{isolation:isolate}.z-10{z-index:10}.z-20{z-index:20}.z-30{z-index:30}.z-40{z-index:40}.z-50{z-index:50}.z-\[1\]{z-index:1}.col-span-2{grid-column:span 2/span 2}.col-start-2{grid-column-start:2}.row-span-2{grid-row:span 2/span 2}.row-start-1{grid-row-start:1}.container{width:100%}@media (min-width:40rem){.container{max-width:40rem}}@media (min-width:48rem){.container{max-width:48rem}}@media (min-width:64rem){.container{max-width:64rem}}@media (min-width:80rem){.container{max-width:80rem}}@media (min-width:96rem){.container{max-width:96rem}}.m-auto{margin:auto}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-auto{margin-left:auto;margin-right:auto}.my-5{margin-block:calc(var(--spacing)*5)}.my-6{margin-block:calc(var(--spacing)*6)}.my-8{margin-block:calc(var(--spacing)*8)}.my-12{margin-block:calc(var(--spacing)*12)}.my-auto{margin-top:auto;margin-bottom:auto}.-mt-4{margin-top:calc(var(--spacing)*-4)}.mt-1\.5{margin-top:calc(var(--spacing)*1.5)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-6{margin-top:calc(var(--spacing)*6)}.mt-10{margin-top:calc(var(--spacing)*10)}.mt-auto{margin-top:auto}.mr-2{margin-right:calc(var(--spacing)*2)}.-mb-32{margin-bottom:calc(var(--spacing)*-32)}.mb-0{margin-bottom:calc(var(--spacing)*0)}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-5{margin-bottom:calc(var(--spacing)*5)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.mb-8{margin-bottom:calc(var(--spacing)*8)}.mb-12{margin-bottom:calc(var(--spacing)*12)}.mb-14{margin-bottom:calc(var(--spacing)*14)}.mb-16{margin-bottom:calc(var(--spacing)*16)}.mb-40{margin-bottom:calc(var(--spacing)*40)}.mb-\[160px\]{margin-bottom:160px}.-ml-4{margin-left:calc(var(--spacing)*-4)}.ml-1{margin-left:calc(var(--spacing)*1)}.block{display:block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline-block{display:inline-block}.inline-flex{display:inline-flex}.aspect-\[1\/1\],.aspect-square{aspect-ratio:1}.size-3{width:calc(var(--spacing)*3);height:calc(var(--spacing)*3)}.size-4{width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.size-5{width:calc(var(--spacing)*5);height:calc(var(--spacing)*5)}.size-6{width:calc(var(--spacing)*6);height:calc(var(--spacing)*6)}.size-8{width:calc(var(--spacing)*8);height:calc(var(--spacing)*8)}.size-9{width:calc(var(--spacing)*9);height:calc(var(--spacing)*9)}.size-11{width:calc(var(--spacing)*11);height:calc(var(--spacing)*11)}.size-12{width:calc(var(--spacing)*12);height:calc(var(--spacing)*12)}.size-fit{width:-moz-fit-content;width:fit-content;height:-moz-fit-content;height:fit-content}.h-1\.5{height:calc(var(--spacing)*1.5)}.h-2{height:calc(var(--spacing)*2)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-7{height:calc(var(--spacing)*7)}.h-8{height:calc(var(--spacing)*8)}.h-9{height:calc(var(--spacing)*9)}.h-10{height:calc(var(--spacing)*10)}.h-32{height:calc(var(--spacing)*32)}.h-\[60px\]{height:60px}.h-\[450px\]{height:450px}.h-\[calc\(100\%-1px\)\]{height:calc(100% - 1px)}.h-\[var\(--radix-navigation-menu-viewport-height\)\]{height:var(--radix-navigation-menu-viewport-height)}.h-auto{height:auto}.h-full{height:100%}.max-h-32{max-height:calc(var(--spacing)*32)}.max-h-72{max-height:calc(var(--spacing)*72)}.max-h-\[900px\]{max-height:900px}.min-h-\[500px\]{min-height:500px}.min-h-svh{min-height:100svh}.w-1\/4{width:25%}.w-2{width:calc(var(--spacing)*2)}.w-4{width:calc(var(--spacing)*4)}.w-8{width:calc(var(--spacing)*8)}.w-20{width:calc(var(--spacing)*20)}.w-\[120px\]{width:120px}.w-\[200px\]{width:200px}.w-\[500px\]{width:500px}.w-fit{width:-moz-fit-content;width:fit-content}.w-full{width:100%}.w-max{width:max-content}.max-w-2xl{max-width:var(--container-2xl)}.max-w-3xl{max-width:var(--container-3xl)}.max-w-4xl{max-width:var(--container-4xl)}.max-w-6xl{max-width:var(--container-6xl)}.max-w-7xl{max-width:var(--container-7xl)}.max-w-\[200px\]{max-width:200px}.max-w-\[280px\]{max-width:280px}.max-w-\[320px\]{max-width:320px}.max-w-\[440px\]{max-width:440px}.max-w-\[600px\]{max-width:600px}.max-w-full{max-width:100%}.max-w-lg{max-width:var(--container-lg)}.max-w-max{max-width:max-content}.max-w-none{max-width:none}.max-w-sm{max-width:var(--container-sm)}.max-w-xs{max-width:var(--container-xs)}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-\[120px\]{min-width:120px}.min-w-\[200px\]{min-width:200px}.min-w-\[700px\]{min-width:700px}.flex-1{flex:1}.flex-shrink-0,.shrink-0{flex-shrink:0}.grow-0{flex-grow:0}.basis-full{flex-basis:100%}.border-collapse{border-collapse:collapse}.border-spacing-0{--tw-border-spacing-x:calc(var(--spacing)*0);--tw-border-spacing-y:calc(var(--spacing)*0);border-spacing:var(--tw-border-spacing-x)var(--tw-border-spacing-y)}.origin-left{transform-origin:0}.-translate-x-1\/2{--tw-translate-x:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-2{--tw-translate-x:calc(var(--spacing)*2);translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\/2{--tw-translate-y:calc(calc(1/2*100%)*-1);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-0{--tw-translate-y:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-0\.5{--tw-translate-y:calc(var(--spacing)*.5);translate:var(--tw-translate-x)var(--tw-translate-y)}.scale-0{--tw-scale-x:0%;--tw-scale-y:0%;--tw-scale-z:0%;scale:var(--tw-scale-x)var(--tw-scale-y)}.scale-125{--tw-scale-x:125%;--tw-scale-y:125%;--tw-scale-z:125%;scale:var(--tw-scale-x)var(--tw-scale-y)}.rotate-45{rotate:45deg}.rotate-90{rotate:90deg}.animate-marquee{animation:marquee var(--duration)infinite linear}.animate-marquee-vertical{animation:marquee-vertical var(--duration)linear infinite}.animate-spin{animation:var(--animate-spin)}.cursor-pointer{cursor:pointer}.resize{resize:both}.list-none{list-style-type:none}.auto-rows-min{grid-auto-rows:min-content}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-rows-\[auto_auto\]{grid-template-rows:auto auto}.flex-col{flex-direction:column}.flex-row{flex-direction:row}.items-center{align-items:center}.items-end{align-items:flex-end}.items-start{align-items:flex-start}.justify-around{justify-content:space-around}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.\[gap\:var\(--gap\)\]{gap:var(--gap)}.gap-0\.5{gap:calc(var(--spacing)*.5)}.gap-1{gap:calc(var(--spacing)*1)}.gap-1\.5{gap:calc(var(--spacing)*1.5)}.gap-2{gap:calc(var(--spacing)*2)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}.gap-5{gap:calc(var(--spacing)*5)}.gap-6{gap:calc(var(--spacing)*6)}.gap-8{gap:calc(var(--spacing)*8)}.gap-10{gap:calc(var(--spacing)*10)}.gap-12{gap:calc(var(--spacing)*12)}.gap-16{gap:calc(var(--spacing)*16)}.gap-\[15px\]{gap:15px}.gap-\[50px\]{gap:50px}.gap-\[60px\]{gap:60px}.gap-\[80px\]{gap:80px}:where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0;margin-top:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));margin-bottom:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-top:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-bottom:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0}:where(.space-x-2>:not(:last-child)):not(:-webkit-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))){margin-left:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-right:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)):not(:-moz-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))){margin-left:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-right:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)):not(:is(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi))){margin-left:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-right:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)):-webkit-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)){margin-right:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-left:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)):-moz-any(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)){margin-right:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-left:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)):is(:lang(ae),:lang(ar),:lang(arc),:lang(bcc),:lang(bqi),:lang(ckb),:lang(dv),:lang(fa),:lang(glk),:lang(he),:lang(ku),:lang(mzn),:lang(nqo),:lang(pnb),:lang(ps),:lang(sd),:lang(ug),:lang(ur),:lang(yi)){margin-right:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-left:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}.self-start{align-self:flex-start}.justify-self-end{justify-self:flex-end}.overflow-hidden{overflow:hidden}.overflow-visible{overflow:visible}.overflow-x-auto{overflow-x:auto}.rounded-2xl{border-radius:var(--radius-2xl)}.rounded-\[20px\]{border-radius:20px}.rounded-\[inherit\]{border-radius:inherit}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius)}.rounded-md{border-radius:calc(var(--radius) - 2px)}.rounded-sm{border-radius:calc(var(--radius) - 4px)}.rounded-xl{border-radius:calc(var(--radius) + 4px)}.rounded-t-lg{border-top-left-radius:var(--radius);border-top-right-radius:var(--radius)}.rounded-t-xl{border-top-left-radius:calc(var(--radius) + 4px);border-top-right-radius:calc(var(--radius) + 4px)}.rounded-tl-sm{border-top-left-radius:calc(var(--radius) - 4px)}.rounded-b-lg{border-bottom-right-radius:var(--radius);border-bottom-left-radius:var(--radius)}.border{border-style:var(--tw-border-style);border-width:1px}.border-0{border-style:var(--tw-border-style);border-width:0}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-l-4{border-left-style:var(--tw-border-style);border-left-width:4px}.border-blue-500{border-color:var(--color-blue-500)}.border-gray-200{border-color:var(--color-gray-200)}.border-input{border-color:var(--input)}.border-muted\/5{border-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.border-muted\/5{border-color:color-mix(in oklab,var(--muted)5%,transparent)}}.border-muted\/60{border-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.border-muted\/60{border-color:color-mix(in oklab,var(--muted)60%,transparent)}}.border-transparent{border-color:transparent}.bg-\[\#0D0D0D\]{background-color:#0d0d0d}.bg-\[\#090909\]{background-color:#090909}.bg-\[\#303030\]{background-color:#303030}.bg-background,.bg-background\/40{background-color:var(--background)}@supports (color:color-mix(in lab, red, red)){.bg-background\/40{background-color:color-mix(in oklab,var(--background)40%,transparent)}}.bg-black{background-color:var(--color-black)}.bg-blue-50{background-color:var(--color-blue-50)}.bg-blue-600{background-color:var(--color-blue-600)}.bg-border{background-color:var(--border)}.bg-brand-100{background-color:var(--brand-100)}.bg-brand-400{background-color:var(--brand-400)}.bg-brand-500{background-color:var(--brand-500)}.bg-brand-600{background-color:var(--brand-600)}.bg-card{background-color:var(--card)}.bg-destructive{background-color:var(--destructive)}.bg-gray-50{background-color:var(--color-gray-50)}.bg-muted,.bg-muted\/5{background-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.bg-muted\/5{background-color:color-mix(in oklab,var(--muted)5%,transparent)}}.bg-muted\/20{background-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.bg-muted\/20{background-color:color-mix(in oklab,var(--muted)20%,transparent)}}.bg-muted\/40{background-color:var(--muted)}@supports (color:color-mix(in lab, red, red)){.bg-muted\/40{background-color:color-mix(in oklab,var(--muted)40%,transparent)}}.bg-my-background{background-color:#090909}.bg-popover{background-color:var(--popover)}.bg-primary{background-color:var(--primary)}.bg-secondary{background-color:var(--secondary)}.bg-transparent{background-color:transparent}.bg-white{background-color:var(--color-white)}.bg-linear-to-l{--tw-gradient-position:to left}@supports (background-image:linear-gradient(in lab, red, red)){.bg-linear-to-l{--tw-gradient-position:to left in oklab}}.bg-linear-to-l{background-image:linear-gradient(var(--tw-gradient-stops))}.bg-linear-to-r{--tw-gradient-position:to right}@supports (background-image:linear-gradient(in lab, red, red)){.bg-linear-to-r{--tw-gradient-position:to right in oklab}}.bg-linear-to-r{background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-l{--tw-gradient-position:to left in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.bg-gradient-to-r{--tw-gradient-position:to right in oklab;background-image:linear-gradient(var(--tw-gradient-stops))}.from-background{--tw-gradient-from:var(--background);--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))}.fill-foreground{fill:var(--foreground)}.object-contain{object-fit:contain}.object-cover{object-fit:cover}.p-0{padding:calc(var(--spacing)*0)}.p-2{padding:calc(var(--spacing)*2)}.p-3{padding:calc(var(--spacing)*3)}.p-4{padding:calc(var(--spacing)*4)}.p-5{padding:calc(var(--spacing)*5)}.p-6{padding:calc(var(--spacing)*6)}.p-8{padding:calc(var(--spacing)*8)}.p-\[3px\]{padding:3px}.px-0{padding-inline:calc(var(--spacing)*0)}.px-1\.5{padding-inline:calc(var(--spacing)*1.5)}.px-2{padding-inline:calc(var(--spacing)*2)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-5{padding-inline:calc(var(--spacing)*5)}.px-6{padding-inline:calc(var(--spacing)*6)}.px-7{padding-inline:calc(var(--spacing)*7)}.px-8{padding-inline:calc(var(--spacing)*8)}.py-0\.5{padding-block:calc(var(--spacing)*.5)}.py-1{padding-block:calc(var(--spacing)*1)}.py-2{padding-block:calc(var(--spacing)*2)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-6{padding-block:calc(var(--spacing)*6)}.py-8{padding-block:calc(var(--spacing)*8)}.py-10{padding-block:calc(var(--spacing)*10)}.pt-0{padding-top:calc(var(--spacing)*0)}.pt-4{padding-top:calc(var(--spacing)*4)}.pt-16{padding-top:calc(var(--spacing)*16)}.pt-36{padding-top:calc(var(--spacing)*36)}.pt-\[90px\]{padding-top:90px}.pr-2{padding-right:calc(var(--spacing)*2)}.pr-2\.5{padding-right:calc(var(--spacing)*2.5)}.pr-4{padding-right:calc(var(--spacing)*4)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pb-8{padding-bottom:calc(var(--spacing)*8)}.pb-\[1\.5px\]{padding-bottom:1.5px}.pl-4{padding-left:calc(var(--spacing)*4)}.text-center{text-align:center}.text-left{text-align:left}.text-start{text-align:start}.align-middle{vertical-align:middle}.font-aeonik{font-family:var(--font-aeonik-regular)}.font-aeonik-bold{font-family:var(--font-aeonik-bold)}.font-sans{font-family:var(--font-geist-sans)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}.text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.text-\[3rem\]{font-size:3rem}.text-\[16px\]{font-size:16px}.text-\[250px\]{font-size:250px}.leading-5{--tw-leading:calc(var(--spacing)*5);line-height:calc(var(--spacing)*5)}.leading-none{--tw-leading:1;line-height:1}.leading-tight{--tw-leading:var(--leading-tight);line-height:var(--leading-tight)}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-\[-0\.00em\]{--tw-tracking:-0em;letter-spacing:0}.tracking-\[-0\.4px\]{--tw-tracking:-.4px;letter-spacing:-.4px}.tracking-\[-1\.2px\]{--tw-tracking:-1.2px;letter-spacing:-1.2px}.tracking-\[-1px\]{--tw-tracking:-1px;letter-spacing:-1px}.tracking-normal{--tw-tracking:var(--tracking-normal);letter-spacing:var(--tracking-normal)}.tracking-tight{--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight)}.tracking-tighter{--tw-tracking:var(--tracking-tighter);letter-spacing:var(--tracking-tighter)}.text-balance{text-wrap:balance}.text-nowrap{text-wrap:nowrap}.text-pretty{text-wrap:pretty}.break-words{overflow-wrap:break-word}.whitespace-nowrap{white-space:nowrap}.whitespace-pre-line{white-space:pre-line}.whitespace-pre-wrap{white-space:pre-wrap}.text-black{color:var(--color-black)}.text-blue-500{color:var(--color-blue-500)}.text-blue-800{color:var(--color-blue-800)}.text-brand-500,.text-brand-500\/75{color:var(--brand-500)}@supports (color:color-mix(in lab, red, red)){.text-brand-500\/75{color:color-mix(in oklab,var(--brand-500)75%,transparent)}}.text-brand-600{color:var(--brand-600)}.text-card-foreground{color:var(--card-foreground)}.text-destructive{color:var(--destructive)}.text-foreground{color:var(--foreground)}.text-gray-500{color:var(--color-gray-500)}.text-gray-700{color:var(--color-gray-700)}.text-gray-800{color:var(--color-gray-800)}.text-muted-foreground,.text-muted-foreground\/50{color:var(--muted-foreground)}@supports (color:color-mix(in lab, red, red)){.text-muted-foreground\/50{color:color-mix(in oklab,var(--muted-foreground)50%,transparent)}}.text-muted-foreground\/80{color:var(--muted-foreground)}@supports (color:color-mix(in lab, red, red)){.text-muted-foreground\/80{color:color-mix(in oklab,var(--muted-foreground)80%,transparent)}}.text-popover-foreground{color:var(--popover-foreground)}.text-primary{color:var(--primary)}.text-primary-foreground{color:var(--primary-foreground)}.text-primary\/50{color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.text-primary\/50{color:color-mix(in oklab,var(--primary)50%,transparent)}}.text-primary\/80{color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.text-primary\/80{color:color-mix(in oklab,var(--primary)80%,transparent)}}.text-secondary-foreground{color:var(--secondary-foreground)}.text-white{color:var(--color-white)}.text-white\/50{color:rgba(255,255,255,.5)}@supports (color:color-mix(in lab, red, red)){.text-white\/50{color:color-mix(in oklab,var(--color-white)50%,transparent)}}.text-white\/80{color:rgba(255,255,255,.8)}@supports (color:color-mix(in lab, red, red)){.text-white\/80{color:color-mix(in oklab,var(--color-white)80%,transparent)}}.capitalize{text-transform:capitalize}.uppercase{text-transform:uppercase}.underline{-webkit-text-decoration-line:underline;text-decoration-line:underline}.underline-offset-4{text-underline-offset:4px}.opacity-60{opacity:.6}.opacity-100{opacity:1}.shadow{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,rgba(0,0,0,.1)),0 1px 2px -1px var(--tw-shadow-color,rgba(0,0,0,.1));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,rgba(0,0,0,.1)),0 2px 4px -2px var(--tw-shadow-color,rgba(0,0,0,.1));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,rgba(0,0,0,.1)),0 1px 2px -1px var(--tw-shadow-color,rgba(0,0,0,.1));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xs{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,rgba(0,0,0,.05));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-2{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-primary{--tw-ring-color:var(--primary)}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.invert-75{--tw-invert:invert(75%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.backdrop-blur-lg{--tw-backdrop-blur:blur(var(--blur-lg));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.transition{transition-property:color,background-color,border-color,outline-color,-webkit-text-decoration-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-\[color\,box-shadow\]{transition-property:color,box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-all{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,-webkit-text-decoration-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.delay-75{transition-delay:75ms}.duration-150{--tw-duration:.15s;transition-duration:.15s}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}.duration-500{--tw-duration:.5s;transition-duration:.5s}.duration-700{--tw-duration:.7s;transition-duration:.7s}.ease-in-out{--tw-ease:var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.ease-out{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}.delay-75{--tw-animation-delay:75ms;animation-delay:75ms}.outline-none{--tw-outline-style:none;outline-style:none}.select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.\[--duration\:40s\]{--duration:40s}.\[--duration\:60s\]{--duration:60s}.\[--gap\:1rem\]{--gap:1rem}.\[animation-direction\:reverse\]{animation-direction:reverse}@media (hover:hover){.group-hover\:-translate-x-2:is(:where(.group):hover *){--tw-translate-x:calc(var(--spacing)*-2);translate:var(--tw-translate-x)var(--tw-translate-y)}.group-hover\:scale-100:is(:where(.group):hover *){--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;scale:var(--tw-scale-x)var(--tw-scale-y)}.group-hover\:scale-105:is(:where(.group):hover *){--tw-scale-x:105%;--tw-scale-y:105%;--tw-scale-z:105%;scale:var(--tw-scale-x)var(--tw-scale-y)}.group-hover\:bg-brand-500:is(:where(.group):hover *){background-color:var(--brand-500)}.group-hover\:text-white:is(:where(.group):hover *){color:var(--color-white)}.group-hover\:\[animation-play-state\:paused\]:is(:where(.group):hover *){animation-play-state:paused}}.group-data-\[disabled\=true\]\:pointer-events-none:is(:where(.group)[data-disabled=true] *){pointer-events:none}.group-data-\[disabled\=true\]\:opacity-50:is(:where(.group)[data-disabled=true] *){opacity:.5}.group-data-\[state\=open\]\:rotate-180:is(:where(.group)[data-state=open] *){rotate:180deg}.group-data-\[viewport\=false\]\/navigation-menu\:top-full:is(:where(.group\/navigation-menu)[data-viewport=false] *){top:100%}.group-data-\[viewport\=false\]\/navigation-menu\:mt-1\.5:is(:where(.group\/navigation-menu)[data-viewport=false] *){margin-top:calc(var(--spacing)*1.5)}.group-data-\[viewport\=false\]\/navigation-menu\:overflow-hidden:is(:where(.group\/navigation-menu)[data-viewport=false] *){overflow:hidden}.group-data-\[viewport\=false\]\/navigation-menu\:rounded-md:is(:where(.group\/navigation-menu)[data-viewport=false] *){border-radius:calc(var(--radius) - 2px)}.group-data-\[viewport\=false\]\/navigation-menu\:border:is(:where(.group\/navigation-menu)[data-viewport=false] *){border-style:var(--tw-border-style);border-width:1px}.group-data-\[viewport\=false\]\/navigation-menu\:bg-popover:is(:where(.group\/navigation-menu)[data-viewport=false] *){background-color:var(--popover)}.group-data-\[viewport\=false\]\/navigation-menu\:text-popover-foreground:is(:where(.group\/navigation-menu)[data-viewport=false] *){color:var(--popover-foreground)}.group-data-\[viewport\=false\]\/navigation-menu\:shadow:is(:where(.group\/navigation-menu)[data-viewport=false] *){--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,rgba(0,0,0,.1)),0 1px 2px -1px var(--tw-shadow-color,rgba(0,0,0,.1));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.group-data-\[viewport\=false\]\/navigation-menu\:duration-200:is(:where(.group\/navigation-menu)[data-viewport=false] *){--tw-duration:.2s;transition-duration:.2s}.peer-disabled\:cursor-not-allowed:is(:where(.peer):disabled~*){cursor:not-allowed}.peer-disabled\:opacity-50:is(:where(.peer):disabled~*){opacity:.5}.selection\:bg-primary ::selection,.selection\:bg-primary::selection{background-color:var(--primary)}.selection\:text-primary-foreground ::selection,.selection\:text-primary-foreground::selection{color:var(--primary-foreground)}.file\:inline-flex::-webkit-file-upload-button{display:inline-flex}.file\:inline-flex::file-selector-button{display:inline-flex}.file\:h-7::-webkit-file-upload-button{height:calc(var(--spacing)*7)}.file\:h-7::file-selector-button{height:calc(var(--spacing)*7)}.file\:border-0::-webkit-file-upload-button{border-style:var(--tw-border-style);border-width:0}.file\:border-0::file-selector-button{border-style:var(--tw-border-style);border-width:0}.file\:bg-transparent::-webkit-file-upload-button{background-color:transparent}.file\:bg-transparent::file-selector-button{background-color:transparent}.file\:text-sm::-webkit-file-upload-button{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.file\:text-sm::file-selector-button{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.file\:font-medium::-webkit-file-upload-button{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.file\:font-medium::file-selector-button{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.file\:text-foreground::-webkit-file-upload-button{color:var(--foreground)}.file\:text-foreground::file-selector-button{color:var(--foreground)}.placeholder\:text-muted-foreground::placeholder{color:var(--muted-foreground)}.last\:border-b-0:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}@media (hover:hover){.hover\:scale-\[98\%\]:hover{scale:98%}.hover\:bg-accent:hover{background-color:var(--accent)}.hover\:bg-black\/80:hover{background-color:rgba(0,0,0,.8)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-black\/80:hover{background-color:color-mix(in oklab,var(--color-black)80%,transparent)}}.hover\:bg-blue-700:hover{background-color:var(--color-blue-700)}.hover\:bg-brand-400:hover{background-color:var(--brand-400)}.hover\:bg-brand-500:hover{background-color:var(--brand-500)}.hover\:bg-destructive\/90:hover{background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-destructive\/90:hover{background-color:color-mix(in oklab,var(--destructive)90%,transparent)}}.hover\:bg-primary\/90:hover{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-primary\/90:hover{background-color:color-mix(in oklab,var(--primary)90%,transparent)}}.hover\:bg-secondary\/80:hover{background-color:var(--secondary)}@supports (color:color-mix(in lab, red, red)){.hover\:bg-secondary\/80:hover{background-color:color-mix(in oklab,var(--secondary)80%,transparent)}}.hover\:bg-transparent:hover{background-color:transparent}.hover\:bg-\[url\(\'\/gradient\.webp\'\)\]:hover{background-image:url(/gradient.webp)}.hover\:bg-cover:hover{background-size:cover}.hover\:bg-center:hover{background-position:50%}.hover\:bg-no-repeat:hover{background-repeat:no-repeat}.hover\:text-accent-foreground:hover{color:var(--accent-foreground)}.hover\:text-brand-500:hover,.hover\:text-brand-500\/80:hover{color:var(--brand-500)}@supports (color:color-mix(in lab, red, red)){.hover\:text-brand-500\/80:hover{color:color-mix(in oklab,var(--brand-500)80%,transparent)}}.hover\:text-brand-600\/80:hover{color:var(--brand-600)}@supports (color:color-mix(in lab, red, red)){.hover\:text-brand-600\/80:hover{color:color-mix(in oklab,var(--brand-600)80%,transparent)}}.hover\:text-primary:hover,.hover\:text-primary\/80:hover{color:var(--primary)}@supports (color:color-mix(in lab, red, red)){.hover\:text-primary\/80:hover{color:color-mix(in oklab,var(--primary)80%,transparent)}}.hover\:no-underline:hover{-webkit-text-decoration-line:none;text-decoration-line:none}.hover\:underline:hover{-webkit-text-decoration-line:underline;text-decoration-line:underline}.hover\:opacity-100:hover{opacity:1}.hover\:shadow-md:hover{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,rgba(0,0,0,.1)),0 2px 4px -2px var(--tw-shadow-color,rgba(0,0,0,.1));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}}.focus\:bg-accent:focus{background-color:var(--accent)}.focus\:text-accent-foreground:focus{color:var(--accent-foreground)}.focus-visible\:border-ring:focus-visible{border-color:var(--ring)}.focus-visible\:ring-\[3px\]:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(3px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-visible\:ring-destructive\/20:focus-visible{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.focus-visible\:ring-destructive\/20:focus-visible{--tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)}}.focus-visible\:ring-ring\/50:focus-visible{--tw-ring-color:var(--ring)}@supports (color:color-mix(in lab, red, red)){.focus-visible\:ring-ring\/50:focus-visible{--tw-ring-color:color-mix(in oklab,var(--ring)50%,transparent)}}.focus-visible\:outline-1:focus-visible{outline-style:var(--tw-outline-style);outline-width:1px}.focus-visible\:outline-ring:focus-visible{outline-color:var(--ring)}.disabled\:pointer-events-none:disabled{pointer-events:none}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:opacity-50:disabled{opacity:.5}.has-data-\[slot\=card-action\]\:grid-cols-\[1fr_auto\]:has([data-slot=card-action]){grid-template-columns:1fr auto}.has-\[\>svg\]\:px-2\.5:has(>svg){padding-inline:calc(var(--spacing)*2.5)}.has-\[\>svg\]\:px-3:has(>svg){padding-inline:calc(var(--spacing)*3)}.has-\[\>svg\]\:px-4:has(>svg){padding-inline:calc(var(--spacing)*4)}.aria-invalid\:border-destructive[aria-invalid=true]{border-color:var(--destructive)}.aria-invalid\:ring-destructive\/20[aria-invalid=true]{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.aria-invalid\:ring-destructive\/20[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--destructive)20%,transparent)}}.data-\[active\=true\]\:bg-accent\/50[data-active=true]{background-color:var(--accent)}@supports (color:color-mix(in lab, red, red)){.data-\[active\=true\]\:bg-accent\/50[data-active=true]{background-color:color-mix(in oklab,var(--accent)50%,transparent)}}.data-\[active\=true\]\:text-accent-foreground[data-active=true]{color:var(--accent-foreground)}@media (hover:hover){.data-\[active\=true\]\:hover\:bg-accent[data-active=true]:hover{background-color:var(--accent)}}.data-\[active\=true\]\:focus\:bg-accent[data-active=true]:focus{background-color:var(--accent)}.data-\[error\=true\]\:text-destructive[data-error=true]{color:var(--destructive)}.data-\[motion\=from-end\]\:slide-in-from-right-52[data-motion=from-end]{--tw-enter-translate-x:calc(52*var(--spacing))}.data-\[motion\=from-start\]\:slide-in-from-left-52[data-motion=from-start]{--tw-enter-translate-x:calc(52*var(--spacing)*-1)}.data-\[motion\=to-end\]\:slide-out-to-right-52[data-motion=to-end]{--tw-exit-translate-x:calc(52*var(--spacing))}.data-\[motion\=to-start\]\:slide-out-to-left-52[data-motion=to-start]{--tw-exit-translate-x:calc(52*var(--spacing)*-1)}.data-\[motion\^\=from-\]\:animate-in[data-motion^=from-]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\[motion\^\=from-\]\:fade-in[data-motion^=from-]{--tw-enter-opacity:0}.data-\[motion\^\=to-\]\:animate-out[data-motion^=to-]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\[motion\^\=to-\]\:fade-out[data-motion^=to-]{--tw-exit-opacity:0}.data-\[orientation\=horizontal\]\:h-px[data-orientation=horizontal]{height:1px}.data-\[orientation\=horizontal\]\:w-full[data-orientation=horizontal]{width:100%}.data-\[orientation\=vertical\]\:h-full[data-orientation=vertical]{height:100%}.data-\[orientation\=vertical\]\:w-px[data-orientation=vertical]{width:1px}:is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:ring-0 *)[data-slot=navigation-menu-link]:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}:is(.\*\*\:data-\[slot\=navigation-menu-link\]\:focus\:outline-none *)[data-slot=navigation-menu-link]:focus{--tw-outline-style:none;outline-style:none}.data-\[state\=active\]\:bg-background[data-state=active]{background-color:var(--background)}.data-\[state\=active\]\:shadow-sm[data-state=active]{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,rgba(0,0,0,.1)),0 1px 2px -1px var(--tw-shadow-color,rgba(0,0,0,.1));box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.data-\[state\=closed\]\:animate-accordion-up[data-state=closed]{animation:accordion-up var(--tw-animation-duration,var(--tw-duration,.2s))var(--tw-ease,ease-out)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\[state\=closed\]\:animate-out[data-state=closed]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\[state\=closed\]\:zoom-out-95[data-state=closed]{--tw-exit-scale:.95}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:animate-out:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=closed]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:fade-out-0:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=closed]{--tw-exit-opacity:0}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=closed\]\:zoom-out-95:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=closed]{--tw-exit-scale:.95}.data-\[state\=hidden\]\:animate-out[data-state=hidden]{animation:exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\[state\=hidden\]\:fade-out[data-state=hidden]{--tw-exit-opacity:0}.data-\[state\=open\]\:animate-accordion-down[data-state=open]{animation:accordion-down var(--tw-animation-duration,var(--tw-duration,.2s))var(--tw-ease,ease-out)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\[state\=open\]\:animate-in[data-state=open]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\[state\=open\]\:bg-accent\/50[data-state=open]{background-color:var(--accent)}@supports (color:color-mix(in lab, red, red)){.data-\[state\=open\]\:bg-accent\/50[data-state=open]{background-color:color-mix(in oklab,var(--accent)50%,transparent)}}.data-\[state\=open\]\:text-accent-foreground[data-state=open]{color:var(--accent-foreground)}.data-\[state\=open\]\:zoom-in-90[data-state=open]{--tw-enter-scale:.9}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:animate-in:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=open]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:fade-in-0:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=open]{--tw-enter-opacity:0}.group-data-\[viewport\=false\]\/navigation-menu\:data-\[state\=open\]\:zoom-in-95:is(:where(.group\/navigation-menu)[data-viewport=false] *)[data-state=open]{--tw-enter-scale:.95}@media (hover:hover){.data-\[state\=open\]\:hover\:bg-accent[data-state=open]:hover{background-color:var(--accent)}}.data-\[state\=open\]\:focus\:bg-accent[data-state=open]:focus{background-color:var(--accent)}.data-\[state\=visible\]\:animate-in[data-state=visible]{animation:enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease)var(--tw-animation-delay,0s)var(--tw-animation-iteration-count,1)var(--tw-animation-direction,normal)var(--tw-animation-fill-mode,none)}.data-\[state\=visible\]\:fade-in[data-state=visible]{--tw-enter-opacity:0}@supports (backdrop-blur:var(--tw)){.supports-backdrop-blur\:bg-background\/90{background-color:var(--background)}@supports (color:color-mix(in lab, red, red)){.supports-backdrop-blur\:bg-background\/90{background-color:color-mix(in oklab,var(--background)90%,transparent)}}}@media (min-width:40rem){.sm\:mx-4{margin-inline:calc(var(--spacing)*4)}.sm\:mt-8{margin-top:calc(var(--spacing)*8)}.sm\:mb-20{margin-bottom:calc(var(--spacing)*20)}.sm\:aspect-\[16\/9\]{aspect-ratio:16/9}.sm\:h-full{height:100%}.sm\:min-h-\[280px\]{min-height:280px}.sm\:max-w-2xl{max-width:var(--container-2xl)}.sm\:max-w-none{max-width:none}.sm\:max-w-xl{max-width:var(--container-xl)}.sm\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\:p-8{padding:calc(var(--spacing)*8)}.sm\:px-4{padding-inline:calc(var(--spacing)*4)}.sm\:px-6{padding-inline:calc(var(--spacing)*6)}.sm\:py-0{padding-block:calc(var(--spacing)*0)}.sm\:pt-16{padding-top:calc(var(--spacing)*16)}.sm\:text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}.sm\:text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.sm\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}}@media (min-width:48rem){.md\:absolute{position:absolute}.md\:static{position:static}.md\:left-auto{left:auto}.md\:mb-16{margin-bottom:calc(var(--spacing)*16)}.md\:ml-5{margin-left:calc(var(--spacing)*5)}.md\:block{display:block}.md\:flex{display:flex}.md\:hidden{display:none}.md\:aspect-auto{aspect-ratio:auto}.md\:size-7{width:calc(var(--spacing)*7);height:calc(var(--spacing)*7)}.md\:h-auto{height:auto}.md\:w-\[28rem\]{width:28rem}.md\:w-\[260px\]{width:260px}.md\:w-\[var\(--radix-navigation-menu-viewport-width\)\]{width:var(--radix-navigation-menu-viewport-width)}.md\:w-auto{width:auto}.md\:max-w-3xl{max-width:var(--container-3xl)}.md\:max-w-4xl{max-width:var(--container-4xl)}.md\:max-w-\[600px\]{max-width:600px}.md\:max-w-none{max-width:none}.md\:min-w-\[260px\]{min-width:260px}.md\:scale-110{--tw-scale-x:110%;--tw-scale-y:110%;--tw-scale-z:110%;scale:var(--tw-scale-x)var(--tw-scale-y)}.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.md\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.md\:flex-col{flex-direction:column}.md\:flex-row{flex-direction:row}.md\:items-center{align-items:center}.md\:justify-between{justify-content:space-between}.md\:gap-0{gap:calc(var(--spacing)*0)}.md\:gap-\[25px\]{gap:25px}.md\:rounded-\[30px\]{border-radius:30px}.md\:object-contain{object-fit:contain}.md\:p-6{padding:calc(var(--spacing)*6)}.md\:p-8{padding:calc(var(--spacing)*8)}.md\:px-0{padding-inline:calc(var(--spacing)*0)}.md\:px-4{padding-inline:calc(var(--spacing)*4)}.md\:px-7{padding-inline:calc(var(--spacing)*7)}.md\:px-10{padding-inline:calc(var(--spacing)*10)}.md\:pr-8{padding-right:calc(var(--spacing)*8)}.md\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.md\:text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}.md\:text-5xl{font-size:var(--text-5xl);line-height:var(--tw-leading,var(--text-5xl--line-height))}.md\:text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.md\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.md\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.md\:text-\[20px\]{font-size:20px}.md\:text-\[30px\]{font-size:30px}.md\:text-\[42px\]{font-size:42px}.md\:leading-\[1\.05em\]{--tw-leading:1.05em;line-height:1.05em}.md\:leading-\[1\.25em\]{--tw-leading:1.25em;line-height:1.25em}}@media (min-width:64rem){.lg\:col-span-2{grid-column:span 2/span 2}.lg\:col-span-3{grid-column:span 3/span 3}.lg\:mt-8{margin-top:calc(var(--spacing)*8)}.lg\:mt-20{margin-top:calc(var(--spacing)*20)}.lg\:flex{display:flex}.lg\:w-\[600px\]{width:600px}.lg\:max-w-\[780px\]{max-width:780px}.lg\:grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.lg\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.lg\:grid-cols-12{grid-template-columns:repeat(12,minmax(0,1fr))}.lg\:gap-16{gap:calc(var(--spacing)*16)}.lg\:px-8{padding-inline:calc(var(--spacing)*8)}.lg\:text-left{text-align:left}.lg\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.lg\:text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.lg\:text-5xl{font-size:var(--text-5xl);line-height:var(--tw-leading,var(--text-5xl--line-height))}.lg\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.lg\:text-\[65px\]{font-size:65px}.lg\:tracking-tight{--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight)}}@media (min-width:80rem){.xl\:col-span-3{grid-column:span 3/span 3}.xl\:mt-10{margin-top:calc(var(--spacing)*10)}.xl\:grid{display:grid}.xl\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.xl\:justify-end{justify-content:flex-end}.xl\:justify-start{justify-content:flex-start}.xl\:gap-8{gap:calc(var(--spacing)*8)}}@media (min-width:96rem){.\32 xl\:max-w-7xl{max-width:var(--container-7xl)}}.dark\:border-input:is(.dark *){border-color:var(--input)}.dark\:bg-destructive\/60:is(.dark *){background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\:bg-destructive\/60:is(.dark *){background-color:color-mix(in oklab,var(--destructive)60%,transparent)}}.dark\:bg-input\/30:is(.dark *){background-color:var(--input)}@supports (color:color-mix(in lab, red, red)){.dark\:bg-input\/30:is(.dark *){background-color:color-mix(in oklab,var(--input)30%,transparent)}}.dark\:text-muted-foreground:is(.dark *){color:var(--muted-foreground)}@media (hover:hover){.dark\:group-hover\:bg-brand-500:is(.dark *):is(:where(.group):hover *){background-color:var(--brand-500)}.dark\:group-hover\:text-black:is(.dark *):is(:where(.group):hover *){color:var(--color-black)}.dark\:hover\:bg-accent\/50:is(.dark *):hover{background-color:var(--accent)}@supports (color:color-mix(in lab, red, red)){.dark\:hover\:bg-accent\/50:is(.dark *):hover{background-color:color-mix(in oklab,var(--accent)50%,transparent)}}.dark\:hover\:bg-input\/50:is(.dark *):hover{background-color:var(--input)}@supports (color:color-mix(in lab, red, red)){.dark\:hover\:bg-input\/50:is(.dark *):hover{background-color:color-mix(in oklab,var(--input)50%,transparent)}}.dark\:hover\:bg-transparent:is(.dark *):hover{background-color:transparent}}.dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\:focus-visible\:ring-destructive\/40:is(.dark *):focus-visible{--tw-ring-color:color-mix(in oklab,var(--destructive)40%,transparent)}}.dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid=true]{--tw-ring-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){.dark\:aria-invalid\:ring-destructive\/40:is(.dark *)[aria-invalid=true]{--tw-ring-color:color-mix(in oklab,var(--destructive)40%,transparent)}}.dark\:data-\[state\=active\]\:border-input:is(.dark *)[data-state=active]{border-color:var(--input)}.dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state=active]{background-color:var(--input)}@supports (color:color-mix(in lab, red, red)){.dark\:data-\[state\=active\]\:bg-input\/30:is(.dark *)[data-state=active]{background-color:color-mix(in oklab,var(--input)30%,transparent)}}.dark\:data-\[state\=active\]\:bg-muted:is(.dark *)[data-state=active]{background-color:var(--muted)}.dark\:data-\[state\=active\]\:text-foreground:is(.dark *)[data-state=active]{color:var(--foreground)}.\[\&_\[data-rmiz-btn-unzoom\]\]\:absolute [data-rmiz-btn-unzoom]{position:absolute}.\[\&_\[data-rmiz-btn-unzoom\]\]\:top-5 [data-rmiz-btn-unzoom]{top:calc(var(--spacing)*5)}.\[\&_\[data-rmiz-btn-unzoom\]\]\:right-5 [data-rmiz-btn-unzoom]{right:calc(var(--spacing)*5)}.\[\&_\[data-rmiz-btn-unzoom\]\]\:bottom-auto [data-rmiz-btn-unzoom]{bottom:auto}.\[\&_\[data-rmiz-btn-unzoom\]\]\:left-auto [data-rmiz-btn-unzoom]{left:auto}.\[\&_\[data-rmiz-btn-unzoom\]\]\:z-\[1\] [data-rmiz-btn-unzoom]{z-index:1}.\[\&_\[data-rmiz-btn-unzoom\]\]\:m-0 [data-rmiz-btn-unzoom]{margin:calc(var(--spacing)*0)}.\[\&_\[data-rmiz-btn-unzoom\]\]\:size-10 [data-rmiz-btn-unzoom]{width:calc(var(--spacing)*10);height:calc(var(--spacing)*10)}.\[\&_\[data-rmiz-btn-unzoom\]\]\:cursor-zoom-out [data-rmiz-btn-unzoom]{cursor:zoom-out}.\[\&_\[data-rmiz-btn-unzoom\]\]\:touch-manipulation [data-rmiz-btn-unzoom]{touch-action:manipulation}.\[\&_\[data-rmiz-btn-unzoom\]\]\:appearance-none [data-rmiz-btn-unzoom]{-webkit-appearance:none;-moz-appearance:none;appearance:none}.\[\&_\[data-rmiz-btn-unzoom\]\]\:rounded-\[50\%\] [data-rmiz-btn-unzoom]{border-radius:50%}.\[\&_\[data-rmiz-btn-unzoom\]\]\:border-none [data-rmiz-btn-unzoom]{--tw-border-style:none;border-style:none}.\[\&_\[data-rmiz-btn-unzoom\]\]\:bg-foreground\/70 [data-rmiz-btn-unzoom]{background-color:var(--foreground)}@supports (color:color-mix(in lab, red, red)){.\[\&_\[data-rmiz-btn-unzoom\]\]\:bg-foreground\/70 [data-rmiz-btn-unzoom]{background-color:color-mix(in oklab,var(--foreground)70%,transparent)}}.\[\&_\[data-rmiz-btn-unzoom\]\]\:p-2 [data-rmiz-btn-unzoom]{padding:calc(var(--spacing)*2)}.\[\&_\[data-rmiz-btn-unzoom\]\]\:text-background [data-rmiz-btn-unzoom]{color:var(--background)}.\[\&_\[data-rmiz-btn-unzoom\]\]\:outline-offset-2 [data-rmiz-btn-unzoom]{outline-offset:2px}.\[\&_\[data-rmiz-btn-zoom\]\]\:absolute [data-rmiz-btn-zoom]{position:absolute}.\[\&_\[data-rmiz-btn-zoom\]\]\:top-2\.5 [data-rmiz-btn-zoom]{top:calc(var(--spacing)*2.5)}.\[\&_\[data-rmiz-btn-zoom\]\]\:right-2\.5 [data-rmiz-btn-zoom]{right:calc(var(--spacing)*2.5)}.\[\&_\[data-rmiz-btn-zoom\]\]\:bottom-auto [data-rmiz-btn-zoom]{bottom:auto}.\[\&_\[data-rmiz-btn-zoom\]\]\:left-auto [data-rmiz-btn-zoom]{left:auto}.\[\&_\[data-rmiz-btn-zoom\]\]\:m-0 [data-rmiz-btn-zoom]{margin:calc(var(--spacing)*0)}.\[\&_\[data-rmiz-btn-zoom\]\]\:size-10 [data-rmiz-btn-zoom]{width:calc(var(--spacing)*10);height:calc(var(--spacing)*10)}.\[\&_\[data-rmiz-btn-zoom\]\]\:cursor-zoom-in [data-rmiz-btn-zoom]{cursor:zoom-in}.\[\&_\[data-rmiz-btn-zoom\]\]\:touch-manipulation [data-rmiz-btn-zoom]{touch-action:manipulation}.\[\&_\[data-rmiz-btn-zoom\]\]\:appearance-none [data-rmiz-btn-zoom]{-webkit-appearance:none;-moz-appearance:none;appearance:none}.\[\&_\[data-rmiz-btn-zoom\]\]\:rounded-\[50\%\] [data-rmiz-btn-zoom]{border-radius:50%}.\[\&_\[data-rmiz-btn-zoom\]\]\:border-none [data-rmiz-btn-zoom]{--tw-border-style:none;border-style:none}.\[\&_\[data-rmiz-btn-zoom\]\]\:bg-foreground\/70 [data-rmiz-btn-zoom]{background-color:var(--foreground)}@supports (color:color-mix(in lab, red, red)){.\[\&_\[data-rmiz-btn-zoom\]\]\:bg-foreground\/70 [data-rmiz-btn-zoom]{background-color:color-mix(in oklab,var(--foreground)70%,transparent)}}.\[\&_\[data-rmiz-btn-zoom\]\]\:p-2 [data-rmiz-btn-zoom]{padding:calc(var(--spacing)*2)}.\[\&_\[data-rmiz-btn-zoom\]\]\:text-background [data-rmiz-btn-zoom]{color:var(--background)}.\[\&_\[data-rmiz-btn-zoom\]\]\:outline-offset-2 [data-rmiz-btn-zoom]{outline-offset:2px}.\[\&_\[data-rmiz-btn-zoom\]\:not\(\:focus\)\:not\(\:active\)\]\:pointer-events-none [data-rmiz-btn-zoom]:not(:focus):not(:active){pointer-events:none}.\[\&_\[data-rmiz-btn-zoom\]\:not\(\:focus\)\:not\(\:active\)\]\:absolute [data-rmiz-btn-zoom]:not(:focus):not(:active){position:absolute}.\[\&_\[data-rmiz-btn-zoom\]\:not\(\:focus\)\:not\(\:active\)\]\:size-px [data-rmiz-btn-zoom]:not(:focus):not(:active){width:1px;height:1px}.\[\&_\[data-rmiz-btn-zoom\]\:not\(\:focus\)\:not\(\:active\)\]\:overflow-hidden [data-rmiz-btn-zoom]:not(:focus):not(:active){overflow:hidden}.\[\&_\[data-rmiz-btn-zoom\]\:not\(\:focus\)\:not\(\:active\)\]\:whitespace-nowrap [data-rmiz-btn-zoom]:not(:focus):not(:active){white-space:nowrap}.\[\&_\[data-rmiz-btn-zoom\]\:not\(\:focus\)\:not\(\:active\)\]\:\[clip-path\:inset\(50\%\)\] [data-rmiz-btn-zoom]:not(:focus):not(:active){clip-path:inset(50%)}.\[\&_\[data-rmiz-btn-zoom\]\:not\(\:focus\)\:not\(\:active\)\]\:\[clip\:rect\(0_0_0_0\)\] [data-rmiz-btn-zoom]:not(:focus):not(:active){clip:rect(0 0 0 0)}.\[\&_\[data-rmiz-content\=\"found\"\]_\[data-zoom\]\]\:cursor-zoom-in [data-rmiz-content=found] [data-zoom],.\[\&_\[data-rmiz-content\=\"found\"\]_\[role\=\"img\"\]\]\:cursor-zoom-in [data-rmiz-content=found] [role=img],.\[\&_\[data-rmiz-content\=\"found\"\]_img\]\:cursor-zoom-in [data-rmiz-content=found] img,.\[\&_\[data-rmiz-content\=\"found\"\]_svg\]\:cursor-zoom-in [data-rmiz-content=found] svg{cursor:zoom-in}.\[\&_\[data-rmiz-ghost\]\]\:pointer-events-none [data-rmiz-ghost]{pointer-events:none}.\[\&_\[data-rmiz-ghost\]\]\:absolute [data-rmiz-ghost]{position:absolute}.\[\&_\[data-rmiz-modal-content\]\]\:relative [data-rmiz-modal-content]{position:relative}.\[\&_\[data-rmiz-modal-content\]\]\:size-full [data-rmiz-modal-content]{width:100%;height:100%}.\[\&_\[data-rmiz-modal-img\]\]\:absolute [data-rmiz-modal-img]{position:absolute}.\[\&_\[data-rmiz-modal-img\]\]\:origin-top-left [data-rmiz-modal-img]{transform-origin:0 0}.\[\&_\[data-rmiz-modal-img\]\]\:cursor-zoom-out [data-rmiz-modal-img]{cursor:zoom-out}.\[\&_\[data-rmiz-modal-img\]\]\:transition-transform [data-rmiz-modal-img]{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}@media (prefers-reduced-motion:reduce){.motion-reduce\:\[\&_\[data-rmiz-modal-img\]\]\:transition-none [data-rmiz-modal-img]{transition-property:none}}.\[\&_\[data-rmiz-modal-overlay\=\"hidden\"\]\]\:bg-transparent [data-rmiz-modal-overlay=hidden]{background-color:transparent}.\[\&_\[data-rmiz-modal-overlay\=\"visible\"\]\]\:bg-background\/80 [data-rmiz-modal-overlay=visible]{background-color:var(--background)}@supports (color:color-mix(in lab, red, red)){.\[\&_\[data-rmiz-modal-overlay\=\"visible\"\]\]\:bg-background\/80 [data-rmiz-modal-overlay=visible]{background-color:color-mix(in oklab,var(--background)80%,transparent)}}.\[\&_\[data-rmiz-modal-overlay\=\"visible\"\]\]\:bg-brand-100\/80 [data-rmiz-modal-overlay=visible]{background-color:var(--brand-100)}@supports (color:color-mix(in lab, red, red)){.\[\&_\[data-rmiz-modal-overlay\=\"visible\"\]\]\:bg-brand-100\/80 [data-rmiz-modal-overlay=visible]{background-color:color-mix(in oklab,var(--brand-100)80%,transparent)}}.\[\&_\[data-rmiz-modal-overlay\=\"visible\"\]\]\:backdrop-blur-md [data-rmiz-modal-overlay=visible]{--tw-backdrop-blur:blur(var(--blur-md));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.\[\&_\[data-rmiz-modal-overlay\]\]\:absolute [data-rmiz-modal-overlay]{position:absolute}.\[\&_\[data-rmiz-modal-overlay\]\]\:inset-0 [data-rmiz-modal-overlay]{inset:calc(var(--spacing)*0)}.\[\&_\[data-rmiz-modal-overlay\]\]\:transition-all [data-rmiz-modal-overlay]{transition-property:all;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}@media (prefers-reduced-motion:reduce){.motion-reduce\:\[\&_\[data-rmiz-modal-overlay\]\]\:transition-none [data-rmiz-modal-overlay]{transition-property:none}}.\[\&_svg\]\:pointer-events-none svg{pointer-events:none}.\[\&_svg\]\:shrink-0 svg{flex-shrink:0}.\[\&_svg\:not\(\[class\*\=\'size-\'\]\)\]\:size-4 svg:not([class*=size-]){width:calc(var(--spacing)*4);height:calc(var(--spacing)*4)}.\[\&_svg\:not\(\[class\*\=\'text-\'\]\)\]\:text-muted-foreground svg:not([class*=text-]){color:var(--muted-foreground)}.\[\&\:\:backdrop\]\:hidden::backdrop{display:none}.\[\&\:hover_\.external-link-icon\]\:opacity-100:hover .external-link-icon{opacity:1}.\[\&\:hover_\.social-icon-border\]\:border-brand-600:hover .social-icon-border{border-color:var(--brand-600)}.\[\.border-b\]\:pb-6.border-b{padding-bottom:calc(var(--spacing)*6)}.\[\.border-t\]\:pt-6.border-t{padding-top:calc(var(--spacing)*6)}.\[\&\>svg\]\:pointer-events-none>svg{pointer-events:none}.\[\&\>svg\]\:size-3>svg{width:calc(var(--spacing)*3);height:calc(var(--spacing)*3)}.\[\&\[data-state\=open\]\>svg\]\:rotate-180[data-state=open]>svg{rotate:180deg}.\[\&\[open\]\]\:fixed[open]{position:fixed}.\[\&\[open\]\]\:m-0[open]{margin:calc(var(--spacing)*0)}.\[\&\[open\]\]\:h-dvh[open]{height:100dvh}.\[\&\[open\]\]\:max-h-none[open]{max-height:none}.\[\&\[open\]\]\:w-dvw[open]{width:100dvw}.\[\&\[open\]\]\:max-w-none[open]{max-width:none}.\[\&\[open\]\]\:overflow-hidden[open]{overflow:hidden}.\[\&\[open\]\]\:border-0[open]{border-style:var(--tw-border-style);border-width:0}.\[\&\[open\]\]\:bg-transparent[open]{background-color:transparent}.\[\&\[open\]\]\:p-0[open]{padding:calc(var(--spacing)*0)}@media (hover:hover){a.\[a\&\]\:hover\:bg-accent:hover{background-color:var(--accent)}a.\[a\&\]\:hover\:bg-destructive\/90:hover{background-color:var(--destructive)}@supports (color:color-mix(in lab, red, red)){a.\[a\&\]\:hover\:bg-destructive\/90:hover{background-color:color-mix(in oklab,var(--destructive)90%,transparent)}}a.\[a\&\]\:hover\:bg-primary\/90:hover{background-color:var(--primary)}@supports (color:color-mix(in lab, red, red)){a.\[a\&\]\:hover\:bg-primary\/90:hover{background-color:color-mix(in oklab,var(--primary)90%,transparent)}}a.\[a\&\]\:hover\:bg-secondary\/90:hover{background-color:var(--secondary)}@supports (color:color-mix(in lab, red, red)){a.\[a\&\]\:hover\:bg-secondary\/90:hover{background-color:color-mix(in oklab,var(--secondary)90%,transparent)}}a.\[a\&\]\:hover\:text-accent-foreground:hover{color:var(--accent-foreground)}}.text-md{font-size:16px}.no-scrollbar::-webkit-scrollbar{display:none}.no-scrollbar{-ms-overflow-style:none;scrollbar-width:none}.border-grid{--tw-border-style:dashed;border-style:dashed;border-color:var(--border)}@supports (color:color-mix(in lab, red, red)){.border-grid{border-color:color-mix(in oklab,var(--border)50%,transparent)}}.border-grid:is(.dark *){border-color:var(--border)}.container-wrapper{--tw-border-style:dashed;border-style:dashed;border-color:var(--border);width:100%;max-width:1400px;margin-left:auto;margin-right:auto}@supports (color:color-mix(in lab, red, red)){.container-wrapper{border-color:color-mix(in oklab,var(--border)70%,transparent)}}@media (min-width:1400px){.container-wrapper{border-inline-style:var(--tw-border-style);border-left-width:1px;border-right-width:1px}}@media (min-width:1800px){.container-wrapper{max-width:var(--breakpoint-2xl)}}.container-wrapper:is(.dark *){border-color:var(--border)}.container{max-width:var(--breakpoint-2xl);padding-inline:calc(var(--spacing)*4);margin-left:auto;margin-right:auto}@media (min-width:64rem){.container{padding-inline:calc(var(--spacing)*6)}}@media (min-width:80rem){.container{padding-inline:calc(var(--spacing)*8)}}}@property --tw-animation-delay{syntax:"*";inherits:false;initial-value:0s}@property --tw-animation-direction{syntax:"*";inherits:false;initial-value:normal}@property --tw-animation-duration{syntax:"*";inherits:false}@property --tw-animation-fill-mode{syntax:"*";inherits:false;initial-value:none}@property --tw-animation-iteration-count{syntax:"*";inherits:false;initial-value:1}@property --tw-enter-blur{syntax:"*";inherits:false;initial-value:0}@property --tw-enter-opacity{syntax:"*";inherits:false;initial-value:1}@property --tw-enter-rotate{syntax:"*";inherits:false;initial-value:0}@property --tw-enter-scale{syntax:"*";inherits:false;initial-value:1}@property --tw-enter-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-enter-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-exit-blur{syntax:"*";inherits:false;initial-value:0}@property --tw-exit-opacity{syntax:"*";inherits:false;initial-value:1}@property --tw-exit-rotate{syntax:"*";inherits:false;initial-value:0}@property --tw-exit-scale{syntax:"*";inherits:false;initial-value:1}@property --tw-exit-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-exit-translate-y{syntax:"*";inherits:false;initial-value:0}:root{--radius:.625rem;--background:#fff;--foreground:#0a0a0a;--card:#fff;--card-foreground:#0a0a0a;--popover:#fff;--popover-foreground:#0a0a0a;--primary:#171717;--primary-foreground:#fafafa;--secondary:#f5f5f5;--secondary-foreground:#171717;--muted:#f5f5f5;--muted-foreground:#737373;--accent:#f5f5f5;--accent-foreground:#171717;--destructive:#e40014;--border:#e5e5e5;--input:#e5e5e5;--ring:#a1a1a1;--chart-1:#f05100;--chart-2:#009588;--chart-3:#104e64;--chart-4:#fcbb00;--chart-5:#f99c00;--sidebar:#fafafa;--sidebar-foreground:#0a0a0a;--sidebar-primary:#171717;--sidebar-primary-foreground:#fafafa;--sidebar-accent:#f5f5f5;--sidebar-accent-foreground:#171717;--sidebar-border:#e5e5e5;--sidebar-ring:#a1a1a1;--brand-100:#fbffce;--brand-200:#f7ff9d;--brand-300:#f3ff6c;--brand-400:#efff3b;--brand-500:#ebff0a;--brand-600:#bccc08;--brand-700:#8d9906;--brand-800:#5e6604;--brand-900:#2f3302}@supports (color:color(display-p3 0 0 0)){:root{--background:color(display-p3 1 1 1);--foreground:color(display-p3 .0393882 .0393882 .0393882);--card:color(display-p3 1 1 1);--card-foreground:color(display-p3 .0393882 .0393882 .0393882);--popover:color(display-p3 1 1 1);--popover-foreground:color(display-p3 .0393882 .0393882 .0393882);--primary:color(display-p3 .0905274 .0905274 .0905274);--primary-foreground:color(display-p3 .980256 .980256 .980256);--secondary:color(display-p3 .960587 .960587 .960587);--secondary-foreground:color(display-p3 .0905274 .0905274 .0905274);--muted:color(display-p3 .960587 .960587 .960587);--muted-foreground:color(display-p3 .451519 .451519 .451519);--accent:color(display-p3 .960587 .960587 .960587);--accent-foreground:color(display-p3 .0905274 .0905274 .0905274);--destructive:color(display-p3 .830323 .140383 .133196);--border:color(display-p3 .898161 .898161 .898161);--input:color(display-p3 .898161 .898161 .898161);--ring:color(display-p3 .630163 .630163 .630163);--chart-1:color(display-p3 .887467 .341665 .0219962);--chart-2:color(display-p3 .207114 .579584 .53668);--chart-3:color(display-p3 .142586 .302008 .385094);--chart-4:color(display-p3 .959186 .738519 .118268);--chart-5:color(display-p3 .93994 .620584 .0585367);--sidebar:color(display-p3 .980256 .980256 .980256);--sidebar-foreground:color(display-p3 .0393882 .0393882 .0393882);--sidebar-primary:color(display-p3 .0905274 .0905274 .0905274);--sidebar-primary-foreground:color(display-p3 .980256 .980256 .980256);--sidebar-accent:color(display-p3 .960587 .960587 .960587);--sidebar-accent-foreground:color(display-p3 .0905274 .0905274 .0905274);--sidebar-border:color(display-p3 .898161 .898161 .898161);--sidebar-ring:color(display-p3 .630163 .630163 .630163)}}@supports (color:lab(0% 0 0)){:root{--background:lab(100% 0 0);--foreground:lab(2.75381% 0 0);--card:lab(100% 0 0);--card-foreground:lab(2.75381% 0 0);--popover:lab(100% 0 0);--popover-foreground:lab(2.75381% 0 0);--primary:lab(7.78201% -.0000149012 0);--primary-foreground:lab(98.26% 0 0);--secondary:lab(96.52% -.0000596046 0);--secondary-foreground:lab(7.78201% -.0000149012 0);--muted:lab(96.52% -.0000596046 0);--muted-foreground:lab(48.496% 0 0);--accent:lab(96.52% -.0000596046 0);--accent-foreground:lab(7.78201% -.0000149012 0);--destructive:lab(48.4493% 77.4328 61.5452);--border:lab(90.952% -.0000596046 0);--input:lab(90.952% -.0000596046 0);--ring:lab(66.128% -.0000298023 .0000119209);--chart-1:lab(57.1026% 64.2584 89.8886);--chart-2:lab(55.0223% -41.0774 -3.90277);--chart-3:lab(30.372% -13.1853 -18.7887);--chart-4:lab(80.1641% 16.6016 99.2089);--chart-5:lab(72.7183% 31.8672 97.9407);--sidebar:lab(98.26% 0 0);--sidebar-foreground:lab(2.75381% 0 0);--sidebar-primary:lab(7.78201% -.0000149012 0);--sidebar-primary-foreground:lab(98.26% 0 0);--sidebar-accent:lab(96.52% -.0000596046 0);--sidebar-accent-foreground:lab(7.78201% -.0000149012 0);--sidebar-border:lab(90.952% -.0000596046 0);--sidebar-ring:lab(66.128% -.0000298023 .0000119209)}}.dark{--background:#0a0a0a;--foreground:#fafafa;--card:#171717;--card-foreground:#fafafa;--popover:#171717;--popover-foreground:#fafafa;--primary:#e5e5e5;--primary-foreground:#171717;--secondary:#262626;--secondary-foreground:#fafafa;--muted:#262626;--muted-foreground:#a1a1a1;--accent:#262626;--accent-foreground:#fafafa;--destructive:#ff6568;--border:rgba(255,255,255,.1);--input:rgba(255,255,255,.15);--ring:#737373;--chart-1:#1447e6;--chart-2:#00bb7f;--chart-3:#f99c00;--chart-4:#ac4bff;--chart-5:#ff2357;--sidebar:#171717;--sidebar-foreground:#fafafa;--sidebar-primary:#1447e6;--sidebar-primary-foreground:#fafafa;--sidebar-accent:#262626;--sidebar-accent-foreground:#fafafa;--sidebar-border:rgba(255,255,255,.1);--sidebar-ring:#737373;--brand-100:#2f3302;--brand-200:#5e6604;--brand-300:#8d9906;--brand-400:#bccc08;--brand-500:#ebff0a;--brand-600:#efff3b;--brand-700:#f3ff6c;--brand-800:#f7ff9d;--brand-900:#fbffce}@supports (color:color(display-p3 0 0 0)){.dark{--background:color(display-p3 .0393882 .0393882 .0393882);--foreground:color(display-p3 .980256 .980256 .980256);--card:color(display-p3 .0905274 .0905274 .0905274);--card-foreground:color(display-p3 .980256 .980256 .980256);--popover:color(display-p3 .0905274 .0905274 .0905274);--popover-foreground:color(display-p3 .980256 .980256 .980256);--primary:color(display-p3 .898161 .898161 .898161);--primary-foreground:color(display-p3 .0905274 .0905274 .0905274);--secondary:color(display-p3 .149382 .149382 .149382);--secondary-foreground:color(display-p3 .980256 .980256 .980256);--muted:color(display-p3 .149382 .149382 .149382);--muted-foreground:color(display-p3 .630163 .630163 .630163);--accent:color(display-p3 .149382 .149382 .149382);--accent-foreground:color(display-p3 .980256 .980256 .980256);--destructive:color(display-p3 .933534 .431676 .423491);--border:color(display-p3 1 1 1/.1);--input:color(display-p3 1 1 1/.15);--ring:color(display-p3 .451519 .451519 .451519);--chart-1:color(display-p3 .1379 .274983 .867624);--chart-2:color(display-p3 .267113 .726847 .508397);--chart-3:color(display-p3 .93994 .620584 .0585367);--chart-4:color(display-p3 .629519 .30089 .990817);--chart-5:color(display-p3 .921824 .240748 .355666);--sidebar:color(display-p3 .0905274 .0905274 .0905274);--sidebar-foreground:color(display-p3 .980256 .980256 .980256);--sidebar-primary:color(display-p3 .1379 .274983 .867624);--sidebar-primary-foreground:color(display-p3 .980256 .980256 .980256);--sidebar-accent:color(display-p3 .149382 .149382 .149382);--sidebar-accent-foreground:color(display-p3 .980256 .980256 .980256);--sidebar-border:color(display-p3 1 1 1/.1);--sidebar-ring:color(display-p3 .451519 .451519 .451519)}}@supports (color:lab(0% 0 0)){.dark{--background:lab(2.75381% 0 0);--foreground:lab(98.26% 0 0);--card:lab(7.78201% -.0000149012 0);--card-foreground:lab(98.26% 0 0);--popover:lab(7.78201% -.0000149012 0);--popover-foreground:lab(98.26% 0 0);--primary:lab(90.952% -.0000596046 0);--primary-foreground:lab(7.78201% -.0000149012 0);--secondary:lab(15.204% 0 0);--secondary-foreground:lab(98.26% 0 0);--muted:lab(15.204% 0 0);--muted-foreground:lab(66.128% -.0000298023 .0000119209);--accent:lab(15.204% 0 0);--accent-foreground:lab(98.26% 0 0);--destructive:lab(63.7053% 60.7449 31.3109);--border:lab(100% 0 0/.1);--input:lab(100% 0 0/.15);--ring:lab(48.496% 0 0);--chart-1:lab(36.9089% 35.0961 -85.6872);--chart-2:lab(66.9756% -58.27 19.5419);--chart-3:lab(72.7183% 31.8672 97.9407);--chart-4:lab(52.0183% 66.11 -78.2316);--chart-5:lab(56.101% 79.4329 31.4532);--sidebar:lab(7.78201% -.0000149012 0);--sidebar-foreground:lab(98.26% 0 0);--sidebar-primary:lab(36.9089% 35.0961 -85.6872);--sidebar-primary-foreground:lab(98.26% 0 0);--sidebar-accent:lab(15.204% 0 0);--sidebar-accent-foreground:lab(98.26% 0 0);--sidebar-border:lab(100% 0 0/.1);--sidebar-ring:lab(48.496% 0 0)}}@property --tw-border-spacing-x{syntax:"<length>";inherits:false;initial-value:0}@property --tw-border-spacing-y{syntax:"<length>";inherits:false;initial-value:0}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:transparent}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:transparent}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:transparent}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-leading{syntax:"*";inherits:false}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 transparent}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 transparent}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 transparent}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 transparent}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 transparent}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(360deg)}}@keyframes enter{0%{opacity:var(--tw-enter-opacity,1);transform:translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));filter:blur(var(--tw-enter-blur,0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity,1);transform:translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));filter:blur(var(--tw-exit-blur,0))}}@keyframes accordion-down{0%{height:0}to{height:var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))))}}@keyframes accordion-up{0%{height:var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,var(--kb-accordion-content-height,auto))))}to{height:0}}@keyframes marquee{0%{transform:translate(0)}to{transform:translateX(calc(-100% - var(--gap)))}}@keyframes marquee-vertical{0%{transform:translateY(0)}to{transform:translateY(calc(-100% - var(--gap)))}}
@font-face{font-family:aeonikBold;src:url(../media/fonnts_com_Aeonik_Bold-s.p.895166fa.ttf)format("truetype");font-display:swap}@font-face{font-family:aeonikBold Fallback;src:local(Arial);ascent-override:93.63%;descent-override:18.73%;line-gap-override:0.0%;size-adjust:106.8%}.aeonikbold_b98b5370-module__HbNZGa__className{font-family:aeonikBold,aeonikBold Fallback}.aeonikbold_b98b5370-module__HbNZGa__variable{--font-aeonik-bold:"aeonikBold","aeonikBold Fallback"}
@font-face{font-family:aeonikRegular;src:url(../media/fonnts_com_Aeonik_Regular-s.p.62bbd4fd.ttf)format("truetype");font-display:swap}@font-face{font-family:aeonikRegular Fallback;src:local(Arial);ascent-override:98.63%;descent-override:19.73%;line-gap-override:0.0%;size-adjust:101.39%}.aeonikregular_756171f0-module__It3Qua__className{font-family:aeonikRegular,aeonikRegular Fallback}.aeonikregular_756171f0-module__It3Qua__variable{--font-aeonik-regular:"aeonikRegular","aeonikRegular Fallback"}
