# @calcom/embed-snippet

## 1.3.3

### Patch Changes

- Added react-19 as peer dependency
- Updated dependencies
  - @calcom/embed-core@1.5.3

## 1.3.2

### Patch Changes

- Bundle size reduction for embed-react
- Updated dependencies
  - @calcom/embed-core@1.5.2

## 1.3.1

### Patch Changes

- Ships latest types of new events added like bookingSuccessfulV2
- Updated dependencies
  - @calcom/embed-core@1.5.1

## 1.3.0

### Minor Changes

- Added namespacing support throughout

### Patch Changes

- Updated dependencies
  - @calcom/embed-core@1.5.0

## 1.2.0

### Minor Changes

- Added a few more events

### Patch Changes

- Updated dependencies
  - @calcom/embed-core@1.4.0

## 1.1.2

### Patch Changes

- Improve UI instruction layout typings
- Updated dependencies
  - @calcom/embed-core@1.3.2

## 1.1.1

### Patch Changes

- layout type fix as zod-utils can't be used in npm package
- Updated dependencies
  - @calcom/embed-core@1.3.1

## 1.1.0

### Minor Changes

- Supports new booker layout

### Patch Changes

- Updated dependencies
  - @calcom/embed-core@1.3.0

## 1.0.9

### Patch Changes

- Fix the build for embed-react
- Updated dependencies
  - @calcom/embed-core@1.2.1

## 1.0.8

### Patch Changes

- Updated dependencies
  - @calcom/embed-core@1.2.0

## 1.0.7

### Patch Changes

- Add changesets. Use prepack instead of prePublish and prepublish only as that works with both yarn and npm
- Updated dependencies
  - @calcom/embed-core@1.1.5
