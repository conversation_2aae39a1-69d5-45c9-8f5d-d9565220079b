import FeatureTableLg from './compare-table-lg';
import FeatureTableSm from './compare-table-sm';

export default function Comparison() {
  return (
    <section className="mb-40 w-full">
      <div className="mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl">
        <h2 className="mx-auto mb-14 max-w-[320px] whitespace-pre-wrap text-center font-aeonik font-medium text-3xl leading-tight sm:max-w-2xl sm:text-4xl md:text-4xl lg:text-5xl">
          How does Highlight AI compare against other tools?
        </h2>
        <div className="flex flex-col items-center justify-center gap-8">
          <div className="flex w-full items-center justify-center align-center font-aeonik">
            <FeatureTableLg />
            <FeatureTableSm />
          </div>
        </div>
      </div>
    </section>
  );
}
