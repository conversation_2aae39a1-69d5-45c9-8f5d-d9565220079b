import Link from 'next/link';
import { Button } from '@/components/ui/button';
import LogoCloud from './logo-cloud';

export default function HeroHome() {
  return (
    <section
      aria-labelledby="hero-section"
      className="relative mt-10 mb-[160px] flex h-full flex-col justify-center sm:h-full lg:mt-20"
    >
      <div className="grid grid-cols-1 items-center gap-8 py-8 sm:py-0 lg:grid-cols-1">
        <div className="flex flex-col items-center justify-center py-8 text-center sm:min-h-[280px] lg:text-left">
          <div className="flex w-full flex-col">
            <h1 className="flex flex-col items-center">
              <div className="mx-auto inline-block max-w-[440px] text-pretty break-words text-center font-aeonik font-medium text-4xl text-white/80 tracking-none md:max-w-[600px] md:text-5xl md:leading-[1.05em] lg:max-w-[780px] lg:text-[65px]">
                The AI Assistant that works everywhere you do.
              </div>
            </h1>
          </div>
          <p className="mt-4 max-w-2xl whitespace-pre-line text-center font-aeonik font-normal text-lg text-muted-foreground sm:mt-8 md:text-xl">
            Highlight keeps track of your meetings, chats, and tasks so you can
            find answers, create, and act, all in one place, personalized for
            you.
          </p>
          <div className="mt-6 flex items-center gap-6 lg:mt-8 xl:mt-10">
            <Button
              asChild
              className="rounded-full bg-brand-500 hover:bg-brand-400"
              size={'lg'}
            >
              <Link href="/book">Book Demo</Link>
            </Button>
            <Button
              asChild
              className="rounded-full bg-brand-500 hover:bg-brand-400"
              size={'lg'}
              variant={'outline'}
            >
              <Link href="/about">Learn more</Link>
            </Button>
          </div>
        </div>
        <div className="flex flex-col gap-12 md:flex-col lg:gap-16">
          {/* companies we work with */}
          <section className="flex flex-col items-center gap-4">
            <div className="flex flex-col items-center font-aeonik">
              <h2 className="max-w-[200px] text-center font-medium text-md text-muted-foreground/50 sm:max-w-none sm:text-lg">
                Loved by 100,000+ users and teams worldwide!
              </h2>
            </div>
            <LogoCloud />
          </section>
          {/* hero image */}
          <section className="aspect-[1/1] w-full sm:aspect-[16/9] md:p-8">
            <div className="relative h-full w-full overflow-hidden rounded-[20px] bg-[#0D0D0D] md:rounded-[30px]">
              <video
                aria-label="Demo video 1"
                autoPlay
                className="absolute inset-0 h-full w-full object-cover"
                loop
                muted
                playsInline
                src="https://cdn.highlightai.com/media/landing/misc/hero_demo.webm"
              >
                <track kind="captions" label="English captions" srcLang="en" />
              </video>
            </div>
          </section>
        </div>
      </div>
    </section>
  );
}
