(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,81808,68768,e=>{"use strict";e.s(["Slot",()=>u,"createSlot",()=>l],81808);var t=e.i(6943);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{let n=!1,o=t.map(t=>{let o=r(t,e);return n||"function"!=typeof o||(n=!0),o});if(n)return()=>{for(let e=0;e<o.length;e++){let n=o[e];"function"==typeof n?n():r(t[e],null)}}}}function o(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return t.useCallback(n(...r),r)}e.s(["composeRefs",()=>n,"useComposedRefs",()=>o],68768);var i=e.i(65830);function l(e){let r=function(e){let r=t.forwardRef((e,r)=>{let{children:o,...i}=e;if(t.isValidElement(o)){var l,u,a;let e,s,c=(s=(e=null==(u=Object.getOwnPropertyDescriptor((l=o).props,"ref"))?void 0:u.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(s=(e=null==(a=Object.getOwnPropertyDescriptor(l,"ref"))?void 0:a.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref,f=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=i(...t);return o(...t),n}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(i,o.props);return o.type!==t.Fragment&&(f.ref=r?n(r,c):c),t.cloneElement(o,f)}return t.Children.count(o)>1?t.Children.only(null):null});return r.displayName="".concat(e,".SlotClone"),r}(e),o=t.forwardRef((e,n)=>{let{children:o,...l}=e,u=t.Children.toArray(o),a=u.find(s);if(a){let e=a.props.children,o=u.map(r=>r!==a?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,i.jsx)(r,{...l,ref:n,children:t.isValidElement(e)?t.cloneElement(e,void 0,o):null})}return(0,i.jsx)(r,{...l,ref:n,children:o})});return o.displayName="".concat(e,".Slot"),o}var u=l("Slot"),a=Symbol("radix.slottable");function s(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}},25666,e=>{"use strict";e.s(["Primitive",()=>i,"dispatchDiscreteCustomEvent",()=>l]);var t=e.i(6943),r=e.i(14931),n=e.i(81808),o=e.i(65830),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let i=(0,n.createSlot)("Primitive.".concat(r)),l=t.forwardRef((e,t)=>{let{asChild:n,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(n?i:r,{...l,ref:t})});return l.displayName="Primitive.".concat(r),{...e,[r]:l}},{});function l(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},91076,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return u}});let n=e.r(6943),o="undefined"==typeof window,i=o?()=>{}:n.useLayoutEffect,l=o?()=>{}:n.useEffect;function u(e){let{headManager:t,reduceComponentsToState:r}=e;function u(){if(t&&t.mountedInstances){let o=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(o,e))}}if(o){var a;null==t||null==(a=t.mountedInstances)||a.add(e.children),u()}return i(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),i(()=>(t&&(t._pendingUpdate=u),()=>{t&&(t._pendingUpdate=u)})),l(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},91007,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"AmpStateContext",{enumerable:!0,get:function(){return n}});let n=e.r(81258)._(e.r(6943)).default.createContext({})},8677,(e,t,r)=>{"use strict";function n(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isInAmpMode",{enumerable:!0,get:function(){return n}})},6018,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return m},defaultHead:function(){return f}});let n=e.r(81258),o=e.r(44066),i=e.r(65830),l=o._(e.r(6943)),u=n._(e.r(91076)),a=e.r(91007),s=e.r(64943),c=e.r(8677);function f(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===l.default.Fragment?e.concat(l.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}e.r(69214);let p=["name","httpEquiv","charSet","itemProp"];function g(e,t){let{inAmpMode:r}=t;return e.reduce(d,[]).reverse().concat(f(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let i=!0,l=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){l=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?i=!1:t.add(o.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(o.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!l)&&r.has(e)?i=!1:(r.add(e),n[t]=r)}}}return i}}()).reverse().map((e,t)=>{let r=e.key||t;return l.default.cloneElement(e,{key:r})})}let m=function(e){let{children:t}=e,r=(0,l.useContext)(a.AmpStateContext),n=(0,l.useContext)(s.HeadManagerContext);return(0,i.jsx)(u.default,{reduceComponentsToState:g,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},65169,(e,t,r)=>{"use strict";function n(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:o,blurDataURL:i,objectFit:l}=e,u=n?40*n:t,a=o?40*o:r,s=u&&a?"viewBox='0 0 "+u+" "+a+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+s+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(s?"none":"contain"===l?"xMidYMid":"cover"===l?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getImageBlurSvg",{enumerable:!0,get:function(){return n}})},21531,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{VALID_LOADERS:function(){return n},imageConfigDefault:function(){return o}});let n=["default","imgix","cloudinary","akamai","custom"],o={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},16783,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getImgProps",{enumerable:!0,get:function(){return a}}),e.r(69214);let n=e.r(65169),o=e.r(21531),i=["-moz-initial","fill","none","scale-down",void 0];function l(e){return void 0!==e.default}function u(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r,a;let s,c,f,{src:d,sizes:p,unoptimized:g=!1,priority:m=!1,loading:h,className:y,quality:b,width:v,height:_,fill:P=!1,style:j,overrideSrc:O,onLoad:w,onLoadingComplete:S,placeholder:E="empty",blurDataURL:C,fetchPriority:x,decoding:R="async",layout:M,objectFit:A,objectPosition:I,lazyBoundary:T,lazyRoot:N,...z}=e,{imgConf:D,showAltText:k,blurComplete:U,defaultLoader:L}=t,F=D||o.imageConfigDefault;if("allSizes"in F)s=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),n=null==(r=F.qualities)?void 0:r.sort((e,t)=>e-t);s={...F,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===L)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=z.loader||L;delete z.loader,delete z.srcSet;let W="__next_img_default"in B;if(W){if("custom"===s.loader)throw Object.defineProperty(Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:r,...n}=t;return e(n)}}if(M){"fill"===M&&(P=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!p&&(p=t)}let V="",q=u(v),G=u(_);if((a=d)&&"object"==typeof a&&(l(a)||void 0!==a.src)){let e=l(d)?d.default:d;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,f=e.blurHeight,C=C||e.blurDataURL,V=e.src,!P)if(q||G){if(q&&!G){let t=q/e.width;G=Math.round(e.height*t)}else if(!q&&G){let t=G/e.height;q=Math.round(e.width*t)}}else q=e.width,G=e.height}let K=!m&&("lazy"===h||void 0===h);(!(d="string"==typeof d?d:V)||d.startsWith("data:")||d.startsWith("blob:"))&&(g=!0,K=!1),s.unoptimized&&(g=!0),W&&!s.dangerouslyAllowSVG&&d.split("?",1)[0].endsWith(".svg")&&(g=!0);let X=u(b),H=Object.assign(P?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:A,objectPosition:I}:{},k?{}:{color:"transparent"},j),J=U||"empty"===E?null:"blur"===E?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:q,heightInt:G,blurWidth:c,blurHeight:f,blurDataURL:C||"",objectFit:H.objectFit})+'")':'url("'+E+'")',Q=i.includes(H.objectFit)?"fill"===H.objectFit?"100% 100%":"cover":H.objectFit,Z=J?{backgroundSize:Q,backgroundPosition:H.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},$=function(e){let{config:t,src:r,unoptimized:n,width:o,quality:i,sizes:l,loader:u}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:a,kind:s}=function(e,t,r){let{deviceSizes:n,allSizes:o}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,l),c=a.length-1;return{sizes:l||"w"!==s?l:"100vw",srcSet:a.map((e,n)=>u({config:t,src:r,quality:i,width:e})+" "+("w"===s?e:n+1)+s).join(", "),src:u({config:t,src:r,quality:i,width:a[c]})}}({config:s,src:d,unoptimized:g,width:q,quality:X,sizes:p,loader:B});return{props:{...z,loading:K?"lazy":h,fetchPriority:x,width:q,height:G,decoding:R,className:y,style:{...H,...Z},sizes:$.sizes,srcSet:$.srcSet,src:O||$.src},meta:{unoptimized:g,priority:m,placeholder:E,fill:P}}}},50532,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ImageConfigContext",{enumerable:!0,get:function(){return i}});let n=e.r(81258)._(e.r(6943)),o=e.r(21531),i=n.default.createContext(o.imageConfigDefault)},84996,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"RouterContext",{enumerable:!0,get:function(){return n}});let n=e.r(81258)._(e.r(6943)).default.createContext(null)},98475,(e,t,r)=>{"use strict";function n(e){var t;let{config:r,src:n,width:o,quality:i}=e,l=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+o+"&q="+l+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return o}}),n.__next_img_default=!0;let o=n},9286,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=e.r(6943);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=i(e,n)),t&&(o.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},38235,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"Image",{enumerable:!0,get:function(){return _}});let n=e.r(81258),o=e.r(44066),i=e.r(65830),l=o._(e.r(6943)),u=n._(e.r(14931)),a=n._(e.r(6018)),s=e.r(16783),c=e.r(21531),f=e.r(50532);e.r(69214);let d=e.r(84996),p=n._(e.r(98475)),g=e.r(9286),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function h(e,t,r,n,o,i,l){let u=null==e?void 0:e.src;e&&e["data-loaded-src"]!==u&&(e["data-loaded-src"]=u,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,o=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function y(e){return l.use?{fetchPriority:e}:{fetchpriority:e}}"undefined"==typeof window&&(globalThis.__NEXT_IMAGE_IMPORTED=!0);let b=(0,l.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:o,height:u,width:a,decoding:s,className:c,style:f,fetchPriority:d,placeholder:p,loading:m,unoptimized:b,fill:v,onLoadRef:_,onLoadingCompleteRef:P,setBlurComplete:j,setShowAltText:O,sizesInput:w,onLoad:S,onError:E,...C}=e,x=(0,l.useCallback)(e=>{e&&(E&&(e.src=e.src),e.complete&&h(e,p,_,P,j,b,w))},[r,p,_,P,j,E,b,w]),R=(0,g.useMergedRef)(t,x);return(0,i.jsx)("img",{...C,...y(d),loading:m,width:a,height:u,decoding:s,"data-nimg":v?"fill":"1",className:c,style:f,sizes:o,srcSet:n,src:r,ref:R,onLoad:e=>{h(e.currentTarget,p,_,P,j,b,w)},onError:e=>{O(!0),"empty"!==p&&j(!0),E&&E(e)}})});function v(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...y(r.fetchPriority)};return t&&u.default.preload?(u.default.preload(r.src,n),null):(0,i.jsx)(a.default,{children:(0,i.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let _=(0,l.forwardRef)((e,t)=>{let r=(0,l.useContext)(d.RouterContext),n=(0,l.useContext)(f.ImageConfigContext),o=(0,l.useMemo)(()=>{var e;let t=m||n||c.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:o,qualities:i}},[n]),{onLoad:u,onLoadingComplete:a}=e,g=(0,l.useRef)(u);(0,l.useEffect)(()=>{g.current=u},[u]);let h=(0,l.useRef)(a);(0,l.useEffect)(()=>{h.current=a},[a]);let[y,_]=(0,l.useState)(!1),[P,j]=(0,l.useState)(!1),{props:O,meta:w}=(0,s.getImgProps)(e,{defaultLoader:p.default,imgConf:o,blurComplete:y,showAltText:P});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(b,{...O,unoptimized:w.unoptimized,placeholder:w.placeholder,fill:w.fill,onLoadRef:g,onLoadingCompleteRef:h,setBlurComplete:_,setShowAltText:j,sizesInput:e.sizes,ref:t}),w.priority?(0,i.jsx)(v,{isAppRouter:!r,imgAttributes:O}):null]})});("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},93677,(e,t,r)=>{"use strict";function n(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,o(e));else t.set(r,o(n));return t}function l(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{assign:function(){return l},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},81121,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{formatUrl:function(){return i},formatWithValidation:function(){return u},urlObjectKeys:function(){return l}});let n=e.r(44066)._(e.r(93677)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",l=e.pathname||"",u=e.hash||"",a=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:r&&(s=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(s+=":"+e.port)),a&&"object"==typeof a&&(a=String(n.urlQueryToSearchParams(a)));let c=e.search||a&&"?"+a||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==s?(s="//"+(s||""),l&&"/"!==l[0]&&(l="/"+l)):s||(s=""),u&&"#"!==u[0]&&(u="#"+u),c&&"?"!==c[0]&&(c="?"+c),""+i+s+(l=l.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+u}let l=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function u(e){return i(e)}},25050,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return b},NormalizeError:function(){return h},PageNotFoundError:function(){return y},SP:function(){return p},ST:function(){return g},WEB_VITALS:function(){return n},execOnce:function(){return o},getDisplayName:function(){return s},getLocationOrigin:function(){return u},getURL:function(){return a},isAbsoluteUrl:function(){return l},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return f},stringifyError:function(){return _}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,l=e=>i.test(e);function u(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function a(){let{href:e}=window.location,t=u();return e.substring(t.length)}function s(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function f(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+s(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,g=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function _(e){return JSON.stringify({message:e.message,stack:e.stack})}},58966,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=e.r(25050),o=e.r(10992);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},8528,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},21336,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(r,{default:function(){return h},useLinkStatus:function(){return b}});let n=e.r(44066),o=e.r(65830),i=n._(e.r(6943)),l=e.r(81121),u=e.r(97741),a=e.r(9286),s=e.r(25050),c=e.r(63584);e.r(69214);let f=e.r(22962),d=e.r(58966),p=e.r(86263);e.r(8528);let g=e.r(30638);function m(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}function h(e){var t;let r,n,l,[h,b]=(0,i.useOptimistic)(f.IDLE_LINK_STATUS),v=(0,i.useRef)(null),{href:_,as:P,children:j,prefetch:O=null,passHref:w,replace:S,shallow:E,scroll:C,onClick:x,onMouseEnter:R,onTouchStart:M,legacyBehavior:A=!1,onNavigate:I,ref:T,unstable_dynamicOnHover:N,...z}=e;r=j,A&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let D=i.default.useContext(u.AppRouterContext),k=!1!==O,U=!1!==O?null===(t=O)||"auto"===t?g.FetchStrategy.PPR:g.FetchStrategy.Full:g.FetchStrategy.PPR,{href:L,as:F}=i.default.useMemo(()=>{let e=m(_);return{href:e,as:P?m(P):e}},[_,P]);A&&(n=i.default.Children.only(r));let B=A?n&&"object"==typeof n&&n.ref:T,W=i.default.useCallback(e=>(null!==D&&(v.current=(0,f.mountLinkInstance)(e,L,D,U,k,b)),()=>{v.current&&((0,f.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,f.unmountPrefetchableInstance)(e)}),[k,L,D,U,b]),V={ref:(0,a.useMergedRef)(W,B),onClick(e){A||"function"!=typeof x||x(e),A&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),D&&(e.defaultPrevented||function(e,t,r,n,o,l,u){let{nodeName:a}=e.currentTarget;if(!("A"===a.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),u){let e=!1;if(u({preventDefault:()=>{e=!0}}),e)return}i.default.startTransition(()=>{(0,p.dispatchNavigateAction)(r||t,o?"replace":"push",null==l||l,n.current)})}}(e,L,F,v,S,C,I))},onMouseEnter(e){A||"function"!=typeof R||R(e),A&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),D&&k&&(0,f.onNavigationIntent)(e.currentTarget,!0===N)},onTouchStart:function(e){A||"function"!=typeof M||M(e),A&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),D&&k&&(0,f.onNavigationIntent)(e.currentTarget,!0===N)}};return(0,s.isAbsoluteUrl)(F)?V.href=F:A&&!w&&("a"!==n.type||"href"in n.props)||(V.href=(0,c.addBasePath)(F)),l=A?i.default.cloneElement(n,V):(0,o.jsx)("a",{...z,...V,children:r}),(0,o.jsx)(y.Provider,{value:h,children:l})}let y=(0,i.createContext)(f.IDLE_LINK_STATUS),b=()=>(0,i.useContext)(y);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)}]);