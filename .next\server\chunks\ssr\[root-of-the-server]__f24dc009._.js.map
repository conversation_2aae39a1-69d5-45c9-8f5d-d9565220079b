{"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/deepmerge@4.3.1/node_modules/deepmerge/dist/cjs.js", "turbopack:///[project]/node_modules/.pnpm/leac@0.6.0/node_modules/leac/lib/leac.mjs", "turbopack:///[project]/node_modules/.pnpm/peberminta@0.9.0/node_modules/peberminta/lib/util.mjs", "turbopack:///[project]/node_modules/.pnpm/dom-serializer@2.0.0/node_modules/dom-serializer/lib/esm/foreignNames.js", "decode_codepoint.ts", "Tokenizer.ts", "escape.ts", "turbopack:///[project]/node_modules/.pnpm/domelementtype@2.3.0/node_modules/domelementtype/lib/esm/index.js", "generated/encode-html.ts", "encode.ts", "Parser.ts", "feeds.ts", "generated/decode-data-html.ts", "generated/decode-data-xml.ts", "index.ts", "turbopack:///[project]/node_modules/.pnpm/peberminta@0.9.0/node_modules/peberminta/lib/core.mjs", "turbopack:///[project]/node_modules/.pnpm/selderee@0.11.0/node_modules/selderee/lib/selderee.mjs", "turbopack:///[project]/node_modules/.pnpm/@selderee+plugin-htmlparser2@0.11.0/node_modules/@selderee/plugin-htmlparser2/lib/hp2-builder.mjs", "turbopack:///[project]/node_modules/.pnpm/html-to-text@9.0.5/node_modules/html-to-text/lib/html-to-text.mjs", "turbopack:///[project]/node_modules/.pnpm/domhandler@5.0.3/node_modules/domhandler/lib/esm/index.js", "turbopack:///[project]/node_modules/.pnpm/parseley@0.12.1/node_modules/parseley/lib/parseley.mjs", "legacy.ts", "turbopack:///[project]/node_modules/.pnpm/domhandler@5.0.3/node_modules/domhandler/lib/esm/node.js", "manipulation.ts", "traversal.ts", "decode.ts", "stringify.ts", "helpers.ts", "querying.ts", "turbopack:///[project]/node_modules/.pnpm/dom-serializer@2.0.0/node_modules/dom-serializer/lib/esm/index.js", "turbopack:///[project]/node_modules/.pnpm/@react-email+render@1.2.1_r_c5a25c3dc1e6309fdf7858c89bda5b7e/node_modules/@react-email/render/dist/node/index.mjs"], "sourcesContent": ["'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n", "const e=/\\n/g;function n(n){const o=[...n.matchAll(e)].map((e=>e.index||0));o.unshift(-1);const s=t(o,0,o.length);return e=>r(s,e)}function t(e,n,r){if(r-n==1)return{offset:e[n],index:n+1};const o=Math.ceil((n+r)/2),s=t(e,n,o),l=t(e,o,r);return{offset:s.offset,low:s,high:l}}function r(e,n){return function(e){return Object.prototype.hasOwnProperty.call(e,\"index\")}(e)?{line:e.index,column:n-e.offset}:r(e.high.offset<n?e.high:e.low,n)}function o(e,t=\"\",r={}){const o=\"string\"!=typeof t?t:r,l=\"string\"==typeof t?t:\"\",c=e.map(s),f=!!o.lineNumbers;return function(e,t=0){const r=f?n(e):()=>({line:0,column:0});let o=t;const s=[];e:for(;o<e.length;){let n=!1;for(const t of c){t.regex.lastIndex=o;const c=t.regex.exec(e);if(c&&c[0].length>0){if(!t.discard){const e=r(o),n=\"string\"==typeof t.replace?c[0].replace(new RegExp(t.regex.source,t.regex.flags),t.replace):c[0];s.push({state:l,name:t.name,text:n,offset:o,len:c[0].length,line:e.line,column:e.column})}if(o=t.regex.lastIndex,n=!0,t.push){const n=t.push(e,o);s.push(...n.tokens),o=n.offset}if(t.pop)break e;break}}if(!n)break}return{tokens:s,offset:o,complete:e.length<=o}}}function s(e,n){return{...e,regex:l(e,n)}}function l(e,n){if(0===e.name.length)throw new Error(`Rule #${n} has empty name, which is not allowed.`);if(function(e){return Object.prototype.hasOwnProperty.call(e,\"regex\")}(e))return function(e){if(e.global)throw new Error(`Regular expression /${e.source}/${e.flags} contains the global flag, which is not allowed.`);return e.sticky?e:new RegExp(e.source,e.flags+\"y\")}(e.regex);if(function(e){return Object.prototype.hasOwnProperty.call(e,\"str\")}(e)){if(0===e.str.length)throw new Error(`Rule #${n} (\"${e.name}\") has empty \"str\" property, which is not allowed.`);return new RegExp(c(e.str),\"y\")}return new RegExp(c(e.name),\"y\")}function c(e){return e.replace(/[-[\\]{}()*+!<=:?./\\\\^$|#\\s,]/g,\"\\\\$&\")}export{o as createLexer};\n", "function clamp(left, x, right) {\n    return Math.max(left, Math.min(x, right));\n}\nfunction escapeWhitespace(str) {\n    return str.replace(/(\\t)|(\\r)|(\\n)/g, (m, t, r) => t ? '\\\\t' : r ? '\\\\r' : '\\\\n');\n}\n\nexport { clamp, escapeWhitespace };\n", "export const elementNames = new Map([\n    \"altGlyph\",\n    \"altGlyphDef\",\n    \"altGlyphItem\",\n    \"animateColor\",\n    \"animateMotion\",\n    \"animateTransform\",\n    \"clipPath\",\n    \"feBlend\",\n    \"feColorMatrix\",\n    \"feComponentTransfer\",\n    \"feComposite\",\n    \"feConvolveMatrix\",\n    \"feDiffuseLighting\",\n    \"feDisplacementMap\",\n    \"feDistantLight\",\n    \"feDropShadow\",\n    \"feFlood\",\n    \"feFuncA\",\n    \"feFuncB\",\n    \"feFuncG\",\n    \"feFuncR\",\n    \"feGaussianBlur\",\n    \"feImage\",\n    \"feMerge\",\n    \"feMergeNode\",\n    \"feMorphology\",\n    \"feOffset\",\n    \"fePointLight\",\n    \"feSpecularLighting\",\n    \"feSpotLight\",\n    \"feTile\",\n    \"feTurbulence\",\n    \"foreignObject\",\n    \"glyphRef\",\n    \"linearGradient\",\n    \"radialGradient\",\n    \"textPath\",\n].map((val) => [val.toLowerCase(), val]));\nexport const attributeNames = new Map([\n    \"definitionURL\",\n    \"attributeName\",\n    \"attributeType\",\n    \"baseFrequency\",\n    \"baseProfile\",\n    \"calcMode\",\n    \"clipPathUnits\",\n    \"diffuseConstant\",\n    \"edgeMode\",\n    \"filterUnits\",\n    \"glyphRef\",\n    \"gradientTransform\",\n    \"gradientUnits\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keyPoints\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"lengthAdjust\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerUnits\",\n    \"markerWidth\",\n    \"maskContentUnits\",\n    \"maskUnits\",\n    \"numOctaves\",\n    \"pathLength\",\n    \"patternContentUnits\",\n    \"patternTransform\",\n    \"patternUnits\",\n    \"pointsAtX\",\n    \"pointsAtY\",\n    \"pointsAtZ\",\n    \"preserveAlpha\",\n    \"preserveAspectRatio\",\n    \"primitiveUnits\",\n    \"refX\",\n    \"refY\",\n    \"repeatCount\",\n    \"repeatDur\",\n    \"requiredExtensions\",\n    \"requiredFeatures\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"spreadMethod\",\n    \"startOffset\",\n    \"stdDeviation\",\n    \"stitchTiles\",\n    \"surfaceScale\",\n    \"systemLanguage\",\n    \"tableValues\",\n    \"targetX\",\n    \"targetY\",\n    \"textLength\",\n    \"viewBox\",\n    \"viewTarget\",\n    \"xChannelSelector\",\n    \"yChannelSelector\",\n    \"zoomAndPan\",\n].map((val) => [val.toLowerCase(), val]));\n", null, null, null, "/** Types of elements found in htmlparser2's DOM */\nexport var ElementType;\n(function (ElementType) {\n    /** Type for the root element of a document */\n    ElementType[\"Root\"] = \"root\";\n    /** Type for Text */\n    ElementType[\"Text\"] = \"text\";\n    /** Type for <? ... ?> */\n    ElementType[\"Directive\"] = \"directive\";\n    /** Type for <!-- ... --> */\n    ElementType[\"Comment\"] = \"comment\";\n    /** Type for <script> tags */\n    ElementType[\"Script\"] = \"script\";\n    /** Type for <style> tags */\n    ElementType[\"Style\"] = \"style\";\n    /** Type for Any tag */\n    ElementType[\"Tag\"] = \"tag\";\n    /** Type for <![CDATA[ ... ]]> */\n    ElementType[\"CDATA\"] = \"cdata\";\n    /** Type for <!doctype ...> */\n    ElementType[\"Doctype\"] = \"doctype\";\n})(ElementType || (ElementType = {}));\n/**\n * Tests whether an element is a tag or not.\n *\n * @param elem Element to test\n */\nexport function isTag(elem) {\n    return (elem.type === ElementType.Tag ||\n        elem.type === ElementType.Script ||\n        elem.type === ElementType.Style);\n}\n// Exports for backwards compatibility\n/** Type for the root element of a document */\nexport const Root = ElementType.Root;\n/** Type for Text */\nexport const Text = ElementType.Text;\n/** Type for <? ... ?> */\nexport const Directive = ElementType.Directive;\n/** Type for <!-- ... --> */\nexport const Comment = ElementType.Comment;\n/** Type for <script> tags */\nexport const Script = ElementType.Script;\n/** Type for <style> tags */\nexport const Style = ElementType.Style;\n/** Type for Any tag */\nexport const Tag = ElementType.Tag;\n/** Type for <![CDATA[ ... ]]> */\nexport const CDATA = ElementType.CDATA;\n/** Type for <!doctype ...> */\nexport const Doctype = ElementType.Doctype;\n", null, null, null, null, null, null, null, "import { clamp, escapeWhitespace } from './util.mjs';\n\nfunction emit(value) {\n    return (data, i) => ({\n        matched: true,\n        position: i,\n        value: value\n    });\n}\nfunction make(\nf) {\n    return (data, i) => ({\n        matched: true,\n        position: i,\n        value: f(data, i)\n    });\n}\nfunction action(\nf) {\n    return (data, i) => {\n        f(data, i);\n        return {\n            matched: true,\n            position: i,\n            value: null\n        };\n    };\n}\nfunction fail(\ndata, i) {\n    return { matched: false };\n}\nfunction error(message) {\n    return (data, i) => {\n        throw new Error((message instanceof Function) ? message(data, i) : message);\n    };\n}\nfunction token(\nonToken,\nonEnd) {\n    return (data, i) => {\n        let position = i;\n        let value = undefined;\n        if (i < data.tokens.length) {\n            value = onToken(data.tokens[i], data, i);\n            if (value !== undefined) {\n                position++;\n            }\n        }\n        else {\n            onEnd?.(data, i);\n        }\n        return (value === undefined)\n            ? { matched: false }\n            : {\n                matched: true,\n                position: position,\n                value: value\n            };\n    };\n}\nfunction any(data, i) {\n    return (i < data.tokens.length)\n        ? {\n            matched: true,\n            position: i + 1,\n            value: data.tokens[i]\n        }\n        : { matched: false };\n}\nfunction satisfy(\ntest) {\n    return (data, i) => (i < data.tokens.length && test(data.tokens[i], data, i))\n        ? {\n            matched: true,\n            position: i + 1,\n            value: data.tokens[i]\n        }\n        : { matched: false };\n}\nfunction mapInner(r, f) {\n    return (r.matched) ? ({\n        matched: true,\n        position: r.position,\n        value: f(r.value, r.position)\n    }) : r;\n}\nfunction mapOuter(r, f) {\n    return (r.matched) ? f(r) : r;\n}\nfunction map(p, mapper) {\n    return (data, i) => mapInner(p(data, i), (v, j) => mapper(v, data, i, j));\n}\nfunction map1(p,\nmapper) {\n    return (data, i) => mapOuter(p(data, i), (m) => mapper(m, data, i));\n}\nfunction peek(p, f) {\n    return (data, i) => {\n        const r = p(data, i);\n        f(r, data, i);\n        return r;\n    };\n}\nfunction option(p, def) {\n    return (data, i) => {\n        const r = p(data, i);\n        return (r.matched)\n            ? r\n            : {\n                matched: true,\n                position: i,\n                value: def\n            };\n    };\n}\nfunction not(p) {\n    return (data, i) => {\n        const r = p(data, i);\n        return (r.matched)\n            ? { matched: false }\n            : {\n                matched: true,\n                position: i,\n                value: true\n            };\n    };\n}\nfunction choice(...ps) {\n    return (data, i) => {\n        for (const p of ps) {\n            const result = p(data, i);\n            if (result.matched) {\n                return result;\n            }\n        }\n        return { matched: false };\n    };\n}\nfunction otherwise(pa, pb) {\n    return (data, i) => {\n        const r1 = pa(data, i);\n        return (r1.matched)\n            ? r1\n            : pb(data, i);\n    };\n}\nfunction longest(...ps) {\n    return (data, i) => {\n        let match = undefined;\n        for (const p of ps) {\n            const result = p(data, i);\n            if (result.matched && (!match || match.position < result.position)) {\n                match = result;\n            }\n        }\n        return match || { matched: false };\n    };\n}\nfunction takeWhile(p,\ntest) {\n    return (data, i) => {\n        const values = [];\n        let success = true;\n        do {\n            const r = p(data, i);\n            if (r.matched && test(r.value, values.length + 1, data, i, r.position)) {\n                values.push(r.value);\n                i = r.position;\n            }\n            else {\n                success = false;\n            }\n        } while (success);\n        return {\n            matched: true,\n            position: i,\n            value: values\n        };\n    };\n}\nfunction takeUntil(p,\ntest) {\n    return takeWhile(p, (value, n, data, i, j) => !test(value, n, data, i, j));\n}\nfunction takeWhileP(pValue, pTest) {\n    return takeWhile(pValue, (value, n, data, i) => pTest(data, i).matched);\n}\nfunction takeUntilP(pValue, pTest) {\n    return takeWhile(pValue, (value, n, data, i) => !pTest(data, i).matched);\n}\nfunction many(p) {\n    return takeWhile(p, () => true);\n}\nfunction many1(p) {\n    return ab(p, many(p), (head, tail) => [head, ...tail]);\n}\nfunction ab(pa, pb, join) {\n    return (data, i) => mapOuter(pa(data, i), (ma) => mapInner(pb(data, ma.position), (vb, j) => join(ma.value, vb, data, i, j)));\n}\nfunction left(pa, pb) {\n    return ab(pa, pb, (va) => va);\n}\nfunction right(pa, pb) {\n    return ab(pa, pb, (va, vb) => vb);\n}\nfunction abc(pa, pb, pc, join) {\n    return (data, i) => mapOuter(pa(data, i), (ma) => mapOuter(pb(data, ma.position), (mb) => mapInner(pc(data, mb.position), (vc, j) => join(ma.value, mb.value, vc, data, i, j))));\n}\nfunction middle(pa, pb, pc) {\n    return abc(pa, pb, pc, (ra, rb) => rb);\n}\nfunction all(...ps) {\n    return (data, i) => {\n        const result = [];\n        let position = i;\n        for (const p of ps) {\n            const r1 = p(data, position);\n            if (r1.matched) {\n                result.push(r1.value);\n                position = r1.position;\n            }\n            else {\n                return { matched: false };\n            }\n        }\n        return {\n            matched: true,\n            position: position,\n            value: result\n        };\n    };\n}\nfunction skip(...ps) {\n    return map(all(...ps), () => null);\n}\nfunction flatten(...ps) {\n    return flatten1(all(...ps));\n}\nfunction flatten1(p) {\n    return map(p, (vs) => vs.flatMap((v) => v));\n}\nfunction sepBy1(pValue, pSep) {\n    return ab(pValue, many(right(pSep, pValue)), (head, tail) => [head, ...tail]);\n}\nfunction sepBy(pValue, pSep) {\n    return otherwise(sepBy1(pValue, pSep), emit([]));\n}\nfunction chainReduce(acc,\nf) {\n    return (data, i) => {\n        let loop = true;\n        let acc1 = acc;\n        let pos = i;\n        do {\n            const r = f(acc1, data, pos)(data, pos);\n            if (r.matched) {\n                acc1 = r.value;\n                pos = r.position;\n            }\n            else {\n                loop = false;\n            }\n        } while (loop);\n        return {\n            matched: true,\n            position: pos,\n            value: acc1\n        };\n    };\n}\nfunction reduceLeft(acc, p,\nreducer) {\n    return chainReduce(acc, (acc) => map(p, (v, data, i, j) => reducer(acc, v, data, i, j)));\n}\nfunction reduceRight(p, acc,\nreducer) {\n    return map(many(p), (vs, data, i, j) => vs.reduceRight((acc, v) => reducer(v, acc, data, i, j), acc));\n}\nfunction leftAssoc1(pLeft, pOper) {\n    return chain(pLeft, (v0) => reduceLeft(v0, pOper, (acc, f) => f(acc)));\n}\nfunction rightAssoc1(pOper, pRight) {\n    return ab(reduceRight(pOper, (y) => y, (f, acc) => (y) => f(acc(y))), pRight, (f, v) => f(v));\n}\nfunction leftAssoc2(pLeft, pOper, pRight) {\n    return chain(pLeft, (v0) => reduceLeft(v0, ab(pOper, pRight, (f, y) => [f, y]), (acc, [f, y]) => f(acc, y)));\n}\nfunction rightAssoc2(pLeft, pOper, pRight) {\n    return ab(reduceRight(ab(pLeft, pOper, (x, f) => [x, f]), (y) => y, ([x, f], acc) => (y) => f(x, acc(y))), pRight, (f, v) => f(v));\n}\nfunction condition(cond, pTrue, pFalse) {\n    return (data, i) => (cond(data, i))\n        ? pTrue(data, i)\n        : pFalse(data, i);\n}\nfunction decide(p) {\n    return (data, i) => mapOuter(p(data, i), (m1) => m1.value(data, m1.position));\n}\nfunction chain(p,\nf) {\n    return (data, i) => mapOuter(p(data, i), (m1) => f(m1.value, data, i, m1.position)(data, m1.position));\n}\nfunction ahead(p) {\n    return (data, i) => mapOuter(p(data, i), (m1) => ({\n        matched: true,\n        position: i,\n        value: m1.value\n    }));\n}\nfunction recursive(f) {\n    return function (data, i) {\n        return f()(data, i);\n    };\n}\nfunction start(data, i) {\n    return (i !== 0)\n        ? { matched: false }\n        : {\n            matched: true,\n            position: i,\n            value: true\n        };\n}\nfunction end(data, i) {\n    return (i < data.tokens.length)\n        ? { matched: false }\n        : {\n            matched: true,\n            position: i,\n            value: true\n        };\n}\nfunction remainingTokensNumber(data, i) {\n    return data.tokens.length - i;\n}\nfunction parserPosition(data, i, formatToken, contextTokens = 3) {\n    const len = data.tokens.length;\n    const lowIndex = clamp(0, i - contextTokens, len - contextTokens);\n    const highIndex = clamp(contextTokens, i + 1 + contextTokens, len);\n    const tokensSlice = data.tokens.slice(lowIndex, highIndex);\n    const lines = [];\n    const indexWidth = String(highIndex - 1).length + 1;\n    if (i < 0) {\n        lines.push(`${String(i).padStart(indexWidth)} >>`);\n    }\n    if (0 < lowIndex) {\n        lines.push('...'.padStart(indexWidth + 6));\n    }\n    for (let j = 0; j < tokensSlice.length; j++) {\n        const index = lowIndex + j;\n        lines.push(`${String(index).padStart(indexWidth)} ${(index === i ? '>' : ' ')} ${escapeWhitespace(formatToken(tokensSlice[j]))}`);\n    }\n    if (highIndex < len) {\n        lines.push('...'.padStart(indexWidth + 6));\n    }\n    if (len <= i) {\n        lines.push(`${String(i).padStart(indexWidth)} >>`);\n    }\n    return lines.join('\\n');\n}\nfunction parse(parser, tokens, options, formatToken = JSON.stringify) {\n    const data = { tokens: tokens, options: options };\n    const result = parser(data, 0);\n    if (!result.matched) {\n        throw new Error('No match');\n    }\n    if (result.position < data.tokens.length) {\n        throw new Error(`Partial match. Parsing stopped at:\\n${parserPosition(data, result.position, formatToken)}`);\n    }\n    return result.value;\n}\nfunction tryParse(parser, tokens, options) {\n    const result = parser({ tokens: tokens, options: options }, 0);\n    return (result.matched)\n        ? result.value\n        : undefined;\n}\nfunction match(matcher, tokens, options) {\n    const result = matcher({ tokens: tokens, options: options }, 0);\n    return result.value;\n}\n\nexport { ab, abc, action, ahead, all, all as and, any, chain, chainReduce, choice, condition, decide, skip as discard, otherwise as eitherOr, emit, end, end as eof, error, fail, flatten, flatten1, left, leftAssoc1, leftAssoc2, longest, ahead as lookAhead, make, many, many1, map, map1, match, middle, not, emit as of, option, choice as or, otherwise, parse, parserPosition, peek, recursive, reduceLeft, reduceRight, remainingTokensNumber, right, rightAssoc1, rightAssoc2, satisfy, sepBy, sepBy1, skip, many1 as some, start, takeUntil, takeUntilP, takeWhile, takeWhileP, token, tryParse };\n", "import * as parseley from 'parseley';\nimport { compareSpecificity } from 'parseley';\n\nvar Ast = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nvar Types = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst treeify = (nodes) => '▽\\n' + treeifyArray(nodes, thinLines);\nconst thinLines = [['├─', '│ '], ['└─', '  ']];\nconst heavyLines = [['┠─', '┃ '], ['┖─', '  ']];\nconst doubleLines = [['╟─', '║ '], ['╙─', '  ']];\nfunction treeifyArray(nodes, tpl = heavyLines) {\n    return prefixItems(tpl, nodes.map(n => treeifyNode(n)));\n}\nfunction treeifyNode(node) {\n    switch (node.type) {\n        case 'terminal': {\n            const vctr = node.valueContainer;\n            return `◁ #${vctr.index} ${JSON.stringify(vctr.specificity)} ${vctr.value}`;\n        }\n        case 'tagName':\n            return `◻ Tag name\\n${treeifyArray(node.variants, doubleLines)}`;\n        case 'attrValue':\n            return `▣ Attr value: ${node.name}\\n${treeifyArray(node.matchers, doubleLines)}`;\n        case 'attrPresence':\n            return `◨ Attr presence: ${node.name}\\n${treeifyArray(node.cont)}`;\n        case 'pushElement':\n            return `◉ Push element: ${node.combinator}\\n${treeifyArray(node.cont, thinLines)}`;\n        case 'popElement':\n            return `◌ Pop element\\n${treeifyArray(node.cont, thinLines)}`;\n        case 'variant':\n            return `◇ = ${node.value}\\n${treeifyArray(node.cont)}`;\n        case 'matcher':\n            return `◈ ${node.matcher} \"${node.value}\"${node.modifier || ''}\\n${treeifyArray(node.cont)}`;\n    }\n}\nfunction prefixItems(tpl, items) {\n    return items\n        .map((item, i, { length }) => prefixItem(tpl, item, i === length - 1))\n        .join('\\n');\n}\nfunction prefixItem(tpl, item, tail = true) {\n    const tpl1 = tpl[tail ? 1 : 0];\n    return tpl1[0] + item.split('\\n').join('\\n' + tpl1[1]);\n}\n\nvar TreeifyBuilder = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    treeify: treeify\n});\n\nclass DecisionTree {\n    constructor(input) {\n        this.branches = weave(toAstTerminalPairs(input));\n    }\n    build(builder) {\n        return builder(this.branches);\n    }\n}\nfunction toAstTerminalPairs(array) {\n    const len = array.length;\n    const results = new Array(len);\n    for (let i = 0; i < len; i++) {\n        const [selectorString, val] = array[i];\n        const ast = preprocess(parseley.parse1(selectorString));\n        results[i] = {\n            ast: ast,\n            terminal: {\n                type: 'terminal',\n                valueContainer: { index: i, value: val, specificity: ast.specificity }\n            }\n        };\n    }\n    return results;\n}\nfunction preprocess(ast) {\n    reduceSelectorVariants(ast);\n    parseley.normalize(ast);\n    return ast;\n}\nfunction reduceSelectorVariants(ast) {\n    const newList = [];\n    ast.list.forEach(sel => {\n        switch (sel.type) {\n            case 'class':\n                newList.push({\n                    matcher: '~=',\n                    modifier: null,\n                    name: 'class',\n                    namespace: null,\n                    specificity: sel.specificity,\n                    type: 'attrValue',\n                    value: sel.name,\n                });\n                break;\n            case 'id':\n                newList.push({\n                    matcher: '=',\n                    modifier: null,\n                    name: 'id',\n                    namespace: null,\n                    specificity: sel.specificity,\n                    type: 'attrValue',\n                    value: sel.name,\n                });\n                break;\n            case 'combinator':\n                reduceSelectorVariants(sel.left);\n                newList.push(sel);\n                break;\n            case 'universal':\n                break;\n            default:\n                newList.push(sel);\n                break;\n        }\n    });\n    ast.list = newList;\n}\nfunction weave(items) {\n    const branches = [];\n    while (items.length) {\n        const topKind = findTopKey(items, (sel) => true, getSelectorKind);\n        const { matches, nonmatches, empty } = breakByKind(items, topKind);\n        items = nonmatches;\n        if (matches.length) {\n            branches.push(branchOfKind(topKind, matches));\n        }\n        if (empty.length) {\n            branches.push(...terminate(empty));\n        }\n    }\n    return branches;\n}\nfunction terminate(items) {\n    const results = [];\n    for (const item of items) {\n        const terminal = item.terminal;\n        if (terminal.type === 'terminal') {\n            results.push(terminal);\n        }\n        else {\n            const { matches, rest } = partition(terminal.cont, (node) => node.type === 'terminal');\n            matches.forEach((node) => results.push(node));\n            if (rest.length) {\n                terminal.cont = rest;\n                results.push(terminal);\n            }\n        }\n    }\n    return results;\n}\nfunction breakByKind(items, selectedKind) {\n    const matches = [];\n    const nonmatches = [];\n    const empty = [];\n    for (const item of items) {\n        const simpsels = item.ast.list;\n        if (simpsels.length) {\n            const isMatch = simpsels.some(node => getSelectorKind(node) === selectedKind);\n            (isMatch ? matches : nonmatches).push(item);\n        }\n        else {\n            empty.push(item);\n        }\n    }\n    return { matches, nonmatches, empty };\n}\nfunction getSelectorKind(sel) {\n    switch (sel.type) {\n        case 'attrPresence':\n            return `attrPresence ${sel.name}`;\n        case 'attrValue':\n            return `attrValue ${sel.name}`;\n        case 'combinator':\n            return `combinator ${sel.combinator}`;\n        default:\n            return sel.type;\n    }\n}\nfunction branchOfKind(kind, items) {\n    if (kind === 'tag') {\n        return tagNameBranch(items);\n    }\n    if (kind.startsWith('attrValue ')) {\n        return attrValueBranch(kind.substring(10), items);\n    }\n    if (kind.startsWith('attrPresence ')) {\n        return attrPresenceBranch(kind.substring(13), items);\n    }\n    if (kind === 'combinator >') {\n        return combinatorBranch('>', items);\n    }\n    if (kind === 'combinator +') {\n        return combinatorBranch('+', items);\n    }\n    throw new Error(`Unsupported selector kind: ${kind}`);\n}\nfunction tagNameBranch(items) {\n    const groups = spliceAndGroup(items, (x) => x.type === 'tag', (x) => x.name);\n    const variants = Object.entries(groups).map(([name, group]) => ({\n        type: 'variant',\n        value: name,\n        cont: weave(group.items)\n    }));\n    return {\n        type: 'tagName',\n        variants: variants\n    };\n}\nfunction attrPresenceBranch(name, items) {\n    for (const item of items) {\n        spliceSimpleSelector(item, (x) => (x.type === 'attrPresence') && (x.name === name));\n    }\n    return {\n        type: 'attrPresence',\n        name: name,\n        cont: weave(items)\n    };\n}\nfunction attrValueBranch(name, items) {\n    const groups = spliceAndGroup(items, (x) => (x.type === 'attrValue') && (x.name === name), (x) => `${x.matcher} ${x.modifier || ''} ${x.value}`);\n    const matchers = [];\n    for (const group of Object.values(groups)) {\n        const sel = group.oneSimpleSelector;\n        const predicate = getAttrPredicate(sel);\n        const continuation = weave(group.items);\n        matchers.push({\n            type: 'matcher',\n            matcher: sel.matcher,\n            modifier: sel.modifier,\n            value: sel.value,\n            predicate: predicate,\n            cont: continuation\n        });\n    }\n    return {\n        type: 'attrValue',\n        name: name,\n        matchers: matchers\n    };\n}\nfunction getAttrPredicate(sel) {\n    if (sel.modifier === 'i') {\n        const expected = sel.value.toLowerCase();\n        switch (sel.matcher) {\n            case '=':\n                return (actual) => expected === actual.toLowerCase();\n            case '~=':\n                return (actual) => actual.toLowerCase().split(/[ \\t]+/).includes(expected);\n            case '^=':\n                return (actual) => actual.toLowerCase().startsWith(expected);\n            case '$=':\n                return (actual) => actual.toLowerCase().endsWith(expected);\n            case '*=':\n                return (actual) => actual.toLowerCase().includes(expected);\n            case '|=':\n                return (actual) => {\n                    const lower = actual.toLowerCase();\n                    return (expected === lower) || (lower.startsWith(expected) && lower[expected.length] === '-');\n                };\n        }\n    }\n    else {\n        const expected = sel.value;\n        switch (sel.matcher) {\n            case '=':\n                return (actual) => expected === actual;\n            case '~=':\n                return (actual) => actual.split(/[ \\t]+/).includes(expected);\n            case '^=':\n                return (actual) => actual.startsWith(expected);\n            case '$=':\n                return (actual) => actual.endsWith(expected);\n            case '*=':\n                return (actual) => actual.includes(expected);\n            case '|=':\n                return (actual) => (expected === actual) || (actual.startsWith(expected) && actual[expected.length] === '-');\n        }\n    }\n}\nfunction combinatorBranch(combinator, items) {\n    const groups = spliceAndGroup(items, (x) => (x.type === 'combinator') && (x.combinator === combinator), (x) => parseley.serialize(x.left));\n    const leftItems = [];\n    for (const group of Object.values(groups)) {\n        const rightCont = weave(group.items);\n        const leftAst = group.oneSimpleSelector.left;\n        leftItems.push({\n            ast: leftAst,\n            terminal: { type: 'popElement', cont: rightCont }\n        });\n    }\n    return {\n        type: 'pushElement',\n        combinator: combinator,\n        cont: weave(leftItems)\n    };\n}\nfunction spliceAndGroup(items, predicate, keyCallback) {\n    const groups = {};\n    while (items.length) {\n        const bestKey = findTopKey(items, predicate, keyCallback);\n        const bestKeyPredicate = (sel) => predicate(sel) && keyCallback(sel) === bestKey;\n        const hasBestKeyPredicate = (item) => item.ast.list.some(bestKeyPredicate);\n        const { matches, rest } = partition1(items, hasBestKeyPredicate);\n        let oneSimpleSelector = null;\n        for (const item of matches) {\n            const splicedNode = spliceSimpleSelector(item, bestKeyPredicate);\n            if (!oneSimpleSelector) {\n                oneSimpleSelector = splicedNode;\n            }\n        }\n        if (oneSimpleSelector == null) {\n            throw new Error('No simple selector is found.');\n        }\n        groups[bestKey] = { oneSimpleSelector: oneSimpleSelector, items: matches };\n        items = rest;\n    }\n    return groups;\n}\nfunction spliceSimpleSelector(item, predicate) {\n    const simpsels = item.ast.list;\n    const matches = new Array(simpsels.length);\n    let firstIndex = -1;\n    for (let i = simpsels.length; i-- > 0;) {\n        if (predicate(simpsels[i])) {\n            matches[i] = true;\n            firstIndex = i;\n        }\n    }\n    if (firstIndex == -1) {\n        throw new Error(`Couldn't find the required simple selector.`);\n    }\n    const result = simpsels[firstIndex];\n    item.ast.list = simpsels.filter((sel, i) => !matches[i]);\n    return result;\n}\nfunction findTopKey(items, predicate, keyCallback) {\n    const candidates = {};\n    for (const item of items) {\n        const candidates1 = {};\n        for (const node of item.ast.list.filter(predicate)) {\n            candidates1[keyCallback(node)] = true;\n        }\n        for (const key of Object.keys(candidates1)) {\n            if (candidates[key]) {\n                candidates[key]++;\n            }\n            else {\n                candidates[key] = 1;\n            }\n        }\n    }\n    let topKind = '';\n    let topCounter = 0;\n    for (const entry of Object.entries(candidates)) {\n        if (entry[1] > topCounter) {\n            topKind = entry[0];\n            topCounter = entry[1];\n        }\n    }\n    return topKind;\n}\nfunction partition(src, predicate) {\n    const matches = [];\n    const rest = [];\n    for (const x of src) {\n        if (predicate(x)) {\n            matches.push(x);\n        }\n        else {\n            rest.push(x);\n        }\n    }\n    return { matches, rest };\n}\nfunction partition1(src, predicate) {\n    const matches = [];\n    const rest = [];\n    for (const x of src) {\n        if (predicate(x)) {\n            matches.push(x);\n        }\n        else {\n            rest.push(x);\n        }\n    }\n    return { matches, rest };\n}\n\nclass Picker {\n    constructor(f) {\n        this.f = f;\n    }\n    pickAll(el) {\n        return this.f(el);\n    }\n    pick1(el, preferFirst = false) {\n        const results = this.f(el);\n        const len = results.length;\n        if (len === 0) {\n            return null;\n        }\n        if (len === 1) {\n            return results[0].value;\n        }\n        const comparator = (preferFirst)\n            ? comparatorPreferFirst\n            : comparatorPreferLast;\n        let result = results[0];\n        for (let i = 1; i < len; i++) {\n            const next = results[i];\n            if (comparator(result, next)) {\n                result = next;\n            }\n        }\n        return result.value;\n    }\n}\nfunction comparatorPreferFirst(acc, next) {\n    const diff = compareSpecificity(next.specificity, acc.specificity);\n    return diff > 0 || (diff === 0 && next.index < acc.index);\n}\nfunction comparatorPreferLast(acc, next) {\n    const diff = compareSpecificity(next.specificity, acc.specificity);\n    return diff > 0 || (diff === 0 && next.index > acc.index);\n}\n\nexport { Ast, DecisionTree, Picker, TreeifyBuilder as Treeify, Types };\n", "import { isTag } from 'domhandler';\nimport { Picker } from 'selderee';\n\nfunction hp2Builder(nodes) {\n    return new Picker(handleArray(nodes));\n}\nfunction handleArray(nodes) {\n    const matchers = nodes.map(handleNode);\n    return (el, ...tail) => matchers.flatMap(m => m(el, ...tail));\n}\nfunction handleNode(node) {\n    switch (node.type) {\n        case 'terminal': {\n            const result = [node.valueContainer];\n            return (el, ...tail) => result;\n        }\n        case 'tagName':\n            return handleTagName(node);\n        case 'attrValue':\n            return handleAttrValueName(node);\n        case 'attrPresence':\n            return handleAttrPresenceName(node);\n        case 'pushElement':\n            return handlePushElementNode(node);\n        case 'popElement':\n            return handlePopElementNode(node);\n    }\n}\nfunction handleTagName(node) {\n    const variants = {};\n    for (const variant of node.variants) {\n        variants[variant.value] = handleArray(variant.cont);\n    }\n    return (el, ...tail) => {\n        const continuation = variants[el.name];\n        return (continuation) ? continuation(el, ...tail) : [];\n    };\n}\nfunction handleAttrPresenceName(node) {\n    const attrName = node.name;\n    const continuation = handleArray(node.cont);\n    return (el, ...tail) => (Object.prototype.hasOwnProperty.call(el.attribs, attrName))\n        ? continuation(el, ...tail)\n        : [];\n}\nfunction handleAttrValueName(node) {\n    const callbacks = [];\n    for (const matcher of node.matchers) {\n        const predicate = matcher.predicate;\n        const continuation = handleArray(matcher.cont);\n        callbacks.push((attr, el, ...tail) => (predicate(attr) ? continuation(el, ...tail) : []));\n    }\n    const attrName = node.name;\n    return (el, ...tail) => {\n        const attr = el.attribs[attrName];\n        return (attr || attr === '')\n            ? callbacks.flatMap(cb => cb(attr, el, ...tail))\n            : [];\n    };\n}\nfunction handlePushElementNode(node) {\n    const continuation = handleArray(node.cont);\n    const leftElementGetter = (node.combinator === '+')\n        ? getPrecedingElement\n        : getParentElement;\n    return (el, ...tail) => {\n        const next = leftElementGetter(el);\n        if (next === null) {\n            return [];\n        }\n        return continuation(next, el, ...tail);\n    };\n}\nconst getPrecedingElement = (el) => {\n    const prev = el.prev;\n    if (prev === null) {\n        return null;\n    }\n    return (isTag(prev)) ? prev : getPrecedingElement(prev);\n};\nconst getParentElement = (el) => {\n    const parent = el.parent;\n    return (parent && isTag(parent)) ? parent : null;\n};\nfunction handlePopElementNode(node) {\n    const continuation = handleArray(node.cont);\n    return (el, next, ...tail) => continuation(next, ...tail);\n}\n\nexport { hp2Builder };\n", "import { hp2Builder } from '@selderee/plugin-htmlparser2';\nimport { parseDocument } from 'htmlparser2';\nimport { DecisionTree } from 'selderee';\nimport merge from 'deepmerge';\nimport { render } from 'dom-serializer';\n\n/**\n * Make a recursive function that will only run to a given depth\n * and switches to an alternative function at that depth. \\\n * No limitation if `n` is `undefined` (Just wraps `f` in that case).\n *\n * @param   { number | undefined } n   Allowed depth of recursion. `undefined` for no limitation.\n * @param   { Function }           f   Function that accepts recursive callback as the first argument.\n * @param   { Function }           [g] Function to run instead, when maximum depth was reached. Do nothing by default.\n * @returns { Function }\n */\nfunction limitedDepthRecursive (n, f, g = () => undefined) {\n  if (n === undefined) {\n    const f1 = function (...args) { return f(f1, ...args); };\n    return f1;\n  }\n  if (n >= 0) {\n    return function (...args) { return f(limitedDepthRecursive(n - 1, f, g), ...args); };\n  }\n  return g;\n}\n\n/**\n * Return the same string or a substring with\n * the given character occurrences removed from each side.\n *\n * @param   { string } str  A string to trim.\n * @param   { string } char A character to be trimmed.\n * @returns { string }\n */\nfunction trimCharacter (str, char) {\n  let start = 0;\n  let end = str.length;\n  while (start < end && str[start] === char) { ++start; }\n  while (end > start && str[end - 1] === char) { --end; }\n  return (start > 0 || end < str.length)\n    ? str.substring(start, end)\n    : str;\n}\n\n/**\n * Return the same string or a substring with\n * the given character occurrences removed from the end only.\n *\n * @param   { string } str  A string to trim.\n * @param   { string } char A character to be trimmed.\n * @returns { string }\n */\nfunction trimCharacterEnd (str, char) {\n  let end = str.length;\n  while (end > 0 && str[end - 1] === char) { --end; }\n  return (end < str.length)\n    ? str.substring(0, end)\n    : str;\n}\n\n/**\n * Return a new string will all characters replaced with unicode escape sequences.\n * This extreme kind of escaping can used to be safely compose regular expressions.\n *\n * @param { string } str A string to escape.\n * @returns { string } A string of unicode escape sequences.\n */\nfunction unicodeEscape (str) {\n  return str.replace(/[\\s\\S]/g, c => '\\\\u' + c.charCodeAt().toString(16).padStart(4, '0'));\n}\n\n/**\n * Deduplicate an array by a given key callback.\n * Item properties are merged recursively and with the preference for last defined values.\n * Of items with the same key, merged item takes the place of the last item,\n * others are omitted.\n *\n * @param { any[] } items An array to deduplicate.\n * @param { (x: any) => string } getKey Callback to get a value that distinguishes unique items.\n * @returns { any[] }\n */\nfunction mergeDuplicatesPreferLast (items, getKey) {\n  const map = new Map();\n  for (let i = items.length; i-- > 0;) {\n    const item = items[i];\n    const key = getKey(item);\n    map.set(\n      key,\n      (map.has(key))\n        ? merge(item, map.get(key), { arrayMerge: overwriteMerge$1 })\n        : item\n    );\n  }\n  return [...map.values()].reverse();\n}\n\nconst overwriteMerge$1 = (acc, src, options) => [...src];\n\n/**\n * Get a nested property from an object.\n *\n * @param   { object }   obj  The object to query for the value.\n * @param   { string[] } path The path to the property.\n * @returns { any }\n */\nfunction get (obj, path) {\n  for (const key of path) {\n    if (!obj) { return undefined; }\n    obj = obj[key];\n  }\n  return obj;\n}\n\n/**\n * Convert a number into alphabetic sequence representation (Sequence without zeroes).\n *\n * For example: `a, ..., z, aa, ..., zz, aaa, ...`.\n *\n * @param   { number } num              Number to convert. Must be >= 1.\n * @param   { string } [baseChar = 'a'] Character for 1 in the sequence.\n * @param   { number } [base = 26]      Number of characters in the sequence.\n * @returns { string }\n */\nfunction numberToLetterSequence (num, baseChar = 'a', base = 26) {\n  const digits = [];\n  do {\n    num -= 1;\n    digits.push(num % base);\n    num = (num / base) >> 0; // quick `floor`\n  } while (num > 0);\n  const baseCode = baseChar.charCodeAt(0);\n  return digits\n    .reverse()\n    .map(n => String.fromCharCode(baseCode + n))\n    .join('');\n}\n\nconst I = ['I', 'X', 'C', 'M'];\nconst V = ['V', 'L', 'D'];\n\n/**\n * Convert a number to it's Roman representation. No large numbers extension.\n *\n * @param   { number } num Number to convert. `0 < num <= 3999`.\n * @returns { string }\n */\nfunction numberToRoman (num) {\n  return [...(num) + '']\n    .map(n => +n)\n    .reverse()\n    .map((v, i) => ((v % 5 < 4)\n      ? (v < 5 ? '' : V[i]) + I[i].repeat(v % 5)\n      : I[i] + (v < 5 ? V[i] : I[i + 1])))\n    .reverse()\n    .join('');\n}\n\n/**\n * Helps to build text from words.\n */\nclass InlineTextBuilder {\n  /**\n   * Creates an instance of InlineTextBuilder.\n   *\n   * If `maxLineLength` is not provided then it is either `options.wordwrap` or unlimited.\n   *\n   * @param { Options } options           HtmlToText options.\n   * @param { number }  [ maxLineLength ] This builder will try to wrap text to fit this line length.\n   */\n  constructor (options, maxLineLength = undefined) {\n    /** @type { string[][] } */\n    this.lines = [];\n    /** @type { string[] }   */\n    this.nextLineWords = [];\n    this.maxLineLength = maxLineLength || options.wordwrap || Number.MAX_VALUE;\n    this.nextLineAvailableChars = this.maxLineLength;\n    this.wrapCharacters = get(options, ['longWordSplit', 'wrapCharacters']) || [];\n    this.forceWrapOnLimit = get(options, ['longWordSplit', 'forceWrapOnLimit']) || false;\n\n    this.stashedSpace = false;\n    this.wordBreakOpportunity = false;\n  }\n\n  /**\n   * Add a new word.\n   *\n   * @param { string } word A word to add.\n   * @param { boolean } [noWrap] Don't wrap text even if the line is too long.\n   */\n  pushWord (word, noWrap = false) {\n    if (this.nextLineAvailableChars <= 0 && !noWrap) {\n      this.startNewLine();\n    }\n    const isLineStart = this.nextLineWords.length === 0;\n    const cost = word.length + (isLineStart ? 0 : 1);\n    if ((cost <= this.nextLineAvailableChars) || noWrap) { // Fits into available budget\n\n      this.nextLineWords.push(word);\n      this.nextLineAvailableChars -= cost;\n\n    } else { // Does not fit - try to split the word\n\n      // The word is moved to a new line - prefer to wrap between words.\n      const [first, ...rest] = this.splitLongWord(word);\n      if (!isLineStart) { this.startNewLine(); }\n      this.nextLineWords.push(first);\n      this.nextLineAvailableChars -= first.length;\n      for (const part of rest) {\n        this.startNewLine();\n        this.nextLineWords.push(part);\n        this.nextLineAvailableChars -= part.length;\n      }\n\n    }\n  }\n\n  /**\n   * Pop a word from the currently built line.\n   * This doesn't affect completed lines.\n   *\n   * @returns { string }\n   */\n  popWord () {\n    const lastWord = this.nextLineWords.pop();\n    if (lastWord !== undefined) {\n      const isLineStart = this.nextLineWords.length === 0;\n      const cost = lastWord.length + (isLineStart ? 0 : 1);\n      this.nextLineAvailableChars += cost;\n    }\n    return lastWord;\n  }\n\n  /**\n   * Concat a word to the last word already in the builder.\n   * Adds a new word in case there are no words yet in the last line.\n   *\n   * @param { string } word A word to be concatenated.\n   * @param { boolean } [noWrap] Don't wrap text even if the line is too long.\n   */\n  concatWord (word, noWrap = false) {\n    if (this.wordBreakOpportunity && word.length > this.nextLineAvailableChars) {\n      this.pushWord(word, noWrap);\n      this.wordBreakOpportunity = false;\n    } else {\n      const lastWord = this.popWord();\n      this.pushWord((lastWord) ? lastWord.concat(word) : word, noWrap);\n    }\n  }\n\n  /**\n   * Add current line (and more empty lines if provided argument > 1) to the list of complete lines and start a new one.\n   *\n   * @param { number } n Number of line breaks that will be added to the resulting string.\n   */\n  startNewLine (n = 1) {\n    this.lines.push(this.nextLineWords);\n    if (n > 1) {\n      this.lines.push(...Array.from({ length: n - 1 }, () => []));\n    }\n    this.nextLineWords = [];\n    this.nextLineAvailableChars = this.maxLineLength;\n  }\n\n  /**\n   * No words in this builder.\n   *\n   * @returns { boolean }\n   */\n  isEmpty () {\n    return this.lines.length === 0\n        && this.nextLineWords.length === 0;\n  }\n\n  clear () {\n    this.lines.length = 0;\n    this.nextLineWords.length = 0;\n    this.nextLineAvailableChars = this.maxLineLength;\n  }\n\n  /**\n   * Join all lines of words inside the InlineTextBuilder into a complete string.\n   *\n   * @returns { string }\n   */\n  toString () {\n    return [...this.lines, this.nextLineWords]\n      .map(words => words.join(' '))\n      .join('\\n');\n  }\n\n  /**\n   * Split a long word up to fit within the word wrap limit.\n   * Use either a character to split looking back from the word wrap limit,\n   * or truncate to the word wrap limit.\n   *\n   * @param   { string }   word Input word.\n   * @returns { string[] }      Parts of the word.\n   */\n  splitLongWord (word) {\n    const parts = [];\n    let idx = 0;\n    while (word.length > this.maxLineLength) {\n\n      const firstLine = word.substring(0, this.maxLineLength);\n      const remainingChars = word.substring(this.maxLineLength);\n\n      const splitIndex = firstLine.lastIndexOf(this.wrapCharacters[idx]);\n\n      if (splitIndex > -1) { // Found a character to split on\n\n        word = firstLine.substring(splitIndex + 1) + remainingChars;\n        parts.push(firstLine.substring(0, splitIndex + 1));\n\n      } else { // Not found a character to split on\n\n        idx++;\n        if (idx < this.wrapCharacters.length) { // There is next character to try\n\n          word = firstLine + remainingChars;\n\n        } else { // No more characters to try\n\n          if (this.forceWrapOnLimit) {\n            parts.push(firstLine);\n            word = remainingChars;\n            if (word.length > this.maxLineLength) {\n              continue;\n            }\n          } else {\n            word = firstLine + remainingChars;\n          }\n          break;\n\n        }\n\n      }\n\n    }\n    parts.push(word); // Add remaining part to array\n    return parts;\n  }\n}\n\n/* eslint-disable max-classes-per-file */\n\n\nclass StackItem {\n  constructor (next = null) { this.next = next; }\n\n  getRoot () { return (this.next) ? this.next : this; }\n}\n\nclass BlockStackItem extends StackItem {\n  constructor (options, next = null, leadingLineBreaks = 1, maxLineLength = undefined) {\n    super(next);\n    this.leadingLineBreaks = leadingLineBreaks;\n    this.inlineTextBuilder = new InlineTextBuilder(options, maxLineLength);\n    this.rawText = '';\n    this.stashedLineBreaks = 0;\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass ListStackItem extends BlockStackItem {\n  constructor (\n    options,\n    next = null,\n    {\n      interRowLineBreaks = 1,\n      leadingLineBreaks = 2,\n      maxLineLength = undefined,\n      maxPrefixLength = 0,\n      prefixAlign = 'left',\n    } = {}\n  ) {\n    super(options, next, leadingLineBreaks, maxLineLength);\n    this.maxPrefixLength = maxPrefixLength;\n    this.prefixAlign = prefixAlign;\n    this.interRowLineBreaks = interRowLineBreaks;\n  }\n}\n\nclass ListItemStackItem extends BlockStackItem {\n  constructor (\n    options,\n    next = null,\n    {\n      leadingLineBreaks = 1,\n      maxLineLength = undefined,\n      prefix = '',\n    } = {}\n  ) {\n    super(options, next, leadingLineBreaks, maxLineLength);\n    this.prefix = prefix;\n  }\n}\n\nclass TableStackItem extends StackItem {\n  constructor (next = null) {\n    super(next);\n    this.rows = [];\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TableRowStackItem extends StackItem {\n  constructor (next = null) {\n    super(next);\n    this.cells = [];\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TableCellStackItem extends StackItem {\n  constructor (options, next = null, maxColumnWidth = undefined) {\n    super(next);\n    this.inlineTextBuilder = new InlineTextBuilder(options, maxColumnWidth);\n    this.rawText = '';\n    this.stashedLineBreaks = 0;\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TransformerStackItem extends StackItem {\n  constructor (next = null, transform) {\n    super(next);\n    this.transform = transform;\n  }\n}\n\nfunction charactersToCodes (str) {\n  return [...str]\n    .map(c => '\\\\u' + c.charCodeAt(0).toString(16).padStart(4, '0'))\n    .join('');\n}\n\n/**\n * Helps to handle HTML whitespaces.\n *\n * @class WhitespaceProcessor\n */\nclass WhitespaceProcessor {\n\n  /**\n   * Creates an instance of WhitespaceProcessor.\n   *\n   * @param { Options } options    HtmlToText options.\n   * @memberof WhitespaceProcessor\n   */\n  constructor (options) {\n    this.whitespaceChars = (options.preserveNewlines)\n      ? options.whitespaceCharacters.replace(/\\n/g, '')\n      : options.whitespaceCharacters;\n    const whitespaceCodes = charactersToCodes(this.whitespaceChars);\n    this.leadingWhitespaceRe = new RegExp(`^[${whitespaceCodes}]`);\n    this.trailingWhitespaceRe = new RegExp(`[${whitespaceCodes}]$`);\n    this.allWhitespaceOrEmptyRe = new RegExp(`^[${whitespaceCodes}]*$`);\n    this.newlineOrNonWhitespaceRe = new RegExp(`(\\\\n|[^\\\\n${whitespaceCodes}])`, 'g');\n    this.newlineOrNonNewlineStringRe = new RegExp(`(\\\\n|[^\\\\n]+)`, 'g');\n\n    if (options.preserveNewlines) {\n\n      const wordOrNewlineRe = new RegExp(`\\\\n|[^\\\\n${whitespaceCodes}]+`, 'gm');\n\n      /**\n       * Shrink whitespaces and wrap text, add to the builder.\n       *\n       * @param { string }                  text              Input text.\n       * @param { InlineTextBuilder }       inlineTextBuilder A builder to receive processed text.\n       * @param { (str: string) => string } [ transform ]     A transform to be applied to words.\n       * @param { boolean }                 [noWrap] Don't wrap text even if the line is too long.\n       */\n      this.shrinkWrapAdd = function (text, inlineTextBuilder, transform = (str => str), noWrap = false) {\n        if (!text) { return; }\n        const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n        let anyMatch = false;\n        let m = wordOrNewlineRe.exec(text);\n        if (m) {\n          anyMatch = true;\n          if (m[0] === '\\n') {\n            inlineTextBuilder.startNewLine();\n          } else if (previouslyStashedSpace || this.testLeadingWhitespace(text)) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          } else {\n            inlineTextBuilder.concatWord(transform(m[0]), noWrap);\n          }\n          while ((m = wordOrNewlineRe.exec(text)) !== null) {\n            if (m[0] === '\\n') {\n              inlineTextBuilder.startNewLine();\n            } else {\n              inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n            }\n          }\n        }\n        inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch) || (this.testTrailingWhitespace(text));\n        // No need to stash a space in case last added item was a new line,\n        // but that won't affect anything later anyway.\n      };\n\n    } else {\n\n      const wordRe = new RegExp(`[^${whitespaceCodes}]+`, 'g');\n\n      this.shrinkWrapAdd = function (text, inlineTextBuilder, transform = (str => str), noWrap = false) {\n        if (!text) { return; }\n        const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n        let anyMatch = false;\n        let m = wordRe.exec(text);\n        if (m) {\n          anyMatch = true;\n          if (previouslyStashedSpace || this.testLeadingWhitespace(text)) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          } else {\n            inlineTextBuilder.concatWord(transform(m[0]), noWrap);\n          }\n          while ((m = wordRe.exec(text)) !== null) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          }\n        }\n        inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch) || this.testTrailingWhitespace(text);\n      };\n\n    }\n  }\n\n  /**\n   * Add text with only minimal processing.\n   * Everything between newlines considered a single word.\n   * No whitespace is trimmed.\n   * Not affected by preserveNewlines option - `\\n` always starts a new line.\n   *\n   * `noWrap` argument is `true` by default - this won't start a new line\n   * even if there is not enough space left in the current line.\n   *\n   * @param { string }            text              Input text.\n   * @param { InlineTextBuilder } inlineTextBuilder A builder to receive processed text.\n   * @param { boolean }           [noWrap] Don't wrap text even if the line is too long.\n   */\n  addLiteral (text, inlineTextBuilder, noWrap = true) {\n    if (!text) { return; }\n    const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n    let anyMatch = false;\n    let m = this.newlineOrNonNewlineStringRe.exec(text);\n    if (m) {\n      anyMatch = true;\n      if (m[0] === '\\n') {\n        inlineTextBuilder.startNewLine();\n      } else if (previouslyStashedSpace) {\n        inlineTextBuilder.pushWord(m[0], noWrap);\n      } else {\n        inlineTextBuilder.concatWord(m[0], noWrap);\n      }\n      while ((m = this.newlineOrNonNewlineStringRe.exec(text)) !== null) {\n        if (m[0] === '\\n') {\n          inlineTextBuilder.startNewLine();\n        } else {\n          inlineTextBuilder.pushWord(m[0], noWrap);\n        }\n      }\n    }\n    inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch);\n  }\n\n  /**\n   * Test whether the given text starts with HTML whitespace character.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testLeadingWhitespace (text) {\n    return this.leadingWhitespaceRe.test(text);\n  }\n\n  /**\n   * Test whether the given text ends with HTML whitespace character.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testTrailingWhitespace (text) {\n    return this.trailingWhitespaceRe.test(text);\n  }\n\n  /**\n   * Test whether the given text contains any non-whitespace characters.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testContainsWords (text) {\n    return !this.allWhitespaceOrEmptyRe.test(text);\n  }\n\n  /**\n   * Return the number of newlines if there are no words.\n   *\n   * If any word is found then return zero regardless of the actual number of newlines.\n   *\n   * @param   { string }  text  Input string.\n   * @returns { number }\n   */\n  countNewlinesNoWords (text) {\n    this.newlineOrNonWhitespaceRe.lastIndex = 0;\n    let counter = 0;\n    let match;\n    while ((match = this.newlineOrNonWhitespaceRe.exec(text)) !== null) {\n      if (match[0] === '\\n') {\n        counter++;\n      } else {\n        return 0;\n      }\n    }\n    return counter;\n  }\n\n}\n\n/**\n * Helps to build text from inline and block elements.\n *\n * @class BlockTextBuilder\n */\nclass BlockTextBuilder {\n\n  /**\n   * Creates an instance of BlockTextBuilder.\n   *\n   * @param { Options } options HtmlToText options.\n   * @param { import('selderee').Picker<DomNode, TagDefinition> } picker Selectors decision tree picker.\n   * @param { any} [metadata] Optional metadata for HTML document, for use in formatters.\n   */\n  constructor (options, picker, metadata = undefined) {\n    this.options = options;\n    this.picker = picker;\n    this.metadata = metadata;\n    this.whitespaceProcessor = new WhitespaceProcessor(options);\n    /** @type { StackItem } */\n    this._stackItem = new BlockStackItem(options);\n    /** @type { TransformerStackItem } */\n    this._wordTransformer = undefined;\n  }\n\n  /**\n   * Put a word-by-word transform function onto the transformations stack.\n   *\n   * Mainly used for uppercasing. Can be bypassed to add unformatted text such as URLs.\n   *\n   * Word transformations applied before wrapping.\n   *\n   * @param { (str: string) => string } wordTransform Word transformation function.\n   */\n  pushWordTransform (wordTransform) {\n    this._wordTransformer = new TransformerStackItem(this._wordTransformer, wordTransform);\n  }\n\n  /**\n   * Remove a function from the word transformations stack.\n   *\n   * @returns { (str: string) => string } A function that was removed.\n   */\n  popWordTransform () {\n    if (!this._wordTransformer) { return undefined; }\n    const transform = this._wordTransformer.transform;\n    this._wordTransformer = this._wordTransformer.next;\n    return transform;\n  }\n\n  /**\n   * Ignore wordwrap option in followup inline additions and disable automatic wrapping.\n   */\n  startNoWrap () {\n    this._stackItem.isNoWrap = true;\n  }\n\n  /**\n   * Return automatic wrapping to behavior defined by options.\n   */\n  stopNoWrap () {\n    this._stackItem.isNoWrap = false;\n  }\n\n  /** @returns { (str: string) => string } */\n  _getCombinedWordTransformer () {\n    const wt = (this._wordTransformer)\n      ? ((str) => applyTransformer(str, this._wordTransformer))\n      : undefined;\n    const ce = this.options.encodeCharacters;\n    return (wt)\n      ? ((ce) ? (str) => ce(wt(str)) : wt)\n      : ce;\n  }\n\n  _popStackItem () {\n    const item = this._stackItem;\n    this._stackItem = item.next;\n    return item;\n  }\n\n  /**\n   * Add a line break into currently built block.\n   */\n  addLineBreak () {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += '\\n';\n    } else {\n      this._stackItem.inlineTextBuilder.startNewLine();\n    }\n  }\n\n  /**\n   * Allow to break line in case directly following text will not fit.\n   */\n  addWordBreakOpportunity () {\n    if (\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    ) {\n      this._stackItem.inlineTextBuilder.wordBreakOpportunity = true;\n    }\n  }\n\n  /**\n   * Add a node inline into the currently built block.\n   *\n   * @param { string } str\n   * Text content of a node to add.\n   *\n   * @param { object } [param1]\n   * Object holding the parameters of the operation.\n   *\n   * @param { boolean } [param1.noWordTransform]\n   * Ignore word transformers if there are any.\n   * Don't encode characters as well.\n   * (Use this for things like URL addresses).\n   */\n  addInline (str, { noWordTransform = false } = {}) {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += str;\n      return;\n    }\n\n    if (\n      str.length === 0 || // empty string\n      (\n        this._stackItem.stashedLineBreaks && // stashed linebreaks make whitespace irrelevant\n        !this.whitespaceProcessor.testContainsWords(str) // no words to add\n      )\n    ) { return; }\n\n    if (this.options.preserveNewlines) {\n      const newlinesNumber = this.whitespaceProcessor.countNewlinesNoWords(str);\n      if (newlinesNumber > 0) {\n        this._stackItem.inlineTextBuilder.startNewLine(newlinesNumber);\n        // keep stashedLineBreaks unchanged\n        return;\n      }\n    }\n\n    if (this._stackItem.stashedLineBreaks) {\n      this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks);\n    }\n    this.whitespaceProcessor.shrinkWrapAdd(\n      str,\n      this._stackItem.inlineTextBuilder,\n      (noWordTransform) ? undefined : this._getCombinedWordTransformer(),\n      this._stackItem.isNoWrap\n    );\n    this._stackItem.stashedLineBreaks = 0; // inline text doesn't introduce line breaks\n  }\n\n  /**\n   * Add a string inline into the currently built block.\n   *\n   * Use this for markup elements that don't have to adhere\n   * to text layout rules.\n   *\n   * @param { string } str Text to add.\n   */\n  addLiteral (str) {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n\n    if (str.length === 0) { return; }\n\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += str;\n      return;\n    }\n\n    if (this._stackItem.stashedLineBreaks) {\n      this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks);\n    }\n    this.whitespaceProcessor.addLiteral(\n      str,\n      this._stackItem.inlineTextBuilder,\n      this._stackItem.isNoWrap\n    );\n    this._stackItem.stashedLineBreaks = 0;\n  }\n\n  /**\n   * Start building a new block.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the block.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This block should have at least this number of line breaks to separate it from any preceding block.\n   *\n   * @param { number }  [param0.reservedLineLength]\n   * Reserve this number of characters on each line for block markup.\n   *\n   * @param { boolean } [param0.isPre]\n   * Should HTML whitespace be preserved inside this block.\n   */\n  openBlock ({ leadingLineBreaks = 1, reservedLineLength = 0, isPre = false } = {}) {\n    const maxLineLength = Math.max(20, this._stackItem.inlineTextBuilder.maxLineLength - reservedLineLength);\n    this._stackItem = new BlockStackItem(\n      this.options,\n      this._stackItem,\n      leadingLineBreaks,\n      maxLineLength\n    );\n    if (isPre) { this._stackItem.isPre = true; }\n  }\n\n  /**\n   * Finalize currently built block, add it's content to the parent block.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the block.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This block should have at least this number of line breaks to separate it from any following block.\n   *\n   * @param { (str: string) => string } [param0.blockTransform]\n   * A function to transform the block text before adding to the parent block.\n   * This happens after word wrap and should be used in combination with reserved line length\n   * in order to keep line lengths correct.\n   * Used for whole block markup.\n   */\n  closeBlock ({ trailingLineBreaks = 1, blockTransform = undefined } = {}) {\n    const block = this._popStackItem();\n    const blockText = (blockTransform) ? blockTransform(getText(block)) : getText(block);\n    addText(this._stackItem, blockText, block.leadingLineBreaks, Math.max(block.stashedLineBreaks, trailingLineBreaks));\n  }\n\n  /**\n   * Start building a new list.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the list.\n   *\n   * @param { number } [param0.maxPrefixLength]\n   * Length of the longest list item prefix.\n   * If not supplied or too small then list items won't be aligned properly.\n   *\n   * @param { 'left' | 'right' } [param0.prefixAlign]\n   * Specify how prefixes of different lengths have to be aligned\n   * within a column.\n   *\n   * @param { number } [param0.interRowLineBreaks]\n   * Minimum number of line breaks between list items.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This list should have at least this number of line breaks to separate it from any preceding block.\n   */\n  openList ({ maxPrefixLength = 0, prefixAlign = 'left', interRowLineBreaks = 1, leadingLineBreaks = 2 } = {}) {\n    this._stackItem = new ListStackItem(this.options, this._stackItem, {\n      interRowLineBreaks: interRowLineBreaks,\n      leadingLineBreaks: leadingLineBreaks,\n      maxLineLength: this._stackItem.inlineTextBuilder.maxLineLength,\n      maxPrefixLength: maxPrefixLength,\n      prefixAlign: prefixAlign\n    });\n  }\n\n  /**\n   * Start building a new list item.\n   *\n   * @param {object} param0\n   * Object holding the parameters of the list item.\n   *\n   * @param { string } [param0.prefix]\n   * Prefix for this list item (item number, bullet point, etc).\n   */\n  openListItem ({ prefix = '' } = {}) {\n    if (!(this._stackItem instanceof ListStackItem)) {\n      throw new Error('Can\\'t add a list item to something that is not a list! Check the formatter.');\n    }\n    const list = this._stackItem;\n    const prefixLength = Math.max(prefix.length, list.maxPrefixLength);\n    const maxLineLength = Math.max(20, list.inlineTextBuilder.maxLineLength - prefixLength);\n    this._stackItem = new ListItemStackItem(this.options, list, {\n      prefix: prefix,\n      maxLineLength: maxLineLength,\n      leadingLineBreaks: list.interRowLineBreaks\n    });\n  }\n\n  /**\n   * Finalize currently built list item, add it's content to the parent list.\n   */\n  closeListItem () {\n    const listItem = this._popStackItem();\n    const list = listItem.next;\n\n    const prefixLength = Math.max(listItem.prefix.length, list.maxPrefixLength);\n    const spacing = '\\n' + ' '.repeat(prefixLength);\n    const prefix = (list.prefixAlign === 'right')\n      ? listItem.prefix.padStart(prefixLength)\n      : listItem.prefix.padEnd(prefixLength);\n    const text = prefix + getText(listItem).replace(/\\n/g, spacing);\n\n    addText(\n      list,\n      text,\n      listItem.leadingLineBreaks,\n      Math.max(listItem.stashedLineBreaks, list.interRowLineBreaks)\n    );\n  }\n\n  /**\n   * Finalize currently built list, add it's content to the parent block.\n   *\n   * @param { object } param0\n   * Object holding the parameters of the list.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This list should have at least this number of line breaks to separate it from any following block.\n   */\n  closeList ({ trailingLineBreaks = 2 } = {}) {\n    const list = this._popStackItem();\n    const text = getText(list);\n    if (text) {\n      addText(this._stackItem, text, list.leadingLineBreaks, trailingLineBreaks);\n    }\n  }\n\n  /**\n   * Start building a table.\n   */\n  openTable () {\n    this._stackItem = new TableStackItem(this._stackItem);\n  }\n\n  /**\n   * Start building a table row.\n   */\n  openTableRow () {\n    if (!(this._stackItem instanceof TableStackItem)) {\n      throw new Error('Can\\'t add a table row to something that is not a table! Check the formatter.');\n    }\n    this._stackItem = new TableRowStackItem(this._stackItem);\n  }\n\n  /**\n   * Start building a table cell.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the cell.\n   *\n   * @param { number } [param0.maxColumnWidth]\n   * Wrap cell content to this width. Fall back to global wordwrap value if undefined.\n   */\n  openTableCell ({ maxColumnWidth = undefined } = {}) {\n    if (!(this._stackItem instanceof TableRowStackItem)) {\n      throw new Error('Can\\'t add a table cell to something that is not a table row! Check the formatter.');\n    }\n    this._stackItem = new TableCellStackItem(this.options, this._stackItem, maxColumnWidth);\n  }\n\n  /**\n   * Finalize currently built table cell and add it to parent table row's cells.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the cell.\n   *\n   * @param { number } [param0.colspan] How many columns this cell should occupy.\n   * @param { number } [param0.rowspan] How many rows this cell should occupy.\n   */\n  closeTableCell ({ colspan = 1, rowspan = 1 } = {}) {\n    const cell = this._popStackItem();\n    const text = trimCharacter(getText(cell), '\\n');\n    cell.next.cells.push({ colspan: colspan, rowspan: rowspan, text: text });\n  }\n\n  /**\n   * Finalize currently built table row and add it to parent table's rows.\n   */\n  closeTableRow () {\n    const row = this._popStackItem();\n    row.next.rows.push(row.cells);\n  }\n\n  /**\n   * Finalize currently built table and add the rendered text to the parent block.\n   *\n   * @param { object } param0\n   * Object holding the parameters of the table.\n   *\n   * @param { TablePrinter } param0.tableToString\n   * A function to convert a table of stringified cells into a complete table.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This table should have at least this number of line breaks to separate if from any preceding block.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This table should have at least this number of line breaks to separate it from any following block.\n   */\n  closeTable ({ tableToString, leadingLineBreaks = 2, trailingLineBreaks = 2 }) {\n    const table = this._popStackItem();\n    const output = tableToString(table.rows);\n    if (output) {\n      addText(this._stackItem, output, leadingLineBreaks, trailingLineBreaks);\n    }\n  }\n\n  /**\n   * Return the rendered text content of this builder.\n   *\n   * @returns { string }\n   */\n  toString () {\n    return getText(this._stackItem.getRoot());\n    // There should only be the root item if everything is closed properly.\n  }\n\n}\n\nfunction getText (stackItem) {\n  if (!(\n    stackItem instanceof BlockStackItem\n    || stackItem instanceof ListItemStackItem\n    || stackItem instanceof TableCellStackItem\n  )) {\n    throw new Error('Only blocks, list items and table cells can be requested for text contents.');\n  }\n  return (stackItem.inlineTextBuilder.isEmpty())\n    ? stackItem.rawText\n    : stackItem.rawText + stackItem.inlineTextBuilder.toString();\n}\n\nfunction addText (stackItem, text, leadingLineBreaks, trailingLineBreaks) {\n  if (!(\n    stackItem instanceof BlockStackItem\n    || stackItem instanceof ListItemStackItem\n    || stackItem instanceof TableCellStackItem\n  )) {\n    throw new Error('Only blocks, list items and table cells can contain text.');\n  }\n  const parentText = getText(stackItem);\n  const lineBreaks = Math.max(stackItem.stashedLineBreaks, leadingLineBreaks);\n  stackItem.inlineTextBuilder.clear();\n  if (parentText) {\n    stackItem.rawText = parentText + '\\n'.repeat(lineBreaks) + text;\n  } else {\n    stackItem.rawText = text;\n    stackItem.leadingLineBreaks = lineBreaks;\n  }\n  stackItem.stashedLineBreaks = trailingLineBreaks;\n}\n\n/**\n * @param { string } str A string to transform.\n * @param { TransformerStackItem } transformer A transformer item (with possible continuation).\n * @returns { string }\n */\nfunction applyTransformer (str, transformer) {\n  return ((transformer) ? applyTransformer(transformer.transform(str), transformer.next) : str);\n}\n\n/**\n * Compile selectors into a decision tree,\n * return a function intended for batch processing.\n *\n * @param   { Options } [options = {}]   HtmlToText options (defaults, formatters, user options merged, deduplicated).\n * @returns { (html: string, metadata?: any) => string } Pre-configured converter function.\n * @static\n */\nfunction compile$1 (options = {}) {\n  const selectorsWithoutFormat = options.selectors.filter(s => !s.format);\n  if (selectorsWithoutFormat.length) {\n    throw new Error(\n      'Following selectors have no specified format: ' +\n      selectorsWithoutFormat.map(s => `\\`${s.selector}\\``).join(', ')\n    );\n  }\n  const picker = new DecisionTree(\n    options.selectors.map(s => [s.selector, s])\n  ).build(hp2Builder);\n\n  if (typeof options.encodeCharacters !== 'function') {\n    options.encodeCharacters = makeReplacerFromDict(options.encodeCharacters);\n  }\n\n  const baseSelectorsPicker = new DecisionTree(\n    options.baseElements.selectors.map((s, i) => [s, i + 1])\n  ).build(hp2Builder);\n  function findBaseElements (dom) {\n    return findBases(dom, options, baseSelectorsPicker);\n  }\n\n  const limitedWalk = limitedDepthRecursive(\n    options.limits.maxDepth,\n    recursiveWalk,\n    function (dom, builder) {\n      builder.addInline(options.limits.ellipsis || '');\n    }\n  );\n\n  return function (html, metadata = undefined) {\n    return process(html, metadata, options, picker, findBaseElements, limitedWalk);\n  };\n}\n\n\n/**\n * Convert given HTML according to preprocessed options.\n *\n * @param { string } html HTML content to convert.\n * @param { any } metadata Optional metadata for HTML document, for use in formatters.\n * @param { Options } options HtmlToText options (preprocessed).\n * @param { import('selderee').Picker<DomNode, TagDefinition> } picker\n * Tag definition picker for DOM nodes processing.\n * @param { (dom: DomNode[]) => DomNode[] } findBaseElements\n * Function to extract elements from HTML DOM\n * that will only be present in the output text.\n * @param { RecursiveCallback } walk Recursive callback.\n * @returns { string }\n */\nfunction process (html, metadata, options, picker, findBaseElements, walk) {\n  const maxInputLength = options.limits.maxInputLength;\n  if (maxInputLength && html && html.length > maxInputLength) {\n    console.warn(\n      `Input length ${html.length} is above allowed limit of ${maxInputLength}. Truncating without ellipsis.`\n    );\n    html = html.substring(0, maxInputLength);\n  }\n\n  const document = parseDocument(html, { decodeEntities: options.decodeEntities });\n  const bases = findBaseElements(document.children);\n  const builder = new BlockTextBuilder(options, picker, metadata);\n  walk(bases, builder);\n  return builder.toString();\n}\n\n\nfunction findBases (dom, options, baseSelectorsPicker) {\n  const results = [];\n\n  function recursiveWalk (walk, /** @type { DomNode[] } */ dom) {\n    dom = dom.slice(0, options.limits.maxChildNodes);\n    for (const elem of dom) {\n      if (elem.type !== 'tag') {\n        continue;\n      }\n      const pickedSelectorIndex = baseSelectorsPicker.pick1(elem);\n      if (pickedSelectorIndex > 0) {\n        results.push({ selectorIndex: pickedSelectorIndex, element: elem });\n      } else if (elem.children) {\n        walk(elem.children);\n      }\n      if (results.length >= options.limits.maxBaseElements) {\n        return;\n      }\n    }\n  }\n\n  const limitedWalk = limitedDepthRecursive(\n    options.limits.maxDepth,\n    recursiveWalk\n  );\n  limitedWalk(dom);\n\n  if (options.baseElements.orderBy !== 'occurrence') { // 'selectors'\n    results.sort((a, b) => a.selectorIndex - b.selectorIndex);\n  }\n  return (options.baseElements.returnDomByDefault && results.length === 0)\n    ? dom\n    : results.map(x => x.element);\n}\n\n/**\n * Function to walk through DOM nodes and accumulate their string representations.\n *\n * @param   { RecursiveCallback } walk    Recursive callback.\n * @param   { DomNode[] }         [dom]   Nodes array to process.\n * @param   { BlockTextBuilder }  builder Passed around to accumulate output text.\n * @private\n */\nfunction recursiveWalk (walk, dom, builder) {\n  if (!dom) { return; }\n\n  const options = builder.options;\n\n  const tooManyChildNodes = dom.length > options.limits.maxChildNodes;\n  if (tooManyChildNodes) {\n    dom = dom.slice(0, options.limits.maxChildNodes);\n    dom.push({\n      data: options.limits.ellipsis,\n      type: 'text'\n    });\n  }\n\n  for (const elem of dom) {\n    switch (elem.type) {\n      case 'text': {\n        builder.addInline(elem.data);\n        break;\n      }\n      case 'tag': {\n        const tagDefinition = builder.picker.pick1(elem);\n        const format = options.formatters[tagDefinition.format];\n        format(elem, walk, builder, tagDefinition.options || {});\n        break;\n      }\n    }\n  }\n\n  return;\n}\n\n/**\n * @param { Object<string,string | false> } dict\n * A dictionary where keys are characters to replace\n * and values are replacement strings.\n *\n * First code point from dict keys is used.\n * Compound emojis with ZWJ are not supported (not until Node 16).\n *\n * @returns { ((str: string) => string) | undefined }\n */\nfunction makeReplacerFromDict (dict) {\n  if (!dict || Object.keys(dict).length === 0) {\n    return undefined;\n  }\n  /** @type { [string, string][] } */\n  const entries = Object.entries(dict).filter(([, v]) => v !== false);\n  const regex = new RegExp(\n    entries\n      .map(([c]) => `(${unicodeEscape([...c][0])})`)\n      .join('|'),\n    'g'\n  );\n  const values = entries.map(([, v]) => v);\n  const replacer = (m, ...cgs) => values[cgs.findIndex(cg => cg)];\n  return (str) => str.replace(regex, replacer);\n}\n\n/**\n * Dummy formatter that discards the input and does nothing.\n *\n * @type { FormatCallback }\n */\nfunction formatSkip (elem, walk, builder, formatOptions) {\n  /* do nothing */\n}\n\n/**\n * Insert the given string literal inline instead of a tag.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineString (elem, walk, builder, formatOptions) {\n  builder.addLiteral(formatOptions.string || '');\n}\n\n/**\n * Insert a block with the given string literal instead of a tag.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockString (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.addLiteral(formatOptions.string || '');\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process an inline-level element.\n *\n * @type { FormatCallback }\n */\nfunction formatInline (elem, walk, builder, formatOptions) {\n  walk(elem.children, builder);\n}\n\n/**\n * Process a block-level container.\n *\n * @type { FormatCallback }\n */\nfunction formatBlock$1 (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\nfunction renderOpenTag (elem) {\n  const attrs = (elem.attribs && elem.attribs.length)\n    ? ' ' + Object.entries(elem.attribs)\n      .map(([k, v]) => ((v === '') ? k : `${k}=${v.replace(/\"/g, '&quot;')}`))\n      .join(' ')\n    : '';\n  return `<${elem.name}${attrs}>`;\n}\n\nfunction renderCloseTag (elem) {\n  return `</${elem.name}>`;\n}\n\n/**\n * Render an element as inline HTML tag, walk through it's children.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineTag (elem, walk, builder, formatOptions) {\n  builder.startNoWrap();\n  builder.addLiteral(renderOpenTag(elem));\n  builder.stopNoWrap();\n  walk(elem.children, builder);\n  builder.startNoWrap();\n  builder.addLiteral(renderCloseTag(elem));\n  builder.stopNoWrap();\n}\n\n/**\n * Render an element as HTML block bag, walk through it's children.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockTag (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.startNoWrap();\n  builder.addLiteral(renderOpenTag(elem));\n  builder.stopNoWrap();\n  walk(elem.children, builder);\n  builder.startNoWrap();\n  builder.addLiteral(renderCloseTag(elem));\n  builder.stopNoWrap();\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Render an element with all it's children as inline HTML.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineHtml (elem, walk, builder, formatOptions) {\n  builder.startNoWrap();\n  builder.addLiteral(\n    render(elem, { decodeEntities: builder.options.decodeEntities })\n  );\n  builder.stopNoWrap();\n}\n\n/**\n * Render an element with all it's children as HTML block.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockHtml (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.startNoWrap();\n  builder.addLiteral(\n    render(elem, { decodeEntities: builder.options.decodeEntities })\n  );\n  builder.stopNoWrap();\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Render inline element wrapped with given strings.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineSurround (elem, walk, builder, formatOptions) {\n  builder.addLiteral(formatOptions.prefix || '');\n  walk(elem.children, builder);\n  builder.addLiteral(formatOptions.suffix || '');\n}\n\nvar genericFormatters = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  block: formatBlock$1,\n  blockHtml: formatBlockHtml,\n  blockString: formatBlockString,\n  blockTag: formatBlockTag,\n  inline: formatInline,\n  inlineHtml: formatInlineHtml,\n  inlineString: formatInlineString,\n  inlineSurround: formatInlineSurround,\n  inlineTag: formatInlineTag,\n  skip: formatSkip\n});\n\nfunction getRow (matrix, j) {\n  if (!matrix[j]) { matrix[j] = []; }\n  return matrix[j];\n}\n\nfunction findFirstVacantIndex (row, x = 0) {\n  while (row[x]) { x++; }\n  return x;\n}\n\nfunction transposeInPlace (matrix, maxSize) {\n  for (let i = 0; i < maxSize; i++) {\n    const rowI = getRow(matrix, i);\n    for (let j = 0; j < i; j++) {\n      const rowJ = getRow(matrix, j);\n      if (rowI[j] || rowJ[i]) {\n        const temp = rowI[j];\n        rowI[j] = rowJ[i];\n        rowJ[i] = temp;\n      }\n    }\n  }\n}\n\nfunction putCellIntoLayout (cell, layout, baseRow, baseCol) {\n  for (let r = 0; r < cell.rowspan; r++) {\n    const layoutRow = getRow(layout, baseRow + r);\n    for (let c = 0; c < cell.colspan; c++) {\n      layoutRow[baseCol + c] = cell;\n    }\n  }\n}\n\nfunction getOrInitOffset (offsets, index) {\n  if (offsets[index] === undefined) {\n    offsets[index] = (index === 0) ? 0 : 1 + getOrInitOffset(offsets, index - 1);\n  }\n  return offsets[index];\n}\n\nfunction updateOffset (offsets, base, span, value) {\n  offsets[base + span] = Math.max(\n    getOrInitOffset(offsets, base + span),\n    getOrInitOffset(offsets, base) + value\n  );\n}\n\n/**\n * Render a table into a string.\n * Cells can contain multiline text and span across multiple rows and columns.\n *\n * Modifies cells to add lines array.\n *\n * @param { TablePrinterCell[][] } tableRows Table to render.\n * @param { number } rowSpacing Number of spaces between columns.\n * @param { number } colSpacing Number of empty lines between rows.\n * @returns { string }\n */\nfunction tableToString (tableRows, rowSpacing, colSpacing) {\n  const layout = [];\n  let colNumber = 0;\n  const rowNumber = tableRows.length;\n  const rowOffsets = [0];\n  // Fill the layout table and row offsets row-by-row.\n  for (let j = 0; j < rowNumber; j++) {\n    const layoutRow = getRow(layout, j);\n    const cells = tableRows[j];\n    let x = 0;\n    for (let i = 0; i < cells.length; i++) {\n      const cell = cells[i];\n      x = findFirstVacantIndex(layoutRow, x);\n      putCellIntoLayout(cell, layout, j, x);\n      x += cell.colspan;\n      cell.lines = cell.text.split('\\n');\n      const cellHeight = cell.lines.length;\n      updateOffset(rowOffsets, j, cell.rowspan, cellHeight + rowSpacing);\n    }\n    colNumber = (layoutRow.length > colNumber) ? layoutRow.length : colNumber;\n  }\n\n  transposeInPlace(layout, (rowNumber > colNumber) ? rowNumber : colNumber);\n\n  const outputLines = [];\n  const colOffsets = [0];\n  // Fill column offsets and output lines column-by-column.\n  for (let x = 0; x < colNumber; x++) {\n    let y = 0;\n    let cell;\n    const rowsInThisColumn = Math.min(rowNumber, layout[x].length);\n    while (y < rowsInThisColumn) {\n      cell = layout[x][y];\n      if (cell) {\n        if (!cell.rendered) {\n          let cellWidth = 0;\n          for (let j = 0; j < cell.lines.length; j++) {\n            const line = cell.lines[j];\n            const lineOffset = rowOffsets[y] + j;\n            outputLines[lineOffset] = (outputLines[lineOffset] || '').padEnd(colOffsets[x]) + line;\n            cellWidth = (line.length > cellWidth) ? line.length : cellWidth;\n          }\n          updateOffset(colOffsets, x, cell.colspan, cellWidth + colSpacing);\n          cell.rendered = true;\n        }\n        y += cell.rowspan;\n      } else {\n        const lineOffset = rowOffsets[y];\n        outputLines[lineOffset] = (outputLines[lineOffset] || '');\n        y++;\n      }\n    }\n  }\n\n  return outputLines.join('\\n');\n}\n\n/**\n * Process a line-break.\n *\n * @type { FormatCallback }\n */\nfunction formatLineBreak (elem, walk, builder, formatOptions) {\n  builder.addLineBreak();\n}\n\n/**\n * Process a `wbr` tag (word break opportunity).\n *\n * @type { FormatCallback }\n */\nfunction formatWbr (elem, walk, builder, formatOptions) {\n  builder.addWordBreakOpportunity();\n}\n\n/**\n * Process a horizontal line.\n *\n * @type { FormatCallback }\n */\nfunction formatHorizontalLine (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.addInline('-'.repeat(formatOptions.length || builder.options.wordwrap || 40));\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a paragraph.\n *\n * @type { FormatCallback }\n */\nfunction formatParagraph (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a preformatted content.\n *\n * @type { FormatCallback }\n */\nfunction formatPre (elem, walk, builder, formatOptions) {\n  builder.openBlock({\n    isPre: true,\n    leadingLineBreaks: formatOptions.leadingLineBreaks || 2\n  });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a heading.\n *\n * @type { FormatCallback }\n */\nfunction formatHeading (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  if (formatOptions.uppercase !== false) {\n    builder.pushWordTransform(str => str.toUpperCase());\n    walk(elem.children, builder);\n    builder.popWordTransform();\n  } else {\n    walk(elem.children, builder);\n  }\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a blockquote.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockquote (elem, walk, builder, formatOptions) {\n  builder.openBlock({\n    leadingLineBreaks: formatOptions.leadingLineBreaks || 2,\n    reservedLineLength: 2\n  });\n  walk(elem.children, builder);\n  builder.closeBlock({\n    trailingLineBreaks: formatOptions.trailingLineBreaks || 2,\n    blockTransform: str => ((formatOptions.trimEmptyLines !== false) ? trimCharacter(str, '\\n') : str)\n      .split('\\n')\n      .map(line => '> ' + line)\n      .join('\\n')\n  });\n}\n\nfunction withBrackets (str, brackets) {\n  if (!brackets) { return str; }\n\n  const lbr = (typeof brackets[0] === 'string')\n    ? brackets[0]\n    : '[';\n  const rbr = (typeof brackets[1] === 'string')\n    ? brackets[1]\n    : ']';\n  return lbr + str + rbr;\n}\n\nfunction pathRewrite (path, rewriter, baseUrl, metadata, elem) {\n  const modifiedPath = (typeof rewriter === 'function')\n    ? rewriter(path, metadata, elem)\n    : path;\n  return (modifiedPath[0] === '/' && baseUrl)\n    ? trimCharacterEnd(baseUrl, '/') + modifiedPath\n    : modifiedPath;\n}\n\n/**\n * Process an image.\n *\n * @type { FormatCallback }\n */\nfunction formatImage (elem, walk, builder, formatOptions) {\n  const attribs = elem.attribs || {};\n  const alt = (attribs.alt)\n    ? attribs.alt\n    : '';\n  const src = (!attribs.src)\n    ? ''\n    : pathRewrite(attribs.src, formatOptions.pathRewrite, formatOptions.baseUrl, builder.metadata, elem);\n  const text = (!src)\n    ? alt\n    : (!alt)\n      ? withBrackets(src, formatOptions.linkBrackets)\n      : alt + ' ' + withBrackets(src, formatOptions.linkBrackets);\n\n  builder.addInline(text, { noWordTransform: true });\n}\n\n// a img baseUrl\n// a img pathRewrite\n// a img linkBrackets\n\n// a     ignoreHref: false\n//            ignoreText ?\n// a     noAnchorUrl: true\n//            can be replaced with selector\n// a     hideLinkHrefIfSameAsText: false\n//            how to compare, what to show (text, href, normalized) ?\n// a     mailto protocol removed without options\n\n// a     protocols: mailto, tel, ...\n//            can be matched with selector?\n\n// anchors, protocols - only if no pathRewrite fn is provided\n\n// normalize-url ?\n\n// a\n// a[href^=\"#\"] - format:skip by default\n// a[href^=\"mailto:\"] - ?\n\n/**\n * Process an anchor.\n *\n * @type { FormatCallback }\n */\nfunction formatAnchor (elem, walk, builder, formatOptions) {\n  function getHref () {\n    if (formatOptions.ignoreHref) { return ''; }\n    if (!elem.attribs || !elem.attribs.href) { return ''; }\n    let href = elem.attribs.href.replace(/^mailto:/, '');\n    if (formatOptions.noAnchorUrl && href[0] === '#') { return ''; }\n    href = pathRewrite(href, formatOptions.pathRewrite, formatOptions.baseUrl, builder.metadata, elem);\n    return href;\n  }\n  const href = getHref();\n  if (!href) {\n    walk(elem.children, builder);\n  } else {\n    let text = '';\n    builder.pushWordTransform(\n      str => {\n        if (str) { text += str; }\n        return str;\n      }\n    );\n    walk(elem.children, builder);\n    builder.popWordTransform();\n\n    const hideSameLink = formatOptions.hideLinkHrefIfSameAsText && href === text;\n    if (!hideSameLink) {\n      builder.addInline(\n        (!text)\n          ? href\n          : ' ' + withBrackets(href, formatOptions.linkBrackets),\n        { noWordTransform: true }\n      );\n    }\n  }\n}\n\n/**\n * @param { DomNode }           elem               List items with their prefixes.\n * @param { RecursiveCallback } walk               Recursive callback to process child nodes.\n * @param { BlockTextBuilder }  builder            Passed around to accumulate output text.\n * @param { FormatOptions }     formatOptions      Options specific to a formatter.\n * @param { () => string }      nextPrefixCallback Function that returns increasing index each time it is called.\n */\nfunction formatList (elem, walk, builder, formatOptions, nextPrefixCallback) {\n  const isNestedList = get(elem, ['parent', 'name']) === 'li';\n\n  // With Roman numbers, index length is not as straightforward as with Arabic numbers or letters,\n  // so the dumb length comparison is the most robust way to get the correct value.\n  let maxPrefixLength = 0;\n  const listItems = (elem.children || [])\n    // it might be more accurate to check only for html spaces here, but no significant benefit\n    .filter(child => child.type !== 'text' || !/^\\s*$/.test(child.data))\n    .map(function (child) {\n      if (child.name !== 'li') {\n        return { node: child, prefix: '' };\n      }\n      const prefix = (isNestedList)\n        ? nextPrefixCallback().trimStart()\n        : nextPrefixCallback();\n      if (prefix.length > maxPrefixLength) { maxPrefixLength = prefix.length; }\n      return { node: child, prefix: prefix };\n    });\n  if (!listItems.length) { return; }\n\n  builder.openList({\n    interRowLineBreaks: 1,\n    leadingLineBreaks: isNestedList ? 1 : (formatOptions.leadingLineBreaks || 2),\n    maxPrefixLength: maxPrefixLength,\n    prefixAlign: 'left'\n  });\n\n  for (const { node, prefix } of listItems) {\n    builder.openListItem({ prefix: prefix });\n    walk([node], builder);\n    builder.closeListItem();\n  }\n\n  builder.closeList({ trailingLineBreaks: isNestedList ? 1 : (formatOptions.trailingLineBreaks || 2) });\n}\n\n/**\n * Process an unordered list.\n *\n * @type { FormatCallback }\n */\nfunction formatUnorderedList (elem, walk, builder, formatOptions) {\n  const prefix = formatOptions.itemPrefix || ' * ';\n  return formatList(elem, walk, builder, formatOptions, () => prefix);\n}\n\n/**\n * Process an ordered list.\n *\n * @type { FormatCallback }\n */\nfunction formatOrderedList (elem, walk, builder, formatOptions) {\n  let nextIndex = Number(elem.attribs.start || '1');\n  const indexFunction = getOrderedListIndexFunction(elem.attribs.type);\n  const nextPrefixCallback = () => ' ' + indexFunction(nextIndex++) + '. ';\n  return formatList(elem, walk, builder, formatOptions, nextPrefixCallback);\n}\n\n/**\n * Return a function that can be used to generate index markers of a specified format.\n *\n * @param   { string } [olType='1'] Marker type.\n * @returns { (i: number) => string }\n */\nfunction getOrderedListIndexFunction (olType = '1') {\n  switch (olType) {\n    case 'a': return (i) => numberToLetterSequence(i, 'a');\n    case 'A': return (i) => numberToLetterSequence(i, 'A');\n    case 'i': return (i) => numberToRoman(i).toLowerCase();\n    case 'I': return (i) => numberToRoman(i);\n    case '1':\n    default: return (i) => (i).toString();\n  }\n}\n\n/**\n * Given a list of class and ID selectors (prefixed with '.' and '#'),\n * return them as separate lists of names without prefixes.\n *\n * @param { string[] } selectors Class and ID selectors (`[\".class\", \"#id\"]` etc).\n * @returns { { classes: string[], ids: string[] } }\n */\nfunction splitClassesAndIds (selectors) {\n  const classes = [];\n  const ids = [];\n  for (const selector of selectors) {\n    if (selector.startsWith('.')) {\n      classes.push(selector.substring(1));\n    } else if (selector.startsWith('#')) {\n      ids.push(selector.substring(1));\n    }\n  }\n  return { classes: classes, ids: ids };\n}\n\nfunction isDataTable (attr, tables) {\n  if (tables === true) { return true; }\n  if (!attr) { return false; }\n\n  const { classes, ids } = splitClassesAndIds(tables);\n  const attrClasses = (attr['class'] || '').split(' ');\n  const attrIds = (attr['id'] || '').split(' ');\n\n  return attrClasses.some(x => classes.includes(x)) || attrIds.some(x => ids.includes(x));\n}\n\n/**\n * Process a table (either as a container or as a data table, depending on options).\n *\n * @type { FormatCallback }\n */\nfunction formatTable (elem, walk, builder, formatOptions) {\n  return isDataTable(elem.attribs, builder.options.tables)\n    ? formatDataTable(elem, walk, builder, formatOptions)\n    : formatBlock(elem, walk, builder, formatOptions);\n}\n\nfunction formatBlock (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks });\n}\n\n/**\n * Process a data table.\n *\n * @type { FormatCallback }\n */\nfunction formatDataTable (elem, walk, builder, formatOptions) {\n  builder.openTable();\n  elem.children.forEach(walkTable);\n  builder.closeTable({\n    tableToString: (rows) => tableToString(rows, formatOptions.rowSpacing ?? 0, formatOptions.colSpacing ?? 3),\n    leadingLineBreaks: formatOptions.leadingLineBreaks,\n    trailingLineBreaks: formatOptions.trailingLineBreaks\n  });\n\n  function formatCell (cellNode) {\n    const colspan = +get(cellNode, ['attribs', 'colspan']) || 1;\n    const rowspan = +get(cellNode, ['attribs', 'rowspan']) || 1;\n    builder.openTableCell({ maxColumnWidth: formatOptions.maxColumnWidth });\n    walk(cellNode.children, builder);\n    builder.closeTableCell({ colspan: colspan, rowspan: rowspan });\n  }\n\n  function walkTable (elem) {\n    if (elem.type !== 'tag') { return; }\n\n    const formatHeaderCell = (formatOptions.uppercaseHeaderCells !== false)\n      ? (cellNode) => {\n        builder.pushWordTransform(str => str.toUpperCase());\n        formatCell(cellNode);\n        builder.popWordTransform();\n      }\n      : formatCell;\n\n    switch (elem.name) {\n      case 'thead':\n      case 'tbody':\n      case 'tfoot':\n      case 'center':\n        elem.children.forEach(walkTable);\n        return;\n\n      case 'tr': {\n        builder.openTableRow();\n        for (const childOfTr of elem.children) {\n          if (childOfTr.type !== 'tag') { continue; }\n          switch (childOfTr.name) {\n            case 'th': {\n              formatHeaderCell(childOfTr);\n              break;\n            }\n            case 'td': {\n              formatCell(childOfTr);\n              break;\n            }\n              // do nothing\n          }\n        }\n        builder.closeTableRow();\n        break;\n      }\n        // do nothing\n    }\n  }\n}\n\nvar textFormatters = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  anchor: formatAnchor,\n  blockquote: formatBlockquote,\n  dataTable: formatDataTable,\n  heading: formatHeading,\n  horizontalLine: formatHorizontalLine,\n  image: formatImage,\n  lineBreak: formatLineBreak,\n  orderedList: formatOrderedList,\n  paragraph: formatParagraph,\n  pre: formatPre,\n  table: formatTable,\n  unorderedList: formatUnorderedList,\n  wbr: formatWbr\n});\n\n/**\n * Default options.\n *\n * @constant\n * @type { Options }\n * @default\n * @private\n */\nconst DEFAULT_OPTIONS = {\n  baseElements: {\n    selectors: [ 'body' ],\n    orderBy: 'selectors', // 'selectors' | 'occurrence'\n    returnDomByDefault: true\n  },\n  decodeEntities: true,\n  encodeCharacters: {},\n  formatters: {},\n  limits: {\n    ellipsis: '...',\n    maxBaseElements: undefined,\n    maxChildNodes: undefined,\n    maxDepth: undefined,\n    maxInputLength: (1 << 24) // 16_777_216\n  },\n  longWordSplit: {\n    forceWrapOnLimit: false,\n    wrapCharacters: []\n  },\n  preserveNewlines: false,\n  selectors: [\n    { selector: '*', format: 'inline' },\n    {\n      selector: 'a',\n      format: 'anchor',\n      options: {\n        baseUrl: null,\n        hideLinkHrefIfSameAsText: false,\n        ignoreHref: false,\n        linkBrackets: ['[', ']'],\n        noAnchorUrl: true\n      }\n    },\n    { selector: 'article', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'aside', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'blockquote',\n      format: 'blockquote',\n      options: { leadingLineBreaks: 2, trailingLineBreaks: 2, trimEmptyLines: true }\n    },\n    { selector: 'br', format: 'lineBreak' },\n    { selector: 'div', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'footer', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'form', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'h1', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h2', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h3', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h4', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h5', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h6', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'header', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'hr',\n      format: 'horizontalLine',\n      options: { leadingLineBreaks: 2, length: undefined, trailingLineBreaks: 2 }\n    },\n    {\n      selector: 'img',\n      format: 'image',\n      options: { baseUrl: null, linkBrackets: ['[', ']'] }\n    },\n    { selector: 'main', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'nav', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'ol',\n      format: 'orderedList',\n      options: { leadingLineBreaks: 2, trailingLineBreaks: 2 }\n    },\n    { selector: 'p', format: 'paragraph', options: { leadingLineBreaks: 2, trailingLineBreaks: 2 } },\n    { selector: 'pre', format: 'pre', options: { leadingLineBreaks: 2, trailingLineBreaks: 2 } },\n    { selector: 'section', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'table',\n      format: 'table',\n      options: {\n        colSpacing: 3,\n        leadingLineBreaks: 2,\n        maxColumnWidth: 60,\n        rowSpacing: 0,\n        trailingLineBreaks: 2,\n        uppercaseHeaderCells: true\n      }\n    },\n    {\n      selector: 'ul',\n      format: 'unorderedList',\n      options: { itemPrefix: ' * ', leadingLineBreaks: 2, trailingLineBreaks: 2 }\n    },\n    { selector: 'wbr', format: 'wbr' },\n  ],\n  tables: [], // deprecated\n  whitespaceCharacters: ' \\t\\r\\n\\f\\u200b',\n  wordwrap: 80\n};\n\nconst concatMerge = (acc, src, options) => [...acc, ...src];\nconst overwriteMerge = (acc, src, options) => [...src];\nconst selectorsMerge = (acc, src, options) => (\n  (acc.some(s => typeof s === 'object'))\n    ? concatMerge(acc, src) // selectors\n    : overwriteMerge(acc, src) // baseElements.selectors\n);\n\n/**\n * Preprocess options, compile selectors into a decision tree,\n * return a function intended for batch processing.\n *\n * @param   { Options } [options = {}]   HtmlToText options.\n * @returns { (html: string, metadata?: any) => string } Pre-configured converter function.\n * @static\n */\nfunction compile (options = {}) {\n  options = merge(\n    DEFAULT_OPTIONS,\n    options,\n    {\n      arrayMerge: overwriteMerge,\n      customMerge: (key) => ((key === 'selectors') ? selectorsMerge : undefined)\n    }\n  );\n  options.formatters = Object.assign({}, genericFormatters, textFormatters, options.formatters);\n  options.selectors = mergeDuplicatesPreferLast(options.selectors, (s => s.selector));\n\n  handleDeprecatedOptions(options);\n\n  return compile$1(options);\n}\n\n/**\n * Convert given HTML content to plain text string.\n *\n * @param   { string }  html           HTML content to convert.\n * @param   { Options } [options = {}] HtmlToText options.\n * @param   { any }     [metadata]     Optional metadata for HTML document, for use in formatters.\n * @returns { string }                 Plain text string.\n * @static\n *\n * @example\n * const { convert } = require('html-to-text');\n * const text = convert('<h1>Hello World</h1>', {\n *   wordwrap: 130\n * });\n * console.log(text); // HELLO WORLD\n */\nfunction convert (html, options = {}, metadata = undefined) {\n  return compile(options)(html, metadata);\n}\n\n/**\n * Map previously existing and now deprecated options to the new options layout.\n * This is a subject for cleanup in major releases.\n *\n * @param { Options } options HtmlToText options.\n */\nfunction handleDeprecatedOptions (options) {\n  if (options.tags) {\n    const tagDefinitions = Object.entries(options.tags).map(\n      ([selector, definition]) => ({ ...definition, selector: selector || '*' })\n    );\n    options.selectors.push(...tagDefinitions);\n    options.selectors = mergeDuplicatesPreferLast(options.selectors, (s => s.selector));\n  }\n\n  function set (obj, path, value) {\n    const valueKey = path.pop();\n    for (const key of path) {\n      let nested = obj[key];\n      if (!nested) {\n        nested = {};\n        obj[key] = nested;\n      }\n      obj = nested;\n    }\n    obj[valueKey] = value;\n  }\n\n  if (options['baseElement']) {\n    const baseElement = options['baseElement'];\n    set(\n      options,\n      ['baseElements', 'selectors'],\n      (Array.isArray(baseElement) ? baseElement : [baseElement])\n    );\n  }\n  if (options['returnDomByDefault'] !== undefined) {\n    set(options, ['baseElements', 'returnDomByDefault'], options['returnDomByDefault']);\n  }\n\n  for (const definition of options.selectors) {\n    if (definition.format === 'anchor' && get(definition, ['options', 'noLinkBrackets'])) {\n      set(definition, ['options', 'linkBrackets'], false);\n    }\n  }\n}\n\nexport { compile, convert, convert as htmlToText };\n", "import { ElementType } from \"domelementtype\";\nimport { Element, Text, Comment, CDATA, Document, ProcessingInstruction, } from \"./node.js\";\nexport * from \"./node.js\";\n// Default options\nconst defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nexport class DomHandler {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    constructor(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    onparserinit(parser) {\n        this.parser = parser;\n    }\n    // Resets the handler back to starting state\n    onreset() {\n        this.dom = [];\n        this.root = new Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    }\n    // Signals the handler that parsing is done\n    onend() {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    }\n    onerror(error) {\n        this.handleCallback(error);\n    }\n    onclosetag() {\n        this.lastNode = null;\n        const elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    }\n    onopentag(name, attribs) {\n        const type = this.options.xmlMode ? ElementType.Tag : undefined;\n        const element = new Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    }\n    ontext(data) {\n        const { lastNode } = this;\n        if (lastNode && lastNode.type === ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            const node = new Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    }\n    oncomment(data) {\n        if (this.lastNode && this.lastNode.type === ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        const node = new Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    }\n    oncommentend() {\n        this.lastNode = null;\n    }\n    oncdatastart() {\n        const text = new Text(\"\");\n        const node = new CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    }\n    oncdataend() {\n        this.lastNode = null;\n    }\n    onprocessinginstruction(name, data) {\n        const node = new ProcessingInstruction(name, data);\n        this.addNode(node);\n    }\n    handleCallback(error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    }\n    addNode(node) {\n        const parent = this.tagStack[this.tagStack.length - 1];\n        const previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    }\n}\nexport default DomHandler;\n", "import { createLexer } from 'leac';\nimport * as p from 'peberminta';\n\nvar ast = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst ws = `(?:[ \\\\t\\\\r\\\\n\\\\f]*)`;\nconst nl = `(?:\\\\n|\\\\r\\\\n|\\\\r|\\\\f)`;\nconst nonascii = `[^\\\\x00-\\\\x7F]`;\nconst unicode = `(?:\\\\\\\\[0-9a-f]{1,6}(?:\\\\r\\\\n|[ \\\\n\\\\r\\\\t\\\\f])?)`;\nconst escape = `(?:\\\\\\\\[^\\\\n\\\\r\\\\f0-9a-f])`;\nconst nmstart = `(?:[_a-z]|${nonascii}|${unicode}|${escape})`;\nconst nmchar = `(?:[_a-z0-9-]|${nonascii}|${unicode}|${escape})`;\nconst name = `(?:${nmchar}+)`;\nconst ident = `(?:[-]?${nmstart}${nmchar}*)`;\nconst string1 = `'([^\\\\n\\\\r\\\\f\\\\\\\\']|\\\\\\\\${nl}|${nonascii}|${unicode}|${escape})*'`;\nconst string2 = `\"([^\\\\n\\\\r\\\\f\\\\\\\\\"]|\\\\\\\\${nl}|${nonascii}|${unicode}|${escape})*\"`;\nconst lexSelector = createLexer([\n    { name: 'ws', regex: new RegExp(ws) },\n    { name: 'hash', regex: new RegExp(`#${name}`, 'i') },\n    { name: 'ident', regex: new RegExp(ident, 'i') },\n    { name: 'str1', regex: new RegExp(string1, 'i') },\n    { name: 'str2', regex: new RegExp(string2, 'i') },\n    { name: '*' },\n    { name: '.' },\n    { name: ',' },\n    { name: '[' },\n    { name: ']' },\n    { name: '=' },\n    { name: '>' },\n    { name: '|' },\n    { name: '+' },\n    { name: '~' },\n    { name: '^' },\n    { name: '$' },\n]);\nconst lexEscapedString = createLexer([\n    { name: 'unicode', regex: new RegExp(unicode, 'i') },\n    { name: 'escape', regex: new RegExp(escape, 'i') },\n    { name: 'any', regex: new RegExp('[\\\\s\\\\S]', 'i') }\n]);\nfunction sumSpec([a0, a1, a2], [b0, b1, b2]) {\n    return [a0 + b0, a1 + b1, a2 + b2];\n}\nfunction sumAllSpec(ss) {\n    return ss.reduce(sumSpec, [0, 0, 0]);\n}\nconst unicodeEscapedSequence_ = p.token((t) => t.name === 'unicode' ? String.fromCodePoint(parseInt(t.text.slice(1), 16)) : undefined);\nconst escapedSequence_ = p.token((t) => t.name === 'escape' ? t.text.slice(1) : undefined);\nconst anyChar_ = p.token((t) => t.name === 'any' ? t.text : undefined);\nconst escapedString_ = p.map(p.many(p.or(unicodeEscapedSequence_, escapedSequence_, anyChar_)), (cs) => cs.join(''));\nfunction unescape(escapedString) {\n    const lexerResult = lexEscapedString(escapedString);\n    const result = escapedString_({ tokens: lexerResult.tokens, options: undefined }, 0);\n    return result.value;\n}\nfunction literal(name) {\n    return p.token((t) => t.name === name ? true : undefined);\n}\nconst whitespace_ = p.token((t) => t.name === 'ws' ? null : undefined);\nconst optionalWhitespace_ = p.option(whitespace_, null);\nfunction optionallySpaced(parser) {\n    return p.middle(optionalWhitespace_, parser, optionalWhitespace_);\n}\nconst identifier_ = p.token((t) => t.name === 'ident' ? unescape(t.text) : undefined);\nconst hashId_ = p.token((t) => t.name === 'hash' ? unescape(t.text.slice(1)) : undefined);\nconst string_ = p.token((t) => t.name.startsWith('str') ? unescape(t.text.slice(1, -1)) : undefined);\nconst namespace_ = p.left(p.option(identifier_, ''), literal('|'));\nconst qualifiedName_ = p.eitherOr(p.ab(namespace_, identifier_, (ns, name) => ({ name: name, namespace: ns })), p.map(identifier_, (name) => ({ name: name, namespace: null })));\nconst uniSelector_ = p.eitherOr(p.ab(namespace_, literal('*'), (ns) => ({ type: 'universal', namespace: ns, specificity: [0, 0, 0] })), p.map(literal('*'), () => ({ type: 'universal', namespace: null, specificity: [0, 0, 0] })));\nconst tagSelector_ = p.map(qualifiedName_, ({ name, namespace }) => ({\n    type: 'tag',\n    name: name,\n    namespace: namespace,\n    specificity: [0, 0, 1]\n}));\nconst classSelector_ = p.ab(literal('.'), identifier_, (fullstop, name) => ({\n    type: 'class',\n    name: name,\n    specificity: [0, 1, 0]\n}));\nconst idSelector_ = p.map(hashId_, (name) => ({\n    type: 'id',\n    name: name,\n    specificity: [1, 0, 0]\n}));\nconst attrModifier_ = p.token((t) => {\n    if (t.name === 'ident') {\n        if (t.text === 'i' || t.text === 'I') {\n            return 'i';\n        }\n        if (t.text === 's' || t.text === 'S') {\n            return 's';\n        }\n    }\n    return undefined;\n});\nconst attrValue_ = p.eitherOr(p.ab(string_, p.option(p.right(optionalWhitespace_, attrModifier_), null), (v, mod) => ({ value: v, modifier: mod })), p.ab(identifier_, p.option(p.right(whitespace_, attrModifier_), null), (v, mod) => ({ value: v, modifier: mod })));\nconst attrMatcher_ = p.choice(p.map(literal('='), () => '='), p.ab(literal('~'), literal('='), () => '~='), p.ab(literal('|'), literal('='), () => '|='), p.ab(literal('^'), literal('='), () => '^='), p.ab(literal('$'), literal('='), () => '$='), p.ab(literal('*'), literal('='), () => '*='));\nconst attrPresenceSelector_ = p.abc(literal('['), optionallySpaced(qualifiedName_), literal(']'), (lbr, { name, namespace }) => ({\n    type: 'attrPresence',\n    name: name,\n    namespace: namespace,\n    specificity: [0, 1, 0]\n}));\nconst attrValueSelector_ = p.middle(literal('['), p.abc(optionallySpaced(qualifiedName_), attrMatcher_, optionallySpaced(attrValue_), ({ name, namespace }, matcher, { value, modifier }) => ({\n    type: 'attrValue',\n    name: name,\n    namespace: namespace,\n    matcher: matcher,\n    value: value,\n    modifier: modifier,\n    specificity: [0, 1, 0]\n})), literal(']'));\nconst attrSelector_ = p.eitherOr(attrPresenceSelector_, attrValueSelector_);\nconst typeSelector_ = p.eitherOr(uniSelector_, tagSelector_);\nconst subclassSelector_ = p.choice(idSelector_, classSelector_, attrSelector_);\nconst compoundSelector_ = p.map(p.eitherOr(p.flatten(typeSelector_, p.many(subclassSelector_)), p.many1(subclassSelector_)), (ss) => {\n    return {\n        type: 'compound',\n        list: ss,\n        specificity: sumAllSpec(ss.map(s => s.specificity))\n    };\n});\nconst combinator_ = p.choice(p.map(literal('>'), () => '>'), p.map(literal('+'), () => '+'), p.map(literal('~'), () => '~'), p.ab(literal('|'), literal('|'), () => '||'));\nconst combinatorSeparator_ = p.eitherOr(optionallySpaced(combinator_), p.map(whitespace_, () => ' '));\nconst complexSelector_ = p.leftAssoc2(compoundSelector_, p.map(combinatorSeparator_, (c) => (left, right) => ({\n    type: 'compound',\n    list: [...right.list, { type: 'combinator', combinator: c, left: left, specificity: left.specificity }],\n    specificity: sumSpec(left.specificity, right.specificity)\n})), compoundSelector_);\nconst listSelector_ = p.leftAssoc2(p.map(complexSelector_, (s) => ({ type: 'list', list: [s] })), p.map(optionallySpaced(literal(',')), () => (acc, next) => ({ type: 'list', list: [...acc.list, next] })), complexSelector_);\nfunction parse_(parser, str) {\n    if (!(typeof str === 'string' || str instanceof String)) {\n        throw new Error('Expected a selector string. Actual input is not a string!');\n    }\n    const lexerResult = lexSelector(str);\n    if (!lexerResult.complete) {\n        throw new Error(`The input \"${str}\" was only partially tokenized, stopped at offset ${lexerResult.offset}!\\n` +\n            prettyPrintPosition(str, lexerResult.offset));\n    }\n    const result = optionallySpaced(parser)({ tokens: lexerResult.tokens, options: undefined }, 0);\n    if (!result.matched) {\n        throw new Error(`No match for \"${str}\" input!`);\n    }\n    if (result.position < lexerResult.tokens.length) {\n        const token = lexerResult.tokens[result.position];\n        throw new Error(`The input \"${str}\" was only partially parsed, stopped at offset ${token.offset}!\\n` +\n            prettyPrintPosition(str, token.offset, token.len));\n    }\n    return result.value;\n}\nfunction prettyPrintPosition(str, offset, len = 1) {\n    return `${str.replace(/(\\t)|(\\r)|(\\n)/g, (m, t, r) => t ? '\\u2409' : r ? '\\u240d' : '\\u240a')}\\n${''.padEnd(offset)}${'^'.repeat(len)}`;\n}\nfunction parse(str) {\n    return parse_(listSelector_, str);\n}\nfunction parse1(str) {\n    return parse_(complexSelector_, str);\n}\n\nfunction serialize(selector) {\n    if (!selector.type) {\n        throw new Error('This is not an AST node.');\n    }\n    switch (selector.type) {\n        case 'universal':\n            return _serNs(selector.namespace) + '*';\n        case 'tag':\n            return _serNs(selector.namespace) + _serIdent(selector.name);\n        case 'class':\n            return '.' + _serIdent(selector.name);\n        case 'id':\n            return '#' + _serIdent(selector.name);\n        case 'attrPresence':\n            return `[${_serNs(selector.namespace)}${_serIdent(selector.name)}]`;\n        case 'attrValue':\n            return `[${_serNs(selector.namespace)}${_serIdent(selector.name)}${selector.matcher}\"${_serStr(selector.value)}\"${(selector.modifier ? selector.modifier : '')}]`;\n        case 'combinator':\n            return serialize(selector.left) + selector.combinator;\n        case 'compound':\n            return selector.list.reduce((acc, node) => {\n                if (node.type === 'combinator') {\n                    return serialize(node) + acc;\n                }\n                else {\n                    return acc + serialize(node);\n                }\n            }, '');\n        case 'list':\n            return selector.list.map(serialize).join(',');\n    }\n}\nfunction _serNs(ns) {\n    return (ns || ns === '')\n        ? _serIdent(ns) + '|'\n        : '';\n}\nfunction _codePoint(char) {\n    return `\\\\${char.codePointAt(0).toString(16)} `;\n}\nfunction _serIdent(str) {\n    return str.replace(\n    /(^[0-9])|(^-[0-9])|(^-$)|([-0-9a-zA-Z_]|[^\\x00-\\x7F])|(\\x00)|([\\x01-\\x1f]|\\x7f)|([\\s\\S])/g, (m, d1, d2, hy, safe, nl, ctrl, other) => d1 ? _codePoint(d1) :\n        d2 ? '-' + _codePoint(d2.slice(1)) :\n            hy ? '\\\\-' :\n                safe ? safe :\n                    nl ? '\\ufffd' :\n                        ctrl ? _codePoint(ctrl) :\n                            '\\\\' + other);\n}\nfunction _serStr(str) {\n    return str.replace(\n    /(\")|(\\\\)|(\\x00)|([\\x01-\\x1f]|\\x7f)/g, (m, dq, bs, nl, ctrl) => dq ? '\\\\\"' :\n        bs ? '\\\\\\\\' :\n            nl ? '\\ufffd' :\n                _codePoint(ctrl));\n}\nfunction normalize(selector) {\n    if (!selector.type) {\n        throw new Error('This is not an AST node.');\n    }\n    switch (selector.type) {\n        case 'compound': {\n            selector.list.forEach(normalize);\n            selector.list.sort((a, b) => _compareArrays(_getSelectorPriority(a), _getSelectorPriority(b)));\n            break;\n        }\n        case 'combinator': {\n            normalize(selector.left);\n            break;\n        }\n        case 'list': {\n            selector.list.forEach(normalize);\n            selector.list.sort((a, b) => (serialize(a) < serialize(b)) ? -1 : 1);\n            break;\n        }\n    }\n    return selector;\n}\nfunction _getSelectorPriority(selector) {\n    switch (selector.type) {\n        case 'universal':\n            return [1];\n        case 'tag':\n            return [1];\n        case 'id':\n            return [2];\n        case 'class':\n            return [3, selector.name];\n        case 'attrPresence':\n            return [4, serialize(selector)];\n        case 'attrValue':\n            return [5, serialize(selector)];\n        case 'combinator':\n            return [15, serialize(selector)];\n    }\n}\nfunction compareSelectors(a, b) {\n    return _compareArrays(a.specificity, b.specificity);\n}\nfunction compareSpecificity(a, b) {\n    return _compareArrays(a, b);\n}\nfunction _compareArrays(a, b) {\n    if (!Array.isArray(a) || !Array.isArray(b)) {\n        throw new Error('Arguments must be arrays.');\n    }\n    const shorter = (a.length < b.length) ? a.length : b.length;\n    for (let i = 0; i < shorter; i++) {\n        if (a[i] === b[i]) {\n            continue;\n        }\n        return (a[i] < b[i]) ? -1 : 1;\n    }\n    return a.length - b.length;\n}\n\nexport { ast as Ast, compareSelectors, compareSpecificity, normalize, parse, parse1, serialize };\n", null, "import { ElementType, isTag as isTagRaw } from \"domelementtype\";\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nexport class Node {\n    constructor() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    // Read-write aliases for properties\n    /**\n     * Same as {@link parent}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get parentNode() {\n        return this.parent;\n    }\n    set parentNode(parent) {\n        this.parent = parent;\n    }\n    /**\n     * Same as {@link prev}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get previousSibling() {\n        return this.prev;\n    }\n    set previousSibling(prev) {\n        this.prev = prev;\n    }\n    /**\n     * Same as {@link next}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nextSibling() {\n        return this.next;\n    }\n    set nextSibling(next) {\n        this.next = next;\n    }\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    cloneNode(recursive = false) {\n        return cloneNode(this, recursive);\n    }\n}\n/**\n * A node that contains some data.\n */\nexport class DataNode extends Node {\n    /**\n     * @param data The content of the data node\n     */\n    constructor(data) {\n        super();\n        this.data = data;\n    }\n    /**\n     * Same as {@link data}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nodeValue() {\n        return this.data;\n    }\n    set nodeValue(data) {\n        this.data = data;\n    }\n}\n/**\n * Text within the document.\n */\nexport class Text extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.Text;\n    }\n    get nodeType() {\n        return 3;\n    }\n}\n/**\n * Comments within the document.\n */\nexport class Comment extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.Comment;\n    }\n    get nodeType() {\n        return 8;\n    }\n}\n/**\n * Processing instructions, including doc types.\n */\nexport class ProcessingInstruction extends DataNode {\n    constructor(name, data) {\n        super(data);\n        this.name = name;\n        this.type = ElementType.Directive;\n    }\n    get nodeType() {\n        return 1;\n    }\n}\n/**\n * A `Node` that can have children.\n */\nexport class NodeWithChildren extends Node {\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    constructor(children) {\n        super();\n        this.children = children;\n    }\n    // Aliases\n    /** First child of the node. */\n    get firstChild() {\n        var _a;\n        return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n    }\n    /** Last child of the node. */\n    get lastChild() {\n        return this.children.length > 0\n            ? this.children[this.children.length - 1]\n            : null;\n    }\n    /**\n     * Same as {@link children}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get childNodes() {\n        return this.children;\n    }\n    set childNodes(children) {\n        this.children = children;\n    }\n}\nexport class CDATA extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.CDATA;\n    }\n    get nodeType() {\n        return 4;\n    }\n}\n/**\n * The root node of the document.\n */\nexport class Document extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.Root;\n    }\n    get nodeType() {\n        return 9;\n    }\n}\n/**\n * An element within the DOM.\n */\nexport class Element extends NodeWithChildren {\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    constructor(name, attribs, children = [], type = name === \"script\"\n        ? ElementType.Script\n        : name === \"style\"\n            ? ElementType.Style\n            : ElementType.Tag) {\n        super(children);\n        this.name = name;\n        this.attribs = attribs;\n        this.type = type;\n    }\n    get nodeType() {\n        return 1;\n    }\n    // DOM Level 1 aliases\n    /**\n     * Same as {@link name}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get tagName() {\n        return this.name;\n    }\n    set tagName(name) {\n        this.name = name;\n    }\n    get attributes() {\n        return Object.keys(this.attribs).map((name) => {\n            var _a, _b;\n            return ({\n                name,\n                value: this.attribs[name],\n                namespace: (_a = this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                prefix: (_b = this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n            });\n        });\n    }\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nexport function isTag(node) {\n    return isTagRaw(node);\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nexport function isCDATA(node) {\n    return node.type === ElementType.CDATA;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nexport function isText(node) {\n    return node.type === ElementType.Text;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nexport function isComment(node) {\n    return node.type === ElementType.Comment;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nexport function isDirective(node) {\n    return node.type === ElementType.Directive;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nexport function isDocument(node) {\n    return node.type === ElementType.Root;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nexport function hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nexport function cloneNode(node, recursive = false) {\n    let result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Element(node.name, { ...node.attribs }, children);\n        children.forEach((child) => (child.parent = clone));\n        if (node.namespace != null) {\n            clone.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone[\"x-attribsNamespace\"] = { ...node[\"x-attribsNamespace\"] };\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone[\"x-attribsPrefix\"] = { ...node[\"x-attribsPrefix\"] };\n        }\n        result = clone;\n    }\n    else if (isCDATA(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new CDATA(children);\n        children.forEach((child) => (child.parent = clone));\n        result = clone;\n    }\n    else if (isDocument(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Document(children);\n        children.forEach((child) => (child.parent = clone));\n        if (node[\"x-mode\"]) {\n            clone[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone;\n    }\n    else if (isDirective(node)) {\n        const instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(`Not implemented yet: ${node.type}`);\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nfunction cloneChildren(childs) {\n    const children = childs.map((child) => cloneNode(child, true));\n    for (let i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n", null, null, null, null, null, null, "/*\n * Module dependencies\n */\nimport * as ElementType from \"domelementtype\";\nimport { encodeXML, escapeAttribute, escapeText } from \"entities\";\n/**\n * Mixed-case SVG and MathML tags & attributes\n * recognized by the HTML parser.\n *\n * @see https://html.spec.whatwg.org/multipage/parsing.html#parsing-main-inforeign\n */\nimport { elementNames, attributeNames } from \"./foreignNames.js\";\nconst unencodedElements = new Set([\n    \"style\",\n    \"script\",\n    \"xmp\",\n    \"iframe\",\n    \"noembed\",\n    \"noframes\",\n    \"plaintext\",\n    \"noscript\",\n]);\nfunction replaceQuotes(value) {\n    return value.replace(/\"/g, \"&quot;\");\n}\n/**\n * Format attributes\n */\nfunction formatAttributes(attributes, opts) {\n    var _a;\n    if (!attributes)\n        return;\n    const encode = ((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) === false\n        ? replaceQuotes\n        : opts.xmlMode || opts.encodeEntities !== \"utf8\"\n            ? encodeXML\n            : escapeAttribute;\n    return Object.keys(attributes)\n        .map((key) => {\n        var _a, _b;\n        const value = (_a = attributes[key]) !== null && _a !== void 0 ? _a : \"\";\n        if (opts.xmlMode === \"foreign\") {\n            /* Fix up mixed-case attribute names */\n            key = (_b = attributeNames.get(key)) !== null && _b !== void 0 ? _b : key;\n        }\n        if (!opts.emptyAttrs && !opts.xmlMode && value === \"\") {\n            return key;\n        }\n        return `${key}=\"${encode(value)}\"`;\n    })\n        .join(\" \");\n}\n/**\n * Self-enclosing tags\n */\nconst singleTag = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\n/**\n * Renders a DOM node or an array of DOM nodes to a string.\n *\n * Can be thought of as the equivalent of the `outerHTML` of the passed node(s).\n *\n * @param node Node to be rendered.\n * @param options Changes serialization behavior\n */\nexport function render(node, options = {}) {\n    const nodes = \"length\" in node ? node : [node];\n    let output = \"\";\n    for (let i = 0; i < nodes.length; i++) {\n        output += renderNode(nodes[i], options);\n    }\n    return output;\n}\nexport default render;\nfunction renderNode(node, options) {\n    switch (node.type) {\n        case ElementType.Root:\n            return render(node.children, options);\n        // @ts-expect-error We don't use `Doctype` yet\n        case ElementType.Doctype:\n        case ElementType.Directive:\n            return renderDirective(node);\n        case ElementType.Comment:\n            return renderComment(node);\n        case ElementType.CDATA:\n            return renderCdata(node);\n        case ElementType.Script:\n        case ElementType.Style:\n        case ElementType.Tag:\n            return renderTag(node, options);\n        case ElementType.Text:\n            return renderText(node, options);\n    }\n}\nconst foreignModeIntegrationPoints = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignObject\",\n    \"desc\",\n    \"title\",\n]);\nconst foreignElements = new Set([\"svg\", \"math\"]);\nfunction renderTag(elem, opts) {\n    var _a;\n    // Handle SVG / MathML in HTML\n    if (opts.xmlMode === \"foreign\") {\n        /* Fix up mixed-case element names */\n        elem.name = (_a = elementNames.get(elem.name)) !== null && _a !== void 0 ? _a : elem.name;\n        /* Exit foreign mode at integration points */\n        if (elem.parent &&\n            foreignModeIntegrationPoints.has(elem.parent.name)) {\n            opts = { ...opts, xmlMode: false };\n        }\n    }\n    if (!opts.xmlMode && foreignElements.has(elem.name)) {\n        opts = { ...opts, xmlMode: \"foreign\" };\n    }\n    let tag = `<${elem.name}`;\n    const attribs = formatAttributes(elem.attribs, opts);\n    if (attribs) {\n        tag += ` ${attribs}`;\n    }\n    if (elem.children.length === 0 &&\n        (opts.xmlMode\n            ? // In XML mode or foreign mode, and user hasn't explicitly turned off self-closing tags\n                opts.selfClosingTags !== false\n            : // User explicitly asked for self-closing tags, even in HTML mode\n                opts.selfClosingTags && singleTag.has(elem.name))) {\n        if (!opts.xmlMode)\n            tag += \" \";\n        tag += \"/>\";\n    }\n    else {\n        tag += \">\";\n        if (elem.children.length > 0) {\n            tag += render(elem.children, opts);\n        }\n        if (opts.xmlMode || !singleTag.has(elem.name)) {\n            tag += `</${elem.name}>`;\n        }\n    }\n    return tag;\n}\nfunction renderDirective(elem) {\n    return `<${elem.data}>`;\n}\nfunction renderText(elem, opts) {\n    var _a;\n    let data = elem.data || \"\";\n    // If entities weren't decoded, no need to encode them back\n    if (((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) !== false &&\n        !(!opts.xmlMode &&\n            elem.parent &&\n            unencodedElements.has(elem.parent.name))) {\n        data =\n            opts.xmlMode || opts.encodeEntities !== \"utf8\"\n                ? encodeXML(data)\n                : escapeText(data);\n    }\n    return data;\n}\nfunction renderCdata(elem) {\n    return `<![CDATA[${elem.children[0].data}]]>`;\n}\nfunction renderComment(elem) {\n    return `<!--${elem.data}-->`;\n}\n", "var __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// src/node/render.tsx\nimport { Suspense } from \"react\";\n\n// src/shared/utils/pretty.ts\nimport * as html from \"prettier/plugins/html\";\nimport { format } from \"prettier/standalone\";\nfunction recursivelyMapDoc(doc, callback) {\n  if (Array.isArray(doc)) {\n    return doc.map((innerDoc) => recursivelyMapDoc(innerDoc, callback));\n  }\n  if (typeof doc === \"object\") {\n    if (doc.type === \"group\") {\n      return __spreadProps(__spreadValues({}, doc), {\n        contents: recursivelyMapDoc(doc.contents, callback),\n        expandedStates: recursivelyMapDoc(\n          doc.expandedStates,\n          callback\n        )\n      });\n    }\n    if (\"contents\" in doc) {\n      return __spreadProps(__spreadValues({}, doc), {\n        contents: recursivelyMapDoc(doc.contents, callback)\n      });\n    }\n    if (\"parts\" in doc) {\n      return __spreadProps(__spreadValues({}, doc), {\n        parts: recursivelyMapDoc(doc.parts, callback)\n      });\n    }\n    if (doc.type === \"if-break\") {\n      return __spreadProps(__spreadValues({}, doc), {\n        breakContents: recursivelyMapDoc(doc.breakContents, callback),\n        flatContents: recursivelyMapDoc(doc.flatContents, callback)\n      });\n    }\n  }\n  return callback(doc);\n}\nvar modifiedHtml = __spreadValues({}, html);\nif (modifiedHtml.printers) {\n  const previousPrint = modifiedHtml.printers.html.print;\n  modifiedHtml.printers.html.print = (path, options, print, args) => {\n    const node = path.getNode();\n    const rawPrintingResult = previousPrint(path, options, print, args);\n    if (node.type === \"ieConditionalComment\") {\n      const printingResult = recursivelyMapDoc(rawPrintingResult, (doc) => {\n        if (typeof doc === \"object\" && doc.type === \"line\") {\n          return doc.soft ? \"\" : \" \";\n        }\n        return doc;\n      });\n      return printingResult;\n    }\n    return rawPrintingResult;\n  };\n}\nvar defaults = {\n  endOfLine: \"lf\",\n  tabWidth: 2,\n  plugins: [modifiedHtml],\n  bracketSameLine: true,\n  parser: \"html\"\n};\nvar pretty = (str, options = {}) => {\n  return format(str.replaceAll(\"\\0\", \"\"), __spreadValues(__spreadValues({}, defaults), options));\n};\n\n// src/shared/utils/to-plain-text.ts\nimport {\n  convert\n} from \"html-to-text\";\nvar plainTextSelectors = [\n  { selector: \"img\", format: \"skip\" },\n  { selector: \"[data-skip-in-text=true]\", format: \"skip\" },\n  {\n    selector: \"a\",\n    options: { linkBrackets: false }\n  }\n];\nfunction toPlainText(html2, options) {\n  return convert(html2, __spreadValues({\n    selectors: plainTextSelectors\n  }, options));\n}\n\n// src/node/read-stream.ts\nimport { Writable } from \"node:stream\";\nvar decoder = new TextDecoder(\"utf-8\");\nvar readStream = (stream) => __async(void 0, null, function* () {\n  let result = \"\";\n  if (\"pipeTo\" in stream) {\n    const writableStream = new WritableStream({\n      write(chunk) {\n        result += decoder.decode(chunk);\n      }\n    });\n    yield stream.pipeTo(writableStream);\n  } else {\n    const writable = new Writable({\n      write(chunk, _encoding, callback) {\n        result += decoder.decode(chunk);\n        callback();\n      }\n    });\n    stream.pipe(writable);\n    yield new Promise((resolve, reject) => {\n      writable.on(\"error\", reject);\n      writable.on(\"close\", () => {\n        resolve();\n      });\n    });\n  }\n  return result;\n});\n\n// src/node/render.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar render = (node, options) => __async(void 0, null, function* () {\n  const suspendedElement = /* @__PURE__ */ jsx(Suspense, { children: node });\n  const reactDOMServer = yield import(\"react-dom/server\").then(\n    // This is beacuse react-dom/server is CJS\n    (m) => m.default\n  );\n  let html2;\n  if (Object.hasOwn(reactDOMServer, \"renderToReadableStream\")) {\n    html2 = yield readStream(\n      yield reactDOMServer.renderToReadableStream(suspendedElement, {\n        progressiveChunkSize: Number.POSITIVE_INFINITY\n      })\n    );\n  } else {\n    yield new Promise((resolve, reject) => {\n      const stream = reactDOMServer.renderToPipeableStream(suspendedElement, {\n        onAllReady() {\n          return __async(this, null, function* () {\n            html2 = yield readStream(stream);\n            resolve();\n          });\n        },\n        onError(error) {\n          reject(error);\n        },\n        progressiveChunkSize: Number.POSITIVE_INFINITY\n      });\n    });\n  }\n  if (options == null ? void 0 : options.plainText) {\n    return toPlainText(html2, options.htmlToTextOptions);\n  }\n  const doctype = '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">';\n  const document = `${doctype}${html2.replace(/<!DOCTYPE.*?>/, \"\")}`;\n  if (options == null ? void 0 : options.pretty) {\n    return pretty(document);\n  }\n  return document;\n});\n\n// src/node/index.ts\nvar renderAsync = (element, options) => {\n  return render(element, options);\n};\nexport {\n  plainTextSelectors,\n  pretty,\n  render,\n  renderAsync,\n  toPlainText\n};\n"], "names": [], "mappings": "uPAEA,IAAI,EAAoB,SAA2B,AAAlB,CAAuB,MAK/B,EAIN,EACd,CALyB,CAJ7B,CAQuB,KAHhB,AALA,CAKC,CAAC,GALc,IAKY,MAJ/B,CAAC,GAIa,OAAO,MAJV,EAUR,AAAgB,uBAFL,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAG7C,AAAgB,mBAChB,EAQG,AARY,EAQN,QAAQ,GAAK,EAnB3B,EAgBI,EAAqB,AADN,AAAkB,mBAAX,QAAyB,OAAO,GAAG,CACrB,OAAO,GAAG,CAAC,iBAAmB,MAUtE,SAAS,EAA8B,CAAK,CAAE,CAAO,EACpD,OAA0B,IAAlB,EAAQ,KAAK,EAAc,EAAQ,iBAAiB,CAAC,GAC1D,EALI,MAAM,EAKA,KALO,CAKK,AALJ,GAAO,EAAE,CAAG,CAAC,EAKD,EAAO,GACrC,CACJ,CAEA,SAAS,EAAkB,CAAM,CAAE,CAAM,CAAE,CAAO,EACjD,OAAO,EAAO,MAAM,CAAC,GAAQ,GAAG,CAAC,SAAS,CAAO,EAChD,OAAO,EAA8B,EAAS,EAC/C,EACD,CAkBA,SAAS,EAAQ,CAAM,EACtB,OAAO,OAAO,IAAI,CAAC,GAAQ,MAAM,CAAC,AAR3B,OAAO,qBAAqB,CAChC,OAAO,qBAAqB,CAAC,GAAQ,MAAM,CAAC,SAAS,CAAM,EAC5D,OAAO,OAAO,oBAAoB,CAAC,IAAI,CAAC,AAMwB,EANhB,EACjD,GACE,EAAE,CAKN,CAEA,SAAS,EAAmB,CAAM,CAAE,CAAQ,EAC3C,GAAI,CACH,OAAO,KAAY,CACpB,CAAE,MAAM,EAAG,CACV,OAAO,CACR,CACD,CA8BA,SAAS,EAAU,CAAM,CAAE,CAAM,CAAE,CAAO,EAEzC,CADA,EAAU,GAAW,EAAC,EACd,UAAU,CAAG,EAAQ,UAAU,EAAI,EAC3C,EAAQ,iBAAiB,CAAG,EAAQ,iBAAiB,EAAI,EAGzD,EAAQ,6BAA6B,CAAG,EAExC,QAAI,EAAgB,MAAM,OAAO,CAAC,UAEF,AAEhC,IAHoB,AAGhB,CAAC,KAHqB,OAAO,CAAC,AACgB,GAG1C,EAA8B,EAAQ,GACnC,EACH,EAHwB,AAGhB,UAAU,CADA,AACC,EAAQ,EAAQ,IAnCvC,EAAc,CAAC,EACf,CAFgC,EAsCA,GApCxB,EAF+B,eAEd,CAAC,IAC7B,KADsC,AACtB,GAAR,IAAe,CAAC,SAAS,CAAG,EACnC,CAAW,CAAC,EAAI,CAAG,EAA8B,CAAM,CAAC,EAAI,CAAE,EAC/D,GAED,KAAgB,GAAR,IAAe,CAAC,SAAS,CAAG,EAZ7B,QACH,CAAC,CAAC,OAAO,EADa,QAAQ,IACP,CADY,AACX,IAAI,CAAC,MAC5B,EADoC,KAAK,AAClC,oBAAoB,CAAC,IAAI,CAAC,EAWR,EAXgB,CAAI,EAAE,CAWhB,AAI/B,AAfyC,EAyC1B,EA1BY,IAAQ,EAAQ,GAhB6C,KADgB,CAiBrF,QAAyC,CA0BrC,AA1BsC,CAAM,CAAC,EAAI,EAC3E,CAAW,AADmE,CAClE,EAAI,CAAG,AAhDtB,IAgCiG,KAhCxF,CAAiB,CAAG,CAAE,CAAO,EACrC,GAAI,CAAC,EAAQ,WAAW,CACvB,CADyB,MAClB,EAER,IAAI,EAAc,EAAQ,WAAW,CAAC,GACtC,MAA8B,YAAvB,OAAO,EAA6B,EAAc,EAC1D,EA0CuC,EAAK,GAAS,CAAM,CAAC,EAAI,CAAE,CAAM,CAAC,EAAI,CAAE,GAE5E,CAAW,CAAC,EAAI,CAAG,EAA8B,CAAM,CAAC,EAAI,CAAE,GAEhE,GACO,EAsBR,CAEA,EAAU,GAAG,CAAG,SAAS,AAAa,CAAK,CAAE,CAAO,EACnD,GAAI,CAAC,MAAM,OAAO,CAAC,GAClB,KAD0B,CACpB,AAAI,MAAM,qCAGjB,OAAO,EAAM,MAAM,CAAC,SAAS,CAAI,CAAE,CAAI,EACtC,OAAO,EAAU,EAAM,EAAM,EAC9B,EAAG,CAAC,EACL,EAIA,EAAO,OAAO,CAFI,EAED,sDOlIjB,AAAC,SAAU,CAAW,EAElB,EAAY,IAAO,CAAG,IAAX,GAEX,EAAY,IAAO,CAAG,IAAX,GAEX,EAAY,SAAD,AAAa,CAAG,YAE3B,EAAY,OAAU,CAAG,CAAd,SAEX,EAAY,MAAS,CAAG,EAAb,OAEX,EAAY,KAAQ,CAAG,GAAZ,KAEX,EAAY,GAAM,CAAG,KAAV,CAEX,EAAY,KAAQ,CAAG,GAAZ,KAEX,EAAY,OAAU,CAAG,CAAd,QACf,CAAC,CAAE,KAAgB,GAAc,EAAC,CAAC,EAa5B,EAbW,EAaL,EAAO,GAAY,IAAI,CAEvB,EAAO,GAAY,IAAI,CAEvB,EAAY,GAAY,SAAS,CAEjC,EAAU,GAAY,OAAO,CAE7B,EAAS,GAAY,MAAM,CAE3B,EAAQ,GAAY,KAAK,CAEzB,EAAM,GAAY,GAAG,CAErB,EAAQ,GAAY,KAAK,CAEzB,EAAU,GAAY,OAAO,Ae7CnC,OAAM,EACT,aAAc,CAEV,IAAI,CAAC,MAAM,CAAG,KAEd,IAAI,CAAC,IAAI,CAAG,KAEZ,IAAI,CAAC,IAAI,CAAG,KAEZ,IAAI,CAAC,UAAU,CAAG,KAElB,IAAI,CAAC,QAAQ,CAAG,IACpB,CAMA,IAAI,YAAa,CACb,OAAO,IAAI,CAAC,MAAM,AACtB,CACA,IAAI,WAAW,CAAM,CAAE,CACnB,IAAI,CAAC,MAAM,CAAG,CAClB,CAKA,IAAI,iBAAkB,CAClB,OAAO,IAAI,CAAC,IAAI,AACpB,CACA,IAAI,gBAAgB,CAAI,CAAE,CACtB,IAAI,CAAC,IAAI,CAAG,CAChB,CAKA,IAAI,aAAc,CACd,OAAO,IAAI,CAAC,IAAI,AACpB,CACA,IAAI,YAAY,CAAI,CAAE,CAClB,IAAI,CAAC,IAAI,CAAG,CAChB,CAOA,UAAU,GAAY,CAAK,CAAE,CACzB,OAAO,EAAU,IAAI,CAAE,EAC3B,CACJ,CAIO,MAAM,UAAiB,EAI1B,YAAY,CAAI,CAAE,CACd,KAAK,GACL,IAAI,CAAC,IAAI,CAAG,CAChB,CAKA,IAAI,WAAY,CACZ,OAAO,IAAI,CAAC,IAAI,AACpB,CACA,IAAI,UAAU,CAAI,CAAE,CAChB,IAAI,CAAC,IAAI,CAAG,CAChB,CACJ,CAIO,MAAM,UAAa,EACtB,aAAc,CACV,KAAK,IAAI,WACT,IAAI,CAAC,IAAI,CAAG,GAAY,IAAI,AAChC,CACA,IAAI,UAAW,CACX,OAAO,CACX,CACJ,CAIO,MAAM,UAAgB,EACzB,aAAc,CACV,KAAK,IAAI,WACT,IAAI,CAAC,IAAI,CAAG,GAAY,OAAO,AACnC,CACA,IAAI,UAAW,CACX,OAAO,CACX,CACJ,CAIO,MAAM,UAA8B,EACvC,YAAY,CAAI,CAAE,CAAI,CAAE,CACpB,KAAK,CAAC,GACN,IAAI,CAAC,IAAI,CAAG,EACZ,IAAI,CAAC,IAAI,CAAG,GAAY,SAAS,AACrC,CACA,IAAI,UAAW,CACX,OAAO,CACX,CACJ,CAIO,MAAM,UAAyB,EAIlC,YAAY,CAAQ,CAAE,CAClB,KAAK,GACL,IAAI,CAAC,QAAQ,CAAG,CACpB,CAGA,IAAI,YAAa,CACb,IAAI,EACJ,OAAO,OAAC,EAAK,IAAI,CAAC,QAAQ,CAAC,EAAA,AAAE,EAA8B,EAAK,EAA7B,EACvC,CAEA,IAAI,CAH2C,OAAO,GAGtC,CACZ,CAJuD,MAIhD,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAG,EACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAG,EAAE,CACvC,IACV,CAKA,IAAI,YAAa,CACb,OAAO,IAAI,CAAC,QAAQ,AACxB,CACA,IAAI,WAAW,CAAQ,CAAE,CACrB,IAAI,CAAC,QAAQ,CAAG,CACpB,CACJ,CACO,MAAM,UAAc,EACvB,aAAc,CACV,KAAK,IAAI,WACT,IAAI,CAAC,IAAI,CAAG,GAAY,KAAK,AACjC,CACA,IAAI,UAAW,CACX,OAAO,CACX,CACJ,CAIO,MAAM,UAAiB,EAC1B,aAAc,CACV,KAAK,IAAI,WACT,IAAI,CAAC,IAAI,CAAG,GAAY,IAAI,AAChC,CACA,IAAI,UAAW,CACX,OAAO,CACX,CACJ,CAIO,MAAM,UAAgB,EAMzB,YAAY,CAAI,CAAE,CAAO,CAAE,EAAW,EAAE,CAAE,EAAgB,AAAT,aAC3C,GAAY,MAAM,CACT,UAAT,EACI,GAAY,KAAK,CACjB,GAAY,GAAG,CAAE,CACvB,KAAK,CAAC,GACN,IAAI,CAAC,IAAI,CAAG,EACZ,IAAI,CAAC,OAAO,CAAG,EACf,IAAI,CAAC,IAAI,CAAG,CAChB,CACA,IAAI,UAAW,CACX,OAAO,CACX,CAMA,IAAI,SAAU,CACV,OAAO,IAAI,CAAC,IAAI,AACpB,CACA,IAAI,QAAQ,CAAI,CAAE,CACd,IAAI,CAAC,IAAI,CAAG,CAChB,CACA,IAAI,YAAa,CACb,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,AAAC,IAClC,IAAI,EAAI,EACR,MAAQ,MACJ,EACA,MAAO,IAAI,CAAC,OAAO,CAAC,EAAK,CACzB,UAAW,MAAC,GAAK,IAAI,CAAC,qBAAA,AAAqB,EAA8B,IAAxB,CAA6B,EAAI,CAAE,CAAC,EAAK,CAAjC,AACzD,OADgE,AACxD,KAD6D,EAC5D,EAAK,IAAI,CAAC,kBAAA,AAAkB,EAA8B,IAAxB,CAA6B,EAAI,CAAE,CAAC,EAAK,AACxF,CADuD,AAE3D,EACJ,CACJ,CAKO,GATmE,KAAK,CAS/D,EAAM,CAAI,EACtB,OAAO,AfnMC,EAAK,IAAI,GAAK,GAAY,GAAG,EACjC,AekMY,EflMP,IAAI,GAAK,GAAY,MAAM,EAChC,EAAK,IAAI,GAAK,GAAY,KAAK,AekMvC,CAiDO,SAAS,EAAU,CAAI,CAAE,GAAY,CAAK,EAC7C,IAAI,EACJ,GAAW,AAtCJ,CAsCH,CAtCQ,IAAI,CAsCE,EAtCG,GAAY,IAAI,CAuCjC,EAAS,IAAI,EAAK,EAAK,IAAI,OAE1B,GAAc,AAlCZ,CAkCE,CAlCG,IAAI,CAkCU,EAlCL,GAAY,OAAO,CAmCpC,EAAS,IAAI,EAAQ,EAAK,IAAI,OAE7B,GAAI,EAAM,GAAO,CAClB,IAAM,EAAW,EAAY,EAAc,EAAK,QAAQ,EAAI,EAAE,CACxD,EAAQ,IAAI,EAAQ,EAAK,IAAI,CAAE,CAAE,GAAG,EAAK,OAAO,AAAC,EAAG,GAC1D,EAAS,OAAO,CAAC,AAAC,GAAW,EAAM,MAAM,CAAG,GACtB,MAAlB,AAAwB,EAAnB,SAAS,GACd,EAAM,SAAS,CAAG,EAAK,SAAA,AAAS,EAEhC,CAAI,CAAC,qBAAqB,EAAE,AAC5B,EAAK,CAAC,qBAAqB,CAAG,CAAE,GAAG,CAAI,CAAC,qBAAqB,CAAC,EAE9D,CAAI,CAAC,kBAAkB,EAAE,CACzB,CAAK,CAAC,kBAAkB,CAAG,CAAE,GAAG,CAAI,CAAC,kBAAkB,CAAC,EAE5D,EAAS,CACb,MACK,GAlEE,AAkEU,CAAR,CAlEG,IAAI,GAAK,GAAY,KAAK,CAkEd,CACpB,IAAM,EAAW,EAAY,EAAc,EAAK,QAAQ,EAAI,EAAE,CACxD,EAAQ,IAAI,EAAM,GACxB,EAAS,OAAO,CAAC,AAAC,GAAW,EAAM,MAAM,CAAG,GAC5C,EAAS,CACb,MACK,GA5CE,AA4Ca,CAAX,CA5CG,IAAI,GAAK,GAAY,IAAI,CA4CV,CACvB,IAAM,EAAW,EAAY,EAAc,EAAK,QAAQ,EAAI,EAAE,CACxD,EAAQ,IAAI,EAAS,GAC3B,EAAS,OAAO,CAAC,AAAC,GAAW,EAAM,MAAM,CAAG,GACxC,CAAI,CAAC,SAAS,EAAE,CAChB,CAAK,CAAC,SAAS,CAAG,CAAI,CAAC,SAAA,AAAS,EAEpC,EAAS,CACb,MACK,GAAgB,AA5Dd,CA4DE,CA5DG,IAAI,GAAK,GAAY,SAAS,CA4Dd,CACxB,IAAM,EAAc,IAAI,EAAsB,EAAK,IAAI,CAAE,EAAK,IAAI,CAC5C,MAAM,CAAxB,CAAI,CAAC,SAAS,GACd,CAAW,CAAC,SAAS,CAAG,CAAI,CAAC,SAAS,CACtC,CAAW,CAAC,aAAa,CAAG,CAAI,CAAC,aAAa,CAC9C,CAAW,CAAC,aAAa,CAAG,CAAI,CAAC,aAAa,EAElD,EAAS,CACb,MAEI,CADC,KACK,AAAI,MAAM,CAAC,qBAAqB,EAAE,EAAK,IAAI,CAAA,CAAE,EAOvD,OALA,EAAO,UAAU,CAAG,EAAK,UAAU,CACnC,EAAO,QAAQ,CAAG,EAAK,QAAQ,CAC3B,AAA2B,MAAM,EAA5B,kBAAkB,GACvB,EAAO,kBAAkB,CAAG,EAAK,kBAAA,AAAkB,EAEhD,CACX,CACA,SAAS,EAAc,CAAM,EACzB,IAAM,EAAW,EAAO,GAAG,CAAC,AAAC,GAAU,EAAU,GAAO,IACxD,IAAK,IAAI,EAAI,EAAG,EAAI,EAAS,MAAM,CAAE,IAAK,AACtC,CAAQ,CAAC,EAAE,CAAC,IAAI,CAAG,CAAQ,CAAC,EAAI,EAAE,CAClC,CAAQ,CAAC,EAAI,EAAE,CAAC,IAAI,CAAG,CAAQ,CAAC,EAAE,CAEtC,OAAO,CACX,CH7UA,IAAM,EAAc,CAChB,kBAAkB,EAClB,gBAAgB,EAChB,SAAS,CACb,CACO,OAAM,EAMT,YAAY,CAAQ,CAAE,CAAO,CAAE,CAAS,CAAE,CAEtC,IAAI,CAAC,GAAG,CAAG,EAAE,CAEb,IAAI,CAAC,IAAI,CAAG,IAAI,EAAS,IAAI,CAAC,GAAG,EAEjC,IAAI,CAAC,IAAI,CAAG,GAEZ,IAAI,CAAC,QAAQ,CAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAE3B,IAAI,CAAC,QAAQ,CAAG,KAEhB,IAAI,CAAC,MAAM,CAAG,KAES,YAAnB,AAA+B,OAAxB,IACP,EAAY,EACZ,EAAU,GAEU,UAApB,AAA8B,OAAvB,IACP,EAAU,EACV,OAAW,GAEf,IAAI,CAAC,QAAQ,OAAG,EAA2C,EAAW,KACtE,IAD6B,AACzB,CAAC,OADgC,AACzB,CAAG,QAAyC,EAAU,EAClE,AAFkD,AACvB,IACvB,CAAC,AAFkD,GACpB,MACrB,MADiC,CAC9B,EAA6C,EADV,AACsB,IAC9E,CACA,KAFmC,QAAQ,AAE9B,CAAM,CAAE,CACjB,IAAI,CAAC,MAHgD,AAG1C,CAAG,CAClB,CAEA,EAN8D,OAMpD,CACN,IAAI,CAAC,GAAG,CAAG,EAAE,CACb,IAAI,CAAC,IAAI,CAAG,IAAI,EAAS,IAAI,CAAC,GAAG,EACjC,IAAI,CAAC,IAAI,EAAG,EACZ,IAAI,CAAC,QAAQ,CAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAC3B,IAAI,CAAC,QAAQ,CAAG,KAChB,IAAI,CAAC,MAAM,CAAG,IAClB,CAEA,OAAQ,CACA,IAAI,CAAC,IAAI,EACT,CACJ,IAAI,CAAC,IAAI,EAAG,EACZ,IAAI,CAAC,MAAM,CAAG,KACd,IAAI,CAAC,cAAc,CAAC,MACxB,CACA,QAAQ,CAAK,CAAE,CACX,IAAI,CAAC,cAAc,CAAC,EACxB,CACA,YAAa,CACT,IAAI,CAAC,QAAQ,CAAG,KAChB,IAAM,EAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,GAC1B,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAC7B,EAAK,QAAQ,CAAG,IAAI,CAAC,MAAM,CAAC,QAAA,AAAQ,EAEpC,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,CAAC,EACvB,CACA,UAAU,CAAI,CAAE,CAAO,CAAE,CAErB,IAAM,EAAU,IAAI,EAAQ,EAAM,OAAS,EAD9B,IAAI,CAAC,IACoC,GAD7B,CAAC,OAAO,CAAG,GAAY,GAAG,MAAG,GAEtD,IAAI,CAAC,OAAO,CAAC,GACb,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EACvB,CACA,OAAO,CAAI,CAAE,CACT,GAAM,UAAE,CAAQ,CAAE,CAAG,IAAI,CACzB,GAAI,GAAY,EAAS,IAAI,GAAK,GAAY,IAAI,CAC9C,CADgD,CACvC,IAAI,EAAI,EACb,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAC7B,EAAS,QAAQ,CAAG,IAAI,CAAC,MAAM,CAAC,QAAA,AAAQ,MAG3C,CACD,IAAM,EAAO,IAAI,EAAK,GACtB,IAAI,CAAC,OAAO,CAAC,GACb,IAAI,CAAC,QAAQ,CAAG,CACpB,CACJ,CACA,UAAU,CAAI,CAAE,CACZ,GAAI,IAAI,CAAC,QAAQ,EAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAK,GAAY,OAAO,CAAE,CAC7D,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAI,EACtB,MACJ,CACA,IAAM,EAAO,IAAI,EAAQ,GACzB,IAAI,CAAC,OAAO,CAAC,GACb,IAAI,CAAC,QAAQ,CAAG,CACpB,CACA,cAAe,CACX,IAAI,CAAC,QAAQ,CAAG,IACpB,CACA,cAAe,CACX,IAAM,EAAO,IAAI,EAAK,IAChB,EAAO,IAAI,EAAM,CAAC,EAAK,EAC7B,IAAI,CAAC,OAAO,CAAC,GACb,EAAK,MAAM,CAAG,EACd,IAAI,CAAC,QAAQ,CAAG,CACpB,CACA,YAAa,CACT,IAAI,CAAC,QAAQ,CAAG,IACpB,CACA,wBAAwB,CAAI,CAAE,CAAI,CAAE,CAChC,IAAM,EAAO,IAAI,EAAsB,EAAM,GAC7C,IAAI,CAAC,OAAO,CAAC,EACjB,CACA,eAAe,CAAK,CAAE,CAClB,GAA6B,YAAzB,AAAqC,OAA9B,IAAI,CAAC,QAAQ,CACpB,IAAI,CAAC,QAAQ,CAAC,EAAO,IAAI,CAAC,GAAG,OAE5B,GAAI,EACL,KADY,CACN,CAEd,CACA,QAAQ,CAAI,CAAE,CACV,IAAM,EAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAG,EAAE,CAChD,EAAkB,EAAO,QAAQ,CAAC,EAAO,QAAQ,CAAC,MAAM,CAAG,EAAE,CAC/D,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAC/B,EAAK,UAAU,CAAG,IAAI,CAAC,MAAM,CAAC,UAAA,AAAU,EAExC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAC7B,EAAK,QAAQ,CAAG,IAAI,CAAC,MAAM,CAAC,QAAA,AAAQ,EAExC,EAAO,QAAQ,CAAC,IAAI,CAAC,GACjB,IACA,EAAK,IAAI,CAAG,EACZ,EAAgB,EAFC,EAEG,CAAG,GAE3B,EAAK,MAAM,CAAG,EACd,IAAI,CAAC,QAAQ,CAAG,IACpB,CACJ,ClBhJA,IAAM,EAAE,MAA4a,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,IAA+B,EAAzB,AAA2B,UAAU,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAhE,UAAU,OAAO,EAAE,GAAE,EAA6C,WAAW,CAAC,OAAO,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAM,EAAE,EAAE,AAArjB,SAAS,AAAE,CAAC,EAAE,IAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAE,GAAG,EAAE,KAAK,EAAE,GAAI,EAAE,OAAO,CAAC,CAAC,GAAG,IAAM,EAAE,AAAiC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,IAAM,EAAE,KAAK,IAAI,CAAC,CAAC,GAAE,CAAC,CAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,GAAG,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAA9K,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,GAAG,CAAuJ,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,AAAmB,OAAO,EAAjB,EAAC,KAAyB,CAAC,cAAc,CAAC,IAAI,CAAa,AAAZ,EAAE,SAAa,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,GAAE,EAArT,EAAE,EAAE,EAAmc,GAAG,IAAI,CAAC,CAAC,KAAK,EAAE,OAAO,EAAC,CAAC,CAAM,EAAE,EAAQ,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,IAAM,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,EAAE,IAAM,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,IAAM,EAAE,EAAE,GAAG,EAAE,UAAU,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,AAAQ,SAAS,AAAE,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,AAAI,MAAM,CAAC,MAAM,EAAE,EAAE,sCAAsC,CAAC,EAAE,GAAsB,CAAnB,MAA0B,GAAjB,EAAC,IAAyB,CAAC,cAAc,CAAC,IAAI,CAAC,AAAY,EAAV,GAAa,MAAO,KAAS,EAAiL,AAAhL,EAAkL,MAAhL,GAAG,EAAE,MAAM,CAAC,MAAM,AAAI,MAAM,CAAC,oBAAoB,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,gDAAgD,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,EAAE,AAAI,OAAO,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,IAAa,CAAE,GAAsB,CAAnB,MAA0B,GAAjB,EAAC,IAAyB,CAAC,cAAc,CAAC,IAAI,CAAW,AAAV,EAAE,OAAW,CAAC,GAAG,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM,AAAI,MAAM,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,kDAAkD,CAAC,EAAE,OAAO,AAAI,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,OAAW,AAAJ,OAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAA5nB,EAAE,EAAE,CAAC,CAAwnB,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,gCAAgC,OAAO,CcqC90D,SAAS,EACT,CAAO,CACP,CAAK,EACD,MAAO,CAAC,EAAM,KACV,IACI,EADA,EAAW,EAWf,EAVY,KACR,EAAI,EAAK,MAAM,CAAC,MAAM,CAElB,CAFoB,IAEV,KADd,EAAQ,EAAQ,EAAK,AACI,MADE,CAAC,EAAE,CAAE,EAAM,EAAA,GAElC,IAIJ,IAAQ,EAAM,QAEA,IAAV,EACF,CAAE,SAAS,CAAM,EACjB,CACE,SAAS,EACT,SAAU,EACV,MAAO,CACX,CACR,CACJ,CAoBA,SAAS,EAAS,CAAC,CAAE,CAAC,EAClB,OAAQ,EAAE,OAAO,CAAK,CAClB,SAAS,EACT,SAAU,EAAE,QAAQ,CACpB,MAAO,EAAE,EAAE,KAAK,CAAE,EAAE,QAAQ,CAChC,EAAK,CACT,CACA,SAAS,EAAS,CAAC,CAAE,CAAC,EAClB,OAAQ,EAAE,OAAO,CAAI,EAAE,GAAK,CAChC,CACA,SAAS,EAAI,CAAC,CAAE,CAAM,EAClB,MAAO,CAAC,EAAM,IAAM,EAAS,EAAE,EAAM,GAAI,CAAC,EAAG,IAAM,EAAO,EAAG,EAAM,EAAG,GAC1E,CAYA,SAAS,EAAO,CAAC,CAAE,CAAG,EAClB,MAAO,CAAC,EAAM,KACV,IAAM,EAAI,EAAE,EAAM,GAClB,OAAO,EAAG,OAAO,CACX,EACA,CACE,QAAS,GACT,SAAU,EACV,MAAO,CACX,CACR,CACJ,CAaA,SAAS,EAAO,GAAG,CAAE,EACjB,MAAO,CAAC,EAAM,KACV,IAAK,IAAM,KAAK,EAAI,CAChB,IAAM,EAAS,EAAE,EAAM,GACvB,GAAI,EAAO,OAAO,CACd,CADgB,MACT,CAEf,CACA,MAAO,CAAE,SAAS,CAAM,CAC5B,CACJ,CACA,SAAS,EAAU,CAAE,CAAE,CAAE,EACrB,MAAO,CAAC,EAAM,KACV,IAAM,EAAK,EAAG,EAAM,GACpB,OAAO,EAAI,OAAO,CACZ,EACA,EAAG,EAAM,EACnB,CACJ,CA6CA,SAAS,EAAK,CAAC,QACX,OAhCJ,AAgCW,EAAa,EAhCpB,GAgC0B,EA/BnB,CAAC,EAAM,KACV,IAAM,EAAS,EAAE,CACb,GAAU,EACd,EAAG,CACC,IAAM,EAAI,AA2BD,EA3BG,EAAM,GACd,EAAE,OAAO,EAAI,EAAK,EAAE,KAAK,CAAE,EAAO,MAAM,CAAG,EAAG,EAAM,EAAG,EAAE,QAAQ,GAAG,AACpE,EAAO,IAAI,CAAC,EAAE,KAAK,EACnB,EAAI,EAAE,QAAQ,EAGd,GAAU,CAElB,OAAS,EAAS,AAClB,MAAO,CACH,SAAS,EACT,SAAU,EACV,MAAO,CACX,CACJ,CAcJ,CAIA,SAAS,EAAG,CAAE,CAAE,CAAE,CAAE,CAAI,EACpB,MAAO,CAAC,EAAM,IAAM,EAAS,EAAG,EAAM,GAAI,AAAC,GAAO,EAAS,EAAG,EAAM,EAAG,QAAQ,EAAG,CAAC,EAAI,IAAM,EAAK,EAAG,KAAK,CAAE,EAAI,EAAM,EAAG,IAC7H,CAIA,SAAS,EAAM,CAAE,CAAE,CAAE,EACjB,OAAO,EAAG,EAAI,EAAI,CAAC,EAAI,IAAO,EAClC,CACA,SAAS,EAAI,CAAE,CAAE,CAAE,CAAE,CAAE,CAAE,CAAI,EACzB,MAAO,CAAC,EAAM,IAAM,EAAS,EAAG,EAAM,GAAI,AAAC,GAAO,EAAS,EAAG,EAAM,EAAG,QAAQ,EAAI,AAAD,GAAQ,EAAS,EAAG,EAAM,EAAG,QAAQ,EAAG,CAAC,EAAI,IAAM,EAAK,EAAG,KAAK,CAAE,EAAG,KAAK,CAAE,EAAI,EAAM,EAAG,KAC/K,CACA,SAAS,EAAO,CAAE,CAAE,CAAE,CAAE,CAAE,EACtB,OAAO,EAAI,EAAI,EAAI,EAAI,CAAC,EAAI,IAAO,EACvC,CA0EA,SAAS,EAAW,CAAK,CAAE,CAAK,CAAE,CAAM,MAczB,CAAC,GAbZ,OAAO,EAAM,EAcjB,CAAC,CAduB,AAAC,KAAO,GAfP,CAAC,GAtB1B,CAAC,UAqC8C,EAAG,EAAO,EAAQ,CAAC,EAAG,IAAM,CAAC,EAAG,EAAE,EAdjF,EAcoF,CAAC,EAAK,CAAC,CAdpF,CAcuF,EAAE,GAAK,EAAE,EAAK,KAbhF,AAAC,GAAQ,EAAI,EAAG,CAAC,EAAG,EAAM,EAAG,IAAM,EAAQ,EAAK,EAAG,EAAM,EAAG,IAvB7E,CAAC,EAAM,KACV,IAAI,GAAO,EACP,EAkC+B,EAjC/B,EAAM,CADC,CAEX,EAAG,CACC,IAAM,EAAI,EAAE,EAAM,EAAM,GAAK,EAAM,GAC/B,EAAE,OAAO,EAAE,AACX,EAAO,EAAE,KAAK,CACd,EAAM,EAAE,QAAQ,EAGhB,GAAO,CAEf,OAAS,EAAM,AACf,MAAO,CACH,SAAS,EACT,SAAU,EACV,MAAO,CACX,CACJ,GAgCO,CAAC,EAAM,IAAM,EAAS,EAAE,EAAM,GAAI,AAAC,GAAO,EAAE,EAAG,KAAK,CAAE,EAAM,EAAG,EAAG,QAAQ,EAAE,EAAM,EAAG,QAAQ,EAdxG,CKvRA,IAAM,EAAK,CAAC,sBAAsB,CAAC,CAC7B,EAAW,CAAC,cAAc,CAAC,CAC3B,EAAU,CAAC,gDAAgD,CAAC,CAC5D,EAAS,CAAC,0BAA0B,CAAC,CACrC,EAAU,CAAC,UAAU,EAAE,EAAS,CAAC,EAAE,EAAQ,CAAC,EAAE,EAAO,CAAC,CAAC,CACvD,EAAS,CAAC,cAAc,EAAE,EAAS,CAAC,EAAE,EAAQ,CAAC,EAAE,EAAO,CAAC,CAAC,CAC1D,EAAO,CAAC,GAAG,EAAE,EAAO,EAAE,CAAC,CACvB,EAAQ,CAAC,OAAO,EAAE,EAAA,EAAU,EAAO,EAAE,CAAC,CACtC,EAAU,CAAC,wBAAwB,EAAE,EAAG,CAAC,EAAE,EAAS,CAAC,EAAE,EAAQ,CAAC,EAAE,EAAO,GAAG,CAAC,CAC7E,EAAU,CAAC,wBAAwB,EAAE,EAAG,CAAC,EAAE,EAAS,CAAC,EAAE,EAAQ,CAAC,EAAE,EAAO,GAAG,CAAC,CAC7E,EAAc,EAAY,CAC5B,CAAE,KAAM,KAAM,MAAO,AAAI,OAAO,AAZzB,CAAC,oBAAoB,CAAC,CAYO,EACpC,CAAE,KAAM,OAAQ,MAAO,AAAI,OAAO,CAAC,CAAC,EAAE,EAAA,CAAM,CAAE,IAAK,EACnD,CAAE,KAAM,QAAS,MAAO,AAAI,OAAO,EAAO,IAAK,EAC/C,CAAE,KAAM,OAAQ,MAAO,AAAI,OAAO,EAAS,IAAK,EAChD,CAAE,KAAM,OAAQ,MAAO,AAAI,OAAO,EAAS,IAAK,EAChD,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACZ,CAAE,KAAM,GAAI,EACf,EACK,EAAmB,EAAY,CACjC,CAAE,KAAM,UAAW,MAAO,AAAI,OAAO,EAAS,IAAK,EACnD,CAAE,KAAM,SAAU,MAAO,AAAI,OAAO,EAAQ,IAAK,EACjD,CAAE,KAAM,MAAO,MAAO,AAAI,OAAO,WAAY,IAAK,EACrD,EACD,SAAS,EAAQ,CAAC,EAAI,EAAI,EAAG,CAAE,CAAC,EAAI,EAAI,EAAG,EACvC,MAAO,CAAC,EAAK,EAAI,EAAK,EAAI,EAAK,EAAG,AACtC,CAIA,IAAM,GAA0B,EAAQ,AAAC,GAAiB,YAAX,EAAE,IAAI,CAAiB,OAAO,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,GAAI,UAAO,GAGtH,GAAiB,EAAM,EAAO,EAAK,GAFhB,EAAQ,AAAC,GAAiB,WAAX,EAAE,IAEwB,AAFpB,CAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,IAES,IAFJ,GAC/D,EAAQ,AAAC,GAAiB,QAAX,EAAE,IAAI,CAAa,EAAE,IAAI,MAAG,KACoC,AAAC,GAAO,EAAG,IAAI,CAAC,KAChH,SAAS,GAAS,CAAa,EAG3B,OADe,AACR,GADuB,CAAE,OADZ,AACoB,EADH,GACe,MAAM,CAAE,aAAS,CAAU,EAAG,GACpE,KAAK,AACvB,CACA,SAAS,GAAQ,CAAI,EACjB,OAAO,EAAQ,AAAC,GAAM,EAAE,IAAI,GAAK,OAAO,CAAO,EACnD,CACA,IAAM,GAAc,EAAQ,AAAC,GAAiB,OAAX,EAAE,IAAI,CAAY,UAAO,GACtD,GAAsB,EAAS,GAAa,MAClD,SAAS,GAAiB,CAAM,EAC5B,OAAO,EAAS,GAAqB,EAAQ,GACjD,CACA,IAAM,GAAc,EAAQ,AAAC,GAAiB,UAAX,EAAE,IAAI,CAAe,GAAS,EAAE,IAAI,OAAI,GACrE,GAAU,EAAQ,AAAC,GAAiB,SAAX,EAAE,IAAI,CAAc,GAAS,EAAE,IAAI,CAAC,KAAK,CAAC,SAAM,GACzE,GAAU,EAAQ,AAAC,GAAM,EAAE,IAAI,CAAC,UAAU,CAAC,OAAS,GAAS,EAAE,IAAI,CAAC,KAAK,CAAC,EAAG,CAAC,IAAM,QACpF,GLqIK,EKrIe,CLqIZ,CKrIqB,GLqIjB,AKrI8B,GAA7B,CAAkC,GAAQ,KLqIvC,AAAC,GAAO,GKpIxB,GAAiB,EAAW,EAAK,GAAY,GAAa,CAAC,EAAI,IAAU,EAAE,CAAH,IAAS,EAAM,UAAW,EAAG,CAAC,EAAI,EAAM,GAAa,AAAC,IAAU,CAAE,EAAH,GAAS,EAAM,UAAW,KAAK,CAAC,GACvK,GAAe,EAAW,EAAK,GAAY,GAAQ,KAAM,AAAC,IAAQ,CAAD,AAAG,KAAM,YAAa,UAAW,EAAI,YAAa,CAAC,EAAG,EAAG,EAAE,CAAC,CAAC,EAAI,EAAM,GAAQ,KAAM,IAAM,CAAC,CAAE,KAAM,YAAa,UAAW,KAAM,YAAa,CAAC,EAAG,EAAG,EAAE,CAAC,CAAC,GAC3N,GAAe,EAAM,GAAgB,CAAC,MAAE,CAAI,CAAE,WAAS,CAAE,GAAK,CAAC,CACjE,KAAM,MACN,KAAM,EACN,UAAW,EACX,YAAa,CAAC,EAAG,EAAG,EAAE,CAC1B,CAAC,EACK,GAAiB,EAAK,GAAQ,KAAM,GAAa,CAAC,EAAU,KAAU,CACxE,CADuE,IACjE,QACN,KAAM,EACN,YAAa,CAAC,EAAG,EAAG,EAAE,CAC1B,CAAC,EACK,GAAc,EAAM,GAAS,AAAC,IAAU,CAC1C,EADyC,GACnC,KACN,KAAM,EACN,YAAa,CAAC,EAAG,EAAG,EAAE,CAC1B,CAAC,EACK,GAAgB,EAAQ,AAAC,IAC3B,GAAe,UAAX,EAAE,IAAI,CAAc,CACpB,GAAe,MAAX,EAAE,IAAI,EAAuB,KAAK,CAAhB,EAAE,IAAI,CACxB,MAAO,IAEX,GAAe,MAAX,EAAE,IAAI,EAAuB,KAAK,CAAhB,EAAE,IAAI,CACxB,MAAO,GAEf,CAEJ,GACM,GAAa,EAAW,EAAK,GAAS,EAAS,EAAQ,GAAqB,IAAgB,MAAO,CAAC,EAAG,KAAS,CAAD,AAAG,MAAO,EAAG,SAAU,EAAI,CAAC,EAAI,EAAK,GAAa,EAAS,EAAQ,GAAa,IAAgB,MAAO,CAAC,EAAG,KAAS,CAAD,AAAG,MAAO,EAAG,SAAU,EAAI,CAAC,GAC9P,GAAe,EAAS,EAAM,GAAQ,KAAM,IAAM,KAAM,EAAK,GAAQ,KAAM,GAAQ,KAAM,IAAM,MAAO,EAAK,GAAQ,KAAM,GAAQ,KAAM,IAAM,MAAO,EAAK,GAAQ,KAAM,GAAQ,KAAM,IAAM,MAAO,EAAK,GAAQ,KAAM,GAAQ,KAAM,IAAM,MAAO,EAAK,GAAQ,KAAM,GAAQ,KAAM,IAAM,OAgBvR,GAAgB,EAfQ,EAAM,GAAQ,GAeX,EAfiB,GAAiB,IAAiB,GAAQ,KAAM,CAAC,EAAK,GAehD,GAfkD,CAAI,WAAE,CAAS,CAAE,GAAK,CAAC,CAC7H,KAAM,eACN,KAAM,EACN,UAAW,EACX,YAAa,CAAC,EAAG,EAAG,EAAE,CAC1B,CAAC,EAC0B,EAAS,GAAQ,KAAM,EAAM,GAAiB,IAAiB,GAAc,GAAiB,IAAa,CAAC,MAAE,CAAI,WAAE,CAAS,CAAE,CAAE,EAAS,OAAE,CAAK,UAAE,CAAQ,CAAE,GAAK,CAAC,CAC1L,KAAM,YACN,KAAM,EACN,UAAW,EACX,QAAS,EACT,MAAO,EACP,SAAU,EACV,YAAa,CAAC,EAAG,EAAG,EAAE,CAC1B,CAAC,EAAI,GAAQ,OAEP,GAAgB,EAAW,GAAc,IACzC,GAAoB,EAAS,GAAa,GAAgB,IAC1D,GAAoB,EAAM,ELsHhC,AKtH2C,SLsHlC,AAAQ,GAAG,CAAE,EAClB,OAAO,AAGA,EA5BX,AAyBoB,EAGL,OA5BN,AAAI,GAAG,CAAE,EACd,MAAO,CAAC,EAAM,KACV,IAAM,EAAS,EAAE,CACb,EAAW,EACf,IAAK,IAAM,KAAK,EAAI,CAChB,IAAM,EAAK,EAAE,EAAM,GACnB,IAAI,EAAG,OAAO,CAKV,CALY,KAKL,CAAE,SAAS,CAAM,EAJxB,EAAO,IAAI,CAAC,EAAG,KAAK,EACpB,EAAW,EAAG,QAKtB,AAL8B,CAM9B,MAAO,CACH,SAAS,EACT,SAAU,EACV,MAAO,CACX,CACJ,CACJ,KAK2B,GAGT,AAAC,GAAO,EAAG,OAAO,CAAC,AAAC,GAAM,GAF5C,EKxHqD,GAAe,EAAO,KL4E3E,AK5EgG,SL4EvF,AAAM,CAAC,EACZ,OAAO,EAAG,EAAG,EAAK,GAAI,CAAC,EAAM,IAAS,CAAC,KAAS,EAAK,CACzD,EK9EwG,KAAqB,AAAC,GACnH,EACH,KAAM,WACN,KAAM,EACN,YAAwB,AA5ErB,CA4EU,CAAc,GAAG,CAAC,GAAK,EAAE,WAAW,EA5E3C,MAAM,CAAC,EAAS,CAAC,EAAG,EAAG,EAAE,EA6EnC,GAGE,GAAuB,EAAW,GADpB,EAAS,EAAM,GAAQ,KAAM,EACQ,EADF,KAAM,EAAM,GAAQ,KAAM,IAAM,KAAM,EAAM,GAAQ,KAAM,IAAM,KAAM,EAAK,GAAQ,KAAM,GAAQ,KAAM,IAAM,QAC7F,EAAM,GAAa,IAAM,MAC1F,GAAmB,EAAa,GAAmB,EAAM,GAAsB,AAAC,GAAM,CAAC,EAAM,KAAW,CAC1G,GADyG,EACnG,WACN,KAAM,IAAI,EAAM,IAAI,CAAE,CAAE,KAAM,aAAc,WAAY,EAAG,KAAM,EAAM,YAAa,EAAK,WAAW,AAAC,EAAE,CACvG,YAAa,EAAQ,EAAK,WAAW,CAAE,EAAM,WAAW,EAC5D,CAAC,EAAI,IAsBL,SAAS,GAAoB,CAAG,CAAE,CAAM,CAAE,EAAM,CAAC,EAC7C,MAAO,CAAA,EAAG,EAAI,OAAO,CAAC,kBAAmB,CAAC,EAAG,EAAG,IAAM,EAAI,IAAW,EAAI,IAAW,UAAU;AAAE,EAAE,GAAG,MAAM,CAAC,GAAA,EAAU,IAAI,MAAM,CAAC,GAAA,CAAM,AAC3I,CAQA,SAAS,GAAU,CAAQ,EACvB,GAAI,CAAC,EAAS,IAAI,CACd,CADgB,KACV,AAAI,MAAM,4BAEpB,OAAQ,EAAS,IAAI,EACjB,IAAK,YACD,OAAO,GAAO,EAAS,SAAS,EAAI,GACxC,KAAK,MACD,OAAO,GAAO,EAAS,SAAS,EAAI,GAAU,EAAS,IAAI,CAC/D,KAAK,QACD,MAAO,IAAM,GAAU,EAAS,IAAI,CACxC,KAAK,KACD,MAAO,IAAM,GAAU,EAAS,IAAI,CACxC,KAAK,eACD,MAAO,CAAC,CAAC,EAAE,GAAO,EAAS,SAAS,EAAA,EAAI,GAAU,EAAS,IAAI,EAAE,CAAC,CAAC,AACvE,KAAK,YACD,MAAO,CAAC,CAAC,EAAE,GAAO,EAAS,SAAS,EAAA,EAAI,GAAU,EAAS,IAAI,EAAA,EAAI,EAAS,OAAO,CAAC,CAAC,EAAE,AAAQ,AAmChG,EAnCyG,KAAK,CAmC1G,OAAO,CAClB,sCAAuC,CAAC,EAAG,EAAI,EAAI,EAAI,IAAS,EAAK,MACjE,EAAK,OACD,EAAK,IACD,GAAW,IAvCgG,CAAC,EAAG,EAAS,QAAQ,CAAG,EAAS,QAAQ,CAAG,GAAI,CAAC,CACpK,AADqK,KAChK,aACD,OAAO,GAAU,EAAS,IAAI,EAAI,EAAS,UAAU,AACzD,KAAK,WACD,OAAO,EAAS,IAAI,CAAC,MAAM,CAAC,CAAC,EAAK,IACZ,AAAlB,cAAgC,CAA5B,EAAK,IAAI,CACF,GAAU,GAAQ,EAGlB,EAAM,GAAU,GAE5B,GACP,KAAK,OACD,OAAO,EAAS,IAAI,CAAC,GAAG,CAAC,IAAW,IAAI,CAAC,IACjD,CACJ,CACA,SAAS,GAAO,CAAE,EACd,OAAQ,GAAa,KAAP,EACR,GAAU,GAAM,IAChB,EACV,CACA,SAAS,GAAW,CAAI,EACpB,MAAO,CAAC,EAAE,EAAE,EAAK,WAAW,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,AACnD,CACA,SAAS,GAAU,CAAG,EAClB,OAAO,EAAI,OAAO,CAClB,4FAA6F,CAAC,EAAG,EAAI,EAAI,EAAI,EAAM,EAAI,EAAM,IAAU,EAAK,GAAW,GACnJ,EAAK,IAAM,GAAW,EAAG,KAAK,CAAC,IAC3B,EAAK,MACD,IACI,EAAK,CADF,GAEC,EAAO,GAAW,GACd,KAAO,CAAA,EACnC,CA8BA,SAAS,GAAqB,CAAQ,EAClC,OAAQ,EAAS,IAAI,EACjB,IAAK,YAEL,IAAK,MADD,MAAO,CAAC,EAAE,AAGd,KAAK,KACD,MAAO,CAAC,EAAE,AACd,KAAK,QACD,MAAO,CAAC,EAAG,EAAS,IAAI,CAAC,AAC7B,KAAK,eACD,MAAO,CAAC,EAAG,GAAU,GAAU,AACnC,KAAK,YACD,MAAO,CAAC,EAAG,GAAU,GAAU,AACnC,KAAK,aACD,MAAO,CAAC,GAAI,GAAU,GAAU,AACxC,CACJ,CA/HsB,EAAa,EAAM,GAAkB,AAAC,IAAM,AAAC,CAAE,KAAM,OAAQ,KAAM,CAAC,EAAE,CAAC,CAAC,EAAI,EAAM,GAAiB,GAAQ,MAAO,IAAM,CAAC,EAAK,KAAU,CAAE,CAAH,IAAS,OAAQ,KAAM,IAAI,EAAI,IAAI,CAAE,EAAK,CAAC,CAAC,EAAI,IAsI7M,SAAS,GAAe,CAAC,CAAE,CAAC,EACxB,GAAI,CAAC,MAAM,OAAO,CAAC,IAAM,CAAC,MAAM,OAAO,CAAC,GACpC,CADwC,KAClC,AAAI,MAAM,6BAEpB,IAAM,EAAW,EAAE,MAAM,CAAG,EAAE,MAAM,CAAI,EAAE,MAAM,CAAG,EAAE,MAAM,CAC3D,IAAK,IAAI,EAAI,EAAG,EAAI,EAAS,IAAK,AAC9B,GAAI,CAAC,CAAC,EAAE,GAAK,CAAC,CAAC,EAAE,CAGjB,CAHmB,MAGX,CAAC,CAAC,EAAE,CAAG,CAAC,CAAC,EAAE,CAAI,CAAC,EAAI,EAEhC,OAAO,EAAE,MAAM,CAAG,EAAE,MAAM,AAC9B,CJ/NA,MAAM,GACF,YAAY,CAAK,CAAE,CACf,IAAI,CAAC,QAAQ,CAAG,GAAM,AAM9B,SAAS,AAAmB,CAAK,EAC7B,IAAM,EAAM,EAAM,MAAM,CAClB,EAAU,AAAI,MAAM,GAC1B,IAAK,IAAI,EAAI,EAAG,EAAI,EAAK,IAAK,KAad,EAZZ,CAYe,EAZT,CAAC,EAAgB,EAAI,CAAG,CAAK,CAAC,EAAE,CAChC,GAYV,AAIJ,GAhBoB,MAgBX,EAAuB,CAAG,EAC/B,IAAM,EAAU,EAAE,CAClB,AANuB,EAMnB,IAAI,CAAC,OAAO,CAAC,IACb,OAAQ,EAAI,IAAI,EACZ,IAAK,QACD,EAAQ,IAAI,CAAC,CACT,QAAS,KACT,SAAU,KACV,KAAM,QACN,UAAW,KACX,YAAa,EAAI,WAAW,CAC5B,KAAM,YACN,MAAO,EAAI,IAAI,AACnB,GACA,KACJ,KAAK,KACD,EAAQ,IAAI,CAAC,CACT,QAAS,IACT,SAAU,KACV,KAAM,KACN,UAAW,KACX,YAAa,EAAI,WAAW,CAC5B,KAAM,YACN,MAAO,EAAI,IAAI,AACnB,GACA,KACJ,KAAK,aACD,EAAuB,EAAI,IAAI,EAC/B,EAAQ,IAAI,CAAC,GACb,KACJ,KAAK,YACD,KACJ,SACI,EAAQ,IAAI,CAAC,EAErB,CACJ,GACA,EAAI,IAAI,CAAG,CACf,IAtD+B,AI2F/B,SAAS,AAAO,CAAG,EAzBf,GAAI,CAAC,CAAgB,UAAf,OA0B0B,AA1BnB,GAAoB,aAAe,MAAA,CAAM,CAClD,EADqD,IAC/C,AAAI,MAAM,6DAEpB,IAAM,EAAc,KACpB,GAAI,CAAC,EAAY,CADe,OACP,CACrB,CADuB,KACjB,AAAI,MAAM,CAAC,WAAW,EAAE,EAAI,kDAAkD,EAAE,EAAY,MAAM,CAAC;AAAG,CAAC,CACzG,KAAyB,EAAY,MAAM,GAEnD,IAF4B,AAEtB,EAAS,GAkBD,IAlB0B,CAAE,OAAQ,EAAY,AAA9B,MAAoC,CAAE,aAAS,CAAU,EAAG,GAC5F,GAAI,CAAC,EAAO,OAAO,CACf,CADiB,KACX,AAAI,MAAM,CAAC,cAAc,EAAE,EAAI,QAAQ,CAAC,EAElD,GAAI,EAAO,QAAQ,CAAG,EAAY,MAAM,CAAC,MAAM,CAAE,CAC7C,IAAM,EAAQ,EAAY,MAAM,CAAC,EAAO,QAAQ,CAAC,AACjD,OAAM,AAAI,MAAM,CAAC,WAAW,EAAE,EAAI,+CAA+C,EAAE,EAAM,MAAM,CAAC;AAAG,CAAC,CAChG,KAAyB,EAAM,MAAM,CAAE,EAAM,GAAG,CAA5B,CAC5B,CACA,OAAO,EAAO,KAAK,AAUvB,EJ7F+C,KIwJ/C,AJ3II,SI2IK,EAAU,CAAQ,EACvB,GAAI,CAAC,EAAS,IAAI,CACd,CADgB,KACN,AAAJ,MAAU,4BAEpB,OAAQ,EAAS,IAAI,EACjB,IAAK,WACD,EAAS,IAAI,CAAC,OAAO,CAAC,GACtB,EAAS,IAAI,CAAC,IAAI,CAAC,CAAC,EAAG,IAAM,GAAe,GAAqB,GAAI,GAAqB,KAC1F,KAEJ,KAAK,aACD,EAAU,EAAS,IAAI,EACvB,KAEJ,KAAK,OACD,EAAS,IAAI,CAAC,OAAO,CAAC,GACtB,EAAS,IAAI,CAAC,IAAI,CAAC,CAAC,EAAG,IAAO,GAAU,GAAK,GAAU,GAAM,CAAC,EAAI,EAG1E,CACA,OAAO,CACX,EJhKuB,GACZ,GAbH,CAAO,CAAC,EAAE,CAAG,CACT,IAAK,EACL,SAAU,CACN,KAAM,WACN,eAAgB,CAAE,MAAO,EAAG,MAAO,EAAK,YAAa,EAAI,WAAW,AAAC,CACzE,CACJ,CACJ,CACA,OAAO,CACX,EArBiD,GAC7C,CACA,MAAM,CAAO,CAAE,CACX,OAAO,EAAQ,IAAI,CAAC,QAAQ,CAChC,CACJ,CA6DA,SAAS,GAAM,CAAK,EAChB,IAAM,EAAW,EAAE,CACnB,KAAO,EAAM,MAAM,EAAE,CACjB,IAAM,EAAU,GAAW,EAAO,AAAC,IAAQ,EAAM,IAC3C,SAAE,CAAO,YAAE,CAAU,OAAE,CAAK,CAAE,CAAG,AA6B/C,SAAS,AAAY,CAAK,CAAE,CAAY,EACpC,IAAM,EAAU,EAAE,CACZ,EAAa,EAAE,CACf,EAAQ,EAAE,CAChB,IAAK,IAAM,KAAQ,EAAO,CACtB,IAAM,EAAW,EAAK,GAAG,CAAC,IAAI,CAC1B,EAAS,MAAM,CAEf,CAAC,AADe,AADC,EACQ,IAAI,CAAC,GAAQ,GAAgB,KAAU,GACrD,EAAU,CAAA,CAAU,CAAE,IAAI,CAAC,GAGtC,EAAM,IAAI,CAAC,EAEnB,CACA,MAAO,SAAE,aAAS,QAAY,CAAM,CACxC,EA5C2D,EAAO,GAC1D,EAAQ,EACJ,EAAQ,MAAM,EAAE,AAChB,EAAS,IAAI,CAAC,AAsD1B,SAAsB,AAAb,CAAiB,CAAE,CAAK,EAC7B,GAAI,AAAS,OAAO,GAChB,MAuBG,CACH,AAxBO,KAwBD,UACN,SAPa,CAOH,MAPU,OAAO,CAAC,AADjB,GAjBU,EAiBY,AAAC,GAAiB,AAAX,OAAd,GAAgB,IAAI,CAAY,AAAC,GAAM,EAAE,IAAI,GACnC,GAAG,CAAC,CAAC,CAAC,EAAM,EAAM,GAAK,CAAC,CAC5D,KAAM,UACN,MAAO,EACP,KAAM,GAAM,EAAM,KAAK,EAC3B,CAAC,CAID,EAxBA,GAAI,EAAK,UAAU,CAAC,cAChB,CAD+B,MACxB,AAmCf,SAAS,AAAgB,CAAI,CAAE,CAAK,EAChC,IAAM,EAAS,GAAe,EAAO,AAAC,GAAkB,cAAX,EAAE,IAAI,EAAsB,EAAE,IAAI,GAAK,EAAO,AAAC,GAAM,CAAA,EAAG,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAI,GAAG,CAAC,EAAE,EAAE,KAAK,CAAA,CAAE,EACzI,EAAW,EAAE,CACnB,IAAK,IAAM,KAAS,OAAO,MAAM,CAAC,GAAS,CACvC,IAAM,EAAM,EAAM,iBAAiB,CAC7B,EAAY,AAiB1B,SAAS,AAAiB,CAAG,EACzB,GAAqB,MAAjB,EAAI,QAAQ,CAAU,CACtB,IAAM,EAAW,EAAI,KAAK,CAAC,WAAW,GACtC,OAAQ,EAAI,OAAO,EACf,IAAK,IACD,OAAO,AAAC,GAAW,IAAa,EAAO,WAAW,EACtD,KAAK,KACD,OAAO,AAAC,GAAW,EAAO,WAAW,GAAG,KAAK,CAAC,UAAU,QAAQ,CAAC,EACrE,KAAK,KACD,OAAO,AAAC,GAAW,EAAO,WAAW,GAAG,UAAU,CAAC,EACvD,KAAK,KACD,OAAO,AAAC,GAAW,EAAO,WAAW,GAAG,QAAQ,CAAC,EACrD,KAAK,KACD,OAAO,AAAC,GAAW,EAAO,WAAW,GAAG,QAAQ,CAAC,EACrD,KAAK,KACD,OAAO,AAAC,IACJ,IAAM,EAAQ,EAAO,WAAW,GAChC,OAAQ,IAAa,GAAW,EAAM,UAAU,CAAC,IAAwC,MAA3B,CAAK,CAAC,EAAS,MAAM,CAAC,AACxF,CACR,CACJ,KACK,CACD,IAAM,EAAW,EAAI,KAAK,CAC1B,OAAQ,EAAI,OAAO,EACf,IAAK,IACD,OAAO,AAAC,GAAW,IAAa,CACpC,KAAK,KACD,OAAO,AAAC,GAAW,EAAO,KAAK,CAAC,UAAU,QAAQ,CAAC,EACvD,KAAK,KACD,OAAO,AAAC,GAAW,EAAO,UAAU,CAAC,EACzC,KAAK,KACD,OAAO,AAAC,GAAW,EAAO,QAAQ,CAAC,EACvC,KAAK,KACD,OAAO,AAAC,GAAW,EAAO,QAAQ,CAAC,EACvC,KAAK,KACD,OAAO,AAAC,GAAY,IAAa,GAAY,EAAO,UAAU,CAAC,IAAyC,MAA5B,CAAM,CAAC,EAAS,MAAM,CAAC,AAC3G,CACJ,CACJ,EAvD2C,GAC7B,EAAe,GAAM,EAAM,KAAK,EACtC,EAAS,IAAI,CAAC,CACV,KAAM,UACN,QAAS,EAAI,OAAO,CACpB,SAAU,EAAI,QAAQ,CACtB,MAAO,EAAI,KAAK,CAChB,UAAW,EACX,KAAM,CACV,EACJ,CACA,MAAO,CACH,KAAM,YACN,KAAM,EACN,SAAU,CACd,CACJ,EAxD+B,EAAK,SAAS,CAAC,IAAK,GAE/C,GAAI,EAAK,UAAU,CAAC,iBAChB,CADkC,MAC3B,AAsBf,SAAS,AAAmB,CAAI,CAAE,CAAK,EACnC,IAAK,IAAM,KAAQ,EACf,GAAqB,CADC,CACK,AAAC,GAAkB,iBAAX,EAAE,IAAI,EAAyB,EAAE,IAAI,GAAK,GAEjF,MAAO,CACH,KAAM,eACN,KAAM,EACN,KAAM,GAAM,EAChB,CACJ,EA/BkC,EAAK,SAAS,CAAC,IAAK,GAElD,GAAa,gBAAgB,CAAzB,EACA,OAAO,GAAiB,IAAK,GAEjC,GAAa,gBAAgB,CAAzB,EACA,OAAO,GAAiB,IAAK,EAEjC,OAAM,AAAI,MAAM,CAAC,2BAA2B,EAAE,EAAA,CAAM,CACxD,EAvEuC,EAAS,IAEpC,EAAM,MAAM,EAAE,AACd,EAAS,IAAI,IAAI,AAK7B,SAAS,AAAU,CAAK,EACpB,IAAM,EAAU,EAAE,CAClB,IAAK,IAAM,KAAQ,EAAO,CACtB,IAAM,EAAW,EAAK,QAAQ,CAC9B,GAAsB,YAAY,CAA9B,EAAS,IAAI,CACb,EAAQ,IAAI,CAAC,OAEZ,CACD,GAAM,SAAE,CAAO,CAAE,MAAI,CAAE,CAAG,AA6NtC,SAAS,AAAU,CAAG,CAAE,CAAS,EAC7B,IAAM,EAAU,EAAE,CACZ,EAAO,EAAE,CACf,IAAK,IAAM,KAAK,EACR,EADa,AACH,GACV,CADc,CACN,IAAI,CAAC,GAGb,EAAK,IAAI,CAAC,GAGlB,MAAO,SAAE,OAAS,CAAK,CAC3B,EAzOgD,EAAS,IAAI,CAAE,AAAC,GAAuB,aAAd,EAAK,IAAI,EACtE,EAAQ,OAAO,CAAC,AAAC,GAAS,EAAQ,IAAI,CAAC,IACnC,EAAK,MAAM,EAAE,CACb,EAAS,IAAI,CAAG,EAChB,EAAQ,IAAI,CAAC,GAErB,CACJ,CACA,OAAO,CACX,EAtBuC,GAEnC,CACA,OAAO,CACX,CAmCA,SAAS,GAAgB,CAAG,EACxB,OAAQ,EAAI,IAAI,EACZ,IAAK,eACD,MAAO,CAAC,aAAa,EAAE,EAAI,IAAI,CAAA,CAAE,AACrC,KAAK,YACD,MAAO,CAAC,UAAU,EAAE,EAAI,IAAI,CAAA,CAAE,AAClC,KAAK,aACD,MAAO,CAAC,WAAW,EAAE,EAAI,UAAU,CAAA,CAAE,AACzC,SACI,OAAO,EAAI,IAAI,AACvB,CACJ,CAsGA,SAAS,GAAiB,CAAU,CAAE,CAAK,EACvC,IAAM,EAAS,GAAe,EAAO,AAAC,GAAkB,eAAX,EAAE,IAAI,EAAuB,EAAE,UAAU,GAAK,EAAa,AAAC,GAAM,GAAmB,EAAE,IAAI,GAClI,EAAY,EAAE,CACpB,IAAK,IAAM,KAAS,OAAO,MAAM,CAAC,GAAS,CACvC,IAAM,EAAY,GAAM,EAAM,KAAK,EAC7B,EAAU,EAAM,iBAAiB,CAAC,IAAI,CAC5C,EAAU,IAAI,CAAC,CACX,IAAK,EACL,SAAU,CAAE,KAAM,aAAc,KAAM,CAAU,CACpD,EACJ,CACA,MAAO,CACH,KAAM,cACN,WAAY,EACZ,KAAM,GAAM,EAChB,CACJ,CACA,SAAS,GAAe,CAAK,CAAE,CAAS,CAAE,CAAW,EACjD,IAAM,EAAS,CAAC,EAChB,KAAO,EAAM,MAAM,EAAE,CACjB,IAAM,EAAU,GAAW,EAAO,EAAW,GACvC,EAAmB,AAAC,GAAQ,EAAU,IAAQ,EAAY,KAAS,EAEnE,CAAE,SAAO,MAAE,CAAI,CAAE,CAAG,AAwElC,SAAS,AAAW,CAAG,CAAE,CAAS,EAC9B,IAAM,EAAU,EAAE,CACZ,EAAO,EAAE,CACf,IAAK,IAAM,KAAK,EACR,EADa,AACH,GACV,CADc,CACN,IAAI,CAAC,GAGb,EAAK,IAAI,CAAC,GAGlB,MAAO,SAAE,EAAS,MAAK,CAC3B,EApF6C,EADT,AAAC,GAAS,EAAK,AACC,GADE,CAAC,IAAI,CAAC,IAAI,CAAC,IAErD,EAAoB,KACxB,IAAK,IAAM,KAAQ,EAAS,CACxB,IAAM,EAAc,GAAqB,EAAM,EAC3C,CAAC,IACD,EAAoB,CAAA,CAE5B,CACA,GAAyB,MAArB,AAA2B,CAJH,CAKxB,MAAU,AAAJ,MAAU,gCAEpB,CAAM,CAAC,EAAQ,CAAG,CAAE,kBAAmB,EAAmB,MAAO,CAAQ,EACzE,EAAQ,CACZ,CACA,OAAO,CACX,CACA,SAAS,GAAqB,CAAI,CAAE,CAAS,EACzC,IAAM,EAAW,EAAK,GAAG,CAAC,IAAI,CACxB,EAAU,AAAI,MAAM,EAAS,MAAM,EACrC,EAAa,CAAC,EAClB,IAAK,IAAI,EAAI,EAAS,MAAM,CAAE,KAAM,GAAI,AAChC,EAAU,CAAQ,CAAC,EAAE,GAAG,CACxB,CAAO,CAAC,EAAE,EAAG,EACb,EAAa,GAGrB,GAAkB,CAAC,GAAf,AAAkB,EAClB,MAAM,AAAI,MAAM,CAAC,2CAA2C,CAAC,EAEjE,IAAM,EAAS,CAAQ,CAAC,EAAW,CAEnC,OADA,EAAK,GAAG,CAAC,IAAI,CAAG,EAAS,MAAM,CAAC,CAAC,EAAK,IAAM,CAAC,CAAO,CAAC,EAAE,EAChD,CACX,CACA,SAAS,GAAW,CAAK,CAAE,CAAS,CAAE,CAAW,EAC7C,IAAM,EAAa,CAAC,EACpB,IAAK,IAAM,KAAQ,EAAO,CACtB,IAAM,EAAc,CAAC,EACrB,IAAK,IAAM,KAAQ,EAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GACpC,CAAW,CAAC,EAAY,GAAM,CADkB,CACf,EAErC,IAAK,IAAM,KAAO,OAAO,IAAI,CAAC,GACtB,CAAU,CAAC,EAAI,CACf,CADiB,AACP,CAAC,EAAI,CAFqB,EAKpC,CAAU,CAAC,EAAI,CAAG,CAG9B,CACA,IAAI,EAAU,GACV,EAAa,EACjB,IAAK,IAAM,KAAS,OAAO,OAAO,CAAC,GAC3B,CAAK,CAAC,EAAE,CAAG,IACX,AAFwC,EAE9B,CAAK,CAAC,EAAE,CAClB,CAFuB,CAEV,CAAK,CAAC,EAAE,EAG7B,OAAO,CACX,CA4BA,MAAM,GACF,YAAY,CAAC,CAAE,CACX,IAAI,CAAC,CAAC,CAAG,CACb,CACA,QAAQ,CAAE,CAAE,CACR,OAAO,IAAI,CAAC,CAAC,CAAC,EAClB,CACA,MAAM,CAAE,CAAE,GAAc,CAAK,CAAE,CAC3B,IAAM,EAAU,IAAI,CAAC,CAAC,CAAC,GACjB,EAAM,EAAQ,MAAM,CAC1B,GAAI,AAAQ,GAAG,GACX,OAAO,KAEX,GAAY,GAAG,CAAX,EACA,OAAO,CAAO,CAAC,EAAE,CAAC,KAAK,CAE3B,IAAM,EAAc,EACd,GACA,GACF,EAAS,CAAO,CAAC,EAAE,CACvB,IAAK,IAAI,EAAI,EAAG,EAAI,EAAK,IAAK,CAC1B,IAAM,EAAO,CAAO,CAAC,EAAE,CACnB,EAAW,EAAQ,KACnB,EAAS,AADiB,CACjB,CAEjB,CACA,OAAO,EAAO,KAAK,AACvB,CACJ,CACA,SAAS,GAAsB,CAAG,CAAE,CAAI,EACpC,IAAM,KAA0B,EAAnB,AAAwB,WAAW,CAAE,EAAI,WAAW,EACjE,OAAO,EAAO,GAAM,AAAS,OAAK,EAAK,KAAK,CAAG,EAAI,KAAK,AAC5D,CACA,SAAS,GAAqB,CAAG,CAAE,CAAI,EACnC,IAAM,EIpKC,GJoKyB,EAAK,AAAxB,UIpKS,CJoK0B,CAAE,CIpKzB,CJoK6B,WAAW,EACjE,OAAO,EAAO,GAAe,IAAT,GAAc,EAAK,KAAK,CAAG,EAAI,KAAK,AAC5D,CC3aA,SAAS,GAAW,CAAK,EACrB,OAAO,IAAI,GAAO,GAAY,GAClC,CACA,SAAS,GAAY,CAAK,EACtB,IAAM,EAAW,EAAM,GAAG,CAAC,IAC3B,MAAO,CAAC,EAAI,GAAG,IAAS,EAAS,OAAO,CAAC,GAAK,EAAE,KAAO,GAC3D,CACA,SAAS,GAAW,CAAI,EACpB,OAAQ,EAAK,IAAI,EACb,IAAK,WAAY,CACb,IAAM,EAAS,CAAC,EAAK,cAAc,CAAC,CACpC,MAAO,CAAC,EAAI,GAAG,IAAS,CAC5B,CACA,IAAK,cAYU,EAXU,EAWN,AACvB,IAAM,EAAW,CAAC,EAClB,IAAK,IAAM,KAAW,EAAK,QAAQ,CAAE,AACjC,CAAQ,CAAC,EAAQ,KAAK,CAAC,CAAG,GAAY,EAAQ,IAAI,EAEtD,MAAO,CAAC,EAAI,GAAG,KACX,IAAM,EAAe,CAAQ,CAAC,EAAG,IAAI,CAAC,CACtC,OAAQ,EAAgB,EAAa,KAAO,GAAQ,EACxD,AAD0D,CAjBtD,KAAK,gBA2BgB,EA1BU,EA2BnC,AAD6B,IACvB,EAAY,EAAE,CACpB,IAAK,IAAM,KAAW,EAAK,QAAQ,CAAE,CACjC,IAAM,EAAY,EAAQ,SAAS,CAC7B,EAAe,GAAY,EAAQ,IAAI,EAC7C,EAAU,IAAI,CAAC,CAAC,EAAM,EAAI,GAAG,IAAU,EAAU,GAAQ,EAAa,KAAO,GAAQ,EAAE,CAC3F,CACA,IAAM,EAAW,EAAK,IAAI,CAC1B,MAAO,CAAC,EAAI,GAAG,KACX,IAAM,EAAO,EAAG,OAAO,CAAC,EAAS,CACjC,OAAQ,GAAiB,KAAT,EACV,EAAU,OAAO,CAAC,GAAM,EAAG,EAAM,KAAO,IACxC,EAAE,AACZ,CAtCI,KAAK,mBAkBmB,EAjBU,EAiBN,AAChC,IAAM,EAAW,EAAK,IAAI,CACpB,EAAe,GAAY,EAAK,IAAI,EAC1C,MAAO,CAAC,EAAI,GAAG,IAAS,OAAQ,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAG,OAAO,CAAE,GACpE,EAAa,KAAO,GACpB,EAAE,AArBJ,KAAK,kBAsCkB,EArCU,EAqCN,AAC/B,IAAM,EAAe,GAAY,EAAK,IAAI,EACpC,EAAyC,AAArB,QAAM,UAAU,CACpC,GACA,GACN,MAAO,CAAC,EAAI,GAAG,KACX,IAAM,EAAO,EAAkB,UAC/B,AAAa,MAAM,CAAf,EACO,EAAE,CAEN,EAAa,EAAM,KAAO,EACrC,CA/CI,KAAK,iBA4DiB,EA3DU,EA2DN,AAC9B,IAAM,EAAe,GAAY,EAAK,IAAI,EAC1C,MAAO,CAAC,EAAI,EAAM,GAAG,IAAS,EAAa,KAAS,EA5DpD,CACJ,CA8CA,IAAM,GAAsB,AAAC,IACzB,IAAM,EAAO,EAAG,IAAI,QACpB,AAAa,MAAM,CAAf,EACO,KAEJ,EAAO,GAAS,EAAO,GAAoB,EACtD,EACM,GAAmB,AAAC,IACtB,IAAM,EAAS,EAAG,MAAM,CACxB,OAAQ,GAAU,EAAM,GAAW,EAAS,IAChD,u5gBL9Ec,CAAA,IAAA,GAAA,CAAA,GAAA,EACQ,UAAA,CAAW,CAAC,ACAA,CAAC,ADAA,AEUlC,CFVmC,ACAA,CDCnC,ACAA,CAAC,ADAA,qHRLwB,CIOC,CJNtB,CAAC,CECC,AQOA,uBVLG,EMCE,AWuBA,KjBvBP,IAAK,MACL,IAAK,OACL,IAAK,OACL,WACA,IAAK,KAAK,EACV,+BAGA,IAAK,WACA,UACA,qBAEA,WACA,gBACK,CiBuBC,qBjBrBD,MACL,eACI,aAET,cACK,CEIC,MFHN,kBACS,EACT,IAAK,KACT,CAAC,CsBgBC,AtBhBA,AAKI,sCAGH,SAAU,CAAiB,OO4EwC,CAAC,CAAC,kDPvEhC,CAAA,IAAA,GACH,KAAS,CuBA9B,QvBRoG,qCAa/E,CEKH,CAAC,EFI9B,SAAA,GAAA,CAAA,mCACkD,CCIvC,CDJmD,OqBOtD,CAAA,ArBP8D,AAC7D,EAD+D,mBAItD,CAAA,EAAA,EAAA,EAAe,CoBOP,QpBPgB,CAAC,CqB9BxC,GAAA,CAAA,YACU,GAAA,IAAc,EAAA,GAAY,GAAU,IAAI,CAAC,WAzBxC,wGAMhB,CAAA,CAAA,EAAA,OAAA,CAAA,GAAA,CAAA,0KANO,KAAA,GAAA,CAAA,CAAA,IAkBX,SAAA,CAAwB,mHAIvB,CAJW,KAAA,GAAA,CAAA,CAAA,2LAyCX,CANU,KAAA,ChBEgB,CAAC,CAAA,CAAA,CAAA,agBMJ,0FAuBlB,OAAO,CDTC,oCCYqB,CAAA,qBAUV,cAEA,EAGrB,IAAA,CAAA,KACa,CAAA,GAAA,WAAiC,CAAC,GdiEI,Wc/DhC,CAAC,YAON,CAAG,gBAGA,CAAA,cAEA,iBAEC,CAAA,GAAA,MAAsB,kCAKpC,IAAA,CAAK,KAAA,CAAQ,GAAA,WAA8B,KACvC,CAAC,CpBkBC,KoBlBK,CAAG,EACd,IAAI,CAAA,SAAU,CAAA,MACV,CAAC,MAAA,CAAA,MACD,CAAC,QAAQ,CAAG,CAAC,CAAC,CfyEkC,CAAC,Ie3DnD,CAAA,CAAa,CAAA,CAAA,YACH,CAAC,KAAK,UACU,WAAW,CAC/B,GAAI,EAAA,UAAc,CAAC,KAAY,GAAU,GAAG,CAGxC,CAH0C,UACtC,CAAC,KAAA,CAAQ,GAAmB,YAAY,CAAC,AAC7C,IAAA,CAAK,QAAA,EAAY,CAAC,CAAC,AACnB,IAAA,CAAA,iBAA6B,CAAC,EAAK,EAAS,CAAC,CAAC,CAGlD,OADA,IAAI,CAAC,EGgBE,GHhBG,CAAG,CpB2cA,EAAA,WAAA,CoB1cb,IAAW,CAAA,gBAAiB,CAAC,EAAK,CJiCT,AIjCO,CJiCN,SI9BN,YAAA,YACT,CAAC,iBAAiB,CAAC,EAAK,CAAF,ApBmBA,CAAC,MoBhBjC,GAAmB,cAAc,QAC3B,IAAA,CAAK,mBAAmB,CAAC,EAAK,CAAF,UAGf,UAAU,CAAC,AAC/B,OAAA,IAAW,CAAC,eAAe,CAAC,EAAK,CAAF,KAAQ,CAAC,CAAC,AAGxC,GAAmB,WAAW,QACxB,IAAA,CAAA,gBAAqB,CAAC,EAAK,IActC,EAd4C,gBAc5C,CAAA,CAA+B,CpBkBf,CoBlB6B,WACnC,EAAI,MAAM,CACb,CADe,AACd,CAAC,CAGR,CA1KQ,KA0KR,UAAc,CAAC,EAAU,IAAkB,GAAU,CAA5B,MAAmC,EAAE,cAC/B,UAAU,CAAC,aAC9B,EAAA,EACN,IAAI,CAAC,CdiFC,ccjFc,CAAC,EAAK,CAAF,CAAW,CAAC,CAAC,CAAC,GdsF9C,GcnFC,CAAA,KAAM,CAAG,GAAA,cAAA,CACN,IAAA,CAAA,mBAAwB,CAAC,EAAK,uBAIrC,CAAW,CACX,CAAa,CAAA,CACF,CACX,CAAY,CAAA,IAER,IAAA,EAAA,SACyB,aACd,CAAA,IACH,CAAC,MAAM,CAAA,KAAQ,GAAA,CAAA,EAAU,EfmFE,CAAA,SelFtB,EAAI,MAAM,CAAA,EAAA,GAAqB,IAAI,CAAC,CAAC,CAC9C,CAAC,QAAQ,EAAI,GAYtB,gBACqB,CAAW,CAAA,CAAA,CAAA,KACzB,EAAW,EAEjB,KAAO,EAAS,EAAI,GfgFG,CAAC,EehFE,EAAE,OACxB,IAAM,EAAO,EAAH,AAAG,UAAc,CAAC,GAC5B,GAAA,CAAA,GAAa,KAnMhB,IAmMyB,CAAuB,IAnMhD,GAAkB,AAmMqC,OAnMrC,IAAA,CAAA,GAAA,GAA6B,OAAA,AAAO,CAAP,AAAQ,MACvD,GAAQ,GAAA,OAAA,IAAA,CAAA,GAAA,GAAuC,KdoED,EcpEQ,AdoER,CAAA,SciIvC,IAAI,CAAC,kBAAkB,CAAC,EAAK,CAAF,CAAY,EAAQ,EAAE,CpBiBL,AoBjBM,CAAb,AAAQ,AAAM,AAC5C,IAAI,CAAC,iBAAiB,CAAC,EAAA,MAHpB,CAAC,CAAC,CpBgBC,8BoBTG,EAAK,EAAU,EAAQ,EAAE,CAAC,EAE1C,CACZ,CAAC,AAWO,oBAAoB,CAAW,CAAE,CAAA,CAAA,CACrC,IAAM,EAAW,EAEjB,KAAO,EAAA,EAAa,MAAM,EAAE,KAClB,EAAO,EAAI,UAAU,CAAC,MAAM,CAAC,AAC/B,CADgC,EACvB,eAGJ,kBAAkB,CAAC,EAAK,EAAA,EAAkB,EAAE,CAAC,CAAC,AAC5C,IAAA,CAAK,iBAAiB,CAAC,EAAM,CAAC,CAAH,AAAI,CAAC,GAH7B,EAKjB,AAID,OAFA,IAAA,CAAK,kBAAkB,CAAC,EAAK,EAAU,EAAQ,IAExC,qBAgBe,CAAc,CAAE,CAAsB,CAAA,UAExD,IAAI,CAAC,QAAQ,EAAA,gBAAoB,EACjC,IAAA,CAAK,MAAM,AAAN,GAAM,EAAE,CAAF,QAAA,OAAA,KAAA,IAAA,KAAA,YAA4C,CACnD,IAAI,CAAC,QAAQ,CAChB,CAAC,AACK,CAAC,AfiFA,We7Ea,IAAA,EAAM,IACtB,QAAQ,EpBaE,AoBbE,CAAC,SACX,IAAA,CAAK,UAAU,EpBcA,CoBdK,GAAa,MAAM,EAAE,MACzC,2BAGO,CAAA,GAAkB,IAAI,CAAC,CpBaC,CAAC,IoBbI,EAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,AAEjE,IAAA,CAAA,MAAe,GACX,IAAe,GAAU,CfiFC,CAAC,CAAC,CejFC,EAAE,AAC3B,IAAI,CAAC,MAAA,CAAO,uCAAuC,EAAE,CAAC,WAG/C,CAAC,iCAAiC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAGxD,IAAI,CAAC,QAAA,kBAYS,CAAA,CAAA,CAAA,CAAA,gBACb,CAAA,CAAY,CAAA,IAAA,IACI,CAAC,IAAI,CAAA,SAAU,CAAC,CAAC,EAEvB,CAAA,EAAW,GAAa,YAAA,AAAY,CAAC,EAAI,EAAE,CAAC,AAE9D,KAAO,EAAS,EAAI,MAAM,CAAE,IAAU,EAAJ,EAAQ,CAAC,MAAM,EAAE,CAAE,CACjD,IAAA,EAAa,EAAI,UAAU,CAAC,MAAM,AAElC,CAFmC,CAAC,EAEhC,CAAA,SAAU,CAAG,GACb,EACA,EACA,IAAI,CAAC,SAAS,CAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAE,GAC7B,GAGA,CAHI,CACP,CAAC,CAEM,CAJoC,AAInC,CAJoC,QAI3B,CAAA,EACd,CADoB,MACF,IAAX,IAAI,CAAC,MAAM,EAAA,IAAA,CAER,UAAU,GAAA,GAAkB,SAAS,GAEtC,IAAA,CACG,EACA,SAlSW,CAAY,EXWN,AAKA,aWflC,IAAA,GAAA,MAAA,KAAiD,IAb3C,AAa+C,CAAC,CpBN3C,AoBM4C,CpBN5C,AoBPK,MA2S2D,CA3S3D,EAAA,GAAmB,GAAA,OAAiB,EAAA,GAAA,GAAA,OAAA,EAAA,GACjB,GAAA,OAAiB,EAAA,GAAA,IA6SL,EAAI,CAAC,CAAE,AAAD,CAAC,CAE3C,IAAI,CAAC,4BAA4B,EAAE,CAAC,GAO1B,OAHL,GADL,CAAU,CAAC,Cf+EI,Ge/EA,CAAC,UAAS,AAAC,CAAC,CACZ,GAAa,YAAA,AAAY,CAAC,EAAI,EAAA,CAAE,CAAC,AAGtC,WAEO,IAAI,EAAE,MAClB,IAAI,CAAC,mBAAmB,CAC3B,IAAI,CAAC,SAAS,CACd,EACA,IAAI,CAAC,IADM,IACE,CAAG,IAAI,CAAC,MAAM,CAC9B,ApBAJ,CoBAK,ApBAL,IoBIO,CAAC,UAAU,GAAK,GAAa,MAAM,EAAE,CACzC,IAAA,CAAA,MAAW,CAAG,IAAA,CAAA,SAAc,CAAC,AAC7B,IAAA,CAAA,QAAa,EAAA,IAAA,CAAA,MAAA,KACT,CAAC,MAAM,CAAG,IAK1B,OAAO,EACX,CAAC,AAOO,8BAAA,OACJ,GAAM,QAAE,CAAA,YAAQ,CAAU,CAAE,CAAG,IAAI,CAAC,AAE9B,EACF,AAAC,EAAU,CAAC,EAAO,CAAG,Gf8EmB,Ae9EN,Gf8EmB,SAAA,Ae9EP,CAAC,EAAI,EAAE,CAAC,YAEtD,mBAAmB,CAAC,EAAQ,EAAa,EAAf,EAAmB,CAAC,QAAQ,CAAC,CAAC,SAC7D,IAAI,CAAC,MAAA,AAAM,GAAA,EAAE,CAAF,QAAA,8BAAyC,EAAE,CAAC,AAEhD,IAAI,CAAC,QAAQ,CAWrB,oBAEC,CAAc,CAAA,CACK,CACnB,CAAgB,CAAA,CAEhB,GAAM,CAAE,YAAU,CAAE,CAAG,IAAI,CAAC,yBAEV,CACd,AAAgB,MAAA,CACA,CAAC,EAAO,CAAG,CAAC,GAAa,YAAY,CAC/C,CAAA,CAAW,EAAA,EAAA,CACjB,MAEmB,CfuEC,CAAC,qBerEF,CAAU,CAAC,EAAS,CAAC,CAAC,CAAA,IAIjD,CfwEC,AexEA,AASD,GAAG,EAAA,cACS,IAAI,CAAC,KAAA,OACJ,GAAmB,WAAW,CAAC,AAEhC,GAFmB,IAEI,IAAhB,CACH,GADO,CAAA,MAAA,GACN,IAAI,CAAC,IpBrBI,MoBqBM,GAAK,GAAa,SAAS,EAAA,IACnC,CAAA,MAAO,GAAK,IAAI,CAAC,SAAS,AAAT,CAAU,CACjC,IAAA,CAAK,4BAA4B,EAAE,CAAA,CAI7C,MAAK,GAAmB,cAAc,CAAC,OAC5B,IAAI,CAAA,iBAAkB,CAAC,CAAC,CAAE,CAAC,CAAC,CAAC,QAEhB,UAAU,CAAC,AAC/B,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAE,CAAC,CAAC,AAEvC,CAFwC,Cf2E/B,IezEJ,GAAmB,YAAY,CAAC,AAIjC,OAHA,OAAA,EAAA,IAAA,CAAK,MAAA,AAAM,GAAA,EAAE,0CAA0C,CACnD,IAAI,CAAC,QAAQ,CAChB,CAAC,AACK,OAEN,GAAmB,WAAW,CAAC,AAEhC,OAAO,CAAC,CAAC,EAIxB,AAQD,SAAS,GAAA,CAAA,QACK,EAAE,CACN,EAAA,IAAc,GAChB,EACA,GAAU,GAAO,EADP,CACqB,GAAG,CAAC,AAGvC,CAHwC,CACvC,CAAC,GADgC,CAG3B,SAAS,AACZ,CAAW,CACX,CAAwB,EAExB,IAAA,EAAgB,EACZ,EAAS,CAAC,CAAC,KAER,AAAC,GAAS,EAAI,CAAP,AAAM,MAAQ,CAAC,GAAG,CAAE,EAAM,CAAC,CAAC,CAAI,CAAC,EAAE,CAC7C,GAAG,EAAQ,KAAK,CAAC,EAAW,GAE5B,EAAQ,EAFkB,MpBlCF,GoBoCL,CAAC,OAEd,EAAM,CAAH,AAFqB,CAAC,AAEX,CAFY,IAEb,AAAM,CACrB,GAAG,CAEM,UAGA,MAJM,IAKiB,EAAE,CAAC,AACnC,MAAM,AAGV,EAAY,EAAS,EAErB,EAAA,AAAiB,CAAC,CAAC,CAAC,CAApB,EAAoB,EAAA,EAAiB,MAGnC,EAAA,CAH4C,CAG5C,AAH6C,EAG1B,CpB1CC,IAAA,CoB0CK,GAK/B,OAFA,EAAM,CAAH,EAEI,EAEf,CAAC,AAYK,SAAU,GACZ,CAAuB,CACvB,CAAe,CACf,CAAe,CACf,CAAY,MAEN,EAAc,CAAA,EAAW,GAAa,aAAA,AAAa,CAAC,EAAI,CAAC,CAAC,AAC1D,EAAa,EAAU,GAAa,UAAU,CAAC,AAGrD,GAAoB,GAAG,CAAnB,EACA,Cf0DC,Me1DqB,CAAC,GpBtDG,AoBsDnB,GpBtDmB,IoBsDU,EAAa,EAAU,CAAC,OAIhD,CACZ,IAAM,EAAQ,EAAI,EAAA,OAEX,EAAQ,CAAC,EAAI,EpBzDE,CoByDO,EACvB,CAAC,EACD,CAAU,CAAC,EAAU,EAFa,AAEP,CAAG,CAAC,CAAL,AAAM,AAM1C,IAAI,EAAK,AAAH,EACF,EAAE,EAAQ,EAAc,EAE5B,KAAO,GAAA,GAAU,KACP,EAAO,EAAK,EAAE,CAAC,CAAK,CAAC,CAAC,AACtB,EAAS,CAAU,CAAA,EAAK,CAAC,AAE/B,GAAA,EAAa,EACT,EAAK,AADQ,AACX,EAAS,AADI,CACP,AAAI,CAAC,EpB7DU,GoB8DpB,IAAA,CAAA,EAAa,CfsDQ,CAAC,ALlHP,AKkHK,AetDH,EAAE,MAGf,CAAU,CAAA,EAAO,EAAY,CAAC,EAFhC,EAAM,CAAC,CfsDC,AetDA,CfsDC,CepDqB,MAIpC,CAAC,CACZ,CAAC,0BpBrea,CCEK,EAAA,KDFU,EAAA,IAAA,GACL,OAAA,EAAA,IAAA,GAAA,GAAA,EAAA,IAAA,GAAA,QAEQ,CCGC,CAAA,IDFnB,GAAA,cAAA,AAEd,aAE2B,CAAA,kCAC+B,EMsEgB,CNtEH,AMsEI,CNtEH,CAAC,CAAC,kCAIxC,GAAK,GAAU,CMqEK,GNrED,AACrD,CoBydoB,ApB1dkC,GoB0dlC,GdrZ4C,CcsZ7C,AdtZ8C,CAAC,EcsZpC,uhBpB9hB1B,CAAA,CAAA,EAAA,MAAA,CAAA,GAAA,CAAA,+JAMJ,CAAC,CA5BU,IAAA,CAAA,GAAA,CAAA,CAAA,+uCA0GX,SAAA,CAAA,qGAAY,IAAA,CAAA,GAAA,CAAA,CAAA,UA+BM,OACP,IAAA,WAAe,CAAC,CkB9BC,CAAC,IlB8BU,CuBZC,CAAC,CAAC,AvBYG,EKuDF,EkBnEM,AvBYA,+BACpB,IAAO,GAAM,EuBVM,CvBUD,4BAChB,CAAC,GAAM,GAAM,CAAF,EAAO,CAAD,eAC7B,WAAA,CAAY,GAAM,EmBvBE,CnBuBI,IAAI,AAAE,EoBjBE,CpBiBI,IAAM,IAAI,AAAE,IAAI,AAAE,IAAI,AAAC,CAAC,CAC3E,SAAA,IAAA,WAAA,CAA0B,CsB5BC,EtB4BK,GAAM,CAAF,GAAQ,IAAI,AAAE,IAAI,AAAE,IAAM,IAAK,CAAC,yBAC3C,CAAC,IAAI,EAAQ,IAAI,AAAE,GCf9B,CDekC,AAAE,QAAY,IAAI,AAAC,CAAC,EAAE,MAGrD,MAHiE,SA0B9E,SACI,EAAA,CAAA,CAAe,gBACf,GAAiB,CAAI,CACyB,CACjC,CAAc,CAAA,0BAzBb,IAAA,YAER,CAAG,oBAEG,CAAA,YAEP,CAAG,CAAC,CAAC,qCAID,CAAA,CAAA,cAEH,CAAA,CAAA,EAEN,IAAA,CAAA,MAAM,CAAG,CAAC,+CAgFG,CAAA,uDAkXD,CAAA,yCApbZ,CAAC,cAAc,CAAG,MAClB,CAAC,UAAA,CAAa,EAAA,GAA0B,KoBnBK,CAAC,CAAC,OpBmBO,CAAC,MAIjD,CAAA,GAAA,IAAa,CAAC,AACxB,IAAA,CAAK,MAAM,CAAG,OACV,CAAC,YAAA,CAAe,CAAC,CAAC,IAClB,CoBnBuB,ApBmBvB,KAAM,CAAA,OACL,SAAA,CAAY,GAAA,IAAA,2BACM,EACvB,IAAA,CAAA,OAAA,CAAA,CAAA,aACW,CAAG,SAGQ,CAAA,KAClB,CAAA,MAAO,EAAA,IAAQ,CAAA,MAAA,CAAA,MAAA,KACf,CAAC,MAAM,CAAG,EACd,IAAA,CAAK,KAAA,uBAIa,IAAA,CAAK,MAAM,EACjC,CAAC,OAEW,CACR,IAAI,CAAC,OAAO,CoBlBS,CpBkBN,EAGZ,QAAA,qBAEK,CAAA,KAAM,CAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CKmEC,ALnEE,IAAA,CAAA,MAAW,EAAE,MoBlBM,OpByB1D,UACY,CACX,OAAO,IAAA,CAAK,EoBjBU,GAAA,ApBkB1B,CAAC,AAKM,iBAAA,CACH,OAAO,IAAA,CAAA,YAAiB,WAGV,CAAS,CAAA,CAEnB,IAAM,GAAU,EAAE,EACjB,CAAC,IAAI,CAAA,cAAA,EAAmB,IAAA,CAAK,aAAa,CAAA,GAAA,EAAa,CAAC,CAAC,CAC5D,AACE,IAAA,CAAA,KAAA,CAAiB,IAAA,CAAK,YAAY,EAC9B,AADgC,IAChC,CAAA,GAAQ,CAAC,MAAA,CAAO,IAAI,CAAA,YAAA,CAAe,IAAI,CAAC,KAAK,CAAC,CAAC,AAEnD,IAAI,CAAC,KAAK,CAAG,GAAM,aAAa,CAAC,IAC7B,CAAA,YAAa,CAAG,IAAI,CAAC,KAAK,MACnB,CAAC,cAAc,EAAI,CAAC,GAAK,GAAU,GAAG,EAAE,CAAN,IACzC,CAAA,KAAM,CAAG,GAAM,YAAA,AAAY,EAM/B,0BAA0B,CAAS,CAAA,CACvC,IAAM,EAAA,IAAY,CAAC,aAAa,GAAK,IAAI,CAAC,eAAe,CAAA,MAAO,CAAC,AAOjE,GANgB,EAEV,GAAkB,CAAC,EAAA,CAEd,GAAJ,CAAI,CAAI,CAAC,EAAK,IAAI,CAAC,eAAe,CAAC,CoBjBG,CAAC,CAAC,CpBiBD,CAAC,aAAa,CAAC,CAAC,OAI3C,gBACX,CAAC,aAAA,YAFD,CAAC,SAAS,EAAG,EAMrB,CK2DwB,CAAC,CLjEC,CAMtB,AANuB,CAMtB,CKmEC,YLnEY,CAAG,MACjB,CAAA,KAAM,CAAG,GAAM,SAAS,CAAC,IACzB,CAAA,cAAA,CAAA,EACR,CAAC,AAED,kBAC0B,CAAA,CAAA,CACtB,GAAA,IAAQ,CAAC,aAAa,GAAK,IAAA,CAAK,eAAe,CAAC,MAAM,CAAE,IAChD,CAAC,GAAK,GAAU,EAAA,EAAA,GAAmB,CAAC,CAAC,CAAE,CACvC,IAAA,EAAkB,IAAI,CAAA,KAAM,CAAA,IAAO,CAAC,eAAe,CAAC,MAAA,IAEhD,CoBhBC,GAAA,CpBgBI,EoBhBE,UAAA,CpBgBa,EAAW,CAE/B,IAAM,EAFuB,AAET,IAAI,CAAC,KAAK,CAAC,AAC/B,CKsEC,CetFC,EAAA,CpBgBG,KAAK,CAAG,OACR,CKsEC,ELtEE,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAE,SAAS,CAAC,CAAC,EACpC,CAAG,EAGjB,IAAA,CAAA,SAAc,EAAG,CKqEC,CLpElB,IAAI,CAAA,YAAa,CAAG,EAAA,EACpB,CADmC,GAC/B,CAAC,iBADmD,IAC9B,CAAA,GAC1B,OAGJ,IAAA,CAAK,aAAa,CAAG,CAAC,CAAC,SAGR,IAAI,CAAC,YANsC,GAMvB,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,GACrD,CAAA,aAAc,EAAA,EACY,CAAC,EAAE,CAA1B,IAAA,CAAK,aAAa,CKiED,ALhEpB,CKgEqB,CAAC,ELhEtB,CAAA,eAAA,GAAyB,CKiEC,CAAC,CLjEQ,MAAD,EAAS,EAAE,GAErC,CAAC,cAAc,EAAI,CAAC,GAAK,GAAU,GAAG,EAAE,CAAN,AACtC,IAAI,CAAC,KAAA,CAAQ,GAAM,YAAA,EAEhB,IAAI,CAAC,aAAa,CAAC,GAAU,EAAE,CAAC,OAEnC,CAAA,aAAA,EAAiB,CAAC,CAI1B,IAAI,CAAC,aAAa,CAAG,OAAO,CAAC,GAAA,GAAe,EAAE,CAAC,AAEvD,CAFwD,AAEvD,AAEO,mBAAA,CAAA,CAAA,CACJ,IAAU,GAAU,KAAK,CAAC,IAAI,CAAC,aAAA,CAAc,CACrC,CADuC,CACrC,IAAI,CAAA,aAAc,EKqEE,CAAC,ALrEE,CKqED,ELrEW,KAAK,CAAC,MAAM,EAAE,CACjD,IAAI,CAAA,KAAM,CAAG,GAAM,aAAa,CoBdG,ApBcF,qBACV,GAAU,QAAQ,CAAC,KACrC,aAAa,CAAG,MACjB,CAAC,YAAY,CAAG,IAAI,CAAC,KAAK,CAAA,SAG7B,aAAa,CAAG,YACX,CAAG,GAAM,aAAa,CAAC,IAC7B,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,AASjC,cACmB,CAAS,CAAA,CAC3B,KAAO,EAAE,EAXiD,EAW7C,CAAC,KAAK,CAAG,IAAI,CAAA,MAAA,CAAA,MAAA,CAAiB,IAAI,CAAC,MAAM,CAAE,CACpD,GAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,MAAM,CAAC,GAAK,CAAC,EAAE,QAahE,OAFA,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CoBFC,ApBEE,IAAI,CAAC,MAAM,CAAG,CAAC,CAAC,CAE3C,EAUR,mBACwB,CAAA,CAAA,CACvB,IAAA,IAAc,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAC1C,CAD4C,AK4E3C,CAAA,IL3EK,CAAA,aAAc,GAAA,IAAA,CAAU,eAAe,CAAC,MAAM,EAAE,CAClD,CK2EH,GL3EO,CAAC,eAAe,GAAK,GAAU,MAAD,EAAS,EAAE,IACxC,GAAA,CAAI,OAAO,CAAC,CoBFC,GpBEG,CAAC,YAAY,CAAE,IAAI,CAAC,KAAK,CAAE,CAAC,CAAC,CAAC,AAEnD,IAAA,CAAA,GAAQ,CAAA,SAAU,CAAC,IAAA,CAAK,YAAY,CAAE,IAAI,CAAC,KAAK,CAAE,CAAC,CAAC,CAAC,AAGzD,IAAA,CAAK,aAAA,CAAgB,EACrB,IAAA,CAAK,YAAA,CAAe,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,AACnC,IAAA,CAAK,CKgFS,ILhFJ,CAAG,GAAM,CoBGC,GpBHG,CAAC,CAEzB,AAA2B,CAAC,EAAE,CAA9B,IAAQ,CAAC,aAAa,CAErB,IAAI,CAAC,aAAA,CAAA,IAAkB,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,AAC7C,KAAA,CAAK,aAAa,EAAG,EAEtB,IAAA,IAAc,CAAC,eAAe,CAAA,IAAK,CAAC,aAAa,CAAG,CAAC,CAAC,EAAE,KAEvD,CAAC,aAAa,EoBOU,ApBPP,CAAC,gBAUP,CAAS,CAAA,QACrB,IAAI,CAAC,OAAO,CAAC,AAAE,CAAD,AAAE,CKuFC,CAAC,CLvFgB,CAAC,CAAC,CAAC,AAhS3C,CAgS4C,CAAC,CAhS7C,GAAe,KAgSwB,CAhSxB,EAAU,AAgSgC,GAhShC,GAAe,MAAM,CAAC,CAC/C,AA+R0D,CAAC,CAAC,CA/R5D,AA+R6D,GA/R7D,MAAA,EAAyB,GAAA,GAAA,MAgS9B,AAhS8B,CAgS7B,AAEO,aAAA,CAAiC,CAAE,CAAA,CAAc,CACrD,IAAA,CAAK,SAAA,CAAA,CAAA,OACA,eAAA,CAAA,MACD,CAAA,aAAc,CAAG,MACjB,CAAC,KAAK,CAAG,GAAM,oBAAoB,CAAC,mBAGjB,CAAA,CAAA,4BACc,IAC5B,KAAK,CAAA,GAAA,iBAAA,kBACO,CAAG,IAAA,CAAA,KAAA,CAAa,CoBezB,ApBf0B,CAAC,AoBgBtC,KpBfM,GAAI,CAAC,GAAK,GAAU,YAAA,CACvB,CADqC,GACrC,CAAA,KAAU,CAAA,GAAA,uBAAgC,CAC1C,IAAI,CAAC,YAAY,CAAG,IAAI,CAAC,KAAA,CAAQ,CAAC,CAAC,KAChC,GAAI,IAAI,CAAA,cAAe,CAAC,CAAC,EAAG,CAC/B,IAAM,EAAY,GAAJ,MACV,CAAA,YAAA,CAAgB,IAAA,CAAK,KAAA,CACrB,AAAC,IAAI,CAAC,OAAO,EAAI,IAAU,GAAU,QAAQ,CAAC,CAAC,CAAC,CAGhD,CAHkD,GAG9C,CAAC,KAAK,CACN,AAAC,IAAI,CAAC,OAAO,EAAI,IAAU,GAAU,SAAA,CAAU,CAAC,CAAC,CAE3C,EoBoBE,CpBpBI,SAAS,CAF4B,AAE3B,GAF2B,cACvB,CAJ9B,IAAI,CAAC,YAAA,CAAA,GAAuB,QAAA,CAAU,CAAC,CAAC,CAAC,KAOtC,CAAJ,GAAI,GAAA,KAAqB,CAC5B,CAD8B,GAC9B,CAAK,KAAK,CAAA,GAAS,oBAAoB,CAAC,CoBqBC,ApBnBzC,IAAI,CAAA,KAAM,CAAG,GAAM,IAAI,CAAC,IACpB,CAAC,SAAS,CAAC,CAAC,CAAC,iBAGF,CAAA,CAAA,CACnB,GAAsB,CAAC,CAAC,EAAE,SACd,CAAC,aAAa,CAAA,IAAK,CAAC,YAAY,CAAE,IAAI,CAAC,KAAK,CAAC,CAAC,kBAClC,OAChB,CAAA,KAAM,CAAG,GAAM,EAAD,iBAAoB,CAAC,IACnC,CAAA,wBAAyB,CAAC,CAAC,CAAC,CAAC,AAEzC,CKkGC,ALlGA,0BACiC,CAAS,CAAA,CACvC,GAAiB,IAAI,CAEV,CAAC,GAAK,CoBsBU,EpBtBA,EAAE,CACzB,CAD2B,GACvB,CAAC,KAAA,CAAA,GAAc,IAAI,CAAC,KAEpB,CAAC,KAAA,CAAQ,IAAA,CAAK,cAAc,CAAC,CAAC,CAAC,CAC7B,GAAM,GKoGG,CAAC,YLpGY,CAAA,GAChB,gBAAgB,CAAC,IACzB,CAAC,YAAY,CAAA,IAAO,CAAC,KAAK,CAAC,wBAGT,CAAA,CAAA,MAChB,GAAA,EAAA,EAAgB,GAAa,EAAC,CAAC,EAAE,IAAL,CAC9B,CAAC,GAAA,CAAI,UAAU,CAAC,IAAI,CAAA,YAAa,CAAA,IAAM,CAAC,EoB6BxC,CAAC,EpB7B4C,CAAC,CoB6BzC,ApB7B0C,IAC/C,CAAC,YAAY,CAAG,CAAC,CoB+BC,ApB/BA,KAClB,CAAA,KAAA,CAAA,GAAe,mBAAmB,CAAC,IACnC,CAAC,CoBkCC,uBpBlCuB,CAAC,CAAC,CAAC,CAAC,0BAGR,CAAS,CAAA,CAEtC,CAAA,IAAU,GAAU,EAAE,EAAI,IAAI,CAAC,aAAa,CAAC,GAAU,GAAE,CAAC,EAAJ,AAAM,YAC3C,GAAM,EAAD,EAAK,CAAC,KACnB,SAAS,CAAA,GAAS,IAAI,CAAC,IACxB,CAAA,YAAa,CAAG,EoBwCE,EpBxCE,CAAA,KAAM,CoBwCE,CAAA,2BpBrCP,CAAA,CAAS,KAC5B,GAAA,EAAA,EAAc,IAChB,CAAC,CoByCC,EpBzCE,CAAA,YAAA,CAAc,IAAI,CAAC,GoByCG,EpBzCE,EAC5B,CKuGC,GAAA,CLvGI,SAAS,EACd,EK2GE,EAAA,CAAA,KL3GQ,CAAG,GoB0CG,ApB1CG,YAAY,CoB0CC,ApB1CA,CoB0CC,CAAC,EpBzC9B,CAAC,aAAa,CAAG,CAAC,CAAC,CAEvB,IAAA,CAAA,KAAU,CAAG,GAAM,EAAD,EAAK,CAAC,AAE5B,GK8GW,CL9GP,CAAA,SAAU,CAAA,IAAA,CAAQ,KAAK,CAAC,IACxB,CAAA,YAAA,CAAgB,IAAI,CAAC,KAAA,CAAA,GAClB,CAAC,GAAK,GAAU,KAAK,CAC5B,CAD8B,GAC9B,CAAK,KAAK,CAAA,GAAS,gBAAgB,CAAC,AAC5B,GAAa,CAAC,CAAC,EAAE,CACzB,IADoB,AAChB,CAAC,KAAK,CAAG,GAAM,eAAe,CAAC,AACnC,IAAA,CAAK,YAAY,CAAG,IAAI,CAAC,KAAK,CAAC,CAG/B,sBAAsB,CAAS,CAAA,CACnC,EoBoDwB,EpBpDxB,GAAoB,EAAA,EAAI,IAChB,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAClC,CAAC,EoBsDE,Af0DA,GLhHG,CAAA,GAAS,IAAI,CACvB,AADwB,IACpB,CAAC,QoBsDQ,CpBtDC,CAAG,GAAA,IAAA,KACb,CAAC,YAAY,CKiHE,CAAA,GLjHK,CAAA,KAAM,CAAG,CAAC,CAAC,KAC9B,SAAS,CAAG,IACT,CoBwDD,EpBxDc,CAAC,CAAC,EAAE,CACzB,GoBuD2B,CAAC,ApBvDxB,CAAC,GoByDG,EpBzDE,CAAG,GAAM,EoByDE,iBpBzDiB,CAAC,IACnC,CAAC,gBAH+E,QAGvD,CAAC,CAAC,CAAC,CAAC,sBAGZ,CAAS,CAAA,EAC9B,CAAC,GAAK,GAAU,EAAE,EAAI,EAAP,CAAyB,EAAC,CAAC,EAAE,CAC5C,IAAI,CAAC,GAAA,CAAA,YAAgB,CAAC,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,KAAK,CAAC,CAAC,AACrD,IAAI,CAAC,YAAA,CAAA,QACA,EoB4DE,GpB5DG,CAAG,GAAM,EAAD,gBAAmB,CAAC,4BACV,CAAC,CAAC,CAAC,CAAC,AAExC,CAAC,AACO,wBAAwB,CAAS,CAAA,KAC3B,GAAU,EAAE,EAAE,EAAL,QACF,GAAM,oBAAoB,CAChC,AADiC,CAChC,GAAA,GAAe,KAAK,EAAI,CAAC,GAAK,GAAU,EAAE,EAAE,AACpD,EAD+C,EAC3C,CAAC,GAAG,CAAC,WAAW,CAAC,GAAU,MAAD,CAAQ,CAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAChD,CAAC,KAAK,CAAG,GAAM,EoB4DE,iBpB5DiB,CAAC,AACvC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CACzB,GAAa,CAAC,CAAC,EAAE,KACrB,CAAC,GAAG,CAAC,WAAW,CAAC,GAAU,OAAO,CAAA,IAAM,CAAC,KAAK,CAAC,CAAC,AACpD,IAAA,CAAA,KAAU,CAAG,GAAK,EAAA,AoB+DA,apB/DA,CAClB,IAAA,CAAK,YAAY,CAAG,IAAA,CAAK,KAAK,CoB+DC,ApB/DA,CoB+DC,CAAC,yBpB5DP,CAAS,CAAA,KAC7B,GAAU,WAAW,EAAE,AAC7B,IAAI,CAAC,KAAK,CAAG,GAAM,kBAAkB,CAAC,iBACrB,CAAG,IAAA,CAAK,KAAA,CAAQ,CAAC,CAAC,CAC5B,CAAC,GAAK,GoBiEC,ApBjES,WAAW,EAAE,KoBiEW,MpBhElC,CoBiEC,EpBjEK,CoBiEC,CAAC,CAAC,epBjEe,CAAC,KACjC,YAAY,CAAA,IAAO,CAAC,KAAK,CAAG,CAAC,CAAC,CAC3B,GAAa,CAAC,CAAC,EAAE,CACzB,IADoB,AAChB,CAAC,YAAY,CAAG,IAAI,CAAC,KAAK,CAC9B,AAD+B,IAC3B,CAAC,KAAK,CAAG,GAAM,EAAD,gBAAmB,CAAC,AACtC,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,AAE9C,CAF+C,kBAAkB,KAGlC,CAAA,CAAW,CAAa,CAAA,GoBmEnB,KpBhE3B,CAAC,IAAI,CAAC,cAAc,EAAA,IAAA,CAAS,aAAa,CAAC,QAC9C,AACM,CAAC,GAAG,CAAA,YAAa,CAAC,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,KAAK,CAAC,CAAC,AACrD,IAAI,CAAC,YAAY,CAAG,CAAC,CAAC,CAAC,IACnB,CAAC,GAAG,CAAC,WAAW,CAChB,IAAU,CAAL,EAAe,MAAD,KAAY,CACzB,GAAU,MAAM,AAAP,CACT,GAAU,MAAM,AAAP,CACf,IAAI,CAAC,KAAK,CACb,CAAC,AACF,IAAA,CAAK,KAAK,CAAG,GAAM,CoB+DC,CpB/DF,iBAAoB,CAAC,KAC5B,CAAA,cAAe,EAAI,CAAC,GAAK,GAAU,GAAG,EAAE,CACnD,AAD6C,IACzC,CAAA,SAAU,CAAG,IAAI,CAAC,KAAK,CAAC,IACxB,CAAC,KAAK,CAAG,GAAM,YAAY,CAAC,AAExC,CAAC,AACO,kCAAkC,CAAS,CAAA,CAC/C,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAE,GAAU,WAAA,CAC7C,CAAC,AACO,CoBgEN,iCpBhEwC,CAAS,CAAA,CAC/C,IAAA,CAAK,sBAAsB,CAAC,CAAC,CAAE,GAAU,MAAD,KAAY,CACxD,AADyD,CAAC,AACzD,AACO,8BAA8B,CAAS,CAAA,CACvC,GAAa,CAAC,CAAC,EAAI,CAAC,GAAK,CAAb,EAAuB,EAAE,EAAE,AACvC,EADkC,EAC9B,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,KAAK,CAAC,CAAC,AACrD,IAAI,CAAC,YAAY,CAAG,CAAC,CAAC,CAAC,AACvB,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,GAAU,MAAD,EAAS,CAAE,IAAI,CAAC,KAAK,CAAC,CAAC,AACrD,IAAI,CAAC,KAAK,CAAG,GAAM,EAAD,iBAAoB,CAAC,AACvC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAC1B,IAAI,CAAC,cAAc,EAAI,CAAC,GAAK,GAAU,GAAG,EAAE,CACnD,AAD6C,IACzC,CAAC,SAAS,CAAG,IAAI,CAAC,KAAK,CAAC,IACxB,CAAC,KAAK,CAAG,GAAM,EAAD,UAAa,CAAC,AAExC,CACQ,AADP,uBAC8B,CAAS,CAAA,CAChC,CAAC,GAAK,GAAU,MAAD,cAAqB,EAAE,IAClC,CAAC,KAAK,CAAG,GAAM,EAAD,WAAc,CAAC,KAC5B,aAAa,CAAG,CAAC,CAAC,CAEvB,IAAI,CAAC,KAAK,CACN,CAAC,GAAK,GAAU,IAAI,CACd,GAAM,EAAD,WAAc,CACnB,GAAM,EAAD,WAAc,AAErC,CAFsC,AAErC,AACO,mBAAmB,CAAS,CAAA,EAC5B,CAAC,GAAK,GAAU,EAAE,EAAI,EAAP,EAAW,CAAC,aAAa,CAAC,GAAU,GAAE,CAAC,EAAJ,AAAM,CACxD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,KAAK,CAAC,CAAC,AACtD,IAAI,CAAC,KAAK,CAAG,GAAM,IAAI,CAAC,AACxB,IAAI,CAAC,YAAY,CAAG,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,CAGnC,4BAA4B,CAAC,CAAS,CAAA,EACtC,CAAC,GAAK,GAAU,EAAE,EAAI,EAAP,EAAW,CAAC,aAAa,CAAC,GAAU,EAAE,CAAC,EAAE,CAAN,CAClD,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,KAAK,CAAC,CAC/D,AADgE,IAC5D,CAAC,KAAK,CAAG,GAAM,EAAD,EAAK,CAAC,AACxB,IAAI,CAAC,YAAY,CAAG,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,AAE3C,CAAC,AACO,kBAAkB,CAAC,CAAS,CAAA,CAC5B,CAAC,GAAK,GAAU,IAAI,EAAE,AACtB,AADe,IACX,CAAC,KAAK,CAAG,GAAM,EAAD,WAAc,CAAC,AACjC,IAAI,CAAC,eAAe,CAAG,GAAU,MAAD,IAAW,CAAC,AAE5C,IAAI,CAAC,aAAa,CAAG,CAAC,CAAC,AACvB,IAAI,CAAC,YAAY,CAAG,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,CAEnC,IAAI,CAAA,KAAM,CAAA,GAAS,aAAa,CAAC,sBAGX,CAAS,CAAA,EAC/B,CAAC,GAAK,GAAU,EAAE,EAAI,EAAP,EAAW,CAAC,aAAa,CAAC,GAAU,GAAE,CAAC,EAAJ,AAAM,CACxD,IAAI,CAAA,GAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,KAAK,CAAE,CAAC,CAAC,CAAC,IACjD,CAAC,KAAK,CAAA,GAAS,IAAI,CAAC,AACxB,IAAI,CAAC,YAAY,CAAG,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,AAE3C,CAAC,AACO,oBAAoB,CAAS,CAAA,KAC3B,EAAQ,AAAI,CAAH,EAAJ,CAAW,AAClB,CADmB,IACd,AAAK,GAAU,MAAD,GAAU,CAAC,CAAC,CAAC,CAChC,CADkC,GAC9B,CAAC,YAAY,CAAC,GAAU,MAAD,GAAU,CAAA,GAC9B,IAAU,CAAL,EAAe,QAAQ,CAAC,CAAC,CAAC,CACtC,CADwC,GACpC,CAAC,YAAY,CAAC,GAAU,MAAD,EAAS,CAAE,CAAC,CAAC,CAAC,CAEzC,IAAI,CAAC,KAAK,CAAG,GAAM,EAAD,OAAU,CAC5B,AAD6B,IACzB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,AAE/B,CAFgC,AAE/B,AAQO,iBAAiB,CAAC,CAAS,CAAA,CAE/B,IAAI,CAZkD,AAYjD,YAAY,CAAG,CAAC,CAAC,AACtB,IAAI,CAAC,YAAY,CAAG,CAAC,CAEjB,AAFkB,CAEjB,GAAK,GAAU,MAAD,AAAO,CACtB,CADwB,GACpB,CAAC,KAAK,CAAG,GAAM,EAAD,iBAAoB,CAAC,AAChC,CAAC,GAAK,GAAU,GAAG,EAAE,CAG5B,IAAI,CAAC,SAAS,CAAG,CAAC,CAAC,AACnB,IAAI,CAAC,WAAW,CAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,AACtC,IAAI,CAAC,KAAK,CAAG,GAAM,EAAD,WAAc,CAAC,AACjC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,AAEnC,CAAC,AAEO,kBAAkB,CAAC,CAAS,CAAA,CAUhC,GATA,IAAI,CAAC,YAAY,EAAI,CAAC,CAAC,AAEvB,IAAI,CAAC,SAAS,CAAG,GACb,IAAI,CAAC,OADuB,GACb,CACf,IAAI,CAAC,WAAW,CAChB,IAAI,CAAC,SAAS,CAAG,CAAC,CAClB,CAAC,CACJ,CAAC,AAEE,IAAI,CAAC,SAAS,CAAG,CAAC,CAAE,CACpB,IAAI,CAAC,eAAe,EAAE,CAAC,AACvB,IAAI,CAAC,KAAK,EAAE,CAAC,AACb,OAAO,AACV,AAED,IAAI,CAAC,WAAW,CAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,AAEnD,IAAM,EAAS,IAAH,AAAO,CAAC,WAAW,CAAG,GAAa,SAAD,GAAa,CAAC,AAG5D,GAAA,EAAY,CAER,IAAM,EAAc,CAAC,GAAU,EAAA,CAAJ,AAAM,CAAC,AAAG,CAApB,AAAqB,CAAC,AAGvC,GAAI,AAAC,IAAI,CAAC,iBAAiB,EAAE,EAAI,CAAC,GAAK,GAAU,IAAI,CAE9C,CAFyC,AAAO,AAInD,IAAM,EAAc,IAAI,CAAC,IAAR,CAAa,CAAG,IAAI,CAAC,YAAY,CAAG,CAAC,CAAC,AAEnD,EAAc,IAAI,CAAC,IAAR,QAAoB,EAAE,AACjC,IAAA,CAAK,WAAW,CAAC,IAAI,CAAC,YAAY,CAAE,GAIxC,IAAI,CAAC,GAJ8C,CAAC,CAAC,OAIpC,CAAG,IAAI,CAAC,SAAS,CAAC,AACnC,IAAA,CAAK,SAAS,EAAI,MACd,CAAC,IADwB,CAAC,OACb,CAAG,CAAC,CAAC,AACtB,IAAI,CAAC,YAAY,CAAG,IAAI,CAAC,KAAK,CAAG,CAAC,CAAC,AAEf,CAAC,EAAE,CAAnB,GACA,IAAI,CAAC,GADM,YACS,EAAE,CAAC,AAE9B,KAlBG,IAAI,CAAC,SAAS,EAAI,EAmBzB,AACL,CAAC,AAEO,QAtBiC,CAAC,MAsBnB,EAAA,CAGnB,GAFA,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,SAAS,CAAC,AAEF,CAAC,EAAE,CAAzB,IAAI,CAAC,YAAY,CAQrB,OAHI,AAGI,CAHH,IAAI,CAAC,KAGS,EAAE,GAHD,CAAC,IAAI,CAAC,YAAY,CAAC,CAAG,GAAa,SAAD,GAAC,AAAY,CAAC,EAChE,EAAE,CAAC,CAGH,KAAK,CAAC,CAAC,AACH,IAAI,CAAC,aAAa,CACd,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAC9B,CAAC,GAAa,SAAD,GAAa,CACjC,CACD,AADE,KAGN,CAFU,KAEL,CAAC,CAAC,AACH,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAG,CAAC,CAAC,CAAC,CAC1D,AAD2D,KAG/D,CAFU,KAEL,CAAC,CAAC,IACC,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAG,CAAC,CAAC,CAAC,CAAC,AAC3D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAG,CAAC,CAAC,CAAC,CAAC,0BAKtC,CAAS,CAAA,CAClC,CAAC,AAAI,CAAH,GAAG,CAAI,CAAC,EAAK,GAAU,MAAD,AAAO,EAAE,AACjC,IAAI,CAAC,YAAY,EAAE,CACnB,AADoB,IAChB,CAAC,KAAK,CAAG,GAAM,EAAD,SAAY,CAAC,EAE/B,IAAI,CAAC,KAAK,CAAG,GAAM,EAAD,aAAgB,CAAC,AACnC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,AAErC,CAAC,AAEO,iBAAiB,CAAC,CAAe,CAAA,CACrC,IAAM,EAAc,IAAI,CAAC,IAAR,CAAa,CAAG,IAAI,CAAC,YAAY,CAAG,CAAC,CAAC,AAEnD,EAAc,CAAC,CAAG,MAAM,CAAb,AAAc,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,SAAY,CAAC,CAAC,EAE3C,IAAI,CAAC,KAAK,EAAE,CAExB,EAAc,IAAI,CAAC,YAAY,EAAE,AACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAE,GAGxC,IAAI,CAAC,GAH8C,CAAC,CAAC,OAGpC,CAAG,IAAI,CAAC,KAAK,CAAG,MAAM,CAAC,GACxC,GAD8C,CAAC,AAC3C,CAAC,AAD2C,aAC9B,CAAC,GAAiB,IAAI,CAAC,QAAN,IAAkB,CAAC,CAAC,CAAC,CAE5D,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,SACtB,AAD+B,CAAC,AAC/B,AACO,qBAAqB,CAAS,CAAA,CAC9B,IAAM,GAAU,IAAA,CAChB,CADe,GACX,CAAC,iBAAiB,EAAC,GAChB,CADoB,CAAC,CAAC,AACb,CAAC,CAAC,EAAE,AACpB,CADe,GACX,CAAC,YAAY,CAAG,AAAoB,EAAE,GAAG,EAArB,CAAC,YAAY,EAAS,CAAC,CAAG,GAAU,IAAA,AAAI,CAAC,CAAN,AAAO,AAClE,IAAI,CAAC,YAAY,EAAE,CAAC,EAEhB,IAAI,CAAC,iBAAiB,EAAE,CACxB,CAD0B,GACtB,CAAC,iBAAiB,EAAC,GAEvB,EAF4B,CAAC,CAAC,AAE1B,CAAC,KAAK,CAAG,IAAI,CAAC,SAAS,CAAC,AAEhC,IAAI,CAAC,KAAK,EAAE,CAAC,AAErB,CAAC,iBACwB,CAAS,CAAA,CAC9B,GAAI,CAAC,GAAK,GAAU,IAAI,CACpB,CADe,AAAO,GAClB,CAAC,iBAAiB,EAAC,IAAI,CAAC,CAAC,EAC1B,GAAI,GAAS,CAAC,CAAC,CAClB,CADoB,CAAL,EACf,CAAK,YAAY,CAAG,AAAoB,EAAE,GAAG,EAArB,CAAC,YAAY,EAAS,CAAC,CAAG,GAAU,IAAA,AAAI,CAAC,CAAN,AAAO,AAClE,IAAI,CAAC,YAAY,EAAE,CAAC,KACb,GAjpBL,GAAU,MAAM,EAipBX,AAjpBe,CAAC,AuBII,CAAC,CHLF,CAAA,EpBCW,MAAM,CAAC,CAC/C,AAgpBU,GAhpBL,GAAU,MAAM,EAAA,AAgpBX,GAhpBoB,CAgpBL,EAhpBe,EMmEE,CAAC,GNnEG,CAAC,CACnD,CAAC,CuBGqD,CAAC,CAAC,AvB6oB7C,CAAC,YAAY,CACO,AADP,EACS,CADT,EACY,EAArB,CAAC,YAAY,EAAS,CAAK,GAAJ,CAAC,AAAG,CAAI,CAAI,AAAH,GAAa,MAAD,AAAO,CAAG,EAAA,CAAE,CAAC,AACjE,CADkE,GAC9D,CAAC,YAAY,EAAE,CAAC,EAEhB,IAAI,CAAC,iBAAiB,EAAE,CACxB,CAD0B,GACtB,CAAC,iBAAiB,EAAC,GAEvB,EAF4B,CAAC,CAAC,AAE1B,CAAC,KAAK,CAAG,IAAI,CAAC,SAAS,CAAC,AAEhC,IAAI,CAAC,KAAK,EAAE,CAEpB,AAFqB,CAEpB,AAEO,mBAAiB,CACrB,MACI,CAAC,IAAI,CAAC,OAAO,GACZ,CAAD,GAAK,CAAC,SAAS,GAAK,GAAM,IAAI,EAC1B,IAAI,CAAC,SAAS,GAAK,GAAM,YAAA,AAAY,CAAC,AAElD,CADK,AACJ,CADK,QAMS,CAEP,IAAI,CAAC,OAAO,EAAI,IAAI,CAAC,YAAY,GAAK,IAAI,CAAC,KAAK,EAAE,CAE9C,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,EAAK,EACxB,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,UAAa,EAA2B,CAAC,CAAC,CACjE,CADwC,IAAI,CAAC,aAAa,EAExD,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,KAAK,CAAC,CAAC,AAC/C,IAAI,CAAC,YAAY,CAAG,IAAI,CAAC,KAAK,CAAC,CAE/B,KAAI,CAAC,KAAK,GAAK,GAAM,EAAD,gBAAmB,EACvC,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,gBAAmB,EACvC,IAAI,CAAA,KAAM,GAAK,GAAM,kBAAA,AAAkB,EACzC,EACE,IAAI,CAAC,GAAA,CAAI,YAAY,CAAC,IAAI,CAAC,YAAY,CAAE,IAAI,CAAC,KAAK,CAAC,CAAC,AACrD,IAAI,CAAC,YAAY,CAAA,IAAO,CAAC,KAAK,CAAC,EAKnC,cAAc,EAAA,CAClB,OAAO,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAG,IAAI,CAAC,MAAM,EAAI,IAAI,CAAC,OAAO,AACxE,CAOQ,AARiE,AACxE,KAOY,EAAA,CACT,KAAO,IAAI,CAAC,cAAc,EAAE,EAAE,CAC1B,IAAM,CAAC,CAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAG,IAAI,CAAC,MAAM,CAAC,CAAC,AAC3D,OAAQ,IAAI,CAAC,KAAK,EAAE,AAChB,KAAK,GAAM,EAAD,EAAK,CAAC,AACZ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,AAClB,MAAM,KAEL,GAAM,EAAD,kBAAqB,CAC3B,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,AAClC,KAEJ,MAAK,GAAM,YAAY,CAAC,AACpB,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,AAC1B,KAEJ,CAFU,KAEL,GAAM,aAAa,CAAC,AACrB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,AAC3B,KAEJ,CAFU,KAEL,GAAM,EAAD,gBAAmB,CACzB,AAD0B,IACtB,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,AAC1C,KAEJ,CAFU,KAEL,GAAM,EAAD,aAAgB,CAAC,AACvB,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAC5B,AAD6B,KAGjC,CAFU,KAEL,GAAM,EAAD,WAAc,CAAC,AACrB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAC1B,AAD2B,KAG/B,CAFU,KAEL,GAAM,EAAD,cAAC,CACP,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,AAC9B,KAEJ,CAFU,KAEL,GAAM,EAAD,iBAAoB,CAAC,AAC3B,IAAA,CAAK,wBAAwB,CAAC,CAAC,CAAC,CAAC,AACjC,KAEJ,MAAK,GAAM,EAAD,OAAU,CAAC,AACjB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CACtB,AADuB,KAG3B,MAAA,GAAW,gBAAgB,CAAC,AACxB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,KAGlC,MAAK,GAAM,EAAD,WAAc,CAAC,AACrB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,AAC3B,KAEJ,CAFU,KAEL,GAAM,EAAD,gBAAmB,CACzB,AAD0B,IACtB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,AAChC,KAEJ,CAFU,KAEL,GAAM,EAAD,gBAAmB,CAAC,AAC1B,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC,AAC1C,KAEJ,CAFU,KAEL,GAAM,oBAAoB,CAAC,AAC5B,IAAA,CAAK,yBAAyB,CAAC,CAAC,CAAC,CAAC,AAClC,KAEJ,CAFU,KAEL,GAAM,oBAAoB,CAC3B,AAD4B,IACxB,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC,AAClC,KAEJ,MAAK,GAAM,EAAD,iBAAoB,CAAC,AAC3B,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,AACjC,KAEJ,CAFU,KAEV,GAAW,cAAc,CAAC,AACtB,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,AAC5B,KAEJ,CAFU,KAEV,GAAW,kBAAkB,CAAC,AAC1B,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,AACtC,KAEJ,CAFU,KAEL,GAAM,EAAD,cAAiB,CACvB,AADwB,IACpB,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,AAC9B,KAEJ,CAFU,KAEL,GAAM,EAAD,WAAc,CAAC,AACrB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,AAC3B,KAEJ,CAFU,KAEL,GAAM,EAAD,eAAkB,CACxB,AADyB,IACrB,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,AAC/B,KAEJ,CAFU,KAEL,GAAM,EAAD,WAAc,CAAC,AACrB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,AAC3B,KAEJ,CAFU,KAEL,GAAM,EAAD,qBAAwB,CAAC,AAC/B,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,AACrC,KAEJ,MAAK,GAAM,EAAD,WAAc,CAAC,AACrB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,AAC3B,KAEJ,CAFU,KAEL,GAAM,EAAD,UAAa,CAAC,AACpB,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,AAC1B,KAEJ,CAFU,KAEL,GAAM,EAAD,SAAY,CAAC,AACnB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,AACzB,KAEJ,CAFU,KAEL,GAAM,EAAD,aAAgB,CAAC,AACvB,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,AAC7B,KAEJ,CAFU,MAEH,CAAC,CAEJ,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,AAExC,AACD,IAAI,CAAC,KAAK,EAAE,CAAC,AAChB,AACD,IAAI,CAAC,OAAO,EAAE,CAAC,QAGL,CACV,IAAQ,CAAA,KAAM,GAAA,GAAW,aAAA,EAAe,AACpC,IAAI,CAAC,eAAe,EAAE,CAAC,AAIvB,IAAI,CAAC,YAAY,CAAG,IAAI,CAAC,KAAK,EAAE,AAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC,AAE9B,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,AACpB,CADqB,AACpB,AAGO,kBAAkB,EAAA,CACtB,IAAM,EAAW,IAAI,CAAC,CAAR,KAAc,CAAC,MAAM,CAAG,IAAI,CAAC,MAAM,AAC7C,CAD8C,IAC1C,CAAC,KAAK,GAAK,GAAM,EAAD,WAAc,CAC9B,CADgC,GAC5B,CAAC,eAAe,GAAK,GAAU,MAAD,EAAS,CAC3C,CAD6C,GACzC,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAE,EAAU,CAAC,CAAC,CAAC,AAEjD,GAF4C,CAExC,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAE,EAAU,CAAC,CAAC,CAAC,AAGvD,GAHkD,CAG9C,CAAC,KAAK,GAAK,GAAM,EAAD,aAAgB,EACpC,IAAI,CAAC,iBAAiB,EAAE,EAKxB,AAJF,IAIM,CAAC,KAAK,GAAK,GAAM,EAAD,SAAY,EAChC,IAAI,CAAC,iBAAiB,EAAE,CAExB,CADF,GACM,CAAC,iBAAiB,CAAC,IAGvB,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,OAAU,EAC9B,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,iBAAoB,EACxC,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,kBAAqB,EACzC,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,gBAAmB,EACvC,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,aAAgB,EACpC,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,gBAAmB,EACvC,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,gBAAmB,EACvC,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,gBAAmB,EACvC,IAAI,CAAC,KAAK,GAAK,GAAM,EAAD,cAAiB,EACvC,AAME,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAE,QAAQ,CAAC,CAAC,KAIjC,CAAa,CAAE,CAAA,CAAgB,CAE3C,IAAI,CAAC,SAAA,GAAc,GAAM,EAAD,EAAK,EAC7B,IAAI,CAAC,SAAS,GAAK,GAAM,EAAD,UAAa,CAErC,CADF,GACM,CAAC,GAAG,CAAC,YAAY,CAAC,EAAO,GAAF,AAE3B,IAAI,CAAC,AAFgC,CAAC,CAAC,CAE/B,CAAC,MAAM,CAAC,EAAO,EAE/B,CAF6B,AAE5B,AACO,KAH+B,CAAC,CAAC,MAGpB,CAAC,CAAU,CAAA,CAExB,IAAI,CAAC,SAAS,GAAK,GAAM,EAAD,EAAK,EAC7B,IAAI,CAAC,SAAS,GAAK,GAAM,EAAD,UAAa,CAErC,CADF,GACM,CAAC,GAAG,CAAA,cAAe,CAAC,EAAE,CAE1B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,AAEhC,CAFiC,AAEhC,CAFiC,AAGrC,yFK/+BY,IAAA,IAAA,CAAS,iBACW,CAAC,6FAKd,KAAM,YAAa,OAC5B,IAAI,IAAA,MAAU,CAAC,AWqBS,CXrBR,CWqBU,MXpB1B,IAAA,IAAQ,CAAC,QAAS,cACxB,uCAA0C,CAAC,CAAC,GeEjC,CAAA,GfDL,IAAA,IAAQ,UACd,cACM,GAAK,iBAEL,KACN,KAAM,KACN,QAAW,OACL,KACN,uCAEmB,WACT,GAAS,aACP,mBACS,EACrB,SAAU,IAAA,IAAQ,cAClB,WAAY,CDEC,GAAA,IAAA,CCFQ,WAAY,SAAS,CAAC,CAAC,EAC5C,KAAM,KACN,UACA,eACA,eACA,QAAS,KACT,sEAKA,CIwBC,iBJvBD,EIsBE,0BJpBF,OAAQ,KACR,cACA,KAAM,KACN,CgBWC,AHRA,MbHO,GAAK,EACb,CNKC,eMJK,KACN,SAAY,EACZ,CJUC,AkBAA,YdVe,CACjB,CAAC,WAAc,CACf,CAAC,eACM,KACN,QAAc,EACd,QAAS,GAAiB,EAC1B,YACJ,CAAC,CAAC,AAEG,GAAA,IAAA,IAAA,kJAoBL,EAEK,GAAyB,IAAI,IAAI,CAAC,OAAQ,MAAM,CAAC,CAAC,AAElD,GAAA,IAAA,IAAkC,oHA0IU,CAAE,CAAA,4BAA3B,kCA3BH,CAAC,AkBjEM,mBlBsEF,CAAC,cAEN,CezEE,CfyEA,iBACC,CezEE,mBf0ED,EAAE,CAAC,YACV,CAAqC,kBACf,CACpB,AADqB,CCJjB,GDKJ,CAAA,cAAc,CAAc,CLzDtB,AMqDI,CDIoB,aAMvB,CAAA,EAAA,kBACJ,CAAA,gCAIJ,OAMP,GAAA,CAAA,MAAA,EAAM,Ca/FkC,Cb+F3B,CAAA,CAAE,CW1Db,AX0Dc,IACjB,CAAC,iBAAiB,CAAA,OAAG,EAAA,EAAQ,CCVO,AgBlFA,YAAA,AjB4FM,EAAA,EAAI,CAAC,CAAL,CAAa,KAAD,EAAZ,AAAoB,CAAC,IAC/D,CAAA,uBAAwB,CiB5FG,MAAA,CAAA,EAAA,EjB6FnB,uBAAA,AAAuB,EAAA,EAAI,CAAC,CAAL,CAAa,OAAO,CAAC,4BAClC,EAAA,SAAA,AAAiB,EAAA,EAAI,EAAJ,AAAI,CAAS,CAAC,IAC7C,CAAC,CAD8B,MACvB,CACZ,AAFmC,IAE/B,CACP,AAHsC,CAGrC,gBACE,CAAA,GAAA,EAAA,YAAA,GAAA,EAAA,IAAA,CAAA,EAAoB,IAAI,CAAC,CAAC,AAKlC,OACO,CAAa,CAAA,CAAA,CAAA,oBACE,QAAA,CAAS,EAAO,OAC9B,CAAC,QAAA,CAAA,EAAA,WACL,CAAA,EAAA,EkBtEc,ElBsEV,CAAC,GAAA,EAAA,MAAU,AAAV,GWrDiB,CAAA,CAAA,IAAA,CAAA,EXqDJ,IAAI,CAAC,CAAC,aACN,EkBpErB,alBwEY,CAAU,CAAA,aAKb,EAAA,IAAA,CAAA,SAAA,CAAuB,eAAe,gBAC/B,CAAA,EAAW,CAAC,UACzB,GAAA,IAAA,CAAA,GAAA,EAAS,MAAA,GAAA,EAAA,IAAA,CAAA,EAAS,GAAc,oBACd,iBAGc,CAAA,OACzB,CAAC,IAAI,CAAC,OAAA,CAAQ,OAAO,EAAI,GAAa,GAAG,CAAC,GAIrD,cAAc,CAAa,CAAE,CAAgB,CAAA,KACrC,CAAC,CCNC,ODMO,CAAA,MAET,EAAA,IAAA,CAAA,QAAoB,CAAC,EAAO,EAEhC,CCLmC,ADKnC,IAAQ,CAAA,iBAAkB,EAAE,CACxB,EAAA,EAAY,WAAW,EAAA,CAAE,CAAC,gCAMd,CAAA,CAAA,mCACQ,CAAC,CerFC,CAAC,QAAA,CfsF3B,IAAA,CAAA,OAAY,CAAG,CLnEC,CKqEhB,CLpEC,GKoEK,EACF,CAAC,CevFkB,GfuFd,CAAC,OAAO,CAAC,OAAO,EAAI,GAAiB,GAAG,CAAC,GAElD,CAFsD,CAAC,CAAC,AAExD,EACI,CCDC,ADFwC,ICExC,IDEO,CAAC,KAAA,CAAA,MAAA,CAAe,CAAC,EACrB,EAAa,GAAG,CAAC,IAAI,CAAC,CexFC,IfwFI,CAAC,IAAI,CAAA,KAAM,CAAC,MAAM,CAAG,CAAC,CAAC,CAAC,EACrD,KACQ,CCJ+B,CDIrB,IAAI,CAAC,KAAK,CAAA,GAAI,EAC9B,QAAA,EAAA,GAAA,IAAI,CAAC,GAAA,AAAG,EAAC,UAAA,AAAU,GAAA,EAAA,CAAA,GAAA,CAAA,EAAG,EAAH,CAAY,IAAF,AAAM,AAGtC,CAHuC,CAAjB,AAAkB,EAGpC,CAAA,EAHkB,IAAA,KAAA,EAGlB,CAAe,SAChB,CAAC,KAAA,CAAA,IAAU,CAAC,aAEZ,IADkC,AAC9B,CAAC,cAAc,CAAC,IAAI,CAAA,CAAA,GACrB,GAA4B,CCFD,EDEI,CAAC,IACnC,GAD0C,CAC1C,CAAK,cAAc,CAAC,IAAI,CAAC,KAGjC,OAAA,EAAA,CAAA,EAAA,IAAI,CAAC,GAAA,EAAI,aAAA,AAAa,GAAA,EAAA,IAAA,CAAA,EAAG,IAAI,CAAC,CAAC,eACT,GAAE,CLjEnB,GKiEuB,CAAC,OAAO,CAAG,EAAA,CAC3C,CAEQ,AAFP,WAEkB,CAAA,CAAkB,QACjC,KAAA,CAAA,UAAe,CAAG,IAAI,CAAC,YAAY,CAAC,YAEpB,EAAE,CACd,GLpEkB,GKoElB,GAAA,CAAA,EAAA,IAAA,CAAK,GAAA,EAAA,SAAA,AAAa,GAAA,EAAA,CAAA,GAAA,CAAA,EAAG,EAAH,EAAO,CAAC,IAAR,GAAe,CAAE,CAAjB,GAAqB,CAArB,AAAsB,KAAtB,EAA6B,CAAE,GACjD,IAAI,CAAC,CADqD,CAAC,CAAC,Ce/EnD,GAAA,CAAA,cfkFD,CAAC,UAAA,EAAc,IAAI,CAAA,aAAc,CAAC,EehFE,EfgFE,CAAC,EehFE,CAAC,IAAA,GfiFlD,AADyD,IACrD,CAAA,GAAI,CAAA,UAAW,CAAC,IAAI,CAAC,OAAA,EAAS,GAGtC,IAAA,CAAK,OAAO,CAAG,gBAIN,CAAgB,CAAA,CACzB,IAAA,CAAK,QAAQ,CAAG,iBACD,EAAC,GAGhB,EAHqB,CAAC,CAGtB,CAAK,UAAU,CAAA,EAAc,CAAC,CAAC,AAInC,WAAW,CAAa,CAAE,CAAgB,CAAA,8BACzB,CAAA,QAEF,IAAA,CAAK,QAAQ,CAAC,EAAO,GAahC,GAXI,CAWA,GAXI,CAAC,iBAAiB,EAAE,CACxB,EAAO,EAAA,WAAA,EAAA,SAImB,CAAC,IAC3B,GAAA,GAAA,CAA4B,EAAA,GAE5B,IAAA,CAAA,cAAA,CAAoB,GAAG,GAGtB,IAAI,CAAC,aAAa,CAAC,GAeZ,IAAI,CAAC,OAAA,CAAQ,OAAO,EAAQ,MAAW,CAAf,IAAI,UAEpC,EAAA,IAAI,CAAA,GAAA,AAAI,EAAA,aAAA,AAAc,GAAA,EAAA,CAAA,GAAA,CAAA,EAAG,EAAH,EAAO,CAAC,CAAC,GAAT,IACtB,CADsB,CACtB,CAAA,EADsB,AACtB,IAAI,CADkB,AACjB,GAAA,AAAG,EAAC,SAAA,AAAS,GAAA,EAAA,IAAA,CAAA,EAAG,IAAI,CAAE,CAAA,CAAE,EAAE,GAC/B,CADmC,CAAC,CAAC,IACrC,EAAA,CAAA,EAAA,IAAI,CAAC,GAAA,AAAG,EAAC,UAAA,AAAU,GAAA,EAAA,CAAA,GAAA,CAAA,EAAG,EAAH,EAAO,EAAE,KAAK,CAAC,CAAC,CAnBR,CAC3B,IAAM,EAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,AACrC,KAAA,UACS,GAAG,CAAC,UAAU,CAAE,GLtEG,CAAC,CKuErB,EAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAG,CevFC,MfwF1B,KAEH,GAFY,CAER,CAAC,ALvES,GKuEN,CAAC,UAAA,CAAA,IAAe,CAAC,KAAK,CAAC,GAAG,EAAG,CAAY,CAAC,CAAC,CAAC,CAAb,KAAK,GAE7C,IAAI,CAAA,KAAM,CAAC,MAAM,CAAG,OACnB,IAAI,CAAC,OAAA,CAAA,OAAe,EAAa,GAAG,EAAE,CAAd,IAAI,AAEpC,IAAI,CAAA,WAAY,CAAC,2BACI,IAE5B,KAQI,CARE,IAAI,KAQN,CAAa,EAAW,CAAC,CAAC,AAInC,iBAAiB,CAAgB,CAAA,CAC7B,CLpEC,AoBbA,GfiFG,CAAC,QAAQ,CAAG,MAER,CAAA,OAAQ,CAAC,OAAA,EAAA,IAAA,CACR,CejFC,MAAA,CAAA,oBfiF2B,EAAA,IAAA,CAAA,cACd,CAAC,IAAI,CAAC,cAAc,CAAA,MAAO,CAAG,CAAC,CAAC,EACrD,KACO,eAAe,CAAA,CAAA,GAGpB,IAAA,CAAK,UAAU,CAAA,EAAA,QAGV,KejFK,OfiFO,CAAC,GAIlB,gBAAgB,CAAsB,CAAA,SAC1C,CLtEC,GKsEK,EAAO,IAAI,CAAC,OAAO,CAAC,KACrB,UAAU,CAAC,GAGZ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAA,EAAK,GAAK,IAAI,AAE1C,EAF4C,KAE5C,EAAA,CAAA,EAAA,IAAI,CAAC,GAAG,AAAH,EAAI,UAAA,AAAU,GAAA,EAAA,CAAA,GAAA,CAAA,EAAG,EAAH,AAAS,CAAC,CAAH,EAC1B,GADmB,CACnB,CAAK,GADc,EAAuB,AACrC,CADsC,AACtC,CADc,AAAyB,EAC9B,GAEtB,AAH2B,CAG1B,AAED,aACa,CAAa,CAAE,CAAgB,CAAA,KACpC,CAAC,UAAA,CAAa,YACD,CAAA,QAAS,CAAA,EAAQ,QAAQ,CAAC,CAAC,SAE1B,IAAA,CAAA,uBAA4B,CACxC,EAAK,WAAA,GACL,CACV,CAGA,AAHC,aAGD,CAA0B,CAAE,CAAgB,CAAA,CACxC,IAAI,CAAC,WAAW,EAAI,IAAI,CAAC,QAAQ,CAAC,EAAO,EAC7C,CAAC,AAED,eAAA,CAAA,CAAA,CAEI,IAAI,CAAC,WAAA,EAAe,GAAc,EAAE,CAAC,AAIzC,CAJ0C,WAI9B,CAAgB,CAAE,CAAgB,CAAA,QAC1C,KAAI,CAAC,QAAQ,CAAG,SAEhB,EAAA,CAAA,EAAA,IAAI,CAAC,GAAA,AAAG,EAAC,WAAA,AAAW,GAAA,EAAA,CAAA,GAAA,CAAA,EAChB,EADgB,EACZ,CAAC,IADW,KAAA,CACD,CACf,EAFgB,EAEZ,CAAC,EAFW,SAEA,CAChB,IAAU,CAAL,EAAe,MAAD,AAAO,CACpB,IACA,IAAU,CAAL,EAAe,CL3ER,KK2Ec,CAAA,Ie9EK,IfgFrB,GAAU,OAAA,MACpB,EACA,MAIN,IAAA,CAAA,OAAY,EACZ,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,UAAU,CAAC,EACtE,CACE,IAAA,CAAK,OAAA,CAAA,IAAA,CAAA,UAAuB,CAAC,CAAA,IAAO,CAAC,WAAA,AAAW,CAAC,CAErD,IAAA,CAAK,WAAA,CAAc,EACvB,CAEQ,AAFP,mBAE0B,CAAa,CAAA,CACpC,IAAA,EAAc,EAAM,MAAA,CAAA,IAChB,Ce7EC,Cf6EM,EAAQ,CAAC,CAAG,EAAQ,EAAM,GAAD,GAAO,CAAA,EAAI,KAAK,CAAC,CAAC,GAElD,IAAI,CAAA,iBAAkB,EAAE,KACZ,WAAW,EAAA,EAGpB,EAIX,cAAc,CAAA,CAAe,CAAgB,CAAA,KACrC,CAAC,QAAQ,CAAG,MACV,EADkB,AACV,IAAI,CAAC,QAAQ,CAAC,EAAO,GAEnC,CLtFC,EKsFG,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAE,KAC5B,EAAO,IAAA,CAAK,kBAAkB,CAAC,GACrC,EAD0C,CAAC,CAAC,AACxC,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA,CAAA,EAAI,EAAA,CAAM,CAAE,CAAA,CAAA,EAAI,Ce9Ef,CAAA,Cf8EsB,CAAC,CAAC,IAI1D,CAAC,UAAU,CAAG,EAAA,CACtB,CAAC,Ae7EA,AfgFD,wBAAwB,CAAa,CAAA,CAAkB,CAAA,CACnD,IAAI,CAAC,QAAQ,CAAG,EAChB,IAAM,EAAQ,IAAA,CAAK,QAAQ,CAAC,EAAO,GAEnC,GAAI,IAAI,CAAA,GAAI,CAAC,uBAAuB,CAAE,KAC5B,EAAA,IAAA,CAAY,kBAAkB,CAAC,KAAK,CAAC,CAAC,AACxC,CAAC,GAAG,CAAA,uBAAwB,CAAC,CAAA,CAAA,EAAI,EAAA,CAAM,CAAE,CAAA,CAAA,EAAI,EAAK,CAAE,CAAC,CAAH,AAAI,IAI1D,CAAC,UAAA,CAAa,EAAW,CAAC,CAIlC,UAAU,CAAa,CAAE,CAAgB,CLxFf,AKwFiB,CAAc,CAAA,iBACjD,CAAC,QAAQ,CAAG,EAEhB,MAAA,GAAA,GAAA,IAAA,CAAK,GAAA,AAAG,EAAC,ELzFiB,OAAA,AKyFR,GAAA,EAAA,IAAA,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAC,EAAO,EAAW,CAAb,GACxC,EADkD,AAAS,CAAC,CAAC,CAAC,CAC9D,GAAA,CAAA,EAAA,IAAI,CAAA,GAAA,AAAI,EAAC,YAAA,GAAA,EAAA,IAAA,CAAA,GAGT,IAAA,CAAK,UAAU,CexEC,AfwEE,EAAW,CAAC,CAAC,AAInC,QAAA,CAAA,CAAuB,CAAgB,CAAA,CAAgB,CAAA,yBACnD,IAAI,CAAC,QAAA,CAAW,EAChB,IAAM,EAAQ,IAAI,CAAC,QAAQ,CAAC,EAAO,EAAW,CAAb,CAE7B,IAFgD,CAAC,AAE7C,CAF8C,AAE9C,OAAQ,CAAC,OAAA,EAAA,IAAe,CAAA,OAAA,CAAS,cAAc,EAAE,AACrD,OAAA,EAAA,CAAA,EAAA,IAAI,CAAA,GAAI,AAAJ,EAAK,YAAA,AAAY,GAAA,EAAA,CAAA,GAAA,CAAA,GAAI,AACzB,CADqB,AAAK,MAC1B,CADqB,CACrB,GAAA,CADqB,GACjB,CADiB,AAChB,GAAA,AAAG,EADa,AACZ,MAAM,AAAN,EL7FqB,CAAA,EAAA,IAAA,CAAA,EK6FZ,GAClB,EADuB,CAAC,CAAC,GACzB,EAAA,CAAA,EAAA,IAAI,CAAA,GAAA,AAAI,EAAC,UAAA,AAAU,GAAA,EAAA,IAAA,CAAA,KAEnB,OAAA,EAAA,GAAA,IAAI,CAAC,GAAA,AAAG,EAAC,SAAA,AAAS,GAAA,EAAA,CAAA,GAAA,CAAA,EAAG,CAAA,OAAA,EAAU,EAAK,EAAA,CAAA,AAAI,CAAC,CAAC,AAC1C,MAAA,CAAA,EAAA,CAAA,EAAA,IAAI,CAAA,GAAA,AAAI,EAAC,YAAA,AAAY,GAAA,EAAA,CAAA,GAAA,CAAA,GAAI,CAAJ,AAAK,IAI1B,CAAA,EAJqB,KAAA,GAIrB,CAJqB,AAIP,EAAW,CAAC,CAAC,AAGnC,CAP6B,MAO7B,SAEI,GAAI,IAAA,CAAK,GAAG,CAAC,UAAU,CAAE,MAEhB,QAAQ,CAAG,IAAI,CAAC,UAAU,KAE3B,IAAI,EAAA,IAAA,CAAA,KAAkB,CAAC,MAAA,CACvB,EAAQ,CAAC,CACT,IAAA,CAAK,GAAG,CAAC,UAAA,CAAW,IAAI,CAAA,KAAM,CAAC,EAAE,EAAM,EAAE,CAAH,aAG9C,CAAA,EAAA,IAAI,CAAC,GAAA,AAAG,EAAC,KAAA,AAAK,GAAA,EAAA,CAAA,GAAA,CAAA,IAAA,MAMN,CANM,KAAA,IAAA,KAAA,OAOd,CAAA,EAAA,IAAA,CAAK,GAAA,AAAG,EAAA,OAAA,AAAQ,GAAA,EAAA,CAAA,GAAA,CAAA,GAAI,CAAJ,AAAK,GACjB,CAAC,GADW,KAAA,CACF,CAAA,EADE,GACI,EADJ,KAEZ,CAAC,OAAA,CAAA,GACL,IAAI,CAAC,UAAU,CAAG,EAAE,CACpB,IAAI,CAAC,GLpGG,IKoGI,CAAG,KACf,IAAI,CAAC,KAAK,CAAC,MAAM,CAAG,CAAC,CAAC,IAClB,CAAC,UAAU,CAAG,CAAC,CAAC,AACpB,CLpGC,GKoGG,CAAC,QAAQ,CAAG,SAChB,EAAA,CAAA,EAAA,IAAI,CAAC,GAAA,AAAG,EAAC,YAAA,AAAY,GAAA,EAAA,CAAA,GAAA,CAAA,EAAG,EAAH,EAAO,CAAC,CAAC,AAC9B,GADqB,CACjB,CAAA,GADiB,IAAA,AACjB,CAAS,IADQ,ALnGI,EAAA,CAAA,OKqGpB,YAAY,CLpGE,CAAA,KKqGf,CAAC,UAAU,CAAA,EACf,IAAA,CAAK,KAAK,EAAG,CACjB,CAAC,AAQM,cAAc,CAAY,Ce9DxB,Mf+DA,KAAK,GACV,IAAA,CAAK,GAAA,CAAA,aAGqB,CAAE,CAAW,CAAA,MAChC,EAAQ,IAAI,CAAC,YAAY,EAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAE,KACpD,CAAA,WAAA,OAGJ,EAAQ,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAC7B,EAAQ,GAAH,CAAO,CAAC,YAAY,CACzB,EAAM,CAAH,GAAO,CAAC,YAAY,CAC1B,CAAC,KAEK,EAAA,IAAU,CAAA,YAAa,CAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAE,CACrD,IAAA,CAAK,WAAW,GAChB,CL3GC,EK2GQ,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAE,EAAM,CAAH,GAAO,CAAC,YAAY,CAAC,CAG9D,AAH+D,OAGxD,oBAIH,CAAA,YAAa,EAAA,IAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CL9GC,CAAC,IK8GI,CAAC,AAC5C,CL9GC,GAAA,CK8GI,UAAU,GACf,IAAA,CAAK,OAAO,CAAC,KAAK,EACtB,CAAC,MAOY,CAAa,CAAA,gBACd,CAAC,KAAK,CAAA,CACV,OAAA,EAAA,CAAA,EAAA,IAAI,CAAC,GAAA,AAAG,EAAC,OAAA,AAAO,GAAA,EAAA,IAAA,CAAA,EAAG,AAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,OAI1D,IAAA,CAAK,OAAO,CAAC,IAAI,CAAA,GACjB,IAAA,CAAA,SAAA,CAAmB,OAAO,CLjHC,EKkHvB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GACrB,EAD0B,CAAC,CACvB,AADwB,CACxB,UAAW,GAEvB,CAAC,kBAQO,IAAI,CAAC,KAAK,CetDC,AfsDC,CACZ,OAAA,EAAA,CAAA,CetDQ,CfsDR,IAAI,CAAC,GAAG,AAAH,EAAG,OAAA,AAAQ,GAAA,EAAA,CAAA,GAAA,CAAA,EAAG,AAAI,EAAP,GAAY,CAAC,GAAb,KAAA,IAAA,KAAA,GAAiC,CAAC,CAAC,CAAC,OAIpD,CLnHC,EKmHM,EAAF,EAAE,CAAK,KAAK,CAAA,GACrB,CLlHC,GKkHG,CAAC,CetDC,IfsDI,EAAG,AetDD,EfuDZ,CetDC,GfsDG,CAAC,SAAS,CAAC,GAAG,EAAE,AACxB,CADyB,AACxB,GLpH6B,SK0HtB,CAAC,SAAS,CAAC,KAAA,EACnB,CAAC,QAKY,SACL,CAAC,SAAS,CAAC,ILtHI,EKsHE,GAGjB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAA,IAAA,CACjB,UAAU,CAAG,IAAI,CAAC,OAAA,CAAA,MAAc,EAErC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,AAGzD,CAH0D,AAG1D,IAAA,CAAA,KAAA,EAAA,IAAoB,CAAA,SAAU,CAAC,GAAA,EACnC,CAAC,WAQiB,CAAa,CAAA,KACvB,CAAC,KAAK,CAAC,EACf,CAAC,CL5HkB,CK2HC,CAAC,CAAC,CAQV,CAAA,CAAc,CACtB,IAAA,CAAA,GAAQ,CAAA,EACZ,CAAC,CACJ,8BJnpBK,GAAA,IAAA,IAAqB,EKAE,ALCxB,CODyB,AHDA,AECA,AEDA,sCRIZ,iBAET,QACR,CAAC,CAAC,AkBOA,GlBFiC,mCAAA,CACzB,EAAa,IAAA,EAAA,WAAA,CAAA,GAAA,CAEb,EAAA,IAAA,CAAA,AAC0B,MAD1B,EAAA,QAJgE,EAIhE,CAAA,EAC0B,CAAM,CAAC,CmBC3B,AXWoC,ARZJ,CmBChC,AXWqC,CAAC,IRZA,CAAA,EAChC,CiBFL,SAAA,CAAA,GAAA,KAAA,EAAA,KjBGE,EAAA,UAAA,CAAa,CqBDH,AjBAA,CJCW,GAAA,MAAA,MAAA,EAAA,UAGT,CAAC,CIDL,qCJejB,AAAoC,IKqEc,ALrEV,GAAxC,CAAA,EAAS,GAAY,CKqEW,GLrEP,CAAC,EAAA,CAAI,CAAC,CAAW,CAC7C,IAAM,EAAI,EAAA,KAAA,CACV,EAAA,EAAiB,UAAA,CAAA,QACO,GAAG,CAAA,oBAGZ,SAAA,CAAA,EAAmB,CAAC,CAAC,CAAA,MAClB,CAAC,uBAEkB,GAAA,GAAA,EAAA,GAC7B,CDAO,CAAA,ACCP,CAAC,EACH,QAAQ,CAAC,EAAE,CAAC,CAAA,CAAA,CAAG,CAAC,gBAEiB,OAAA,CAAA,AACvB,GFLmB,GEII,CFJJ,CEKb,EAAM,eAKzB,EAAM,EAAA,MAAU,CAAA,YAwBlB,GAAA,CAAA,CAAA,CAAA,kBAIW,CAAA,UAEE,cAGE,EAAM,IAAA,CAAA,iBAEd,IAAA,EAAA,SAAA,CAAyB,EAAS,EAAK,EQsCD,CRtCC,GAAM,UAIhC,CAAC,CQqCkC,CAAA,ERrC1B,CAAC,UAAU,CAAC,CAAC,AKoEc,ANtEd,KCK7B,EAAA,KAAW,CKmEK,AGhCmC,Ca5CtC,Ab4CsC,qBRhCnC,CAAC,EACnC,EqBAU,GrBUuB,CQuCvB,UAAA,WR/BiB,CKuEC,EAAA,cLrE5B,IAAI,GAAG,CAAC,EACH,GAAI,WACJ,WAAY,CqBXH,CrBYT,cACJ,CAAC,CACL,AsBEE,AvBeA,CCTI,GAAA,GACH,GDeoC,YCdpC,IAAI,CDckD,GCd9C,EACH,CDauD,CCbrD,CAAE,SACL,CAAC,CDeH,CCfK,ADeJ,SCdC,CAAC,EAAE,CAAE,QACL,CAAC,IAAK,UACT,CAAC,CKkEH,ALjEF,iMQnGA,CA3BW,IAAA,CAAA,GAAA,CAAA,CAAA,GXjBL,IAAM,GAAe,IAAI,IAAI,CAChC,WACA,cACA,eACA,eACA,gBACA,mBACA,WACA,UACA,gBACA,sBACA,cACA,mBACA,oBACA,oBACA,iBACA,eACA,UACA,UACA,UACA,UACA,UACA,iBACA,UACA,UACA,cACA,eACA,WACA,eACA,qBACA,cACA,SACA,eACA,gBACA,WACA,iBACA,iBACA,WACH,CAAC,GAAG,CAAC,AAAC,GAAQ,CAAC,EAAI,WAAW,GAAI,EAAI,GAC1B,GAAiB,IAAI,IAAI,CAClC,gBACA,gBACA,gBACA,gBACA,cACA,WACA,gBACA,kBACA,WACA,cACA,WACA,oBACA,gBACA,eACA,mBACA,YACA,aACA,WACA,eACA,oBACA,eACA,cACA,cACA,mBACA,YACA,aACA,aACA,sBACA,mBACA,eACA,YACA,YACA,YACA,gBACA,sBACA,iBACA,OACA,OACA,cACA,YACA,qBACA,mBACA,mBACA,mBACA,eACA,cACA,eACA,cACA,eACA,iBACA,cACA,UACA,UACA,aACA,UACA,aACA,mBACA,mBACA,aACH,CAAC,GAAG,CAAE,AAAD,GAAS,CAAC,EAAI,WAAW,GAAI,EAAI,G0BvFjC,GAAoB,IAAI,IAAI,CAC9B,QACA,SACA,MACA,SACA,UACA,WACA,YACA,WACH,EACD,SAAS,GAAc,CAAK,EACxB,OAAO,EAAM,OAAO,CAAC,KAAM,SAC/B,CA+BA,IAAM,GAAY,IAAI,IAAI,CACtB,OACA,OACA,WACA,KACA,MACA,UACA,QACA,QACA,KACA,MACA,QACA,UACA,SACA,OACA,OACA,QACA,SACA,QACA,MACH,EASM,SAAS,GAAO,CAAI,CAAE,EAAU,CAAC,CAAC,EACrC,IAAM,EAAQ,WAAY,EAAO,EAAO,CAAC,EAAK,CAC1C,EAAS,GACb,IAAK,IAAI,EAAI,EAAG,EAAI,EAAM,MAAM,CAAE,IAC9B,AADmC,GACzB,AAKlB,SAAS,AAAW,CAAI,CAAE,CAAO,QA8FV,IA7FnB,AA6FuB,OA7Ff,EAAK,IAAI,EACb,KAAK,EACD,OAAO,GAAO,EAAK,QAAQ,CAAE,EAEjC,MAAK,EACL,KAAK,EACD,OAAO,AAkEM,EAlEU,EAkEN,AAClB,CAAC,CAAC,EAAE,EAAK,IAAI,CAAC,CAAC,CAlElB,AAkEmB,MAlEd,EACD,OAAO,EAAc,EAsFtB,CAAC,IAAI,EAAE,EAAK,IAAI,CAAC,GAAG,CArFvB,AAqFwB,MArFnB,EACD,OAAO,AAgFE,EAhFU,EAgFN,AACd,CAAC,SAAS,EAAE,EAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAhFxC,AAgFyC,MAhFpC,EACL,KAAK,EACL,KAAK,EACD,OAiBZ,AAjBmB,SAiBV,AAAU,CAAI,CAAE,CAAI,EACzB,IAAI,EAEiB,WAAW,CAA5B,EAAK,OAAO,GAEZ,EAAK,IAAI,CAAG,OAAC,EAAK,GAAa,GAAG,CAAC,EAAK,KAAI,CAAC,CAA8B,EAAK,EAAK,CAAlC,GAAsC,CAErF,EAAK,EAFkD,IAE5C,EACX,CAH8D,EAGjC,GAHsC,AAGnC,CAAC,EAAK,MAAM,CAAC,IAAI,GAAG,CACpD,EAAO,CAAE,GAAG,CAAI,CAAE,SAAS,EAAM,GAGrC,CAAC,EAAK,OAAO,EAAI,GAAgB,GAAG,CAAC,EAAK,IAAI,GAAG,CACjD,EAAO,CAAE,GAAG,CAAI,CAAE,QAAS,UAAU,EAEzC,IAAI,EAAM,CAAC,CAAC,EAAE,EAAK,IAAI,CAAA,CAAE,CACnB,EAjHV,AAiHoB,SAjHM,AAAjB,CAA2B,CAAE,CAAI,EACtC,IAAI,EACJ,GAAI,CAAC,EACD,OACJ,IAAM,EAAS,CAAC,OAAC,EAAK,EAAK,cAAA,AAAc,EAA8B,EAAK,EAA7B,AAAkC,QAA1B,MAA0B,AAAc,CAAjC,IAAuC,CAAlC,CAC7D,GACA,EAAK,OAAO,EAA4B,SAAxB,EAAK,cAAc,CAC/B,GACA,GACV,OAAO,OAAO,IAAI,CAAC,GACd,GAAG,CAAC,AAAC,IACN,IAAI,EAAI,EACR,IAAM,EAAQ,OAAC,EAAK,CAAU,CAAC,EAAA,AAAI,EAA8B,EAAK,EAA7B,OAKzC,CAJI,AAAiB,AAD4B,OAAO,IACxB,CAD6B,EACpD,OAAO,GAEZ,EAAM,OAAC,EAAK,GAAe,GAAG,CAAC,EAAA,CAAI,CAA8B,EAAK,CAAA,EAA7B,AAEzC,AAAC,EAAK,MAF2C,IAEjC,EAAK,CAFmC,CAEpC,AAAM,IAFmC,GAE5B,EAAc,IAAI,CAAd,GAGlC,CAAA,EAAG,EAAI,EAAE,EAAE,EAAO,GAAO,CAAC,CAAC,CAFvB,CAGf,GACK,IAAI,CAAC,IACd,EA0FqC,EAAK,OAAO,CAAE,GAuB/C,OAtBI,GACA,IAAO,CAAC,CADC,AACA,EAAE,EAAA,CAAA,AAAS,EAEK,IAAzB,CACA,CADK,QAAQ,CAAC,MAAM,EACnB,GAAK,OAAO,EAEoB,IAAzB,EAAK,eAAe,CAEpB,EAAK,eAAe,EAAI,GAAU,GAAG,CAAC,EAAK,KAAI,CAAC,EACpD,AAAC,CADsD,CACjD,OAAO,GACb,GAAO,GAAA,EACX,GAAO,OAGP,GAAO,IACH,EAAK,QAAQ,CAAC,MAAM,CAAG,GAAG,AAC1B,IAAO,GAAO,EAAK,QAAQ,CAAE,EAAA,GAE7B,EAAK,OAAO,EAAI,CAAC,GAAU,GAAG,CAAC,EAAK,KAAI,GACxC,AAD2C,IACpC,CAAC,EAAE,EAAE,EAAK,IAAI,CAAC,EAAC,AAAC,GAGzB,CACX,EAzD6B,EAAM,EAC3B,MAAK,EACD,OA2DZ,AA3DmB,SA2DV,AAAW,CAAI,CAAE,CAAI,EAC1B,IAAI,EACJ,IAAI,EAAO,EAAK,IAAI,EAAI,GAWxB,MATI,CAAC,OAAC,EAAK,EAAK,cAAA,AAAc,EAA8B,EAAK,EAA7B,AAAkC,QAA1B,MAA0B,AAAc,CAAjC,IAAuC,CAAlC,EAClD,CAAC,EAAK,GAAR,CAAC,GAAc,EACX,EAAK,MAAM,EACX,GAAkB,GAAG,CAAC,EAAK,MAAM,CAAC,IAAI,CAAC,GAAG,AAC9C,EACI,EAAK,OAAO,EAA4B,SAAxB,EAAK,cAAc,CAC7B,GAAU,GACV,GAAW,EAAA,EAElB,CACX,EAzE8B,EAAM,EAChC,CACJ,EAxB6B,CAAK,CAAC,EAAE,CAAE,GAEnC,OAAO,CACX,CAsBA,IAAM,GAA+B,IAAI,IAAI,CACzC,KACA,KACA,KACA,KACA,QACA,iBACA,gBACA,OACA,QACH,EACK,GAAkB,IAAI,IAAI,CAAC,MAAO,OAAO,GFhF/C,SAAA,CAAkC,8JAMjC,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,GT/CD,IXFW,SkB4BX,MA2CA,SpB0CA,GSxGY,CJLA,KiBuCM,GTzClB,GAAA,EAAA,CAAA,CAAA,OAaA,SAAS,GAAuB,CAAC,CAAE,CAAC,CAAE,EAAI,SAAM,CAAS,EACvD,GAAI,KAAM,MAAW,CACnB,IAAM,EAAK,SAAU,GAAG,CAAI,EAAI,OAAO,EAAE,KAAO,EAAO,EACvD,OAAO,CACT,QACA,AAAI,GAAK,EACA,CADG,QACO,GAAG,CAAI,EAAI,OAAO,EAAE,GAAsB,EAAI,EAAG,EAAG,MAAO,EAAO,EAE9E,CACT,CAUA,SAAS,GAAe,CAAG,CAAE,CAAI,EAC/B,IAAI,EAAQ,EACR,EAAM,EAAI,MAAM,CACpB,KAAO,EAAQ,GAAO,CAAG,CAAC,EAAM,GAAK,GAAQ,EAAF,AAAI,EAC/C,KAAO,EAAM,GAAS,CAAG,CAAC,EAAM,EAAE,GAAK,GAAQ,EAAE,AAAJ,EAC7C,OAAQ,EAAQ,GAAK,EAAM,EAAI,MAAM,CACjC,EAAI,SAAS,CAAC,EAAO,GACrB,CACN,CAuCA,SAAS,GAA2B,CAAK,CAAE,CAAM,EAC/C,IAAM,EAAM,IAAI,IAChB,IAAK,IAAI,EAAI,EAAM,MAAM,CAAE,KAAM,GAAI,CACnC,IAAM,EAAO,CAAK,CAAC,EAAE,CACf,EAAM,EAAO,GACnB,EAAI,GAAG,CACL,EACC,EAAI,GAAG,CAAC,GACL,CAAA,EAAA,GAAA,OAAA,AAAK,EAAC,EAAM,EAAI,GAAG,CAAC,GAAM,CAAE,WAAY,EAAiB,GACzD,EAER,CACA,MAAO,IAAI,EAAI,MAAM,GAAG,CAAC,OAAO,EAClC,CAEA,IAAM,GAAmB,CAAC,EAAK,EAAK,IAAY,IAAI,EAAI,CASxD,SAAS,GAAK,CAAG,CAAE,CAAI,EACrB,IAAK,IAAM,KAAO,EAAM,CACtB,GAAI,CAAC,EAAO,GAAF,IAAS,AACnB,EAAM,CAAG,CAAC,EAAI,AAChB,CACA,OAAO,CACT,CAYA,SAAS,GAAwB,CAAG,CAAE,EAAW,GAAG,CAAE,EAAO,EAAE,EAC7D,IAAM,EAAS,EAAE,CACjB,GAAG,AAED,EAAO,IAAI,CAAC,CADZ,IAAO,EACW,GAClB,EAAM,EAAO,EAAS,GAAG,KAClB,EAAM,EAAG,AAClB,IAAM,EAAW,CAF0B,CAEjB,UAAU,CAAC,GACrC,OAAO,EACJ,OAAO,GACP,GAAG,CAAC,GAAK,OAAO,YAAY,CAAC,EAAW,IACxC,IAAI,CAAC,GACV,CAEA,IAAM,GAAI,CAAC,IAAK,IAAK,IAAK,IAAI,CACxB,GAAI,CAAC,IAAK,IAAK,IAAI,CAQzB,SAAS,GAAe,CAAG,EACzB,MAAO,IAAK,EAAO,GAAG,CACnB,GAAG,CAAC,GAAK,CAAC,GACV,OAAO,GACP,GAAG,CAAC,CAAC,EAAG,IAAQ,EAAI,EAAI,EACrB,CAAC,EAAI,EAAI,GAAK,EAAC,CAAC,EAAA,AAAE,EAAI,EAAC,CAAC,EAAE,CAAC,MAAM,CAAC,EAAI,GACtC,EAAC,CAAC,EAAE,CAAI,EAAD,CAAK,EAAI,EAAC,CAAC,EAAE,CAAG,EAAC,CAAC,EAAI,EAAE,AAAF,GAChC,OAAO,GACP,IAAI,CAAC,GACV,CAKA,MAAM,GASJ,YAAa,CAAO,CAAE,CAAyB,CAAE,CAE/C,IAAI,CAAC,KAAK,CAAG,EAFuB,AAErB,CAEf,IAAI,CAAC,aAAa,CAAG,EAAE,CACvB,IAAI,CAAC,aAAa,CAAG,GAAiB,EAAQ,QAAQ,EAAI,OAAO,SAAS,CAC1E,IAAI,CAAC,sBAAsB,CAAG,IAAI,CAAC,aAAa,CAChD,IAAI,CAAC,cAAc,CAAG,GAAI,EAAS,CAAC,gBAAiB,iBAAiB,GAAK,EAAE,CAC7E,IAAI,CAAC,gBAAgB,CAAG,GAAI,EAAS,CAAC,gBAAiB,mBAAmB,IAAK,EAE/E,IAAI,CAAC,YAAY,EAAG,EACpB,IAAI,CAAC,oBAAoB,EAAG,CAC9B,CAQA,SAAU,CAAI,CAAE,GAAS,CAAK,CAAE,CAC1B,IAAI,CAAC,sBAAsB,EAAI,GAAK,CAAC,GACvC,IAAI,CAD2C,AAC1C,YAAY,GAEnB,IAAM,EAA4C,IAA9B,IAAI,CAAC,aAAa,CAAC,MAAM,CACvC,EAAO,EAAK,MAAM,GAAG,CAAC,EAC5B,GAAK,GAAQ,IAAI,CAAC,CADwB,IAAI,CAAC,gBACP,EAAK,EAE3C,IAAI,CAAC,CAF8C,YAEjC,CAAC,IAAI,CAAC,GACxB,IAAI,CAAC,sBAAsB,EAAI,MAE1B,CAGL,GAAM,CAAC,EAAO,GAAG,EAAK,CAAG,IAAI,CAAC,aAAa,CAAC,GAI5C,IAAK,IAAM,KAHP,AAAC,GAAe,IAAI,CAAC,KAAP,OAAmB,GACrC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GACxB,IAAI,CAAC,sBAAsB,EAAI,EAAM,MAAM,CACxB,GACjB,EADuB,EACnB,CAAC,YAAY,GACjB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GACxB,IAAI,CAAC,sBAAsB,EAAI,EAAK,MAGxC,AAH8C,CAIhD,CAQA,SAAW,CACT,IAAM,EAAW,IAAI,CAAC,aAAa,CAAC,GAAG,GACvC,GAAI,AAAa,WAAW,CAC1B,IAAM,EAA4C,IAA9B,IAAI,CAAC,aAAa,CAAC,MAAM,CACvC,EAAO,EAAS,MAAM,GAAG,CAAC,EAChC,IAAI,CAAC,OADyC,IAAI,CAAC,UACxB,EAAI,CACjC,CACA,OAAO,CACT,CASA,WAAY,CAAI,CAAE,GAAS,CAAK,CAAE,CAChC,GAAI,IAAI,CAAC,oBAAoB,EAAI,EAAK,MAAM,CAAG,IAAI,CAAC,sBAAsB,CACxE,CAD0E,GACtE,CAAC,QAAQ,CAAC,EAAM,GACpB,IAAI,CAAC,oBAAoB,EAAG,MACvB,CACL,IAAM,EAAW,IAAI,CAAC,OAAO,GAC7B,IAAI,CAAC,QAAQ,CAAE,EAAY,EAAS,MAAM,CAAC,GAAQ,EAAM,EAC3D,CACF,CAOA,aAAc,EAAI,CAAC,CAAE,CACnB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAC9B,EAAI,GACN,AADS,IACL,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,IAAI,CAAC,CAAE,OAAQ,EAAI,CAAE,EAAG,IAAM,EAAE,GAE3D,IAAI,CAAC,aAAa,CAAG,EAAE,CACvB,IAAI,CAAC,sBAAsB,CAAG,IAAI,CAAC,aAAa,AAClD,CAOA,SAAW,CACT,OAA6B,IAAtB,IAAI,CAAC,KAAK,CAAC,MAAM,EACa,IAA9B,IAAI,CAAC,aAAa,CAAC,MAAM,AAClC,CAEA,OAAS,CACP,IAAI,CAAC,KAAK,CAAC,MAAM,CAAG,EACpB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAG,EAC5B,IAAI,CAAC,sBAAsB,CAAG,IAAI,CAAC,aAAa,AAClD,CAOA,UAAY,CACV,MAAO,IAAI,IAAI,CAAC,KAAK,CAAE,IAAI,CAAC,aAAa,CAAC,CACvC,GAAG,CAAC,GAAS,EAAM,IAAI,CAAC,MACxB,IAAI,CAAC,KACV,CAUA,cAAe,CAAI,CAAE,CACnB,IAAM,EAAQ,EAAE,CACZ,EAAM,EACV,KAAO,EAAK,MAAM,CAAG,IAAI,CAAC,aAAa,EAAE,CAEvC,IAAM,EAAY,EAAK,SAAS,CAAC,EAAG,IAAI,CAAC,aAAa,EAChD,EAAiB,EAAK,SAAS,CAAC,IAAI,CAAC,aAAa,EAElD,EAAa,EAAU,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,EAAI,EAEjE,GAAI,EAAa,CAAC,EAEhB,CAFmB,CAEZ,EAAU,SAAS,CAAC,EAAa,GAAK,EAC7C,EAAM,IAAI,CAAC,EAAU,SAAS,CAAC,EAAG,EAAa,SAK/C,KAAI,EAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAElC,CAFoC,CAE7B,EAAY,MAEd,CAEL,GAAI,IAAI,CAAC,gBAAgB,EAAE,AAGzB,GAFA,EAAM,IAAI,CAAC,GAEP,CADJ,EAAO,CAAA,EACE,MAAM,CAAG,IAAI,CAAC,aAAa,CAClC,CADoC,OAEtC,MAEA,EAAO,EAAY,EAErB,KAEF,CAIJ,CAEA,OADA,EAAM,IAAI,CAAC,GACJ,CACT,CACF,CAKA,CARsB,KAQhB,GACJ,YAAa,EAAO,IAAI,CAAE,CAAE,EATsB,EASlB,CAAC,IAAI,CAAG,CAAM,CAE9C,SAAW,CAAE,OAAQ,IAAI,CAAC,IAAI,CAAI,IAAI,CAAC,IAAI,CAAG,IAAI,AAAE,CACtD,CAEA,MAAM,WAAuB,GAC3B,YAAa,CAAO,CAAE,EAAO,IAAI,CAAE,EAAoB,CAAC,CAAE,CAAyB,CAAE,CACnF,KAAK,CAAC,GACN,IAFwE,AAEpE,CAAC,iBAAiB,CAAG,EACzB,IAAI,CAAC,iBAAiB,CAAG,IAAI,GAAkB,EAAS,GACxD,IAAI,CAAC,OAAO,CAAG,GACf,IAAI,CAAC,iBAAiB,CAAG,EACzB,IAAI,CAAC,KAAK,CAAG,GAAQ,EAAK,KAAK,CAC/B,IAAI,CAAC,QAAQ,CAAG,GAAQ,EAAK,QAAQ,AACvC,CACF,CAEA,MAAM,WAAsB,GAC1B,YACE,CAAO,CACP,EAAO,IAAI,CACX,oBACE,EAAqB,CAAC,mBACtB,EAAoB,CAAC,eACrB,CAAA,GAAgB,SAAS,KACzB,EAAkB,CAAC,aACnB,EAAc,MAAM,CACrB,CAAG,CAAC,CAAC,CACN,CACA,KAAK,CAAC,EAAS,EAAM,EAAmB,GACxC,IAAI,CAAC,eAAe,CAAG,EACvB,IAAI,CAAC,WAAW,CAAG,EACnB,IAAI,CAAC,kBAAkB,CAAG,CAC5B,CACF,CAEA,MAAM,WAA0B,GAC9B,YACE,CAAO,CACP,EAAO,IAAI,CACX,mBACE,EAAoB,CAAC,eACrB,CAAA,GAAgB,KAChB,EAAS,EADgB,AACd,CACZ,CAAG,CAAC,CAAC,CACN,CACA,KAAK,CAAC,EAAS,EAAM,EAAmB,GACxC,IAAI,CAAC,MAAM,CAAG,CAChB,CACF,CAEA,MAAM,WAAuB,GAC3B,YAAa,EAAO,IAAI,CAAE,CACxB,KAAK,CAAC,GACN,IAAI,CAAC,IAAI,CAAG,EAAE,CACd,IAAI,CAAC,KAAK,CAAG,GAAQ,EAAK,KAAK,CAC/B,IAAI,CAAC,QAAQ,CAAG,GAAQ,EAAK,QAAQ,AACvC,CACF,CAEA,MAAM,WAA0B,GAC9B,YAAa,EAAO,IAAI,CAAE,CACxB,KAAK,CAAC,GACN,IAAI,CAAC,KAAK,CAAG,EAAE,CACf,IAAI,CAAC,KAAK,CAAG,GAAQ,EAAK,KAAK,CAC/B,IAAI,CAAC,QAAQ,CAAG,GAAQ,EAAK,QAAQ,AACvC,CACF,CAEA,MAAM,WAA2B,GAC/B,YAAa,CAAO,CAAE,EAAO,IAAI,CAAE,CAA0B,CAAE,CAC7D,KAAK,CAAC,GACN,IAAI,CAF8C,AAE7C,iBAAiB,CAAG,IAAI,GAAkB,EAAS,GACxD,IAAI,CAAC,OAAO,CAAG,GACf,IAAI,CAAC,iBAAiB,CAAG,EACzB,IAAI,CAAC,KAAK,CAAG,GAAQ,EAAK,KAAK,CAC/B,IAAI,CAAC,QAAQ,CAAG,GAAQ,EAAK,QAC/B,AADuC,CAEzC,CAEA,MAAM,WAA6B,GACjC,YAAa,EAAO,IAAI,CAAE,CAAS,CAAE,CACnC,KAAK,CAAC,GACN,IAAI,CAAC,SAAS,CAAG,CACnB,CACF,CAaA,MAAM,GAQJ,YAAa,CAAO,CAAE,CACpB,IAAI,CAAC,eAAe,CAAG,EAAS,gBAAgB,CAC5C,EAAQ,oBAAoB,CAAC,OAAO,CAAC,MAAO,IAC5C,EAAQ,oBAAoB,CAChC,IAAM,EAtBD,IAsBqC,IAAI,CAAC,OAAvB,QAAsC,CAtBjD,CACZ,GAAG,CAAC,GAAK,MAAQ,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,IAAI,QAAQ,CAAC,EAAG,MAC1D,IAAI,CAAC,IA2BN,GANA,IAAI,CAAC,mBAAmB,CAAG,AAAI,OAAO,CAAC,EAAE,EAAE,EAAgB,CAAC,CAAC,EAC7D,IAAI,CAAC,oBAAoB,CAAG,AAAI,OAAO,CAAC,CAAC,EAAE,EAAgB,EAAE,CAAC,EAC9D,IAAI,CAAC,sBAAsB,CAAG,AAAI,OAAO,CAAC,EAAE,EAAE,EAAgB,GAAG,CAAC,EAClE,IAAI,CAAC,wBAAwB,CAAG,AAAI,OAAO,CAAC,UAAU,EAAE,EAAgB,EAAE,CAAC,CAAE,KAC7E,IAAI,CAAC,2BAA2B,CAAG,AAAI,OAAO,CAAC,aAAa,CAAC,CAAE,KAE3D,EAAQ,gBAAgB,CAAE,CAE5B,IAAM,EAAsB,AAAJ,OAAW,CAAC,SAAS,EAAE,EAAgB,EAAE,CAAC,CAAE,MAUpE,IAAI,CAAC,aAAa,CAAG,SAAU,CAAI,CAAE,CAAiB,CAAE,EAAa,GAAO,CAAI,CAAE,EAAS,EAAK,EAC9F,GAAI,CAAC,EAAQ,IAAF,GACX,IAAM,EAAyB,EAAkB,YAAY,CACzD,GAAW,EACX,EAAI,EAAgB,IAAI,CAAC,GAC7B,GAAI,EASF,CATK,GACL,GAAW,EACE,MAAM,CAAf,CAAC,CAAC,EAAE,CACN,EAAkB,YAAY,GACrB,GAA0B,IAAI,CAAC,qBAAqB,CAAC,GAC9D,EAAkB,EADmD,MAC3C,CAAC,EAAU,CAAC,CAAC,EAAE,EAAG,GAE5C,EAAkB,UAAU,CAAC,EAAU,CAAC,CAAC,EAAE,EAAG,GAEzC,AAAqC,KAAM,GAA1C,EAAI,EAAgB,IAAI,CAAC,EAAA,CAAK,EACvB,MAAM,CAAf,CAAC,CAAC,EAAE,CACN,EAAkB,YAAY,GAE9B,EAAkB,QAAQ,CAAC,EAAU,CAAC,CAAC,EAAE,EAAG,GAIlD,EAAkB,YAAY,CAAI,GAA0B,CAAC,GAAc,IAAI,CAAC,sBAAsB,CAAC,EAGzG,CAEF,KAAO,CAEL,IAAM,EAAS,AAAI,OAAO,CAAC,EAAE,EAAE,EAAgB,EAAE,CAAC,CAAE,KAEpD,IAAI,CAAC,aAAa,CAAG,SAAU,CAAI,CAAE,CAAiB,CAAE,EAAa,GAAO,CAAI,CAAE,GAAS,CAAK,EAC9F,GAAI,CAAC,EAAQ,IAAF,GACX,IAAM,EAAyB,EAAkB,YAAY,CACzD,GAAW,EACX,EAAI,EAAO,IAAI,CAAC,GACpB,GAAI,EAOF,CAPK,GACL,GAAW,EACP,GAA0B,IAAI,CAAC,qBAAqB,CAAC,GACvD,EAAkB,EAD4C,MACpC,CAAC,EAAU,CAAC,CAAC,EAAE,EAAG,GAE5C,EAAkB,UAAU,CAAC,EAAU,CAAC,CAAC,EAAE,EAAG,GAEb,AAA5B,KAAkC,GAAjC,EAAI,EAAO,IAAI,CAAC,EAAA,CAAK,EAC3B,EAAkB,QAAQ,CAAC,EAAU,CAAC,CAAC,EAAE,EAAG,GAGhD,EAAkB,YAAY,CAAG,GAA2B,CAAC,GAAa,IAAI,CAAC,sBAAsB,CAAC,EACxG,CAEF,CACF,CAeA,WAAY,CAAI,CAAE,CAAiB,CAAE,GAAS,CAAI,CAAE,CAClD,GAAI,CAAC,EAAQ,IAAF,GACX,IAAM,EAAyB,EAAkB,YAAY,CACzD,GAAW,EACX,EAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,GAC9C,GAAI,EASF,CATK,GACL,GAAW,EACE,MAAM,CAAf,CAAC,CAAC,EAAE,CACN,EAAkB,YAAY,GACrB,EACT,EAAkB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAE,GAEjC,EAAkB,CAHe,SAGL,CAAC,CAAC,CAAC,EAAE,CAAE,GAE9B,AAAsD,KAAM,GAA3D,EAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAA,CAAK,EACxC,MAAM,CAAf,CAAC,CAAC,EAAE,CACN,EAAkB,YAAY,GAE9B,EAAkB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAE,GAIvC,EAAkB,YAAY,CAAI,GAA0B,CAAC,CAC/D,CAQA,sBAAuB,CAAI,CAAE,CAC3B,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EACvC,CAQA,uBAAwB,CAAI,CAAE,CAC5B,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EACxC,CAQA,kBAAmB,CAAI,CAAE,CACvB,MAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAC3C,CAUA,qBAAsB,CAAI,CAAE,KAGtB,EAFJ,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAG,EAC1C,IAAI,EAAU,EAEd,KAAO,AAAuD,KAAM,EAA5D,GAAQ,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAA,CAAK,EACtD,GAAI,AAAa,MAAM,EAAd,CAAC,EAAE,CAGV,OAAO,OAFP,IAKJ,OAAO,CACT,CAEF,CAOA,MAAM,GASJ,YAAa,CAAO,CAAE,CAAM,CAAE,CAAoB,CAAE,CAClD,IAAI,CAAC,GADkC,IAC3B,CAAG,EACf,IAAI,CAAC,MAAM,CAAG,EACd,IAAI,CAAC,QAAQ,CAAG,EAChB,IAAI,CAAC,mBAAmB,CAAG,IAAI,GAAoB,GAEnD,IAAI,CAAC,UAAU,CAAG,IAAI,GAAe,GAErC,IAAI,CAAC,gBAAgB,MAAG,CAC1B,CAWA,kBAAmB,CAAa,CAAE,CAChC,IAAI,CAAC,gBAAgB,CAAG,IAAI,GAAqB,IAAI,CAAC,gBAAgB,CAAE,EAC1E,CAOA,kBAAoB,CAClB,GAAI,CAAC,IAAI,CAAC,gBAAgB,CAAI,CAAF,MAAS,AACrC,IAAM,EAAY,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAEjD,OADA,IAAI,CAAC,gBAAgB,CAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAC3C,CACT,CAKA,aAAe,CACb,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAG,CAC7B,CAKA,YAAc,CACZ,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAG,CAC7B,CAGA,6BAA+B,CAC7B,IAAM,EAAM,IAAI,CAAC,gBAAgB,CAC5B,AAAC,GAAQ,CA+YlB,SAAS,EAAkB,CAAG,CAAE,CAAW,EACzC,OAAS,EAAe,EAAiB,EAAY,SAAS,CAAC,GAAM,EAAY,IAAI,EAAI,EAC3F,EAjZmC,EAAK,IAAI,CAAC,gBAAgB,OACrD,EACE,EAAK,IAAI,CAAC,OAAO,CAAC,gBAAgB,CACxC,OAAQ,EACF,EAAM,AAAC,GAAQ,EAAG,EAAG,IAAQ,EAC/B,CACN,CAEA,eAAiB,CACf,IAAM,EAAO,IAAI,CAAC,UAAU,CAE5B,OADA,IAAI,CAAC,UAAU,CAAG,EAAK,IAAI,CACpB,CACT,CAKA,cAAgB,EAEZ,IAAI,CAAC,UAAU,YAAY,IACxB,IAAI,CAAC,UAAU,YAAY,IAC3B,IAAI,CAAC,UAAU,YAAY,EAAA,CAChC,GAAG,AACC,IAAI,CAAC,UAAU,CAAC,KAAK,CACvB,CADyB,GACrB,CAAC,UAAU,CAAC,OAAO,EAAI,KAE3B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,GAElD,CAKA,yBAA2B,EAEvB,IAAI,CAAC,UAAU,YAAY,IACxB,IAAI,CAAC,UAAU,YAAY,IAC3B,IAAI,CAAC,UAAU,YAAY,EAAA,GAC9B,CACA,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,oBAAoB,EAAG,CAAA,CAE7D,CAgBA,UAAW,CAAG,CAAE,iBAAE,GAAkB,CAAK,CAAE,CAAG,CAAC,CAAC,CAAE,CAChD,GACE,CADE,CAAC,EACC,CAAC,UAAU,YAAY,IACxB,IAAI,CAAC,UAAU,YAAY,IAC3B,IAAI,CAAC,UAAU,YAAY,IAGhC,GAAI,IAAI,CAAC,MAFT,GAAG,CAEgB,CAAC,KAAK,CAAE,CACzB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAI,EAC3B,MACF,CAEA,GACiB,IAAf,EAAI,MAAM,EAER,MAAI,CAAC,UAAU,CAAC,iBAAiB,EAChC,EADoC,EAChC,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,EAAA,EAAK,CAIrD,EALyF,CAKrF,IAAI,CAAC,OAAO,CAAC,CAJsD,eAItC,CAAE,CACjC,IAAM,EAAiB,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,GACrE,GAAI,EAAiB,EAAG,YACtB,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAInD,CAEI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,AACrC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAElF,IAAI,CAAC,mBAAmB,CAAC,aAAa,CACpC,EACA,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAChC,OAAmB,EAAY,IAAI,CAAC,2BAA2B,GAChE,IAAI,CAAC,UAAU,CAAC,QAAQ,EAE1B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAG,GAAG,AACzC,CAUA,WAAY,CAAG,CAAE,CACf,IACE,AADE,CAAC,GACC,CAAC,UAAU,UAbkE,EAatD,IACxB,IAAI,CAAC,UAAU,YAAY,IAC3B,IAAI,CAAC,UAAU,YAAY,EAAA,CAChC,EAEmB,CAFhB,EAEmB,CAAlB,EAAI,MAAM,EAEd,GAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAE,CACzB,IAAI,CAAC,UAAU,CAAC,OAAO,EAAI,EAC3B,MACF,CAEI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,AACrC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAElF,IAAI,CAAC,mBAAmB,CAAC,UAAU,CACjC,EACA,IAAI,CAAC,UAAU,CAAC,iBAAiB,CACjC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAE1B,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAG,EACtC,CAiBA,UAAW,mBAAE,EAAoB,CAAC,oBAAE,EAAqB,CAAC,OAAE,EAAQ,EAAK,CAAE,CAAG,CAAC,CAAC,CAAE,CAChF,IAAM,EAAgB,KAAK,GAAG,CAAC,GAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAAG,EACrF,KAAI,CAAC,UAAU,CAAG,IAAI,GACpB,IAAI,CAAC,OAAO,CACZ,IAAI,CAAC,UAAU,CACf,EACA,GAEE,GAAS,IAAF,CAAM,CAAC,UAAU,CAAC,KAAK,EAAG,CAAA,CACvC,CAiBA,WAAY,oBAAE,EAAqB,CAAC,gBAAE,CAAA,CAA4B,CAAG,CAAd,AAAe,CAAC,CAAE,CACvE,IAAM,EADwD,AAChD,IAAI,CAAC,aAAa,GAC1B,EAAa,EAAkB,EAAe,GAAQ,IAAU,GAAQ,GAC9E,GAAQ,IAAI,CAAC,UAAU,CAAE,EAAW,EAAM,iBAAiB,CAAE,KAAK,GAAG,CAAC,EAAM,iBAAiB,CAAE,GACjG,CAsBA,SAAU,iBAAE,EAAkB,CAAC,aAAE,EAAc,MAAM,oBAAE,EAAqB,CAAC,mBAAE,EAAoB,CAAC,CAAE,CAAG,CAAC,CAAC,CAAE,CAC3G,IAAI,CAAC,UAAU,CAAG,IAAI,GAAc,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,UAAU,CAAE,CACjE,mBAAoB,EACpB,kBAAmB,EACnB,cAAe,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,CAC9D,gBAAiB,EACjB,YAAa,CACf,EACF,CAWA,aAAc,QAAE,EAAS,EAAE,CAAE,CAAG,CAAC,CAAC,CAAE,CAClC,GAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,EAAA,CAAa,CAC5C,EAD+C,IACzC,AAAI,MAAM,+EAElB,IAAM,EAAO,IAAI,CAAC,UAAU,CACtB,EAAe,KAAK,GAAG,CAAC,EAAO,MAAM,CAAE,EAAK,eAAe,EAC3D,EAAgB,KAAK,GAAG,CAAC,GAAI,EAAK,iBAAiB,CAAC,aAAa,CAAG,GAC1E,IAAI,CAAC,UAAU,CAAG,IAAI,GAAkB,IAAI,CAAC,OAAO,CAAE,EAAM,CAC1D,OAAQ,EACR,cAAe,EACf,kBAAmB,EAAK,kBAAkB,AAC5C,EACF,CAKA,eAAiB,CACf,IAAM,EAAW,IAAI,CAAC,aAAa,GAC7B,EAAO,EAAS,IAAI,CAEpB,EAAe,KAAK,GAAG,CAAC,EAAS,MAAM,CAAC,MAAM,CAAE,EAAK,eAAe,EACpE,EAAU,KAAO,IAAI,MAAM,CAAC,GAI5B,EAAO,CAHwB,UAAtB,EAAM,WAAW,CAC5B,EAAS,MAAM,CAAC,QAAQ,CAAC,GACzB,EAAS,MAAM,CAAC,MAAM,CAAC,EAAA,EACL,GAAQ,GAAU,OAAO,CAAC,MAAO,GAEvD,GACE,EACA,EACA,EAAS,iBAAiB,CAC1B,KAAK,GAAG,CAAC,EAAS,iBAAiB,CAAE,EAAK,kBAAkB,EAEhE,CAWA,UAAW,oBAAE,EAAqB,CAAC,CAAE,CAAG,CAAC,CAAC,CAAE,CAC1C,IAAM,EAAO,IAAI,CAAC,aAAa,GACzB,EAAO,GAAQ,EACjB,IACF,EADQ,CACA,IAAI,CAAC,UAAU,CAAE,EAAM,EAAK,iBAAiB,CAAE,EAE3D,CAKA,WAAa,CACX,IAAI,CAAC,UAAU,CAAG,IAAI,GAAe,IAAI,CAAC,UAAU,CACtD,CAKA,cAAgB,CACd,GAAI,CAAC,AAAC,KAAI,CAAC,UAAU,YAAY,EAAA,CAAc,CAC7C,EADgD,IAC1C,AAAI,MAAM,gFAElB,IAAI,CAAC,UAAU,CAAG,IAAI,GAAkB,IAAI,CAAC,UAAU,CACzD,CAWA,cAAe,gBAAE,CAAA,CAA4B,CAAG,CAAC,AAAf,CAAgB,CAAE,CAClD,GAAI,CAAC,CAAC,CADmC,GAC/B,CAAC,UAAU,YAAY,EAAA,CAAiB,CAChD,EADmD,IAC7C,AAAI,MAAM,qFAElB,IAAI,CAAC,UAAU,CAAG,IAAI,GAAmB,IAAI,CAAC,OAAO,CAAE,IAAI,CAAC,UAAU,CAAE,EAC1E,CAWA,eAAgB,SAAE,EAAU,CAAC,SAAE,EAAU,CAAC,CAAE,CAAG,CAAC,CAAC,CAAE,CACjD,IAAM,EAAO,IAAI,CAAC,aAAa,GACzB,EAAO,GAAc,GAAQ,GAAO,MAC1C,EAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAE,QAAS,EAAS,QAAS,EAAS,KAAM,CAAK,EACxE,CAKA,eAAiB,CACf,IAAM,EAAM,IAAI,CAAC,aAAa,GAC9B,EAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAI,KAAK,CAC9B,CAiBA,WAAY,eAAE,CAAa,mBAAE,EAAoB,CAAC,oBAAE,EAAqB,CAAC,CAAE,CAAE,CAE5E,IAAM,EAAS,EAAc,AADf,IAAI,CAAC,aAAa,GACG,IAAI,EACnC,GACF,GAAQ,EADE,EACE,CAAC,UAAU,CAAE,EAAQ,EAAmB,EAExD,CAOA,UAAY,CACV,OAAO,GAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,GAExC,CAEF,CAEA,SAAS,GAAS,CAAS,EACzB,GAAI,CAAC,CACH,aAAqB,IAClB,aAAqB,IACrB,aAAqB,EAAA,CAC1B,CACE,EADC,IACK,AAAI,MAAM,+EAElB,OAAQ,EAAU,iBAAiB,CAAC,OAAO,GACvC,EAAU,OAAO,CACjB,EAAU,OAAO,CAAG,EAAU,iBAAiB,CAAC,QAAQ,EAC9D,CAEA,SAAS,GAAS,CAAS,CAAE,CAAI,CAAE,CAAiB,CAAE,CAAkB,EACtE,GAAI,CACF,AADG,cACkB,IAClB,aAAqB,IACrB,aAAqB,EAAA,CAC1B,CACE,EADC,IACK,AAAI,MAAM,6DAElB,IAAM,EAAa,GAAQ,GACrB,EAAa,KAAK,GAAG,CAAC,EAAU,iBAAiB,CAAE,GACzD,EAAU,iBAAiB,CAAC,KAAK,GAC7B,EACF,EAAU,OAAO,CAAG,AADN,EACmB,KAAK,MAAM,CAAC,GAAc,GAE3D,EAAU,OAAO,CAAG,EACpB,EAAU,iBAAiB,CAAG,GAEhC,EAAU,iBAAiB,CAAG,CAChC,CAkIA,SAAS,GAAe,CAAI,CAAE,CAAG,CAAE,CAAO,EACxC,GAAI,CAAC,EAAO,GAAF,IAEV,IAAM,EAAU,EAAQ,OAAO,CAW/B,IAAK,IAAM,KATe,EAAI,MAAM,CAAG,EAAQ,MAAM,CAAC,aAAa,EAGjE,CADA,EAAM,EAAI,KAAK,CAAC,EAAG,EAAQ,MAAM,CAAC,cAAa,EAC3C,IAAI,CAAC,CACP,KAAM,EAAQ,MAAM,CAAC,QAAQ,CAC7B,KAAM,MACR,GAGiB,GACjB,CADsB,MACd,EAAK,IAAI,EACf,IAAK,OACH,EAAQ,SAAS,CAAC,EAAK,IAAI,EAC3B,KAEF,KAAK,MAAO,CACV,IAAM,EAAgB,EAAQ,MAAM,CAAC,KAAK,CAAC,GAE3C,GADe,EAAQ,UAAU,CAAC,EAAc,OAAM,AAAC,EAChD,EAAM,EAAM,EAAS,EAAc,OAAO,EAAI,CAAC,EAExD,CACF,CAIJ,CA8EA,SAAS,GAAe,CAAI,EAC1B,IAAM,EAAS,EAAK,OAAO,EAAI,EAAK,OAAO,CAAC,MAAM,CAC9C,IAAM,OAAO,OAAO,CAAC,EAAK,OAAO,EAChC,GAAG,CAAC,CAAC,CAAC,EAAG,EAAE,GAAO,AAAM,OAAM,EAAI,CAAA,EAAG,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,KAAM,UAAA,CAAW,EACrE,IAAI,CAAC,KACN,GACJ,MAAO,CAAC,CAAC,EAAE,EAAK,IAAI,CAAA,EAAG,EAAM,CAAC,CAAC,AACjC,CAEA,SAAS,GAAgB,CAAI,EAC3B,MAAO,CAAC,EAAE,EAAE,EAAK,IAAI,CAAC,CAAC,CAAC,AAC1B,CAyEA,IAAI,GAAiC,OAAO,MAAM,CAAC,CACjD,EADsB,QACX,GADsB,EAEjC,MA5FF,CA4FS,QA5FA,AAAe,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EACxD,EAAQ,SAAS,CAAC,CAAE,kBAAmB,EAAc,iBAAiB,EAAI,CAAE,GAC5E,EAAK,EAAK,QAAQ,CAAE,GACpB,EAAQ,UAAU,CAAC,CAAE,mBAAoB,EAAc,kBAAkB,EAAI,CAAE,EACjF,EAyFE,UAxBF,CAwBa,QAxBJ,AAAiB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC1D,EAAQ,SAAS,CAAC,CAAE,kBAAmB,EAAc,iBAAiB,EAAI,CAAE,GAC5E,EAAQ,WAAW,GACnB,EAAQ,UAAU,CAChB,GAAO,EAAM,CAAE,eAAgB,EAAQ,OAAO,CAAC,cAAc,AAAC,IAEhE,EAAQ,UAAU,GAClB,EAAQ,UAAU,CAAC,CAAE,mBAAoB,EAAc,kBAAkB,EAAI,CAAE,EACjF,EAiBE,YAlHF,CAkHe,QAlHN,AAAmB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC5D,EAAQ,SAAS,CAAC,CAAE,kBAAmB,EAAc,iBAAiB,EAAI,CAAE,GAC5E,EAAQ,UAAU,CAAC,EAAc,MAAM,EAAI,IAC3C,EAAQ,UAAU,CAAC,CAAE,mBAAoB,EAAc,kBAAkB,EAAI,CAAE,EACjF,EA+GE,SAxDF,CAwDY,QAxDH,AAAgB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EACzD,EAAQ,SAAS,CAAC,CAAE,kBAAmB,EAAc,iBAAiB,EAAI,CAAE,GAC5E,EAAQ,WAAW,GACnB,EAAQ,UAAU,CAAC,GAAc,IACjC,EAAQ,UAAU,GAClB,EAAK,EAAK,QAAQ,CAAE,GACpB,EAAQ,WAAW,GACnB,EAAQ,UAAU,CAAC,GAAe,IAClC,EAAQ,UAAU,GAClB,EAAQ,UAAU,CAAC,CAAE,mBAAoB,EAAc,kBAAkB,EAAI,CAAE,EACjF,EA+CE,OAzGF,CAyGU,QAzGa,AAAd,CAAkB,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EACvD,EAAK,EAAK,QAAQ,CAAE,EACtB,EAwGE,WAzCF,CAyCc,QAzCL,AAAkB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC3D,EAAQ,WAAW,GACnB,EAAQ,UAAU,CAChB,GAAO,EAAM,CAAE,eAAgB,EAAQ,OAAO,CAAC,cAAc,AAAC,IAEhE,EAAQ,UAAU,EACpB,EAoCE,aA/HF,CA+HgB,QA/HP,AAAoB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC7D,EAAQ,UAAU,CAAC,EAAc,MAAM,EAAI,GAC7C,EA8HE,eAfF,CAekB,QAfT,AAAsB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC/D,EAAQ,UAAU,CAAC,EAAc,MAAM,EAAI,IAC3C,EAAK,EAAK,QAAQ,CAAE,GACpB,EAAQ,UAAU,CAAC,EAAc,MAAM,EAAI,GAC7C,EAYE,UA5EF,CA4Ea,QA5EJ,AAAiB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC1D,EAAQ,WAAW,GACnB,EAAQ,UAAU,CAAC,GAAc,IACjC,EAAQ,UAAU,GAClB,EAAK,EAAK,QAAQ,CAAE,GACpB,EAAQ,WAAW,GACnB,EAAQ,UAAU,CAAC,GAAe,IAClC,EAAQ,UAAU,EACpB,EAqEE,KA3IF,CA2IQ,QA3IC,AAAY,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAEvD,CA0IA,GAEA,SAAS,GAAQ,CAAM,CAAE,CAAC,EAExB,OADI,AAAC,CAAM,CAAC,EAAE,EAAE,CAAE,CAAM,CAAC,EAAE,CAAG,EAAA,AAAE,EACzB,CAAM,CAAC,EAAE,AAClB,CA8BA,SAAS,GAAiB,CAAO,CAAE,CAAK,EAItC,OAHI,AAAmB,UAAZ,CAAC,AAAsB,EAAhB,GAChB,CAAO,CAAC,EAAM,CAAG,AAAW,MAAK,EAAI,EAAI,GAAgB,EAAS,EAAQ,EAAA,EAErE,CAAO,CAAC,EAAM,AACvB,CAEA,SAAS,GAAc,CAAO,CAAE,CAAI,CAAE,CAAI,CAAE,CAAK,EAC/C,CAAO,CAAC,EAAO,EAAK,CAAG,KAAK,GAAG,CAC7B,GAAgB,EAAS,EAAO,GAChC,GAAgB,EAAS,GAAQ,EAErC,CAiKA,SAAS,GAAc,CAAG,CAAE,CAAQ,SAClC,AAAK,EAQE,CAN6B,CAFhC,MAAW,GAEF,OAAO,CAAQ,CAAC,EAAE,CAC3B,CAAQ,CAAC,EAAE,CACX,GAAA,EAIS,GAHuB,GAGjB,OAHP,OAAQ,CAAQ,CAAC,EAAE,CAC3B,CAAQ,CAAC,EAAE,CACX,GAAA,EAPoB,CAS1B,CAEA,SAAS,GAAa,CAAI,CAAE,CAAQ,CAAE,CAAO,CAAE,CAAQ,CAAE,CAAI,EAC3D,IAAM,EAAoC,YAArB,OAAQ,EACzB,EAAS,EAAM,EAAU,GACzB,EACJ,MAA4B,MAApB,CAAY,CAAC,EAAE,EAAY,EAljDrC,AAmjDM,SAnjDG,AAAkB,CAAG,CAAE,CAAI,EAClC,IAAI,EAAM,EAAI,MAAM,CACpB,KAAO,EAAM,SAAK,CAAG,CAAC,EAAM,EAAE,EAAa,EAAE,CAAV,CACnC,IADyC,GACjC,EAAM,EAAI,MAAM,CACpB,EAAI,SAAS,CAAC,EAAG,GACjB,CACN,EA6iDuB,EAAS,GAAO,EACjC,CACN,CA8FA,SAAS,GAAY,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,CAAE,CAAkB,EACzE,IAAM,EAAiD,OAAlC,GAAI,EAAM,CAAC,SAAU,OAAO,EAI7C,EAAkB,EAChB,EAAY,CAAC,EAAK,QAAQ,EAAI,EAAA,AAAE,CACpC,CACC,MAAM,CAAC,GAAwB,SAAf,EAAM,IAAI,EAAe,CAAC,QAAQ,IAAI,CAAC,EAAM,IAAI,GACjE,GAAG,CAAC,SAAU,CAAK,EAClB,GAAmB,MAAM,CAArB,EAAM,IAAI,CACZ,MAAO,CAJgF,AAI9E,KAAM,EAAO,OAAQ,EAAG,EAEnC,IAAM,EAAU,EACZ,IAAqB,SAAS,GAC9B,IAEJ,OADI,EAAO,MAAM,CAAG,IAAmB,EAAkB,EAAO,MAAA,AAAM,EAC/D,CAD8B,AAC5B,KAAM,EAAO,OAAQ,CAAO,CACvC,GACF,GAAK,CAAD,CAAW,MAAM,EAAE,AASvB,IAAK,GAAM,MAAE,CAAI,QAAE,CAAM,CAAE,GAP3B,EAAQ,QAAQ,CAAC,CACf,mBAAoB,EACpB,kBAAmB,EAAe,EAAK,EAAc,iBAAiB,EAAI,EAC1E,gBAAiB,EACjB,YAAa,MACf,GAE+B,GAC7B,EAAQ,KADgC,OACpB,CAAC,CAAE,OAAQ,CAAO,GACtC,EAAK,CAAC,EAAK,CAAE,GACb,EAAQ,aAAa,GAGvB,EAAQ,SAAS,CAAC,CAAE,mBAAoB,EAAe,EAAK,EAAc,kBAAkB,EAAI,CAAG,GACrG,CA8FA,SAAS,GAAiB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAS1D,SAAS,EAAY,CAAQ,EAC3B,IAAM,EAAU,CAAC,GAAI,EAAU,CAAC,UAAW,UAAU,GAAK,EACpD,EAAU,CAAC,GAAI,EAAU,CAAC,UAAW,UAAU,GAAK,EAC1D,EAAQ,aAAa,CAAC,CAAE,eAAgB,EAAc,cAAc,AAAC,GACrE,EAAK,EAAS,QAAQ,CAAE,GACxB,EAAQ,cAAc,CAAC,CAAE,QAAS,EAAS,QAAS,CAAQ,EAC9D,CAdA,EAAQ,SAAS,GACjB,EAAK,QAAQ,CAAC,OAAO,CAAC,AAetB,SAAS,EAAW,CAAI,EACtB,GAAI,AAAc,OAAO,GAAhB,IAAI,CAAc,OAE3B,IAAM,GAA2D,IAAvC,EAAc,oBAAoB,CACvD,AAAD,IACA,EAAQ,iBAAiB,CAAC,GAAO,EAAI,WAAW,IAChD,EAAW,GACX,EAAQ,gBAAgB,EAC1B,EACE,EAEJ,OAAQ,EAAK,IAAI,EACf,IAAK,QACL,IAAK,QACL,IAAK,QACL,IAAK,SACH,EAAK,QAAQ,CAAC,OAAO,CAAC,GACtB,MAEF,KAAK,KAEH,IAAK,IAAM,KADX,EAAQ,YAAY,GACI,EAAK,QAAQ,CAAE,CACrC,GAAI,AAAmB,OAAO,GAAhB,IAAI,CAClB,OAAQ,EAAU,IAAI,EACpB,IAAK,KACH,EAAiB,GACjB,KAEF,KAAK,KACH,EAAW,EAIf,CAEF,EAAQ,aAAa,EAIzB,CACF,GAtDA,EAAQ,UAAU,CAAC,CACjB,cAAgB,AAAD,GA1YnB,AA0Y6B,SA1YpB,CAAe,CAAS,CAAE,CAAU,CAAE,CAAU,EACvD,IAAM,EAAS,EAAE,CACb,EAAY,EACV,EAAY,EAAU,MAAM,CAC5B,EAAa,CAAC,EAAE,CAEtB,IAAK,IAAI,EAAI,EAAG,EAAI,EAAW,IAAK,CAClC,IAAM,EAAY,GAAO,EAAQ,GAC3B,EAAQ,CAAS,CAAC,EAAE,CACtB,EAAI,EACR,IAAK,IAAI,EAAI,EAAG,EAAI,EAAM,MAAM,CAAE,IAAK,CACrC,IAAM,EAAO,CAAK,CAAC,EAAE,CACrB,EAjEN,AAiEU,SAjED,AAAsB,CAAG,CAAE,EAAI,CAAC,EACvC,KAAO,CAAG,CAAC,EAAE,CAAE,CAAE,IACjB,OAAO,CACT,EA8D+B,EAAW,OA9CA,EA+CJ,EA/Ca,EA+CV,CA/CQ,CAC/C,GADwD,CACnD,IAAI,EAAI,EAAG,EAAI,EAAK,OAAO,CAAE,IAAK,CACrC,IAAM,EAAY,GA6CQ,EA7CO,EAAR,AAAkB,GAC3C,IAAK,IAAI,EAAI,EAAG,EAAI,EAAK,OAAO,CAAE,IAAK,AACrC,CAAS,CAAC,EAAU,EAAE,CA2CJ,CAzCtB,CA0CI,AA5CyB,GA4CpB,EAAK,OAAO,CACjB,EAAK,KAAK,CAAG,EAAK,IAAI,CAAC,KAAK,CAAC,MAC7B,IAAM,EAAa,EAAK,KAAK,CAAC,MAAM,CACpC,GAAa,EAAY,EAAG,EAAK,OAAO,CAAE,EAAa,EACzD,CACA,EAAa,EAAU,MAAM,CAAG,EAAa,EAAU,MAAM,CAAG,CAClE,EApEF,AAsEE,SAtEO,AAAkB,CAAM,CAAE,CAAO,EACxC,IAAK,IAAI,EAAI,EAAG,EAAI,EAAS,IAAK,CAChC,IAAM,EAAO,GAAO,EAAQ,GAC5B,IAAK,IAAI,EAAI,EAAG,EAAI,EAAG,IAAK,CAC1B,IAAM,EAAO,GAAO,EAAQ,GAC5B,GAAI,CAAI,CAAC,EAAE,EAAI,CAAI,CAAC,EAAE,CAAE,CACtB,IAAM,EAAO,CAAI,CAAC,EAAE,AACpB,EAAI,CAAC,EAAE,CAAG,CAAI,CAAC,EAAE,CACjB,CAAI,CAAC,EAAE,CAAG,CACZ,CACF,CACF,CACF,EA0DmB,EAAS,EAAY,EAAa,EAAY,GAE/D,IAAM,EAAc,EAAE,CAChB,EAAa,CAAC,EAAE,CAEtB,IAAK,IAAI,EAAI,EAAG,EAAI,EAAW,IAAK,CAClC,IACI,EADA,EAAI,EAEF,EAAmB,KAAK,GAAG,CAAC,EAAW,CAAM,CAAC,EAAE,CAAC,MAAM,EAC7D,KAAO,EAAI,GAET,GADA,CACI,CADG,CAAM,CAAC,EAAE,CAAC,EAAE,CACT,CAFiB,AAGzB,GAAI,CAAC,EAAK,QAAQ,CAAE,CAClB,IAAI,EAAY,EAChB,IAAK,IAAI,EAAI,EAAG,EAAI,EAAK,KAAK,CAAC,MAAM,CAAE,IAAK,CAC1C,IAAM,EAAO,EAAK,KAAK,CAAC,EAAE,CACpB,EAAa,CAAU,CAAC,EAAE,CAAG,EACnC,CAAW,CAAC,EAAW,CAAG,CAAC,CAAW,CAAC,EAAW,EAAI,EAAA,CAAE,CAAE,MAAM,CAAC,CAAU,CAAC,EAAE,EAAI,EAClF,EAAa,EAAK,MAAM,CAAG,EAAa,EAAK,MAAM,CAAG,CACxD,CACA,GAAa,EAAY,EAAG,EAAK,OAAO,CAAE,EAAY,GACtD,EAAK,QAAQ,EAAG,CAClB,CACA,GAAK,EAAK,OAAO,AACnB,KAAO,CACL,IAAM,EAAa,CAAU,CAAC,EAAE,CAChC,CAAW,CAAC,EAAW,CAAI,CAAW,CAAC,EAAW,EAAI,GACtD,GACF,CAEJ,CAEA,OAAO,EAAY,IAAI,CAAC,KAC1B,GAmV2C,EAAM,EAAc,UAAU,EAAI,EAAG,EAAc,UAAU,EAAI,GACxG,kBAAmB,EAAc,iBAAiB,CAClD,mBAAoB,EAAc,kBAAkB,AACtD,EAmDF,CAEA,IAAI,GAA8B,OAAO,MAAM,CAA1B,AAA2B,CAC9C,UAD8B,AACnB,KACX,OAzOF,CAyOU,QAzOD,AAAc,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EASvD,IAAM,EAAO,AARb,SAAS,EACP,GAAI,EAAc,UAAU,EAAE,AAC1B,CAAC,EAAK,OAAO,EAAI,CAAC,EAAK,OAAO,CAAC,IAAI,CADP,CACS,KADF,GAEvC,IAAI,EAAO,EAAK,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,WAAY,WACjD,AAAI,EAAc,WAAW,EAAgB,KAAK,CAAjB,CAAI,CAAC,EAAE,CAAmB,GAC3D,EAAO,GAAY,EAAM,EAAc,WAAW,CAAE,EAAc,OAAO,CAAE,EAAQ,QAAQ,CAAE,EAE/F,IAEA,GAAK,CAAD,CAEG,CACL,GAHS,CAGL,EAAO,GACX,EAAQ,iBAAiB,CACvB,IACM,GAAO,EAAF,EAAU,CAAA,EACZ,IAGX,EAAK,EAAK,QAAQ,CAAE,GACpB,EAAQ,gBAAgB,GAEH,AACjB,CAAC,CAD8B,aAChB,WADwC,EAAI,IAAS,GAEtE,EAAQ,SAAS,CACd,AAAC,EAEE,IAAM,GAAa,EAAM,EAAc,YAAY,EADnD,EAEJ,CAAE,iBAAiB,CAAK,EAG9B,MArBE,EAAK,EAAK,QAAQ,CAAE,EAsBxB,EAyME,WAhUF,CAgUc,QAhUL,AAAkB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC3D,EAAQ,SAAS,CAAC,CAChB,kBAAmB,EAAc,iBAAiB,EAAI,EACtD,mBAAoB,CACtB,GACA,EAAK,EAAK,QAAQ,CAAE,GACpB,EAAQ,UAAU,CAAC,CACjB,mBAAoB,EAAc,kBAAkB,EAAI,EACxD,eAAgB,GAAO,EAAmC,IAAjC,EAAc,cAAc,CAAc,GAAc,EAAK,MAAQ,CAAA,CAAG,CAC9F,KAAK,CAAC,MACN,GAAG,CAAC,GAAQ,KAAO,GACnB,IAAI,CAAC,KACV,EACF,EAoTE,UAAW,GACX,QAnVF,CAmVW,QAnVF,AAAe,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EACxD,EAAQ,SAAS,CAAC,CAAE,kBAAmB,EAAc,iBAAiB,EAAI,CAAE,GACxE,CAA4B,MAAd,CAAqB,QAAZ,EACzB,EAAQ,iBAAiB,CAAC,GAAO,EAAI,WAAW,IAChD,EAAK,EAAK,QAAQ,CAAE,GACpB,EAAQ,gBAAgB,IAExB,EAAK,EAAK,QAAQ,CAAE,GAEtB,EAAQ,UAAU,CAAC,CAAE,mBAAoB,EAAc,kBAAkB,EAAI,CAAE,EACjF,EA0UE,eAxXF,CAwXkB,QAxXT,AAAsB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC/D,EAAQ,SAAS,CAAC,CAAE,kBAAmB,EAAc,iBAAiB,EAAI,CAAE,GAC5E,EAAQ,SAAS,CAAC,IAAI,MAAM,CAAC,EAAc,MAAM,EAAI,EAAQ,OAAO,CAAC,QAAQ,EAAI,KACjF,EAAQ,UAAU,CAAC,CAAE,mBAAoB,EAAc,kBAAkB,EAAI,CAAE,EACjF,EAqXE,MA3RF,CA2RS,QA3RA,AAAa,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EACtD,IAAM,EAAU,EAAK,OAAO,EAAI,CAAC,EAC3B,EAAO,EAAQ,GAAG,CACpB,EAAQ,GAAG,CACX,GACE,EAAO,AAAC,EAAQ,GAAG,CAErB,GAAY,EAAQ,GAAG,CAAE,EAAc,WAAW,CAAE,EAAc,OAAO,CAAE,EAAQ,QAAQ,CAAE,GAD7F,GAEE,EAAQ,AAAC,EAET,AAAD,EAEC,EAAM,IAAM,GAAa,EAAK,EAAc,YAAY,EADxD,GAAa,EAAK,EAAc,YAAY,EAF9C,EAKJ,EAAQ,SAAS,CAAC,EAAM,CAAE,iBAAiB,CAAK,EAClD,EA6QE,UA5YF,CA4Ya,QA5YJ,AAAiB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC1D,EAAQ,YAAY,EACtB,EA2YE,YAlJF,CAkJe,QAlJa,AAAnB,CAAuB,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC5D,IAAI,EAAY,OAAO,EAAK,OAAO,CAAC,KAAK,EAAI,KACvC,EAAgB,AAWxB,SAAS,AAA6B,EAAS,GAAG,EAChD,OAAQ,GACN,IAAK,IAAK,OAAO,AAAC,GAAM,GAAuB,EAAG,IAClD,KAAK,IAAK,OAAO,AAAC,GAAM,GAAuB,EAAG,IAClD,KAAK,IAAK,OAAO,AAAC,GAAM,GAAc,GAAG,WAAW,EACpD,KAAK,IAAK,OAAO,AAAC,GAAM,GAAc,EACtC,KAAK,IACI,OAAO,AAAC,GAAO,EAAG,QAAQ,EACrC,CACF,EApBoD,EAAK,OAAO,CAAC,IAAI,EAEnE,OAAO,GAAW,EAAM,EAAM,EAAS,EADZ,IAAM,IAAM,EAAc,GACC,EADc,KAEtE,EA8IE,UAjXF,CAiXa,QAjXJ,AAAiB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC1D,EAAQ,SAAS,CAAC,CAAE,kBAAmB,EAAc,iBAAiB,EAAI,CAAE,GAC5E,EAAK,EAAK,QAAQ,CAAE,GACpB,EAAQ,UAAU,CAAC,CAAE,mBAAoB,EAAc,kBAAkB,EAAI,CAAE,EACjF,EA8WE,IAvWF,CAuWO,QAvWE,AAAW,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EACpD,EAAQ,SAAS,CAAC,CAChB,OAAO,EACP,kBAAmB,EAAc,iBAAiB,EAAI,CACxD,GACA,EAAK,EAAK,QAAQ,CAAE,GACpB,EAAQ,UAAU,CAAC,CAAE,mBAAoB,EAAc,kBAAkB,EAAI,CAAE,EACjF,EAiWE,MAzFF,CAyFS,QAzFA,AAAa,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,MAMlC,EAAM,EAAF,AAAQ,EAAF,AAAW,EALzC,GAKuC,IAtBzC,AAiBS,IAK+C,KAtB/C,AAAa,CAAI,CAAE,CAAM,EAChC,IAAe,IAAX,EAAiB,AAAE,OAAO,EAC9B,GAAI,CAAC,EAAQ,IAAF,EAAS,GAEpB,GAAM,SAAE,CAAO,CAAE,KAAG,CAAE,CAAG,AAjB3B,SAAS,AAAoB,CAAS,EACpC,IAAM,EAAU,EAAE,CACZ,EAAM,EAAE,CACd,IAAK,IAAM,KAAY,EACjB,EAAS,MADmB,IACT,CAAC,KACtB,CAD4B,CACpB,IAAI,CAAC,EAAS,SAAS,CAAC,IACvB,EAAS,UAAU,CAAC,MAAM,AACnC,EAAI,IAAI,CAAC,EAAS,SAAS,CAAC,IAGhC,MAAO,CAAE,QAAS,EAAS,IAAK,CAAI,CACtC,EAM8C,GACtC,EAAc,CAAC,EAAK,EAAD,GAAS,EAAI,EAAA,CAAE,CAAE,KAAK,CAAC,KAC1C,EAAU,CAAC,EAAK,EAAD,AAAM,EAAI,EAAA,CAAE,CAAE,KAAK,CAAC,KAEzC,OAAO,EAAY,IAAI,CAAC,GAAK,EAAQ,QAAQ,CAAC,KAAO,EAAQ,IAAI,CAAC,GAAK,EAAI,QAAQ,CAAC,GACtF,EAQqB,EAAK,OAAO,CAAE,EAAQ,OAAO,CAAC,MAAM,KAEvC,IAAM,IAAM,IAAS,OAIrC,EAAQ,SAAS,CAAC,CAAE,kBAAmB,EAAc,iBAAiB,AAAC,GACvE,EAAK,EAAK,QAAQ,CAAE,GACpB,EAAQ,UAAU,CAAC,CAAE,mBAAoB,EAAc,kBAAkB,AAAC,KAPtE,GAAgB,EAAM,EAAM,EAAS,EAE3C,EAsFE,aAvFI,CAzEN,CAgKiB,QAhKR,AAAqB,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EAC9D,IAAM,EAAS,EAAc,UAAU,EAAI,MAC3C,OAAO,GAAW,EAAM,EAAM,EAAS,EAAe,IAAM,EAC9D,EA8JE,IAzYF,CAyYO,QAzYE,AAAW,CAAI,CAAE,CAAI,CAAE,CAAO,CAAE,CAAa,EACpD,EAAQ,uBAAuB,EACjC,CAwYA,GAUA,IAAM,GAAkB,CACtB,aAAc,CACZ,UAAW,CAAE,OAAQ,CACrB,QAAS,YACT,oBAAoB,CACtB,EACA,gBAAgB,EAChB,iBAAkB,CAAC,EACnB,WAAY,CAAC,EACb,OAAQ,CACN,SAAU,MACV,qBAAiB,EACjB,mBAAe,EACf,cAAU,EACV,eAAiB,KAAK,IACxB,EACA,cAAe,CACb,iBAAkB,GAClB,eAAgB,EAAE,AACpB,EACA,kBAAkB,EAClB,UAAW,CACT,CAAE,SAAU,IAAK,OAAQ,QAAS,EAClC,CACE,SAAU,IACV,OAAQ,SACR,QAAS,CACP,QAAS,KACT,0BAA0B,EAC1B,YAAY,EACZ,aAAc,CAAC,IAAK,IAAI,CACxB,aAAa,CACf,CACF,EACA,CAAE,SAAU,UAAW,OAAQ,QAAS,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EACjG,CAAE,SAAU,QAAS,OAAQ,QAAS,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EAC/F,CACE,SAAU,aACV,OAAQ,aACR,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,EAAG,gBAAgB,CAAK,CAC/E,EACA,CAAE,SAAU,KAAM,OAAQ,WAAY,EACtC,CAAE,SAAU,MAAO,OAAQ,QAAS,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EAC7F,CAAE,SAAU,SAAU,OAAQ,QAAS,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EAChG,CAAE,SAAU,OAAQ,OAAQ,QAAS,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EAC9F,CAAE,SAAU,KAAM,OAAQ,UAAW,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,EAAG,WAAW,CAAK,CAAE,EAC/G,CAAE,SAAU,KAAM,OAAQ,UAAW,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,EAAG,WAAW,CAAK,CAAE,EAC/G,CAAE,SAAU,KAAM,OAAQ,UAAW,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,EAAG,WAAW,CAAK,CAAE,EAC/G,CAAE,SAAU,KAAM,OAAQ,UAAW,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,EAAG,UAAW,EAAK,CAAE,EAC/G,CAAE,SAAU,KAAM,OAAQ,UAAW,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,EAAG,WAAW,CAAK,CAAE,EAC/G,CAAE,SAAU,KAAM,OAAQ,UAAW,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,EAAG,WAAW,CAAK,CAAE,EAC/G,CAAE,SAAU,SAAU,OAAQ,QAAS,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EAChG,CACE,SAAU,KACV,OAAQ,iBACR,QAAS,CAAE,kBAAmB,EAAG,YAAQ,EAAW,mBAAoB,CAAE,CAC5E,EACA,CACE,SAAU,MACV,OAAQ,QACR,QAAS,CAAE,QAAS,KAAM,aAAc,CAAC,IAAK,IAAI,AAAC,CACrD,EACA,CAAE,SAAU,OAAQ,OAAQ,QAAS,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EAC9F,CAAE,SAAU,MAAO,OAAQ,QAAS,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EAC7F,CACE,SAAU,KACV,OAAQ,cACR,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CACzD,EACA,CAAE,SAAU,IAAK,OAAQ,YAAa,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EAC/F,CAAE,SAAU,MAAO,OAAQ,MAAO,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EAC3F,CAAE,SAAU,UAAW,OAAQ,QAAS,QAAS,CAAE,kBAAmB,EAAG,mBAAoB,CAAE,CAAE,EACjG,CACE,SAAU,QACV,OAAQ,QACR,QAAS,CACP,WAAY,EACZ,kBAAmB,EACnB,eAAgB,GAChB,WAAY,EACZ,mBAAoB,EACpB,sBAAsB,CACxB,CACF,EACA,CACE,SAAU,KACV,OAAQ,gBACR,QAAS,CAAE,WAAY,MAAO,kBAAmB,EAAG,mBAAoB,CAAE,CAC5E,EACA,CAAE,SAAU,MAAO,OAAQ,KAAM,EAClC,CACD,OAAQ,EAAE,CACV,qBAAsB,YACtB,SAAU,EACZ,EAGM,GAAiB,CAAC,EAAK,EAAK,IAAY,IAAI,EAAI,CAChD,GAAiB,CAAC,EAAK,EAAK,IAC/B,EAAI,IAAI,CAAC,GAAkB,UAAb,EACX,KADkB,GAHmB,IAIzB,KAAK,EAJoC,CAKrD,EADsB,CACP,EAAK,GA4C1B,EA5C+B,IADS,GA6C/B,GAAS,CAAI,CAAE,EAAU,CAAC,CAAC,CAAE,CAAoB,EACxD,KA7CsD,CA6C/C,CAlCT,CAiCiD,QAjCxC,AAAS,EAAU,CAAC,CAAC,EAc5B,MALA,CARA,EAAU,CAAA,EAAA,GAAA,OAAA,AAAK,EACb,GACA,EACA,CACE,WAAY,GACZ,YAAa,AAAC,GAAkB,cAAR,EAAuB,QAAiB,CAClE,EAAA,EAEM,UAAU,CAAG,OAAO,MAAM,CAAC,CAAC,EAAG,GAAmB,GAAgB,EAAQ,UAAU,EAC5F,EAAQ,SAAS,CAAG,GAA0B,EAAQ,SAAS,CAAG,GAAK,EAAE,QAAQ,EAEjF,AA+BF,SAAS,AAAyB,CAAO,EACvC,GAAI,EAAQ,IAAI,CAAE,CAChB,IAAM,EAAiB,OAAO,OAAO,CAAC,EAAQ,IAAI,EAAE,GAAG,CACrD,CAAC,CAAC,EAAU,EAAW,GAAK,CAAC,CAAE,GAAG,CAAU,CAAE,SAAU,GAAY,IAAI,CAAC,EAE3E,EAAQ,SAAS,CAAC,IAAI,IAAI,GAC1B,EAAQ,SAAS,CAAG,GAA0B,EAAQ,SAAS,CAAG,GAAK,EAAE,QAAQ,CACnF,CAEA,SAAS,EAAK,CAAG,CAAE,CAAI,CAAE,CAAK,EAC5B,IAAM,EAAW,EAAK,GAAG,GACzB,IAAK,IAAM,KAAO,EAAM,CACtB,IAAI,EAAS,CAAG,CAAC,EAAI,CAChB,IACH,EAAS,CAAC,CADC,CAEX,CAAG,CAAC,EAAI,CAAG,GAEb,EAAM,CACR,CACA,CAAG,CAAC,EAAS,CAAG,CAClB,CAEA,GAAI,EAAQ,KAAD,MAAe,CAAE,CAC1B,IAAM,EAAc,EAAQ,KAAD,MAAe,CAC1C,EACE,EACA,CAAC,eAAgB,YAAY,CAC5B,MAAM,OAAO,CAAC,GAAe,EAAc,CAAC,EAAY,CAE7D,CAKA,IAAK,IAAM,UAJ2B,IAAlC,EAAQ,KAAqC,AAAtC,aAAsB,EAC/B,EAAI,EAAS,CAAC,eAAgB,qBAAqB,CAAE,EAAQ,KAAD,aAAsB,EAG3D,EAAQ,SAAS,CAAE,CAChB,WAAtB,EAAW,MAAM,EAAiB,GAAI,EAAY,CAAC,UAAW,iBAAiB,GAAG,AACpF,EAAI,EAAY,CAAC,UAAW,eAAe,EAAE,EAGnD,EAtE0B,GA98B1B,AAg9BS,SAh9BA,AAAW,EAAU,CAAC,CAAC,EAC9B,IAAM,EAAyB,EAAQ,SAAS,CAAC,MAAM,CAAC,GAAK,CAAC,EAAE,MAAM,EACtE,GAAI,EAAuB,MAAM,CAC/B,CADiC,KAC3B,AAAI,MACR,iDACA,EAAuB,GAAG,CAAC,GAAK,CAAC,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,OAG9D,IAAM,EAAS,IAAI,GACjB,EAAQ,SAAS,CAAC,GAAG,CAAC,GAAK,CAAC,EAAE,QAAQ,CAAE,EAAE,GAC1C,KAAK,CAAC,IAEgC,YAApC,AAAgD,OAAzC,EAAQ,gBAAgB,GACjC,EAAQ,gBAAgB,CAAG,AA4I/B,SAAS,AAAsB,CAAI,EACjC,GAAI,CAAC,GAAqC,GAAG,CAAhC,OAAO,IAAI,CAAC,GAAM,MAAM,CACnC,OAAO,AAGT,IAAM,EAAU,OAAO,OAAO,CAAC,GAAM,MAAM,CAAC,CAAC,EAAG,EAAE,GAAK,AAAM,QACvD,EAAQ,AAAI,OAChB,EACG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAK,CAAC,CAAC,EAAE,AAxqCf,AAwqC6B,IAAI,EAAE,CAAC,EAAE,CAxqClC,OAAO,CAAC,UAAW,GAAK,MAAQ,EAAE,UAAU,GAAG,QAAQ,CAAC,IAAI,QAAQ,CAAC,EAAG,MAwqCpC,CAAC,CAAC,EAC5C,IAAI,CAAC,KACR,KAEI,EAAS,EAAQ,GAAG,CAAC,CAAC,EAAG,EAAE,GAAK,GAChC,EAAW,CAAC,EAAG,GAAG,IAAQ,CAAM,CAAC,EAAI,SAAS,CAAC,GAAM,GAAI,CAC/D,OAAO,AAAC,GAAQ,EAAI,OAAO,CAAC,EAAO,EACrC,EA3JoD,EAAQ,gBAAgB,GAG1E,IAAM,EAAsB,IAAI,GAC9B,EAAQ,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAG,IAAM,CAAC,EAAG,EAAI,EAAE,GACvD,KAAK,CAAC,IACR,SAAS,EAAkB,CAAG,MAiDZ,EAhDC,CAgDE,CAAE,EAhDC,EAgDQ,EAhDC,CAgDH,CAC9B,IAAM,EAAU,EAAE,CA6BlB,MA9BmD,CAqB/B,AAIpB,GAHE,EAAQ,MAAM,CAAC,QAAQ,CAnBzB,CAoBE,QApBsB,AAAf,CAAmB,CAA6B,CAA3B,AAA8B,EAE1D,IAAK,IAAM,KADX,EAAM,CACa,CADT,KAAK,AADqC,CACpC,EAAG,EAAQ,MAAM,CAAC,aAAa,EACvB,CACtB,GAAkB,AAAd,OAAqB,GAAhB,IAAI,CACX,SAEF,IAAM,EAAsB,EAAoB,KAAK,CAAC,GAMtD,GALI,EAAsB,EACxB,CAD2B,CACnB,IAAI,CAAC,CAAE,cAAe,EAAqB,QAAS,CAAK,GACxD,EAAK,QAAQ,EAAE,AACxB,EAAK,EAAK,QAAQ,EAEhB,EAAQ,MAAM,EAAI,EAAQ,MAAM,CAAC,eAAe,CAClD,CADoD,KAGxD,CACF,GAMY,GAEyB,cAAc,CAA/C,EAAQ,YAAY,CAAC,OAAO,EAC9B,EAAQ,IAAI,CAAC,CAAC,EAAG,IAAM,EAAE,aAAa,CAAG,EAAE,aAAa,EAElD,EAAQ,YAAY,CAAC,kBAAkB,EAAuB,IAAnB,EAAQ,MAAM,CAC7D,EACA,EAAQ,GAAG,CAAC,GAAK,EAAE,OAAO,CA/E9B,CAEA,IAAM,EAAc,GAClB,EAAQ,MAAM,CAAC,QAAQ,CACvB,GACA,SAAU,CAAG,CAAE,CAAO,EACpB,EAAQ,SAAS,CAAC,EAAQ,MAAM,CAAC,QAAQ,EAAI,GAC/C,GAGF,OAAO,SAAU,CAAI,CAAE,CAAoB,MAoB3B,EAnBC,EAmBG,AAAE,AApBY,EACX,EAmBS,EAnBC,EAmBH,AAAW,EAnBC,CAmBH,CAAU,EAnBC,AAmBH,EAAoB,EAnBC,EAmBG,AACvE,IAAM,EAAiB,EAAQ,AADkC,MAC5B,CAAC,cAAc,CAChD,GAAkB,GAAQ,EAAK,MAAM,CAAG,IAC1C,QAAQ,IADkD,AAC9C,CACV,CAAC,aAAa,EAAE,EAAK,MAAM,CAAC,2BAA2B,EAAE,EAAe,8BAA8B,CAAC,EAEzG,EAAO,EAAK,SAAS,CAAC,EAAG,IAI3B,IAAM,EAAQ,EAAiB,AJ7mC3B,AI4mCa,SJ5mCb,CAAoC,CAAE,CAAiB,MACnD,EAAU,IAAI,EAAA,KAAA,EAAsB,GAE1C,OADA,IAAI,GAAA,EAAgB,GAAS,GAAG,CAAA,GAChC,EAAe,IAAA,EIymCc,EAAM,CAAE,eAAgB,EAAQ,cAAc,AAAC,GACtC,QAAQ,EAC1C,EAAU,IAAI,GAAiB,EAAS,EAAQ,GAEtD,OADA,EAAK,EAAO,GACL,EAAQ,QAAQ,EA/BvB,CACF,EA86BmB,GACnB,EAmBiB,GAAS,EAAM,EAChC,kNYxgEA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAgEA,EAAA,EAAA,CAAA,CAAA,OAkBA,EAAA,EAAA,CAAA,CAAA,OA8BA,EAAA,EAAA,CAAA,CAAA,6CA7JA,IAAI,EAAY,OAAO,cAAc,CACjC,EAAa,OAAO,gBAAgB,CACpC,EAAoB,OAAO,yBAAyB,CACpD,EAAsB,OAAO,qBAAqB,CAClD,EAAe,OAAO,SAAS,CAAC,cAAc,CAC9C,EAAe,OAAO,SAAS,CAAC,oBAAoB,CACpD,EAAkB,CAAC,EAAK,EAAK,IAAU,KAAO,EAAM,EAAU,EAAK,EAAK,CAAE,YAAY,EAAM,cAAc,EAAM,UAAU,QAAM,CAAM,GAAK,CAAG,CAAC,EAAI,CAAG,EACtJ,EAAiB,CAAC,EAAG,KACvB,IAAK,IAAI,KAAQ,IAAM,CAAD,CAAK,EAAC,CAAC,CACvB,CAAJ,CAAiB,IAAI,CAAC,EAAG,IACvB,EAAgB,EAAG,EAAM,CAAC,CAAC,EAAK,EACpC,GAAI,EACF,IAAK,IAAI,KAAQ,EAAoB,GAAI,AACnC,EAAa,IAAI,CAAC,EAAG,IACvB,EAAgB,EAAG,EAAM,CAAC,CAAC,EAAK,EAEtC,OAAO,CACT,EACI,EAAgB,CAAC,EAAG,IAAM,EAAW,EAAG,EAAkB,IAC1D,EAAU,CAAC,EAAQ,EAAa,IAC3B,IAAI,QAAQ,CAAC,EAAS,KAC3B,IAAI,EAAY,AAAC,IACf,GAAI,CACF,EAAK,EAAU,IAAI,CAAC,GACtB,CAAE,MAAO,EAAG,CACV,EAAO,EACT,CACF,EACI,EAAW,AAAC,IACd,GAAI,CACF,EAAK,EAAU,KAAK,CAAC,GACvB,CAAE,MAAO,EAAG,CACV,EAAO,EACT,CACF,EACI,EAAO,AAAC,GAAM,EAAE,IAAI,CAAG,EAAQ,EAAE,KAAK,EAAI,QAAQ,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,EAAW,GACvF,EAAK,CAAC,EAAY,EAAU,KAAK,CAAC,EAAQ,EAAA,CAAY,CAAE,IAAI,GAC9D,GA0CE,EAAe,EAAe,CAAC,EAAG,GACtC,GAAI,EAAa,QAAQ,CAAE,CACzB,IAAM,EAAgB,EAAa,QAAQ,CAAC,IAAI,CAAC,KAAK,CACtD,EAAa,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAG,CAAC,EAAM,EAAS,EAAO,KACxD,IAAM,EAAO,EAAK,OAAO,GACnB,EAAoB,EAAc,EAAM,EAAS,EAAO,SAC9D,AAAkB,wBAAwB,CAAtC,EAAK,IAAI,CAvCjB,AAwC6B,SAxCpB,EAAkB,CAAG,CAAE,CAAQ,EACtC,GAAI,MAAM,OAAO,CAAC,GAChB,GADsB,IACf,EAAI,GAAG,CAAC,AAAC,GAAa,EAAkB,EAAU,IAE3D,GAAmB,UAAf,OAAO,EAAkB,CAC3B,GAAiB,SAAS,CAAtB,EAAI,IAAI,CACV,OAAO,EAAc,EAAe,CAAC,EAAG,GAAM,CAC5C,SAAU,EAAkB,EAAI,QAAQ,CAAE,GAC1C,eAAgB,EACd,EAAI,cAAc,CAClB,EAEJ,GAEF,GAAI,aAAc,EAChB,GADqB,IACd,EAAc,EAAe,CAAC,EAAG,GAAM,CAC5C,SAAU,EAAkB,EAAI,QAAQ,CAAE,EAC5C,GAEF,GAAI,UAAW,EACb,GADkB,IACX,EAAc,EAAe,CAAC,EAAG,GAAM,CAC5C,MAAO,EAAkB,EAAI,KAAK,CAAE,EACtC,GAEF,GAAiB,YAAY,CAAzB,EAAI,IAAI,CACV,OAAO,EAAc,EAAe,CAAC,EAAG,GAAM,CAC5C,cAAe,EAAkB,EAAI,aAAa,CAAE,GACpD,aAAc,EAAkB,EAAI,YAAY,CAAE,EACpD,EAEJ,CACA,OAAO,EAAS,EAClB,EAQ+C,EAAoB,AAAD,GAC1D,AAAmB,UAAf,OAAO,GAAiC,QAAQ,CAArB,EAAI,IAAI,CAC9B,EAAI,IAAI,CAAG,GAAK,IAElB,GAIJ,CACT,CACF,CACA,IAAI,EAAW,CACb,UAAW,KACX,SAAU,EACV,QAAS,CAAC,EAAa,CACvB,iBAAiB,EACjB,OAAQ,MACV,EACI,EAAS,CAAC,EAAK,EAAU,CAAC,CAAC,GACtB,CAAA,EAAA,EAAA,MAAA,AAAM,EAAC,EAAI,UAAU,CAAC,KAAM,IAAK,EAAe,EAAe,CAAC,EAAG,GAAW,IAOnF,EAAqB,CACvB,CAAE,SAAU,MAAO,OAAQ,MAAO,EAClC,CAAE,SAAU,2BAA4B,OAAQ,MAAO,EACvD,CACE,SAAU,IACV,QAAS,CAAE,cAAc,CAAM,CACjC,EACD,CACD,SAAS,EAAY,CAAK,CAAE,CAAO,EACjC,MAAO,CAAA,EAAA,EAAA,OAAA,AAAO,EAAC,EAAO,EAAe,CACnC,UAAW,CACb,EAAG,GACL,CAIA,IAAI,EAAU,IAAI,YAAY,SAC1B,EAAa,AAAC,GAAW,EAAQ,KAAK,EAAG,KAAM,YACjD,IAAI,EAAS,GACb,GAAI,WAAY,EAAQ,CACtB,IAAM,EAAiB,IAAI,eAAe,CACxC,MAAM,CAAK,EACT,GAAU,EAAQ,MAAM,CAAC,EAC3B,CACF,EACA,OAAM,EAAO,MAAM,CAAC,EACtB,KAAO,CACL,IAAM,EAAW,IAAI,EAAA,QAAQ,CAAC,CAC5B,MAAM,CAAK,CAAE,CAAS,CAAE,CAAQ,EAC9B,GAAU,EAAQ,MAAM,CAAC,GACzB,GACF,CACF,GACA,EAAO,IAAI,CAAC,GACZ,MAAM,IAAI,QAAQ,CAAC,EAAS,KAC1B,EAAS,EAAE,CAAC,QAAS,GACrB,EAAS,EAAE,CAAC,QAAS,KACnB,GACF,EACF,EACF,CACA,OAAO,CACT,GAII,EAAS,CAAC,EAAM,IAAY,EAAQ,KAAK,EAAG,KAAM,YACpD,IAKI,EALE,EAAmC,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,KAApB,GAA4B,CAAE,CAAE,QAAnB,CAA6B,CAAK,GAClE,EAAiB,MAAM,EAAA,CAAA,CAAA,OAA2B,IAAI,CAC1D,AACA,AAAC,GAAM,EAAE,OAAO,EAyBlB,GAtBI,OAAO,MAAM,CAAC,EAAgB,SAJU,iBAK1C,CAD2D,CACnD,MAAM,GACZ,MAAM,EAAe,AAArB,sBAA2C,CAAC,EAAkB,CAC5D,qBAAsB,GACxB,EAAA,CAAC,CAD8B,CAIjC,MAAM,IAAI,MAJwC,EAIhC,CAAC,EAAS,KAC1B,IAAM,EAAS,EAAe,sBAAsB,CAAC,EAAkB,CACrE,aACE,OAAO,EAAQ,IAAI,CAAE,KAAM,YACzB,EAAQ,MAAM,EAAW,GACzB,GACF,EACF,EACA,QAAQ,CAAK,EACX,EAAO,EACT,EACA,qBAAsB,GACxB,EACF,EAFiC,CAIpB,MAAX,EAAkB,KAAK,EAAI,CAJqB,CAIb,SAAS,CAC9C,CADgD,MACzC,EAAY,EAAO,EAAQ,iBAAiB,EAGrD,IAAM,EAAW,GAAG,yHAAU,EAAM,OAAO,CAAC,gBAAiB,KAAK,OAClE,CAAe,MAAX,EAAkB,KAAK,EAAI,EAAQ,MAAA,AAAM,EAAE,AACtC,EAAO,GAET,CACT,GAGI,EAAc,CAAC,EAAS,IACnB,EAAO,EAAS", "ignoreList": [0, 1, 2, 3, 7, 15, 16, 17, 18, 19, 20, 22, 29, 30]}