module.exports=[80139,a=>{"use strict";a.s(["AspectRatio",()=>f],80139);var b=a.i(69720),c=a.i(29611),d=a.i(53834),e=c.forwardRef((a,c)=>{let{ratio:e=1,style:f,...g}=a;return(0,b.jsx)("div",{style:{position:"relative",width:"100%",paddingBottom:`${100/e}%`},"data-radix-aspect-ratio-wrapper":"",children:(0,b.jsx)(d.Primitive.div,{...g,ref:c,style:{...f,position:"absolute",top:0,right:0,bottom:0,left:0}})})});function f({...a}){return(0,b.jsx)(e,{"data-slot":"aspect-ratio",...a})}e.displayName="AspectRatio"}];

//# sourceMappingURL=components_ui_aspect-ratio_tsx_e19e8e0b._.js.map