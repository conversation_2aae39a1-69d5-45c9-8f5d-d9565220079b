/* [project]/app/aeonikregular_756171f0.module.css [app-client] (css) */
@font-face {
  font-family: aeonikRegular;
  src: url("../media/fonnts_com_Aeonik_Regular-s.p.62bbd4fd.ttf") format("truetype");
  font-display: swap;
}

@font-face {
  font-family: aeonikRegular Fallback;
  src: local(Arial);
  ascent-override: 98.63%;
  descent-override: 19.73%;
  line-gap-override: 0.0%;
  size-adjust: 101.39%;
}

.aeonikregular_756171f0-module__It3Qua__className {
  font-family: aeonikRegular, aeonikRegular Fallback;
}

.aeonikregular_756171f0-module__It3Qua__variable {
  --font-aeonik-regular: "aeonikRegular", "aeonikRegular Fallback";
}

/*# sourceMappingURL=app_aeonikregular_756171f0_module_css_bad6b30c._.single.css.map*/