{"version": 3, "sources": ["turbopack:///[project]/components/ui/aspect-ratio.tsx", "turbopack:///[project]/node_modules/.pnpm/@radix-ui+react-aspect-rati_f674f65a24f4a42a9a85520fdf6e953c/node_modules/@radix-ui/react-aspect-ratio/dist/index.mjs"], "sourcesContent": ["'use client';\n\nimport * as AspectRatioPrimitive from '@radix-ui/react-aspect-ratio';\n\nfunction AspectRatio({\n  ...props\n}: React.ComponentProps<typeof AspectRatioPrimitive.Root>) {\n  return <AspectRatioPrimitive.Root data-slot=\"aspect-ratio\" {...props} />;\n}\n\nexport { AspectRatio };\n", "// src/aspect-ratio.tsx\nimport * as React from \"react\";\nimport { Primitive } from \"@radix-ui/react-primitive\";\nimport { jsx } from \"react/jsx-runtime\";\nvar NAME = \"AspectRatio\";\nvar AspectRatio = React.forwardRef(\n  (props, forwardedRef) => {\n    const { ratio = 1 / 1, style, ...aspectRatioProps } = props;\n    return /* @__PURE__ */ jsx(\n      \"div\",\n      {\n        style: {\n          // ensures inner element is contained\n          position: \"relative\",\n          // ensures padding bottom trick maths works\n          width: \"100%\",\n          paddingBottom: `${100 / ratio}%`\n        },\n        \"data-radix-aspect-ratio-wrapper\": \"\",\n        children: /* @__PURE__ */ jsx(\n          Primitive.div,\n          {\n            ...aspectRatioProps,\n            ref: forwardedRef,\n            style: {\n              ...style,\n              // ensures children expand in ratio\n              position: \"absolute\",\n              top: 0,\n              right: 0,\n              bottom: 0,\n              left: 0\n            }\n          }\n        )\n      }\n    );\n  }\n);\nAspectRatio.displayName = NAME;\nvar Root = AspectRatio;\nexport {\n  AspectRatio,\n  Root\n};\n//# sourceMappingURL=index.mjs.map\n"], "names": [], "mappings": "yFCCA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAGI,EAAc,EAAA,UAAgB,CAChC,CAAC,EAAO,KACN,GAAM,OAAE,EAAQ,CAAK,GAAD,IAAG,CAAK,CAAE,GAAG,EAAkB,CAAG,EACtD,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EACxB,IADkB,EAElB,CACE,MAAO,CAEL,SAAU,WAEV,MAAO,OACP,cAAe,CAAA,EAAG,IAAM,EAAM,CAAC,CAAC,AAClC,EACA,kCAAmC,GACnC,SAA0B,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAC3B,EAAA,EADqB,OACZ,CAAC,GAAG,CACb,CACE,GAAG,CAAgB,CACnB,IAAK,EACL,MAAO,CACL,GAAG,CAAK,CAER,SAAU,WACV,IAAK,EACL,MAAO,EACP,OAAQ,EACR,KAAM,CACR,CACF,EAEJ,EAEJ,GDjCF,SAAS,EAAY,CACnB,GAAG,EACoD,EACvD,MAAO,CAAA,EAAA,EAAA,GAAA,ECiCE,ADjCD,EAAA,CAA0B,YAAU,eAAgB,GAAG,CAAK,EACtE,CC+BA,EAAY,WAAW,CAnCZ,EAmCe", "ignoreList": [1]}