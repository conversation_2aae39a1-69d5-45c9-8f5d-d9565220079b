module.exports = [
"[project]/node_modules/.pnpm/@react-email+render@1.2.1_r_c5a25c3dc1e6309fdf7858c89bda5b7e/node_modules/@react-email/render/dist/node/index.mjs [app-rsc] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/b8cd5_next_dist_compiled_react-dom_server_node_2997ed44.js",
  "server/chunks/ssr/node_modules__pnpm_c928f605._.js",
  "server/chunks/ssr/[root-of-the-server]__f8153c6d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@react-email+render@1.2.1_r_c5a25c3dc1e6309fdf7858c89bda5b7e/node_modules/@react-email/render/dist/node/index.mjs [app-rsc] (ecmascript)");
    });
});
}),
];