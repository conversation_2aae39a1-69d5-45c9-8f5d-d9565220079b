'use server';

import { Resend } from 'resend';
import { SubscribedTemplate } from '@/components/emails/subscribe-template';
import type { TSubFormSchema } from './schema';

// Initialize Resend client with API key from environment variables
const resendClient = new Resend(process.env.RESEND_API_KEY);

export async function subscribe(formData: TSubFormSchema) {
  try {
    const { email } = formData;

    const response = await resendClient.emails.send({
      from: 'Rathon <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: 'New Subscriber from Rathon Website',
      react: SubscribedTemplate({ email }) as React.ReactElement,
      replyTo: email,
      tags: [{ name: 'source', value: 'website_subscribe' }],
    });

    if (response.error) {
      return { success: false, error: response.error };
    }

    return { success: true };
  } catch (error) {
    return { success: false, error };
  }
}
