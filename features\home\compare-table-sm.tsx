/** biome-ignore-all lint/suspicious/noArrayIndexKey: <explanation> */
'use client';

import { Check, X } from 'lucide-react';
import { featuresCompare } from '@/config/docs';
import { cn } from '@/lib/utils';

const tools = ['Highlight', 'ChatGPT', 'Claude', 'Raycast', 'Notion'];

export default function FeatureTableSm() {
  return (
    <div className="no-scrollbar block w-full overflow-x-auto md:hidden">
      <table
        className="w-max min-w-[700px] border-collapse border-spacing-0"
        style={{
          borderSpacing: '0px',
        }}
      >
        <thead>
          <tr>
            <th className="sticky top-0 left-0 z-50 w-[120px] min-w-[120px] bg-[#090909] text-left" />

            {featuresCompare.map((f, i) => (
              <th
                className="sticky top-0 z-40 w-[120px] min-w-[120px] bg-[#090909] text-center font-normal text-muted-foreground text-sm"
                key={i}
              >
                <span className="block px-3 py-2 text-base leading-tight">
                  {f.feature}
                </span>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {tools.map((tool, i) => (
            <tr className="border-0.5 border-muted/60 border-b" key={i}>
              <td className="sticky left-0 z-40 w-[120px] min-w-[120px] bg-[#090909] px-3 py-3 pr-4 align-middle">
                <span className="text-nowrap font-medium font-sans text-base text-primary leading-tight">
                  {tool}
                </span>
              </td>
              {featuresCompare.map((f, index) => (
                <td
                  className={cn(
                    'w-[120px] min-w-[120px] p-4 text-center align-middle'
                  )}
                  key={index}
                >
                  <div
                    className={cn(
                      'mx-auto flex size-6 items-center justify-center rounded-lg text-black md:size-7',
                      f[tool as keyof typeof f]
                        ? 'bg-brand-400'
                        : 'bg-brand-100'
                    )}
                  >
                    {f[tool as keyof typeof f] ? (
                      <Check className="mx-auto size-4 text-black" />
                    ) : (
                      <X className="mx-auto size-4 text-brand-600" />
                    )}
                  </div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
