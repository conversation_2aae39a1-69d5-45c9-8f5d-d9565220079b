'use client';

import { Check, X } from 'lucide-react';
import { featuresCompare } from '@/config/docs';
import { cn } from '@/lib/utils';

const columns = ['Highlight', 'ChatGPT', 'Claude', 'Raycast', 'Notion'];

export default function FeatureTableLg() {
  return (
    <div className="no-scrollbar hidden w-full overflow-x-auto md:block">
      <table
        className="w-full min-w-[700px] border-collapse border-spacing-0"
        style={{
          borderSpacing: '0px',
        }}
      >
        <thead>
          <tr>
            <th
              className="sticky top-0 left-0 z-40 w-[200px] min-w-[200px] bg-[#090909] text-left font-normal text-sm md:w-[260px] md:min-w-[260px] md:text-base"
              scope="col"
            >
              <div className="flex flex-row items-center gap-3 rounded-t-xl py-2">
                <span className="font-medium font-sans text-lg text-white">
                  Features
                </span>
              </div>
            </th>
            {columns.map((col, i) => (
              <th
                className="sticky top-0 left-[200px] z-30 w-[120px] min-w-[120px] bg-[#090909] px-0 text-center font-normal text-sm md:left-auto md:text-base"
                key={col}
              >
                <div
                  className={cn(
                    'flex flex-col items-center font-medium font-sans',
                    i === 0
                      ? 'rounded-t-xl py-2 text-primary'
                      : 'p-4 text-muted-foreground'
                  )}
                >
                  <div className="flex items-center gap-1.5">
                    <a className="flex items-center gap-1.5" href="/">
                      <span className="pb-[1.5px] font-medium text-lg">
                        {col}
                      </span>
                    </a>
                  </div>
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {featuresCompare.map((row, i) => (
            <tr className="border-0.5 border-muted/60 border-b" key={i}>
              <td className="sticky left-0 z-30 w-[200px] min-w-[200px] bg-[#090909] py-3 pr-2 align-middle md:w-[260px] md:min-w-[260px] md:pr-8">
                <span className="text-nowrap font-medium text-base text-muted-foreground leading-tight">
                  {row.feature}
                </span>
              </td>
              {columns.map((col, i) => (
                <td
                  className={cn(
                    'w-[120px] min-w-[120px] p-4 text-center align-middle',
                    i === 0 && 'sticky left-[200px] z-20 bg-[#090909] md:static'
                  )}
                  key={col}
                >
                  <div
                    className={cn(
                      'mx-auto flex size-6 items-center justify-center rounded-lg text-black md:size-7',
                      row[col as keyof typeof row]
                        ? 'bg-brand-400'
                        : 'bg-brand-100'
                    )}
                  >
                    {row[col as keyof typeof row] ? (
                      <Check className="mx-auto size-4 text-black" />
                    ) : (
                      <X className="mx-auto size-4 text-brand-600" />
                    )}
                  </div>
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
