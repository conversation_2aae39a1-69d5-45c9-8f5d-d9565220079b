{"name": "@number-flow/react", "publishConfig": {"access": "public"}, "version": "0.5.10", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://barvian.me"}, "description": "A component to transition and format numbers.", "license": "MIT", "homepage": "https://number-flow.barvian.me", "repository": {"type": "git", "url": "https://github.com/barvian/number-flow", "directory": "src"}, "bugs": {"url": "https://github.com/barvian/number-flow/issues"}, "keywords": ["accessible", "odometer", "animation", "number-format", "number-animation", "animated-number"], "files": ["dist", "README.md"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}, "dependencies": {"esm-env": "^1.1.4", "number-flow": "0.5.8"}, "devDependencies": {"@playwright/test": "^1.48.0", "@types/node": "^22.7.9", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "bunchee": "^6.3.1", "react": "^18.3.1", "react-dom": "^18.3.1"}, "peerDependencies": {"react": "^18 || ^19", "react-dom": "^18 || ^19"}, "scripts": {"build": "bunchee --tsconfig tsconfig.build.json", "dev": "bunchee --watch", "test": "pnpm -r --workspace-concurrency 1 --filter=\"./test/apps/*\" test"}}