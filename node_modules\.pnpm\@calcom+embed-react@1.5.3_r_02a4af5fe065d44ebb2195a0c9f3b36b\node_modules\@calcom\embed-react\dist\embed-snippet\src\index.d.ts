import type { GlobalCalWithoutNs } from "@calcom/embed-core";
/**
 * When modifying this snippet, make sure to keep the snippets in following places in sync
 * 1. EmbedTabs.tsx
 * 2. embed-core/index.html
 * 3. app-store/wordpress/plugin.php
 */
export default function EmbedSnippet(url?: string): GlobalCalWithoutNs & {
    ns: Record<string, GlobalCalWithoutNs>;
};
export declare const EmbedSnippetString: string;
//# sourceMappingURL=index.d.ts.map