'use client';

import { useEffect, useState } from 'react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  useCarousel,
} from '@/components/ui/carousel';
import { reviews } from '@/config/docs';
import { ReviewCard } from './review-card';

function CarouselDots() {
  const { api } = useCarousel();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (!api) return;

    setCount(api.scrollSnapList().length);

    const onSelect = () => {
      setSelectedIndex(api.selectedScrollSnap());
    };

    api.on('select', onSelect);
    onSelect();

    return () => {
      api.off('select', onSelect);
    };
  }, [api]);

  return (
    <div className="mt-4 flex justify-center gap-2">
      {Array.from({ length: count }).map((_, i) => (
        <button
          className={`h-2 rounded-full transition-all ${
            i === selectedIndex ? 'w-8 bg-brand-600' : 'w-2 bg-muted'
          }`}
          key={i}
          onClick={() => api?.scrollTo(i)}
          type="button"
        />
      ))}
    </div>
  );
}

export default function RevieCardsMobile() {
  return (
    <div className="md:hidden">
      <Carousel>
        <CarouselContent>
          {reviews.map((review) => (
            <CarouselItem key={review.name}>
              <ReviewCard {...review} />
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
        <CarouselDots />
      </Carousel>
    </div>
  );
}
