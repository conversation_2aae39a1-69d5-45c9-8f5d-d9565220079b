module.exports=[7379,a=>{"use strict";a.s(["Separator",()=>i],7379);var b=a.i(69720),c=a.i(29611),d=a.i(53834),e="horizontal",f=["horizontal","vertical"],g=c.forwardRef((a,c)=>{var g;let{decorative:h,orientation:i=e,...j}=a,k=(g=i,f.includes(g))?i:e;return(0,b.jsx)(d.Primitive.div,{"data-orientation":k,...h?{role:"none"}:{"aria-orientation":"vertical"===k?k:void 0,role:"separator"},...j,ref:c})});g.displayName="Separator";var h=a.i(97895);function i({className:a,orientation:c="horizontal",decorative:d=!0,...e}){return(0,b.jsx)(g,{className:(0,h.cn)("shrink-0 bg-border data-[orientation=horizontal]:h-px data-[orientation=vertical]:h-full data-[orientation=horizontal]:w-full data-[orientation=vertical]:w-px",a),"data-slot":"separator",decorative:d,orientation:c,...e})}},16218,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{callServer:function(){return d.callServer},createServerReference:function(){return f.createServerReference},findSourceMapURL:function(){return e.findSourceMapURL}});let d=a.r(15467),e=a.r(57575),f=a.r(82211)},34255,a=>{"use strict";a.s(["default",()=>kC],34255);var b,c=a.i(69720),d=a.i(29611),e=a=>a instanceof Date,f=a=>null==a,g=a=>!f(a)&&!Array.isArray(a)&&"object"==typeof a&&!e(a),h=a=>g(a)&&a.target?"checkbox"===a.target.type?a.target.checked:a.target.value:a,i=(a,b)=>a.has((a=>a.substring(0,a.search(/\.\d+(\.|$)/))||a)(b));function j(a){let b,c=Array.isArray(a);if("undefined"!=typeof FileList&&FileList,a instanceof Date)b=new Date(a);else if(!(c||g(a)))return a;else if(b=c?[]:Object.create(Object.getPrototypeOf(a)),c||(a=>{let b=a.constructor&&a.constructor.prototype;return g(b)&&b.hasOwnProperty("isPrototypeOf")})(a))for(let c in a)a.hasOwnProperty(c)&&(b[c]=j(a[c]));else b=a;return b}var k=a=>/^\w*$/.test(a),l=a=>void 0===a,m=a=>Array.isArray(a)?a.filter(Boolean):[],n=a=>m(a.replace(/["|']|\]/g,"").split(/\.|\[/)),o=(a,b,c)=>{if(!b||!g(a))return c;let d=(k(b)?[b]:n(b)).reduce((a,b)=>f(a)?a:a[b],a);return l(d)||d===a?l(a[b])?c:a[b]:d},p=(a,b,c)=>{let d=-1,e=k(b)?[b]:n(b),f=e.length,h=f-1;for(;++d<f;){let b=e[d],f=c;if(d!==h){let c=a[b];f=g(c)||Array.isArray(c)?c:isNaN(+e[d+1])?{}:[]}if("__proto__"===b||"constructor"===b||"prototype"===b)return;a[b]=f,a=a[b]}};let q={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},r={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},s={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},t=d.default.createContext(null);t.displayName="HookFormContext";let u=()=>d.default.useContext(t);var v=(a,b,c,d=!0)=>{let e={defaultValues:b._defaultValues};for(let f in a)Object.defineProperty(e,f,{get:()=>(b._proxyFormState[f]!==r.all&&(b._proxyFormState[f]=!d||r.all),c&&(c[f]=!0),a[f])});return e};let w=d.default.useEffect;function x(a){let b=u(),{control:c=b.control,disabled:e,name:f,exact:g}=a||{},[h,i]=d.default.useState(c._formState),j=d.default.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return w(()=>c._subscribe({name:f,formState:j.current,exact:g,callback:a=>{e||i({...c._formState,...a})}}),[f,e,g]),d.default.useEffect(()=>{j.current.isValid&&c._setValid(!0)},[c]),d.default.useMemo(()=>v(h,c,j.current,!1),[h,c])}var y=(a,b,c,d,e)=>"string"==typeof a?(d&&b.watch.add(a),o(c,a,e)):Array.isArray(a)?a.map(a=>(d&&b.watch.add(a),o(c,a))):(d&&(b.watchAll=!0),c),z=a=>f(a)||"object"!=typeof a;function A(a,b,c=new WeakSet){if(z(a)||z(b))return a===b;if(e(a)&&e(b))return a.getTime()===b.getTime();let d=Object.keys(a),f=Object.keys(b);if(d.length!==f.length)return!1;if(c.has(a)||c.has(b))return!0;for(let h of(c.add(a),c.add(b),d)){let d=a[h];if(!f.includes(h))return!1;if("ref"!==h){let a=b[h];if(e(d)&&e(a)||g(d)&&g(a)||Array.isArray(d)&&Array.isArray(a)?!A(d,a,c):d!==a)return!1}}return!0}let B=a=>a.render(function(a){let b=u(),{name:c,disabled:e,control:f=b.control,shouldUnregister:g,defaultValue:k}=a,m=i(f._names.array,c),n=d.default.useMemo(()=>o(f._formValues,c,o(f._defaultValues,c,k)),[f,c,k]),r=function(a){let b=u(),{control:c=b.control,name:e,defaultValue:f,disabled:g,exact:h,compute:i}=a||{},j=d.default.useRef(f),k=d.default.useRef(i),l=d.default.useRef(void 0);k.current=i;let m=d.default.useMemo(()=>c._getWatch(e,j.current),[c,e]),[n,o]=d.default.useState(k.current?k.current(m):m);return w(()=>c._subscribe({name:e,formState:{values:!0},exact:h,callback:a=>{if(!g){let b=y(e,c._names,a.values||c._formValues,!1,j.current);if(k.current){let a=k.current(b);A(a,l.current)||(o(a),l.current=a)}else o(b)}}}),[c,g,e,h]),d.default.useEffect(()=>c._removeUnmounted()),n}({control:f,name:c,defaultValue:n,exact:!0}),s=x({control:f,name:c,exact:!0}),t=d.default.useRef(a),v=d.default.useRef(f.register(c,{...a.rules,value:r,..."boolean"==typeof a.disabled?{disabled:a.disabled}:{}}));t.current=a;let z=d.default.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!o(s.errors,c)},isDirty:{enumerable:!0,get:()=>!!o(s.dirtyFields,c)},isTouched:{enumerable:!0,get:()=>!!o(s.touchedFields,c)},isValidating:{enumerable:!0,get:()=>!!o(s.validatingFields,c)},error:{enumerable:!0,get:()=>o(s.errors,c)}}),[s,c]),B=d.default.useCallback(a=>v.current.onChange({target:{value:h(a),name:c},type:q.CHANGE}),[c]),C=d.default.useCallback(()=>v.current.onBlur({target:{value:o(f._formValues,c),name:c},type:q.BLUR}),[c,f._formValues]),D=d.default.useCallback(a=>{let b=o(f._fields,c);b&&a&&(b._f.ref={focus:()=>a.focus&&a.focus(),select:()=>a.select&&a.select(),setCustomValidity:b=>a.setCustomValidity(b),reportValidity:()=>a.reportValidity()})},[f._fields,c]),E=d.default.useMemo(()=>({name:c,value:r,..."boolean"==typeof e||s.disabled?{disabled:s.disabled||e}:{},onChange:B,onBlur:C,ref:D}),[c,e,s.disabled,B,C,D,r]);return d.default.useEffect(()=>{let a=f._options.shouldUnregister||g;f.register(c,{...t.current.rules,..."boolean"==typeof t.current.disabled?{disabled:t.current.disabled}:{}});let b=(a,b)=>{let c=o(f._fields,a);c&&c._f&&(c._f.mount=b)};if(b(c,!0),a){let a=j(o(f._options.defaultValues,c));p(f._defaultValues,c,a),l(o(f._formValues,c))&&p(f._formValues,c,a)}return m||f.register(c),()=>{(m?a&&!f._state.action:a)?f.unregister(c):b(c,!1)}},[c,f,m,g]),d.default.useEffect(()=>{f._setDisabledField({disabled:e,name:c})},[e,c,f]),d.default.useMemo(()=>({field:E,formState:s,fieldState:z}),[E,s,z])}(a));var C=(a,b,c,d,e)=>b?{...c[a],types:{...c[a]&&c[a].types?c[a].types:{},[d]:e||!0}}:{},D=a=>Array.isArray(a)?a:[a],E=()=>{let a=[];return{get observers(){return a},next:b=>{for(let c of a)c.next&&c.next(b)},subscribe:b=>(a.push(b),{unsubscribe:()=>{a=a.filter(a=>a!==b)}}),unsubscribe:()=>{a=[]}}},F=a=>g(a)&&!Object.keys(a).length,G=a=>"function"==typeof a,H=a=>!1;function I(a,b){let c=Array.isArray(b)?b:k(b)?[b]:n(b),d=1===c.length?a:function(a,b){let c=b.slice(0,-1).length,d=0;for(;d<c;)a=l(a)?d++:a[b[d++]];return a}(a,c),e=c.length-1,f=c[e];return d&&delete d[f],0!==e&&(g(d)&&F(d)||Array.isArray(d)&&function(a){for(let b in a)if(a.hasOwnProperty(b)&&!l(a[b]))return!1;return!0}(d))&&I(a,c.slice(0,-1)),a}var J=a=>{for(let b in a)if(G(a[b]))return!0;return!1};function K(a,b={}){let c=Array.isArray(a);if(g(a)||c)for(let c in a)Array.isArray(a[c])||g(a[c])&&!J(a[c])?(b[c]=Array.isArray(a[c])?[]:{},K(a[c],b[c])):f(a[c])||(b[c]=!0);return b}var L=(a,b)=>(function a(b,c,d){let e=Array.isArray(b);if(g(b)||e)for(let e in b)Array.isArray(b[e])||g(b[e])&&!J(b[e])?l(c)||z(d[e])?d[e]=Array.isArray(b[e])?K(b[e],[]):{...K(b[e])}:a(b[e],f(c)?{}:c[e],d[e]):d[e]=!A(b[e],c[e]);return d})(a,b,K(b));let M={value:!1,isValid:!1},N={value:!0,isValid:!0};var O=a=>{if(Array.isArray(a)){if(a.length>1){let b=a.filter(a=>a&&a.checked&&!a.disabled).map(a=>a.value);return{value:b,isValid:!!b.length}}return a[0].checked&&!a[0].disabled?a[0].attributes&&!l(a[0].attributes.value)?l(a[0].value)||""===a[0].value?N:{value:a[0].value,isValid:!0}:N:M}return M},P=(a,{valueAsNumber:b,valueAsDate:c,setValueAs:d})=>l(a)?a:b?""===a?NaN:a?+a:a:c&&"string"==typeof a?new Date(a):d?d(a):a;let Q={isValid:!1,value:null};var R=a=>Array.isArray(a)?a.reduce((a,b)=>b&&b.checked&&!b.disabled?{isValid:!0,value:b.value}:a,Q):Q;function S(a){let b=a.ref;return"file"===b.type?b.files:"radio"===b.type?R(a.refs).value:"select-multiple"===b.type?[...b.selectedOptions].map(({value:a})=>a):"checkbox"===b.type?O(a.refs).value:P(l(b.value)?a.ref.value:b.value,a)}var T=a=>l(a)?a:a instanceof RegExp?a.source:g(a)?a.value instanceof RegExp?a.value.source:a.value:a,U=a=>({isOnSubmit:!a||a===r.onSubmit,isOnBlur:a===r.onBlur,isOnChange:a===r.onChange,isOnAll:a===r.all,isOnTouch:a===r.onTouched});let V="AsyncFunction";var W=a=>!!a&&!!a.validate&&!!(G(a.validate)&&a.validate.constructor.name===V||g(a.validate)&&Object.values(a.validate).find(a=>a.constructor.name===V)),X=(a,b,c)=>!c&&(b.watchAll||b.watch.has(a)||[...b.watch].some(b=>a.startsWith(b)&&/^\.\w+/.test(a.slice(b.length))));let Y=(a,b,c,d)=>{for(let e of c||Object.keys(a)){let c=o(a,e);if(c){let{_f:a,...f}=c;if(a){if(a.refs&&a.refs[0]&&b(a.refs[0],e)&&!d)return!0;else if(a.ref&&b(a.ref,a.name)&&!d)return!0;else if(Y(f,b))break}else if(g(f)&&Y(f,b))break}}};function Z(a,b,c){let d=o(a,c);if(d||k(c))return{error:d,name:c};let e=c.split(".");for(;e.length;){let d=e.join("."),f=o(b,d),g=o(a,d);if(f&&!Array.isArray(f)&&c!==d)break;if(g&&g.type)return{name:d,error:g};if(g&&g.root&&g.root.type)return{name:`${d}.root`,error:g.root};e.pop()}return{name:c}}var $=(a,b,c)=>{let d=D(o(a,c));return p(d,"root",b[c]),p(a,c,d),a},_=a=>"string"==typeof a;function aa(a,b,c="validate"){if(_(a)||Array.isArray(a)&&a.every(_)||"boolean"==typeof a&&!a)return{type:c,message:_(a)?a:"",ref:b}}var ab=a=>!g(a)||a instanceof RegExp?{value:a,message:""}:a,ac=async(a,b,c,d,e,h)=>{let{ref:i,refs:j,required:k,maxLength:m,minLength:n,min:p,max:q,pattern:r,validate:t,name:u,valueAsNumber:v,mount:w}=a._f,x=o(c,u);if(!w||b.has(u))return{};let y=j?j[0]:i,z=a=>{e&&y.reportValidity&&(y.setCustomValidity("boolean"==typeof a?"":a||""),y.reportValidity())},A={},B="radio"===i.type,D="checkbox"===i.type,E=(v||"file"===i.type)&&l(i.value)&&l(x)||""===x||Array.isArray(x)&&!x.length,H=C.bind(null,u,d,A),I=(a,b,c,d=s.maxLength,e=s.minLength)=>{let f=a?b:c;A[u]={type:a?d:e,message:f,ref:i,...H(a?d:e,f)}};if(h?!Array.isArray(x)||!x.length:k&&(!(B||D)&&(E||f(x))||"boolean"==typeof x&&!x||D&&!O(j).isValid||B&&!R(j).isValid)){let{value:a,message:b}=_(k)?{value:!!k,message:k}:ab(k);if(a&&(A[u]={type:s.required,message:b,ref:y,...H(s.required,b)},!d))return z(b),A}if(!E&&(!f(p)||!f(q))){let a,b,c=ab(q),e=ab(p);if(f(x)||isNaN(x)){let d=i.valueAsDate||new Date(x),f=a=>new Date(new Date().toDateString()+" "+a),g="time"==i.type,h="week"==i.type;"string"==typeof c.value&&x&&(a=g?f(x)>f(c.value):h?x>c.value:d>new Date(c.value)),"string"==typeof e.value&&x&&(b=g?f(x)<f(e.value):h?x<e.value:d<new Date(e.value))}else{let d=i.valueAsNumber||(x?+x:x);f(c.value)||(a=d>c.value),f(e.value)||(b=d<e.value)}if((a||b)&&(I(!!a,c.message,e.message,s.max,s.min),!d))return z(A[u].message),A}if((m||n)&&!E&&("string"==typeof x||h&&Array.isArray(x))){let a=ab(m),b=ab(n),c=!f(a.value)&&x.length>+a.value,e=!f(b.value)&&x.length<+b.value;if((c||e)&&(I(c,a.message,b.message),!d))return z(A[u].message),A}if(r&&!E&&"string"==typeof x){let{value:a,message:b}=ab(r);if(a instanceof RegExp&&!x.match(a)&&(A[u]={type:s.pattern,message:b,ref:i,...H(s.pattern,b)},!d))return z(b),A}if(t){if(G(t)){let a=aa(await t(x,c),y);if(a&&(A[u]={...a,...H(s.validate,a.message)},!d))return z(a.message),A}else if(g(t)){let a={};for(let b in t){if(!F(a)&&!d)break;let e=aa(await t[b](x,c),y,b);e&&(a={...e,...H(b,e.message)},z(e.message),d&&(A[u]=a))}if(!F(a)&&(A[u]={ref:y,...a},!d))return A}}return z(!0),A};let ad={mode:r.onSubmit,reValidateMode:r.onChange,shouldFocusError:!0},ae=(a,b,c)=>{if(a&&"reportValidity"in a){let d=o(c,b);a.setCustomValidity(d&&d.message||""),a.reportValidity()}},af=(a,b)=>{for(let c in b.fields){let d=b.fields[c];d&&d.ref&&"reportValidity"in d.ref?ae(d.ref,c,a):d&&d.refs&&d.refs.forEach(b=>ae(b,c,a))}},ag=(a,b)=>{b.shouldUseNativeValidation&&af(a,b);let c={};for(let d in a){let e=o(b.fields,d),f=Object.assign(a[d]||{},{ref:e&&e.ref});if(ah(b.names||Object.keys(a),d)){let a=Object.assign({},o(c,d));p(a,"root",f),p(c,d,a)}else p(c,d,f)}return c},ah=(a,b)=>{let c=ai(b);return a.some(a=>ai(a).match(`^${c}\\.\\d+`))};function ai(a){return a.replace(/\]|\[/g,"")}a.s(["$ZodError",()=>bo,"$ZodRealError",()=>bp,"flattenError",()=>bq,"formatError",()=>br,"prettifyError",()=>bu,"toDotPath",()=>bt,"treeifyError",()=>bs],74477),a.s(["$ZodAsyncError",()=>am,"$ZodEncodeError",()=>an,"$brand",()=>al,"$constructor",()=>ak,"NEVER",()=>aj,"config",()=>ap,"globalConfig",()=>ao],937);let aj=Object.freeze({status:"aborted"});function ak(a,b,c){function d(c,d){var e;for(let f in Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(e=c._zod).traits??(e.traits=new Set),c._zod.traits.add(a),b(c,d),g.prototype)f in c||Object.defineProperty(c,f,{value:g.prototype[f].bind(c)});c._zod.constr=g,c._zod.def=d}let e=c?.Parent??Object;class f extends e{}function g(a){var b;let e=c?.Parent?new f:this;for(let c of(d(e,a),(b=e._zod).deferred??(b.deferred=[]),e._zod.deferred))c();return e}return Object.defineProperty(f,"name",{value:a}),Object.defineProperty(g,"init",{value:d}),Object.defineProperty(g,Symbol.hasInstance,{value:b=>!!c?.Parent&&b instanceof c.Parent||b?._zod?.traits?.has(a)}),Object.defineProperty(g,"name",{value:a}),g}let al=Symbol("zod_brand");class am extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}class an extends Error{constructor(a){super(`Encountered unidirectional transform during encode: ${a}`),this.name="ZodEncodeError"}}let ao={};function ap(a){return a&&Object.assign(ao,a),ao}function aq(a){return a}function ar(a){return a}function as(a){}function at(a){throw Error()}function au(a){}function av(a){let b=Object.values(a).filter(a=>"number"==typeof a);return Object.entries(a).filter(([a,c])=>-1===b.indexOf(+a)).map(([a,b])=>b)}function aw(a,b="|"){return a.map(a=>aZ(a)).join(b)}function ax(a,b){return"bigint"==typeof b?b.toString():b}function ay(a){return{get value(){{let b=a();return Object.defineProperty(this,"value",{value:b}),b}}}}function az(a){return null==a}function aA(a){let b=+!!a.startsWith("^"),c=a.endsWith("$")?a.length-1:a.length;return a.slice(b,c)}function aB(a,b){let c=(a.toString().split(".")[1]||"").length,d=b.toString(),e=(d.split(".")[1]||"").length;if(0===e&&/\d?e-\d?/.test(d)){let a=d.match(/\d?e-(\d?)/);a?.[1]&&(e=Number.parseInt(a[1]))}let f=c>e?c:e;return Number.parseInt(a.toFixed(f).replace(".",""))%Number.parseInt(b.toFixed(f).replace(".",""))/10**f}a.s(["BIGINT_FORMAT_RANGES",()=>a0,"Class",()=>bm,"NUMBER_FORMAT_RANGES",()=>a_,"aborted",()=>a8,"allowsEval",()=>aO,"assert",()=>au,"assertEqual",()=>aq,"assertIs",()=>as,"assertNever",()=>at,"assertNotEqual",()=>ar,"assignProp",()=>aF,"base64ToUint8Array",()=>bg,"base64urlToUint8Array",()=>bi,"cached",()=>ay,"captureStackTrace",()=>aM,"cleanEnum",()=>bf,"cleanRegex",()=>aA,"clone",()=>aW,"cloneDef",()=>aH,"createTransparentProxy",()=>aY,"defineLazy",()=>aD,"esc",()=>aL,"escapeRegex",()=>aV,"extend",()=>a3,"finalizeIssue",()=>bb,"floatSafeRemainder",()=>aB,"getElementAtPath",()=>aI,"getEnumValues",()=>av,"getLengthableOrigin",()=>bd,"getParsedType",()=>aS,"getSizableOrigin",()=>bc,"hexToUint8Array",()=>bk,"isObject",()=>aN,"isPlainObject",()=>aP,"issue",()=>be,"joinValues",()=>aw,"jsonStringifyReplacer",()=>ax,"merge",()=>a5,"mergeDefs",()=>aG,"normalizeParams",()=>aX,"nullish",()=>az,"numKeys",()=>aR,"objectClone",()=>aE,"omit",()=>a2,"optionalKeys",()=>a$,"partial",()=>a6,"pick",()=>a1,"prefixIssues",()=>a9,"primitiveTypes",()=>aU,"promiseAllObject",()=>aJ,"propertyKeyTypes",()=>aT,"randomString",()=>aK,"required",()=>a7,"safeExtend",()=>a4,"shallowClone",()=>aQ,"stringifyPrimitive",()=>aZ,"uint8ArrayToBase64",()=>bh,"uint8ArrayToBase64url",()=>bj,"uint8ArrayToHex",()=>bl,"unwrapMessage",()=>ba],9346);let aC=Symbol("evaluating");function aD(a,b,c){let d;Object.defineProperty(a,b,{get(){if(d!==aC)return void 0===d&&(d=aC,d=c()),d},set(c){Object.defineProperty(a,b,{value:c})},configurable:!0})}function aE(a){return Object.create(Object.getPrototypeOf(a),Object.getOwnPropertyDescriptors(a))}function aF(a,b,c){Object.defineProperty(a,b,{value:c,writable:!0,enumerable:!0,configurable:!0})}function aG(...a){let b={};for(let c of a)Object.assign(b,Object.getOwnPropertyDescriptors(c));return Object.defineProperties({},b)}function aH(a){return aG(a._zod.def)}function aI(a,b){return b?b.reduce((a,b)=>a?.[b],a):a}function aJ(a){let b=Object.keys(a);return Promise.all(b.map(b=>a[b])).then(a=>{let c={};for(let d=0;d<b.length;d++)c[b[d]]=a[d];return c})}function aK(a=10){let b="abcdefghijklmnopqrstuvwxyz",c="";for(let d=0;d<a;d++)c+=b[Math.floor(Math.random()*b.length)];return c}function aL(a){return JSON.stringify(a)}let aM="captureStackTrace"in Error?Error.captureStackTrace:(...a)=>{};function aN(a){return"object"==typeof a&&null!==a&&!Array.isArray(a)}let aO=ay(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(a){return!1}});function aP(a){if(!1===aN(a))return!1;let b=a.constructor;if(void 0===b)return!0;let c=b.prototype;return!1!==aN(c)&&!1!==Object.prototype.hasOwnProperty.call(c,"isPrototypeOf")}function aQ(a){return aP(a)?{...a}:a}function aR(a){let b=0;for(let c in a)Object.prototype.hasOwnProperty.call(a,c)&&b++;return b}let aS=a=>{let b=typeof a;switch(b){case"undefined":return"undefined";case"string":return"string";case"number":return Number.isNaN(a)?"nan":"number";case"boolean":return"boolean";case"function":return"function";case"bigint":return"bigint";case"symbol":return"symbol";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(a.then&&"function"==typeof a.then&&a.catch&&"function"==typeof a.catch)return"promise";if("undefined"!=typeof Map&&a instanceof Map)return"map";if("undefined"!=typeof Set&&a instanceof Set)return"set";if("undefined"!=typeof Date&&a instanceof Date)return"date";if("undefined"!=typeof File&&a instanceof File)return"file";return"object";default:throw Error(`Unknown data type: ${b}`)}},aT=new Set(["string","number","symbol"]),aU=new Set(["string","number","bigint","boolean","symbol","undefined"]);function aV(a){return a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function aW(a,b,c){let d=new a._zod.constr(b??a._zod.def);return(!b||c?.parent)&&(d._zod.parent=a),d}function aX(a){if(!a)return{};if("string"==typeof a)return{error:()=>a};if(a?.message!==void 0){if(a?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");a.error=a.message}return(delete a.message,"string"==typeof a.error)?{...a,error:()=>a.error}:a}function aY(a){let b;return new Proxy({},{get:(c,d,e)=>(b??(b=a()),Reflect.get(b,d,e)),set:(c,d,e,f)=>(b??(b=a()),Reflect.set(b,d,e,f)),has:(c,d)=>(b??(b=a()),Reflect.has(b,d)),deleteProperty:(c,d)=>(b??(b=a()),Reflect.deleteProperty(b,d)),ownKeys:c=>(b??(b=a()),Reflect.ownKeys(b)),getOwnPropertyDescriptor:(c,d)=>(b??(b=a()),Reflect.getOwnPropertyDescriptor(b,d)),defineProperty:(c,d,e)=>(b??(b=a()),Reflect.defineProperty(b,d,e))})}function aZ(a){return"bigint"==typeof a?a.toString()+"n":"string"==typeof a?`"${a}"`:`${a}`}function a$(a){return Object.keys(a).filter(b=>"optional"===a[b]._zod.optin&&"optional"===a[b]._zod.optout)}let a_={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-0x80000000,0x7fffffff],uint32:[0,0xffffffff],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]},a0={int64:[BigInt("-9223372036854775808"),BigInt("9223372036854775807")],uint64:[BigInt(0),BigInt("18446744073709551615")]};function a1(a,b){let c=a._zod.def,d=aG(a._zod.def,{get shape(){let a={};for(let d in b){if(!(d in c.shape))throw Error(`Unrecognized key: "${d}"`);b[d]&&(a[d]=c.shape[d])}return aF(this,"shape",a),a},checks:[]});return aW(a,d)}function a2(a,b){let c=a._zod.def,d=aG(a._zod.def,{get shape(){let d={...a._zod.def.shape};for(let a in b){if(!(a in c.shape))throw Error(`Unrecognized key: "${a}"`);b[a]&&delete d[a]}return aF(this,"shape",d),d},checks:[]});return aW(a,d)}function a3(a,b){if(!aP(b))throw Error("Invalid input to extend: expected a plain object");let c=a._zod.def.checks;if(c&&c.length>0)throw Error("Object schemas containing refinements cannot be extended. Use `.safeExtend()` instead.");let d=aG(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b};return aF(this,"shape",c),c},checks:[]});return aW(a,d)}function a4(a,b){if(!aP(b))throw Error("Invalid input to safeExtend: expected a plain object");let c={...a._zod.def,get shape(){let c={...a._zod.def.shape,...b};return aF(this,"shape",c),c},checks:a._zod.def.checks};return aW(a,c)}function a5(a,b){let c=aG(a._zod.def,{get shape(){let c={...a._zod.def.shape,...b._zod.def.shape};return aF(this,"shape",c),c},get catchall(){return b._zod.def.catchall},checks:[]});return aW(a,c)}function a6(a,b,c){let d=aG(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in d))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=a?new a({type:"optional",innerType:d[b]}):d[b])}else for(let b in d)e[b]=a?new a({type:"optional",innerType:d[b]}):d[b];return aF(this,"shape",e),e},checks:[]});return aW(b,d)}function a7(a,b,c){let d=aG(b._zod.def,{get shape(){let d=b._zod.def.shape,e={...d};if(c)for(let b in c){if(!(b in e))throw Error(`Unrecognized key: "${b}"`);c[b]&&(e[b]=new a({type:"nonoptional",innerType:d[b]}))}else for(let b in d)e[b]=new a({type:"nonoptional",innerType:d[b]});return aF(this,"shape",e),e},checks:[]});return aW(b,d)}function a8(a,b=0){if(!0===a.aborted)return!0;for(let c=b;c<a.issues.length;c++)if(a.issues[c]?.continue!==!0)return!0;return!1}function a9(a,b){return b.map(b=>(b.path??(b.path=[]),b.path.unshift(a),b))}function ba(a){return"string"==typeof a?a:a?.message}function bb(a,b,c){let d={...a,path:a.path??[]};return a.message||(d.message=ba(a.inst?._zod.def?.error?.(a))??ba(b?.error?.(a))??ba(c.customError?.(a))??ba(c.localeError?.(a))??"Invalid input"),delete d.inst,delete d.continue,b?.reportInput||delete d.input,d}function bc(a){return a instanceof Set?"set":a instanceof Map?"map":a instanceof File?"file":"unknown"}function bd(a){return Array.isArray(a)?"array":"string"==typeof a?"string":"unknown"}function be(...a){let[b,c,d]=a;return"string"==typeof b?{message:b,code:"custom",input:c,inst:d}:{...b}}function bf(a){return Object.entries(a).filter(([a,b])=>Number.isNaN(Number.parseInt(a,10))).map(a=>a[1])}function bg(a){let b=atob(a),c=new Uint8Array(b.length);for(let a=0;a<b.length;a++)c[a]=b.charCodeAt(a);return c}function bh(a){let b="";for(let c=0;c<a.length;c++)b+=String.fromCharCode(a[c]);return btoa(b)}function bi(a){let b=a.replace(/-/g,"+").replace(/_/g,"/"),c="=".repeat((4-b.length%4)%4);return bg(b+c)}function bj(a){return bh(a).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}function bk(a){let b=a.replace(/^0x/,"");if(b.length%2!=0)throw Error("Invalid hex string length");let c=new Uint8Array(b.length/2);for(let a=0;a<b.length;a+=2)c[a/2]=Number.parseInt(b.slice(a,a+2),16);return c}function bl(a){return Array.from(a).map(a=>a.toString(16).padStart(2,"0")).join("")}class bm{constructor(...a){}}let bn=(a,b)=>{a.name="$ZodError",Object.defineProperty(a,"_zod",{value:a._zod,enumerable:!1}),Object.defineProperty(a,"issues",{value:b,enumerable:!1}),a.message=JSON.stringify(b,ax,2),Object.defineProperty(a,"toString",{value:()=>a.message,enumerable:!1})},bo=ak("$ZodError",bn),bp=ak("$ZodError",bn,{Parent:Error});function bq(a,b=a=>a.message){let c={},d=[];for(let e of a.issues)e.path.length>0?(c[e.path[0]]=c[e.path[0]]||[],c[e.path[0]].push(b(e))):d.push(b(e));return{formErrors:d,fieldErrors:c}}function br(a,b){let c=b||function(a){return a.message},d={_errors:[]},e=a=>{for(let b of a.issues)if("invalid_union"===b.code&&b.errors.length)b.errors.map(a=>e({issues:a}));else if("invalid_key"===b.code)e({issues:b.issues});else if("invalid_element"===b.code)e({issues:b.issues});else if(0===b.path.length)d._errors.push(c(b));else{let a=d,e=0;for(;e<b.path.length;){let d=b.path[e];e===b.path.length-1?(a[d]=a[d]||{_errors:[]},a[d]._errors.push(c(b))):a[d]=a[d]||{_errors:[]},a=a[d],e++}}};return e(a),d}function bs(a,b){let c=b||function(a){return a.message},d={errors:[]},e=(a,b=[])=>{var f,g;for(let h of a.issues)if("invalid_union"===h.code&&h.errors.length)h.errors.map(a=>e({issues:a},h.path));else if("invalid_key"===h.code)e({issues:h.issues},h.path);else if("invalid_element"===h.code)e({issues:h.issues},h.path);else{let a=[...b,...h.path];if(0===a.length){d.errors.push(c(h));continue}let e=d,i=0;for(;i<a.length;){let b=a[i],d=i===a.length-1;"string"==typeof b?(e.properties??(e.properties={}),(f=e.properties)[b]??(f[b]={errors:[]}),e=e.properties[b]):(e.items??(e.items=[]),(g=e.items)[b]??(g[b]={errors:[]}),e=e.items[b]),d&&e.errors.push(c(h)),i++}}};return e(a),d}function bt(a){let b=[];for(let c of a.map(a=>"object"==typeof a?a.key:a))"number"==typeof c?b.push(`[${c}]`):"symbol"==typeof c?b.push(`[${JSON.stringify(String(c))}]`):/[^\w$]/.test(c)?b.push(`[${JSON.stringify(c)}]`):(b.length&&b.push("."),b.push(c));return b.join("")}function bu(a){let b=[];for(let c of[...a.issues].sort((a,b)=>(a.path??[]).length-(b.path??[]).length))b.push(`✖ ${c.message}`),c.path?.length&&b.push(`  → at ${bt(c.path)}`);return b.join("\n")}a.s(["_decode",()=>bF,"_decodeAsync",()=>bJ,"_encode",()=>bD,"_encodeAsync",()=>bH,"_parse",()=>bv,"_parseAsync",()=>bx,"_safeDecode",()=>bN,"_safeDecodeAsync",()=>bR,"_safeEncode",()=>bL,"_safeEncodeAsync",()=>bP,"_safeParse",()=>bz,"_safeParseAsync",()=>bB,"decode",()=>bG,"decodeAsync",()=>bK,"encode",()=>bE,"encodeAsync",()=>bI,"parse",()=>bw,"parseAsync",()=>by,"safeDecode",()=>bO,"safeDecodeAsync",()=>bS,"safeEncode",()=>bM,"safeEncodeAsync",()=>bQ,"safeParse",()=>bA,"safeParseAsync",()=>bC],37799);let bv=a=>(b,c,d,e)=>{let f=d?Object.assign(d,{async:!1}):{async:!1},g=b._zod.run({value:c,issues:[]},f);if(g instanceof Promise)throw new am;if(g.issues.length){let b=new(e?.Err??a)(g.issues.map(a=>bb(a,f,ap())));throw aM(b,e?.callee),b}return g.value},bw=bv(bp),bx=a=>async(b,c,d,e)=>{let f=d?Object.assign(d,{async:!0}):{async:!0},g=b._zod.run({value:c,issues:[]},f);if(g instanceof Promise&&(g=await g),g.issues.length){let b=new(e?.Err??a)(g.issues.map(a=>bb(a,f,ap())));throw aM(b,e?.callee),b}return g.value},by=bx(bp),bz=a=>(b,c,d)=>{let e=d?{...d,async:!1}:{async:!1},f=b._zod.run({value:c,issues:[]},e);if(f instanceof Promise)throw new am;return f.issues.length?{success:!1,error:new(a??bo)(f.issues.map(a=>bb(a,e,ap())))}:{success:!0,data:f.value}},bA=bz(bp),bB=a=>async(b,c,d)=>{let e=d?Object.assign(d,{async:!0}):{async:!0},f=b._zod.run({value:c,issues:[]},e);return f instanceof Promise&&(f=await f),f.issues.length?{success:!1,error:new a(f.issues.map(a=>bb(a,e,ap())))}:{success:!0,data:f.value}},bC=bB(bp),bD=a=>(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return bv(a)(b,c,e)},bE=bD(bp),bF=a=>(b,c,d)=>bv(a)(b,c,d),bG=bF(bp),bH=a=>async(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return bx(a)(b,c,e)},bI=bH(bp),bJ=a=>async(b,c,d)=>bx(a)(b,c,d),bK=bJ(bp),bL=a=>(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return bz(a)(b,c,e)},bM=bL(bp),bN=a=>(b,c,d)=>bz(a)(b,c,d),bO=bN(bp),bP=a=>async(b,c,d)=>{let e=d?Object.assign(d,{direction:"backward"}):{direction:"backward"};return bB(a)(b,c,e)},bQ=bP(bp),bR=a=>async(b,c,d)=>bB(a)(b,c,d),bS=bR(bp);function bT(a,b){try{var c=a()}catch(a){return b(a)}return c&&c.then?c.then(void 0,b):c}let bU=(0,a.i(51827).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var bV=a.i(95811),bW=a.i(54152),bX=a.i(53834),bY=d.forwardRef((a,b)=>(0,c.jsx)(bX.Primitive.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));bY.displayName="Label";var bZ=a.i(97895);function b$({className:a,...b}){return(0,c.jsx)(bY,{className:(0,bZ.cn)("flex select-none items-center gap-2 font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-50 group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50",a),"data-slot":"label",...b})}let b_=a=>{let{children:b,...c}=a;return d.default.createElement(t.Provider,{value:c},b)},b0=d.createContext({}),b1=({...a})=>(0,c.jsx)(b0.Provider,{value:{name:a.name},children:(0,c.jsx)(B,{...a})}),b2=()=>{let a=d.useContext(b0),b=d.useContext(b3),{getFieldState:c}=u(),e=x({name:a.name}),f=c(a.name,e);if(!a)throw Error("useFormField should be used within <FormField>");let{id:g}=b;return{id:g,name:a.name,formItemId:`${g}-form-item`,formDescriptionId:`${g}-form-item-description`,formMessageId:`${g}-form-item-message`,...f}},b3=d.createContext({});function b4({className:a,...b}){let e=d.useId();return(0,c.jsx)(b3.Provider,{value:{id:e},children:(0,c.jsx)("div",{className:(0,bZ.cn)("grid gap-2",a),"data-slot":"form-item",...b})})}function b5({className:a,...b}){let{error:d,formItemId:e}=b2();return(0,c.jsx)(b$,{className:(0,bZ.cn)("data-[error=true]:text-destructive",a),"data-error":!!d,"data-slot":"form-label",htmlFor:e,...b})}function b6({...a}){let{error:b,formItemId:d,formDescriptionId:e,formMessageId:f}=b2();return(0,c.jsx)(bW.Slot,{"aria-describedby":b?`${e} ${f}`:`${e}`,"aria-invalid":!!b,"data-slot":"form-control",id:d,...a})}function b7({className:a,...b}){let{error:d,formMessageId:e}=b2(),f=d?String(d?.message??""):b.children;return f?(0,c.jsx)("p",{className:(0,bZ.cn)("text-destructive text-sm",a),"data-slot":"form-message",id:e,...b,children:f}):null}function b8(){return{localeError:(()=>{let a={string:{unit:"characters",verb:"to have"},file:{unit:"bytes",verb:"to have"},array:{unit:"items",verb:"to have"},set:{unit:"items",verb:"to have"}},b={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Invalid input: expected ${c.expected}, received ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Invalid input: expected ${aZ(c.values[0])}`;return`Invalid option: expected one of ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Too big: expected ${c.origin??"value"} to have ${b}${c.maximum.toString()} ${d.unit??"elements"}`;return`Too big: expected ${c.origin??"value"} to be ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Too small: expected ${c.origin} to have ${b}${c.minimum.toString()} ${d.unit}`;return`Too small: expected ${c.origin} to be ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Invalid string: must start with "${c.prefix}"`;if("ends_with"===c.format)return`Invalid string: must end with "${c.suffix}"`;if("includes"===c.format)return`Invalid string: must include "${c.includes}"`;if("regex"===c.format)return`Invalid string: must match pattern ${c.pattern}`;return`Invalid ${b[c.format]??c.format}`;case"not_multiple_of":return`Invalid number: must be a multiple of ${c.divisor}`;case"unrecognized_keys":return`Unrecognized key${c.keys.length>1?"s":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`Invalid key in ${c.origin}`;case"invalid_union":default:return"Invalid input";case"invalid_element":return`Invalid value in ${c.origin}`}}})()}}a.s(["$brand",()=>al,"$input",()=>fG,"$output",()=>fF,"NEVER",()=>aj,"TimePrecision",()=>f6,"ZodAny",()=>i4,"ZodArray",()=>je,"ZodBase64",()=>iy,"ZodBase64URL",()=>iA,"ZodBigInt",()=>iV,"ZodBigIntFormat",()=>iX,"ZodBoolean",()=>iT,"ZodCIDRv4",()=>iu,"ZodCIDRv6",()=>iw,"ZodCUID",()=>ie,"ZodCUID2",()=>ih,"ZodCatch",()=>jW,"ZodCodec",()=>j0,"ZodCustom",()=>kc,"ZodCustomStringFormat",()=>iG,"ZodDate",()=>jc,"ZodDefault",()=>jO,"ZodDiscriminatedUnion",()=>jn,"ZodE164",()=>iC,"ZodEmail",()=>h$,"ZodEmoji",()=>ia,"ZodEnum",()=>jA,"ZodError",()=>hH,"ZodFile",()=>jF,"ZodFirstPartyTypeKind",()=>b,"ZodFunction",()=>ka,"ZodGUID",()=>h0,"ZodIPv4",()=>iq,"ZodIPv6",()=>is,"ZodISODate",()=>hA,"ZodISODateTime",()=>hy,"ZodISODuration",()=>hE,"ZodISOTime",()=>hC,"ZodIntersection",()=>jp,"ZodIssueCode",()=>kl,"ZodJWT",()=>iE,"ZodKSUID",()=>io,"ZodLazy",()=>j6,"ZodLiteral",()=>jD,"ZodMap",()=>jw,"ZodNaN",()=>jY,"ZodNanoID",()=>ic,"ZodNever",()=>i8,"ZodNonOptional",()=>jS,"ZodNull",()=>i2,"ZodNullable",()=>jL,"ZodNumber",()=>iL,"ZodNumberFormat",()=>iN,"ZodObject",()=>jh,"ZodOptional",()=>jJ,"ZodPipe",()=>j$,"ZodPrefault",()=>jQ,"ZodPromise",()=>j8,"ZodReadonly",()=>j2,"ZodRealError",()=>hI,"ZodRecord",()=>jt,"ZodSet",()=>jy,"ZodString",()=>hX,"ZodStringFormat",()=>hZ,"ZodSuccess",()=>jU,"ZodSymbol",()=>i$,"ZodTemplateLiteral",()=>j4,"ZodTransform",()=>jH,"ZodTuple",()=>jr,"ZodType",()=>hV,"ZodULID",()=>ij,"ZodURL",()=>h7,"ZodUUID",()=>h2,"ZodUndefined",()=>i0,"ZodUnion",()=>jl,"ZodUnknown",()=>i6,"ZodVoid",()=>ja,"ZodXID",()=>il,"_ZodString",()=>hW,"_default",()=>jP,"_function",()=>kb,"any",()=>i5,"array",()=>jf,"base64",()=>iz,"base64url",()=>iB,"bigint",()=>iW,"boolean",()=>iU,"catch",()=>jX,"check",()=>kd,"cidrv4",()=>iv,"cidrv6",()=>ix,"clone",()=>aW,"codec",()=>j1,"coerce",()=>kv,"config",()=>ap,"core",()=>hv,"cuid",()=>ig,"cuid2",()=>ii,"custom",()=>ke,"date",()=>jd,"decode",()=>hO,"decodeAsync",()=>hQ,"discriminatedUnion",()=>jo,"e164",()=>iD,"email",()=>h_,"emoji",()=>ib,"encode",()=>hN,"encodeAsync",()=>hP,"endsWith",()=>gS,"enum",()=>jB,"file",()=>jG,"flattenError",()=>bq,"float32",()=>iP,"float64",()=>iQ,"formatError",()=>br,"function",()=>kb,"getErrorMap",()=>kn,"globalRegistry",()=>fJ,"gt",()=>gA,"gte",()=>gB,"guid",()=>h1,"hash",()=>iK,"hex",()=>iJ,"hostname",()=>iI,"httpUrl",()=>h9,"includes",()=>gQ,"instanceof",()=>kh,"int",()=>iO,"int32",()=>iR,"int64",()=>iY,"intersection",()=>jq,"ipv4",()=>ir,"ipv6",()=>it,"iso",()=>kp,"json",()=>kj,"jwt",()=>iF,"keyof",()=>jg,"ksuid",()=>ip,"lazy",()=>j7,"length",()=>gM,"literal",()=>jE,"locales",()=>ko,"looseObject",()=>jk,"lowercase",()=>gO,"lt",()=>gy,"lte",()=>gz,"map",()=>jx,"maxLength",()=>gK,"maxSize",()=>gH,"mime",()=>gU,"minLength",()=>gL,"minSize",()=>gI,"multipleOf",()=>gG,"nan",()=>jZ,"nanoid",()=>id,"nativeEnum",()=>jC,"negative",()=>gD,"never",()=>i9,"nonnegative",()=>gF,"nonoptional",()=>jT,"nonpositive",()=>gE,"normalize",()=>gW,"null",()=>i3,"nullable",()=>jM,"nullish",()=>jN,"number",()=>iM,"object",()=>ji,"optional",()=>jK,"overwrite",()=>gV,"parse",()=>hJ,"parseAsync",()=>hK,"partialRecord",()=>jv,"pipe",()=>j_,"positive",()=>gC,"prefault",()=>jR,"preprocess",()=>kk,"prettifyError",()=>bu,"promise",()=>j9,"property",()=>gT,"readonly",()=>j3,"record",()=>ju,"refine",()=>kf,"regex",()=>gN,"regexes",()=>hw,"registry",()=>fI,"safeDecode",()=>hS,"safeDecodeAsync",()=>hU,"safeEncode",()=>hR,"safeEncodeAsync",()=>hT,"safeParse",()=>hL,"safeParseAsync",()=>hM,"set",()=>jz,"setErrorMap",()=>km,"size",()=>gJ,"startsWith",()=>gR,"strictObject",()=>jj,"string",()=>hY,"stringFormat",()=>iH,"stringbool",()=>ki,"success",()=>jV,"superRefine",()=>kg,"symbol",()=>i_,"templateLiteral",()=>j5,"toJSONSchema",()=>ht,"toLowerCase",()=>gY,"toUpperCase",()=>gZ,"transform",()=>jI,"treeifyError",()=>bs,"trim",()=>gX,"tuple",()=>js,"uint32",()=>iS,"uint64",()=>iZ,"ulid",()=>ik,"undefined",()=>i1,"union",()=>jm,"unknown",()=>i7,"uppercase",()=>gP,"url",()=>h8,"util",()=>hx,"uuid",()=>h3,"uuidv4",()=>h4,"uuidv6",()=>h5,"uuidv7",()=>h6,"void",()=>jb,"xid",()=>im],99333),a.s([],83783),ap(b8()),a.i(83783),a.s(["$ZodAny",()=>d7,"$ZodArray",()=>ed,"$ZodAsyncError",()=>am,"$ZodBase64",()=>dU,"$ZodBase64URL",()=>dW,"$ZodBigInt",()=>d2,"$ZodBigIntFormat",()=>d3,"$ZodBoolean",()=>d1,"$ZodCIDRv4",()=>dR,"$ZodCIDRv6",()=>dS,"$ZodCUID",()=>dG,"$ZodCUID2",()=>dH,"$ZodCatch",()=>eI,"$ZodCheck",()=>c6,"$ZodCheckBigIntFormat",()=>dc,"$ZodCheckEndsWith",()=>dq,"$ZodCheckGreaterThan",()=>c9,"$ZodCheckIncludes",()=>dn,"$ZodCheckLengthEquals",()=>di,"$ZodCheckLessThan",()=>c8,"$ZodCheckLowerCase",()=>dl,"$ZodCheckMaxLength",()=>dg,"$ZodCheckMaxSize",()=>dd,"$ZodCheckMimeType",()=>dt,"$ZodCheckMinLength",()=>dh,"$ZodCheckMinSize",()=>de,"$ZodCheckMultipleOf",()=>da,"$ZodCheckNumberFormat",()=>db,"$ZodCheckOverwrite",()=>du,"$ZodCheckProperty",()=>ds,"$ZodCheckRegex",()=>dk,"$ZodCheckSizeEquals",()=>df,"$ZodCheckStartsWith",()=>dp,"$ZodCheckStringFormat",()=>dj,"$ZodCheckUpperCase",()=>dm,"$ZodCodec",()=>eM,"$ZodCustom",()=>eV,"$ZodCustomStringFormat",()=>d$,"$ZodDate",()=>eb,"$ZodDefault",()=>eC,"$ZodDiscriminatedUnion",()=>el,"$ZodE164",()=>dX,"$ZodEmail",()=>dC,"$ZodEmoji",()=>dE,"$ZodEncodeError",()=>an,"$ZodEnum",()=>ev,"$ZodError",()=>bo,"$ZodFile",()=>ex,"$ZodFunction",()=>eS,"$ZodGUID",()=>dA,"$ZodIPv4",()=>dP,"$ZodIPv6",()=>dQ,"$ZodISODate",()=>dM,"$ZodISODateTime",()=>dL,"$ZodISODuration",()=>dO,"$ZodISOTime",()=>dN,"$ZodIntersection",()=>em,"$ZodJWT",()=>dZ,"$ZodKSUID",()=>dK,"$ZodLazy",()=>eU,"$ZodLiteral",()=>ew,"$ZodMap",()=>er,"$ZodNaN",()=>eJ,"$ZodNanoID",()=>dF,"$ZodNever",()=>d9,"$ZodNonOptional",()=>eF,"$ZodNull",()=>d6,"$ZodNullable",()=>eB,"$ZodNumber",()=>d_,"$ZodNumberFormat",()=>d0,"$ZodObject",()=>eh,"$ZodObjectJIT",()=>ei,"$ZodOptional",()=>eA,"$ZodPipe",()=>eK,"$ZodPrefault",()=>eE,"$ZodPromise",()=>eT,"$ZodReadonly",()=>eP,"$ZodRealError",()=>bp,"$ZodRecord",()=>eq,"$ZodRegistry",()=>fH,"$ZodSet",()=>et,"$ZodString",()=>dy,"$ZodStringFormat",()=>dz,"$ZodSuccess",()=>eH,"$ZodSymbol",()=>d4,"$ZodTemplateLiteral",()=>eR,"$ZodTransform",()=>ey,"$ZodTuple",()=>eo,"$ZodType",()=>dx,"$ZodULID",()=>dI,"$ZodURL",()=>dD,"$ZodUUID",()=>dB,"$ZodUndefined",()=>d5,"$ZodUnion",()=>ek,"$ZodUnknown",()=>d8,"$ZodVoid",()=>ea,"$ZodXID",()=>dJ,"$brand",()=>al,"$constructor",()=>ak,"$input",()=>fG,"$output",()=>fF,"Doc",()=>dv,"JSONSchema",()=>hu,"JSONSchemaGenerator",()=>hs,"NEVER",()=>aj,"TimePrecision",()=>f6,"_any",()=>gr,"_array",()=>g$,"_base64",()=>f2,"_base64url",()=>f3,"_bigint",()=>gk,"_boolean",()=>gi,"_catch",()=>hg,"_check",()=>hp,"_cidrv4",()=>f0,"_cidrv6",()=>f1,"_coercedBigint",()=>gl,"_coercedBoolean",()=>gj,"_coercedDate",()=>gw,"_coercedNumber",()=>gc,"_coercedString",()=>fL,"_cuid",()=>fV,"_cuid2",()=>fW,"_custom",()=>hm,"_date",()=>gv,"_decode",()=>bF,"_decodeAsync",()=>bJ,"_default",()=>hd,"_discriminatedUnion",()=>g0,"_e164",()=>f4,"_email",()=>fM,"_emoji",()=>fT,"_encode",()=>bD,"_encodeAsync",()=>bH,"_endsWith",()=>gS,"_enum",()=>g6,"_file",()=>g9,"_float32",()=>ge,"_float64",()=>gf,"_gt",()=>gA,"_gte",()=>gB,"_guid",()=>fN,"_includes",()=>gQ,"_int",()=>gd,"_int32",()=>gg,"_int64",()=>gm,"_intersection",()=>g1,"_ipv4",()=>f$,"_ipv6",()=>f_,"_isoDate",()=>f8,"_isoDateTime",()=>f7,"_isoDuration",()=>ga,"_isoTime",()=>f9,"_jwt",()=>f5,"_ksuid",()=>fZ,"_lazy",()=>hk,"_length",()=>gM,"_literal",()=>g8,"_lowercase",()=>gO,"_lt",()=>gy,"_lte",()=>gz,"_map",()=>g4,"_max",()=>gz,"_maxLength",()=>gK,"_maxSize",()=>gH,"_mime",()=>gU,"_min",()=>gB,"_minLength",()=>gL,"_minSize",()=>gI,"_multipleOf",()=>gG,"_nan",()=>gx,"_nanoid",()=>fU,"_nativeEnum",()=>g7,"_negative",()=>gD,"_never",()=>gt,"_nonnegative",()=>gF,"_nonoptional",()=>he,"_nonpositive",()=>gE,"_normalize",()=>gW,"_null",()=>gq,"_nullable",()=>hc,"_number",()=>gb,"_optional",()=>hb,"_overwrite",()=>gV,"_parse",()=>bv,"_parseAsync",()=>bx,"_pipe",()=>hh,"_positive",()=>gC,"_promise",()=>hl,"_property",()=>gT,"_readonly",()=>hi,"_record",()=>g3,"_refine",()=>hn,"_regex",()=>gN,"_safeDecode",()=>bN,"_safeDecodeAsync",()=>bR,"_safeEncode",()=>bL,"_safeEncodeAsync",()=>bP,"_safeParse",()=>bz,"_safeParseAsync",()=>bB,"_set",()=>g5,"_size",()=>gJ,"_startsWith",()=>gR,"_string",()=>fK,"_stringFormat",()=>hr,"_stringbool",()=>hq,"_success",()=>hf,"_superRefine",()=>ho,"_symbol",()=>go,"_templateLiteral",()=>hj,"_toLowerCase",()=>gY,"_toUpperCase",()=>gZ,"_transform",()=>ha,"_trim",()=>gX,"_tuple",()=>g2,"_uint32",()=>gh,"_uint64",()=>gn,"_ulid",()=>fX,"_undefined",()=>gp,"_union",()=>g_,"_unknown",()=>gs,"_uppercase",()=>gP,"_url",()=>fS,"_uuid",()=>fO,"_uuidv4",()=>fP,"_uuidv6",()=>fQ,"_uuidv7",()=>fR,"_void",()=>gu,"_xid",()=>fY,"clone",()=>aW,"config",()=>ap,"decode",()=>bG,"decodeAsync",()=>bK,"encode",()=>bE,"encodeAsync",()=>bI,"flattenError",()=>bq,"formatError",()=>br,"globalConfig",()=>ao,"globalRegistry",()=>fJ,"isValidBase64",()=>dT,"isValidBase64URL",()=>dV,"isValidJWT",()=>dY,"locales",()=>fE,"parse",()=>bw,"parseAsync",()=>by,"prettifyError",()=>bu,"regexes",()=>eY,"registry",()=>fI,"safeDecode",()=>bO,"safeDecodeAsync",()=>bS,"safeEncode",()=>bM,"safeEncodeAsync",()=>bQ,"safeParse",()=>bA,"safeParseAsync",()=>bC,"toDotPath",()=>bt,"toJSONSchema",()=>ht,"treeifyError",()=>bs,"util",()=>eX,"version",()=>dw],98051),a.s([],5699),a.i(5699),a.i(937),a.i(37799),a.i(74477),a.s(["$ZodAny",()=>d7,"$ZodArray",()=>ed,"$ZodBase64",()=>dU,"$ZodBase64URL",()=>dW,"$ZodBigInt",()=>d2,"$ZodBigIntFormat",()=>d3,"$ZodBoolean",()=>d1,"$ZodCIDRv4",()=>dR,"$ZodCIDRv6",()=>dS,"$ZodCUID",()=>dG,"$ZodCUID2",()=>dH,"$ZodCatch",()=>eI,"$ZodCodec",()=>eM,"$ZodCustom",()=>eV,"$ZodCustomStringFormat",()=>d$,"$ZodDate",()=>eb,"$ZodDefault",()=>eC,"$ZodDiscriminatedUnion",()=>el,"$ZodE164",()=>dX,"$ZodEmail",()=>dC,"$ZodEmoji",()=>dE,"$ZodEnum",()=>ev,"$ZodFile",()=>ex,"$ZodFunction",()=>eS,"$ZodGUID",()=>dA,"$ZodIPv4",()=>dP,"$ZodIPv6",()=>dQ,"$ZodISODate",()=>dM,"$ZodISODateTime",()=>dL,"$ZodISODuration",()=>dO,"$ZodISOTime",()=>dN,"$ZodIntersection",()=>em,"$ZodJWT",()=>dZ,"$ZodKSUID",()=>dK,"$ZodLazy",()=>eU,"$ZodLiteral",()=>ew,"$ZodMap",()=>er,"$ZodNaN",()=>eJ,"$ZodNanoID",()=>dF,"$ZodNever",()=>d9,"$ZodNonOptional",()=>eF,"$ZodNull",()=>d6,"$ZodNullable",()=>eB,"$ZodNumber",()=>d_,"$ZodNumberFormat",()=>d0,"$ZodObject",()=>eh,"$ZodObjectJIT",()=>ei,"$ZodOptional",()=>eA,"$ZodPipe",()=>eK,"$ZodPrefault",()=>eE,"$ZodPromise",()=>eT,"$ZodReadonly",()=>eP,"$ZodRecord",()=>eq,"$ZodSet",()=>et,"$ZodString",()=>dy,"$ZodStringFormat",()=>dz,"$ZodSuccess",()=>eH,"$ZodSymbol",()=>d4,"$ZodTemplateLiteral",()=>eR,"$ZodTransform",()=>ey,"$ZodTuple",()=>eo,"$ZodType",()=>dx,"$ZodULID",()=>dI,"$ZodURL",()=>dD,"$ZodUUID",()=>dB,"$ZodUndefined",()=>d5,"$ZodUnion",()=>ek,"$ZodUnknown",()=>d8,"$ZodVoid",()=>ea,"$ZodXID",()=>dJ,"clone",()=>aW,"isValidBase64",()=>dT,"isValidBase64URL",()=>dV,"isValidJWT",()=>dY],60009),a.s(["$ZodAny",()=>d7,"$ZodArray",()=>ed,"$ZodBase64",()=>dU,"$ZodBase64URL",()=>dW,"$ZodBigInt",()=>d2,"$ZodBigIntFormat",()=>d3,"$ZodBoolean",()=>d1,"$ZodCIDRv4",()=>dR,"$ZodCIDRv6",()=>dS,"$ZodCUID",()=>dG,"$ZodCUID2",()=>dH,"$ZodCatch",()=>eI,"$ZodCodec",()=>eM,"$ZodCustom",()=>eV,"$ZodCustomStringFormat",()=>d$,"$ZodDate",()=>eb,"$ZodDefault",()=>eC,"$ZodDiscriminatedUnion",()=>el,"$ZodE164",()=>dX,"$ZodEmail",()=>dC,"$ZodEmoji",()=>dE,"$ZodEnum",()=>ev,"$ZodFile",()=>ex,"$ZodFunction",()=>eS,"$ZodGUID",()=>dA,"$ZodIPv4",()=>dP,"$ZodIPv6",()=>dQ,"$ZodISODate",()=>dM,"$ZodISODateTime",()=>dL,"$ZodISODuration",()=>dO,"$ZodISOTime",()=>dN,"$ZodIntersection",()=>em,"$ZodJWT",()=>dZ,"$ZodKSUID",()=>dK,"$ZodLazy",()=>eU,"$ZodLiteral",()=>ew,"$ZodMap",()=>er,"$ZodNaN",()=>eJ,"$ZodNanoID",()=>dF,"$ZodNever",()=>d9,"$ZodNonOptional",()=>eF,"$ZodNull",()=>d6,"$ZodNullable",()=>eB,"$ZodNumber",()=>d_,"$ZodNumberFormat",()=>d0,"$ZodObject",()=>eh,"$ZodObjectJIT",()=>ei,"$ZodOptional",()=>eA,"$ZodPipe",()=>eK,"$ZodPrefault",()=>eE,"$ZodPromise",()=>eT,"$ZodReadonly",()=>eP,"$ZodRecord",()=>eq,"$ZodSet",()=>et,"$ZodString",()=>dy,"$ZodStringFormat",()=>dz,"$ZodSuccess",()=>eH,"$ZodSymbol",()=>d4,"$ZodTemplateLiteral",()=>eR,"$ZodTransform",()=>ey,"$ZodTuple",()=>eo,"$ZodType",()=>dx,"$ZodULID",()=>dI,"$ZodURL",()=>dD,"$ZodUUID",()=>dB,"$ZodUndefined",()=>d5,"$ZodUnion",()=>ek,"$ZodUnknown",()=>d8,"$ZodVoid",()=>ea,"$ZodXID",()=>dJ,"isValidBase64",()=>dT,"isValidBase64URL",()=>dV,"isValidJWT",()=>dY],96686),a.s(["$ZodCheck",()=>c6,"$ZodCheckBigIntFormat",()=>dc,"$ZodCheckEndsWith",()=>dq,"$ZodCheckGreaterThan",()=>c9,"$ZodCheckIncludes",()=>dn,"$ZodCheckLengthEquals",()=>di,"$ZodCheckLessThan",()=>c8,"$ZodCheckLowerCase",()=>dl,"$ZodCheckMaxLength",()=>dg,"$ZodCheckMaxSize",()=>dd,"$ZodCheckMimeType",()=>dt,"$ZodCheckMinLength",()=>dh,"$ZodCheckMinSize",()=>de,"$ZodCheckMultipleOf",()=>da,"$ZodCheckNumberFormat",()=>db,"$ZodCheckOverwrite",()=>du,"$ZodCheckProperty",()=>ds,"$ZodCheckRegex",()=>dk,"$ZodCheckSizeEquals",()=>df,"$ZodCheckStartsWith",()=>dp,"$ZodCheckStringFormat",()=>dj,"$ZodCheckUpperCase",()=>dm],2381),a.s(["base64",()=>cx,"base64url",()=>cy,"bigint",()=>cI,"boolean",()=>cL,"browserEmail",()=>cr,"cidrv4",()=>cv,"cidrv6",()=>cw,"cuid",()=>b9,"cuid2",()=>ca,"date",()=>cD,"datetime",()=>cG,"domain",()=>cA,"duration",()=>cf,"e164",()=>cB,"email",()=>cm,"emoji",()=>cs,"extendedDuration",()=>cg,"guid",()=>ch,"hex",()=>cQ,"hostname",()=>cz,"html5Email",()=>cn,"idnEmail",()=>cq,"integer",()=>cJ,"ipv4",()=>ct,"ipv6",()=>cu,"ksuid",()=>cd,"lowercase",()=>cO,"md5_base64",()=>cU,"md5_base64url",()=>cV,"md5_hex",()=>cT,"nanoid",()=>ce,"null",()=>cM,"number",()=>cK,"rfc5322Email",()=>co,"sha1_base64",()=>cX,"sha1_base64url",()=>cY,"sha1_hex",()=>cW,"sha256_base64",()=>c$,"sha256_base64url",()=>c_,"sha256_hex",()=>cZ,"sha384_base64",()=>c1,"sha384_base64url",()=>c2,"sha384_hex",()=>c0,"sha512_base64",()=>c4,"sha512_base64url",()=>c5,"sha512_hex",()=>c3,"string",()=>cH,"time",()=>cF,"ulid",()=>cb,"undefined",()=>cN,"unicodeEmail",()=>cp,"uppercase",()=>cP,"uuid",()=>ci,"uuid4",()=>cj,"uuid6",()=>ck,"uuid7",()=>cl,"xid",()=>cc],10395);let b9=/^[cC][^\s-]{8,}$/,ca=/^[0-9a-z]+$/,cb=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,cc=/^[0-9a-vA-V]{20}$/,cd=/^[A-Za-z0-9]{27}$/,ce=/^[a-zA-Z0-9_-]{21}$/,cf=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,cg=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,ch=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,ci=a=>a?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${a}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/,cj=ci(4),ck=ci(6),cl=ci(7),cm=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,cn=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,co=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,cp=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,cq=/^[^\s@"]{1,64}@[^\s@]{1,255}$/u,cr=/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;function cs(){return RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")}let ct=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,cu=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,cv=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,cw=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,cx=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,cy=/^[A-Za-z0-9_-]*$/,cz=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,cA=/^([a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/,cB=/^\+(?:[0-9]){6,14}[0-9]$/,cC="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",cD=RegExp(`^${cC}$`);function cE(a){let b="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof a.precision?-1===a.precision?`${b}`:0===a.precision?`${b}:[0-5]\\d`:`${b}:[0-5]\\d\\.\\d{${a.precision}}`:`${b}(?::[0-5]\\d(?:\\.\\d+)?)?`}function cF(a){return RegExp(`^${cE(a)}$`)}function cG(a){let b=cE({precision:a.precision}),c=["Z"];a.local&&c.push(""),a.offset&&c.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");let d=`${b}(?:${c.join("|")})`;return RegExp(`^${cC}T(?:${d})$`)}let cH=a=>{let b=a?`[\\s\\S]{${a?.minimum??0},${a?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${b}$`)},cI=/^\d+n?$/,cJ=/^\d+$/,cK=/^-?\d+(?:\.\d+)?/i,cL=/true|false/i,cM=/null/i,cN=/undefined/i,cO=/^[^A-Z]*$/,cP=/^[^a-z]*$/,cQ=/^[0-9a-fA-F]*$/;function cR(a,b){return RegExp(`^[A-Za-z0-9+/]{${a}}${b}$`)}function cS(a){return RegExp(`^[A-Za-z0-9-_]{${a}}$`)}let cT=/^[0-9a-fA-F]{32}$/,cU=cR(22,"=="),cV=cS(22),cW=/^[0-9a-fA-F]{40}$/,cX=cR(27,"="),cY=cS(27),cZ=/^[0-9a-fA-F]{64}$/,c$=cR(43,"="),c_=cS(43),c0=/^[0-9a-fA-F]{96}$/,c1=cR(64,""),c2=cS(64),c3=/^[0-9a-fA-F]{128}$/,c4=cR(86,"=="),c5=cS(86),c6=ak("$ZodCheck",(a,b)=>{var c;a._zod??(a._zod={}),a._zod.def=b,(c=a._zod).onattach??(c.onattach=[])}),c7={number:"number",bigint:"bigint",object:"date"},c8=ak("$ZodCheckLessThan",(a,b)=>{c6.init(a,b);let c=c7[typeof b.value];a._zod.onattach.push(a=>{let c=a._zod.bag,d=(b.inclusive?c.maximum:c.exclusiveMaximum)??1/0;b.value<d&&(b.inclusive?c.maximum=b.value:c.exclusiveMaximum=b.value)}),a._zod.check=d=>{(b.inclusive?d.value<=b.value:d.value<b.value)||d.issues.push({origin:c,code:"too_big",maximum:b.value,input:d.value,inclusive:b.inclusive,inst:a,continue:!b.abort})}}),c9=ak("$ZodCheckGreaterThan",(a,b)=>{c6.init(a,b);let c=c7[typeof b.value];a._zod.onattach.push(a=>{let c=a._zod.bag,d=(b.inclusive?c.minimum:c.exclusiveMinimum)??-1/0;b.value>d&&(b.inclusive?c.minimum=b.value:c.exclusiveMinimum=b.value)}),a._zod.check=d=>{(b.inclusive?d.value>=b.value:d.value>b.value)||d.issues.push({origin:c,code:"too_small",minimum:b.value,input:d.value,inclusive:b.inclusive,inst:a,continue:!b.abort})}}),da=ak("$ZodCheckMultipleOf",(a,b)=>{c6.init(a,b),a._zod.onattach.push(a=>{var c;(c=a._zod.bag).multipleOf??(c.multipleOf=b.value)}),a._zod.check=c=>{if(typeof c.value!=typeof b.value)throw Error("Cannot mix number and bigint in multiple_of check.");("bigint"==typeof c.value?c.value%b.value===BigInt(0):0===aB(c.value,b.value))||c.issues.push({origin:typeof c.value,code:"not_multiple_of",divisor:b.value,input:c.value,inst:a,continue:!b.abort})}}),db=ak("$ZodCheckNumberFormat",(a,b)=>{c6.init(a,b),b.format=b.format||"float64";let c=b.format?.includes("int"),d=c?"int":"number",[e,f]=a_[b.format];a._zod.onattach.push(a=>{let d=a._zod.bag;d.format=b.format,d.minimum=e,d.maximum=f,c&&(d.pattern=cJ)}),a._zod.check=g=>{let h=g.value;if(c){if(!Number.isInteger(h))return void g.issues.push({expected:d,format:b.format,code:"invalid_type",continue:!1,input:h,inst:a});if(!Number.isSafeInteger(h))return void(h>0?g.issues.push({input:h,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:d,continue:!b.abort}):g.issues.push({input:h,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:a,origin:d,continue:!b.abort}))}h<e&&g.issues.push({origin:"number",input:h,code:"too_small",minimum:e,inclusive:!0,inst:a,continue:!b.abort}),h>f&&g.issues.push({origin:"number",input:h,code:"too_big",maximum:f,inst:a})}}),dc=ak("$ZodCheckBigIntFormat",(a,b)=>{c6.init(a,b);let[c,d]=a0[b.format];a._zod.onattach.push(a=>{let e=a._zod.bag;e.format=b.format,e.minimum=c,e.maximum=d}),a._zod.check=e=>{let f=e.value;f<c&&e.issues.push({origin:"bigint",input:f,code:"too_small",minimum:c,inclusive:!0,inst:a,continue:!b.abort}),f>d&&e.issues.push({origin:"bigint",input:f,code:"too_big",maximum:d,inst:a})}}),dd=ak("$ZodCheckMaxSize",(a,b)=>{var c;c6.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!az(b)&&void 0!==b.size}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;d.size<=b.maximum||c.issues.push({origin:bc(d),code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),de=ak("$ZodCheckMinSize",(a,b)=>{var c;c6.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!az(b)&&void 0!==b.size}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;d.size>=b.minimum||c.issues.push({origin:bc(d),code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),df=ak("$ZodCheckSizeEquals",(a,b)=>{var c;c6.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!az(b)&&void 0!==b.size}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.size,c.maximum=b.size,c.size=b.size}),a._zod.check=c=>{let d=c.value,e=d.size;if(e===b.size)return;let f=e>b.size;c.issues.push({origin:bc(d),...f?{code:"too_big",maximum:b.size}:{code:"too_small",minimum:b.size},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),dg=ak("$ZodCheckMaxLength",(a,b)=>{var c;c6.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!az(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.maximum??1/0;b.maximum<c&&(a._zod.bag.maximum=b.maximum)}),a._zod.check=c=>{let d=c.value;if(d.length<=b.maximum)return;let e=bd(d);c.issues.push({origin:e,code:"too_big",maximum:b.maximum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),dh=ak("$ZodCheckMinLength",(a,b)=>{var c;c6.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!az(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag.minimum??-1/0;b.minimum>c&&(a._zod.bag.minimum=b.minimum)}),a._zod.check=c=>{let d=c.value;if(d.length>=b.minimum)return;let e=bd(d);c.issues.push({origin:e,code:"too_small",minimum:b.minimum,inclusive:!0,input:d,inst:a,continue:!b.abort})}}),di=ak("$ZodCheckLengthEquals",(a,b)=>{var c;c6.init(a,b),(c=a._zod.def).when??(c.when=a=>{let b=a.value;return!az(b)&&void 0!==b.length}),a._zod.onattach.push(a=>{let c=a._zod.bag;c.minimum=b.length,c.maximum=b.length,c.length=b.length}),a._zod.check=c=>{let d=c.value,e=d.length;if(e===b.length)return;let f=bd(d),g=e>b.length;c.issues.push({origin:f,...g?{code:"too_big",maximum:b.length}:{code:"too_small",minimum:b.length},inclusive:!0,exact:!0,input:c.value,inst:a,continue:!b.abort})}}),dj=ak("$ZodCheckStringFormat",(a,b)=>{var c,d;c6.init(a,b),a._zod.onattach.push(a=>{let c=a._zod.bag;c.format=b.format,b.pattern&&(c.patterns??(c.patterns=new Set),c.patterns.add(b.pattern))}),b.pattern?(c=a._zod).check??(c.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:b.format,input:c.value,...b.pattern?{pattern:b.pattern.toString()}:{},inst:a,continue:!b.abort})}):(d=a._zod).check??(d.check=()=>{})}),dk=ak("$ZodCheckRegex",(a,b)=>{dj.init(a,b),a._zod.check=c=>{b.pattern.lastIndex=0,b.pattern.test(c.value)||c.issues.push({origin:"string",code:"invalid_format",format:"regex",input:c.value,pattern:b.pattern.toString(),inst:a,continue:!b.abort})}}),dl=ak("$ZodCheckLowerCase",(a,b)=>{b.pattern??(b.pattern=cO),dj.init(a,b)}),dm=ak("$ZodCheckUpperCase",(a,b)=>{b.pattern??(b.pattern=cP),dj.init(a,b)}),dn=ak("$ZodCheckIncludes",(a,b)=>{c6.init(a,b);let c=aV(b.includes),d=new RegExp("number"==typeof b.position?`^.{${b.position}}${c}`:c);b.pattern=d,a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(d)}),a._zod.check=c=>{c.value.includes(b.includes,b.position)||c.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:b.includes,input:c.value,inst:a,continue:!b.abort})}}),dp=ak("$ZodCheckStartsWith",(a,b)=>{c6.init(a,b);let c=RegExp(`^${aV(b.prefix)}.*`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.startsWith(b.prefix)||c.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:b.prefix,input:c.value,inst:a,continue:!b.abort})}}),dq=ak("$ZodCheckEndsWith",(a,b)=>{c6.init(a,b);let c=RegExp(`.*${aV(b.suffix)}$`);b.pattern??(b.pattern=c),a._zod.onattach.push(a=>{let b=a._zod.bag;b.patterns??(b.patterns=new Set),b.patterns.add(c)}),a._zod.check=c=>{c.value.endsWith(b.suffix)||c.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:b.suffix,input:c.value,inst:a,continue:!b.abort})}});function dr(a,b,c){a.issues.length&&b.issues.push(...a9(c,a.issues))}let ds=ak("$ZodCheckProperty",(a,b)=>{c6.init(a,b),a._zod.check=a=>{let c=b.schema._zod.run({value:a.value[b.property],issues:[]},{});if(c instanceof Promise)return c.then(c=>dr(c,a,b.property));dr(c,a,b.property)}}),dt=ak("$ZodCheckMimeType",(a,b)=>{c6.init(a,b);let c=new Set(b.mime);a._zod.onattach.push(a=>{a._zod.bag.mime=b.mime}),a._zod.check=d=>{c.has(d.value.type)||d.issues.push({code:"invalid_value",values:b.mime,input:d.value.type,inst:a,continue:!b.abort})}}),du=ak("$ZodCheckOverwrite",(a,b)=>{c6.init(a,b),a._zod.check=a=>{a.value=b.tx(a.value)}});a.s(["Doc",()=>dv],26553);class dv{constructor(a=[]){this.content=[],this.indent=0,this&&(this.args=a)}indented(a){this.indent+=1,a(this),this.indent-=1}write(a){if("function"==typeof a){a(this,{execution:"sync"}),a(this,{execution:"async"});return}let b=a.split("\n").filter(a=>a),c=Math.min(...b.map(a=>a.length-a.trimStart().length));for(let a of b.map(a=>a.slice(c)).map(a=>" ".repeat(2*this.indent)+a))this.content.push(a)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(a=>`  ${a}`)].join("\n"))}}a.s(["version",()=>dw],23215);let dw={major:4,minor:1,patch:5},dx=ak("$ZodType",(a,b)=>{var c;a??(a={}),a._zod.def=b,a._zod.bag=a._zod.bag||{},a._zod.version=dw;let d=[...a._zod.def.checks??[]];for(let b of(a._zod.traits.has("$ZodCheck")&&d.unshift(a),d))for(let c of b._zod.onattach)c(a);if(0===d.length)(c=a._zod).deferred??(c.deferred=[]),a._zod.deferred?.push(()=>{a._zod.run=a._zod.parse});else{let b=(a,b,c)=>{let d,e=a8(a);for(let f of b){if(f._zod.def.when){if(!f._zod.def.when(a))continue}else if(e)continue;let b=a.issues.length,g=f._zod.check(a);if(g instanceof Promise&&c?.async===!1)throw new am;if(d||g instanceof Promise)d=(d??Promise.resolve()).then(async()=>{await g,a.issues.length!==b&&(e||(e=a8(a,b)))});else{if(a.issues.length===b)continue;e||(e=a8(a,b))}}return d?d.then(()=>a):a},c=(c,e,f)=>{if(a8(c))return c.aborted=!0,c;let g=b(e,d,f);if(g instanceof Promise){if(!1===f.async)throw new am;return g.then(b=>a._zod.parse(b,f))}return a._zod.parse(g,f)};a._zod.run=(e,f)=>{if(f.skipChecks)return a._zod.parse(e,f);if("backward"===f.direction){let b=a._zod.parse({value:e.value,issues:[]},{...f,skipChecks:!0});return b instanceof Promise?b.then(a=>c(a,e,f)):c(b,e,f)}let g=a._zod.parse(e,f);if(g instanceof Promise){if(!1===f.async)throw new am;return g.then(a=>b(a,d,f))}return b(g,d,f)}}a["~standard"]={validate:b=>{try{let c=bA(a,b);return c.success?{value:c.data}:{issues:c.error?.issues}}catch(c){return bC(a,b).then(a=>a.success?{value:a.data}:{issues:a.error?.issues})}},vendor:"zod",version:1}}),dy=ak("$ZodString",(a,b)=>{dx.init(a,b),a._zod.pattern=[...a?._zod.bag?.patterns??[]].pop()??cH(a._zod.bag),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=String(c.value)}catch(a){}return"string"==typeof c.value||c.issues.push({expected:"string",code:"invalid_type",input:c.value,inst:a}),c}}),dz=ak("$ZodStringFormat",(a,b)=>{dj.init(a,b),dy.init(a,b)}),dA=ak("$ZodGUID",(a,b)=>{b.pattern??(b.pattern=ch),dz.init(a,b)}),dB=ak("$ZodUUID",(a,b)=>{if(b.version){let a={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[b.version];if(void 0===a)throw Error(`Invalid UUID version: "${b.version}"`);b.pattern??(b.pattern=ci(a))}else b.pattern??(b.pattern=ci());dz.init(a,b)}),dC=ak("$ZodEmail",(a,b)=>{b.pattern??(b.pattern=cm),dz.init(a,b)}),dD=ak("$ZodURL",(a,b)=>{dz.init(a,b),a._zod.check=c=>{try{let d=c.value.trim(),e=new URL(d);b.hostname&&(b.hostname.lastIndex=0,b.hostname.test(e.hostname)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:cz.source,input:c.value,inst:a,continue:!b.abort})),b.protocol&&(b.protocol.lastIndex=0,b.protocol.test(e.protocol.endsWith(":")?e.protocol.slice(0,-1):e.protocol)||c.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:b.protocol.source,input:c.value,inst:a,continue:!b.abort})),b.normalize?c.value=e.href:c.value=d;return}catch(d){c.issues.push({code:"invalid_format",format:"url",input:c.value,inst:a,continue:!b.abort})}}}),dE=ak("$ZodEmoji",(a,b)=>{b.pattern??(b.pattern=cs()),dz.init(a,b)}),dF=ak("$ZodNanoID",(a,b)=>{b.pattern??(b.pattern=ce),dz.init(a,b)}),dG=ak("$ZodCUID",(a,b)=>{b.pattern??(b.pattern=b9),dz.init(a,b)}),dH=ak("$ZodCUID2",(a,b)=>{b.pattern??(b.pattern=ca),dz.init(a,b)}),dI=ak("$ZodULID",(a,b)=>{b.pattern??(b.pattern=cb),dz.init(a,b)}),dJ=ak("$ZodXID",(a,b)=>{b.pattern??(b.pattern=cc),dz.init(a,b)}),dK=ak("$ZodKSUID",(a,b)=>{b.pattern??(b.pattern=cd),dz.init(a,b)}),dL=ak("$ZodISODateTime",(a,b)=>{b.pattern??(b.pattern=cG(b)),dz.init(a,b)}),dM=ak("$ZodISODate",(a,b)=>{b.pattern??(b.pattern=cD),dz.init(a,b)}),dN=ak("$ZodISOTime",(a,b)=>{b.pattern??(b.pattern=cF(b)),dz.init(a,b)}),dO=ak("$ZodISODuration",(a,b)=>{b.pattern??(b.pattern=cf),dz.init(a,b)}),dP=ak("$ZodIPv4",(a,b)=>{b.pattern??(b.pattern=ct),dz.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv4"})}),dQ=ak("$ZodIPv6",(a,b)=>{b.pattern??(b.pattern=cu),dz.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.format="ipv6"}),a._zod.check=c=>{try{new URL(`http://[${c.value}]`)}catch{c.issues.push({code:"invalid_format",format:"ipv6",input:c.value,inst:a,continue:!b.abort})}}}),dR=ak("$ZodCIDRv4",(a,b)=>{b.pattern??(b.pattern=cv),dz.init(a,b)}),dS=ak("$ZodCIDRv6",(a,b)=>{b.pattern??(b.pattern=cw),dz.init(a,b),a._zod.check=c=>{let[d,e]=c.value.split("/");try{if(!e)throw Error();let a=Number(e);if(`${a}`!==e||a<0||a>128)throw Error();new URL(`http://[${d}]`)}catch{c.issues.push({code:"invalid_format",format:"cidrv6",input:c.value,inst:a,continue:!b.abort})}}});function dT(a){if(""===a)return!0;if(a.length%4!=0)return!1;try{return atob(a),!0}catch{return!1}}let dU=ak("$ZodBase64",(a,b)=>{b.pattern??(b.pattern=cx),dz.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64"}),a._zod.check=c=>{dT(c.value)||c.issues.push({code:"invalid_format",format:"base64",input:c.value,inst:a,continue:!b.abort})}});function dV(a){if(!cy.test(a))return!1;let b=a.replace(/[-_]/g,a=>"-"===a?"+":"/");return dT(b.padEnd(4*Math.ceil(b.length/4),"="))}let dW=ak("$ZodBase64URL",(a,b)=>{b.pattern??(b.pattern=cy),dz.init(a,b),a._zod.onattach.push(a=>{a._zod.bag.contentEncoding="base64url"}),a._zod.check=c=>{dV(c.value)||c.issues.push({code:"invalid_format",format:"base64url",input:c.value,inst:a,continue:!b.abort})}}),dX=ak("$ZodE164",(a,b)=>{b.pattern??(b.pattern=cB),dz.init(a,b)});function dY(a,b=null){try{let c=a.split(".");if(3!==c.length)return!1;let[d]=c;if(!d)return!1;let e=JSON.parse(atob(d));if("typ"in e&&e?.typ!=="JWT"||!e.alg||b&&(!("alg"in e)||e.alg!==b))return!1;return!0}catch{return!1}}let dZ=ak("$ZodJWT",(a,b)=>{dz.init(a,b),a._zod.check=c=>{dY(c.value,b.alg)||c.issues.push({code:"invalid_format",format:"jwt",input:c.value,inst:a,continue:!b.abort})}}),d$=ak("$ZodCustomStringFormat",(a,b)=>{dz.init(a,b),a._zod.check=c=>{b.fn(c.value)||c.issues.push({code:"invalid_format",format:b.format,input:c.value,inst:a,continue:!b.abort})}}),d_=ak("$ZodNumber",(a,b)=>{dx.init(a,b),a._zod.pattern=a._zod.bag.pattern??cK,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=Number(c.value)}catch(a){}let e=c.value;if("number"==typeof e&&!Number.isNaN(e)&&Number.isFinite(e))return c;let f="number"==typeof e?Number.isNaN(e)?"NaN":Number.isFinite(e)?void 0:"Infinity":void 0;return c.issues.push({expected:"number",code:"invalid_type",input:e,inst:a,...f?{received:f}:{}}),c}}),d0=ak("$ZodNumber",(a,b)=>{db.init(a,b),d_.init(a,b)}),d1=ak("$ZodBoolean",(a,b)=>{dx.init(a,b),a._zod.pattern=cL,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=!!c.value}catch(a){}let e=c.value;return"boolean"==typeof e||c.issues.push({expected:"boolean",code:"invalid_type",input:e,inst:a}),c}}),d2=ak("$ZodBigInt",(a,b)=>{dx.init(a,b),a._zod.pattern=cI,a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=BigInt(c.value)}catch(a){}return"bigint"==typeof c.value||c.issues.push({expected:"bigint",code:"invalid_type",input:c.value,inst:a}),c}}),d3=ak("$ZodBigInt",(a,b)=>{dc.init(a,b),d2.init(a,b)}),d4=ak("$ZodSymbol",(a,b)=>{dx.init(a,b),a._zod.parse=(b,c)=>{let d=b.value;return"symbol"==typeof d||b.issues.push({expected:"symbol",code:"invalid_type",input:d,inst:a}),b}}),d5=ak("$ZodUndefined",(a,b)=>{dx.init(a,b),a._zod.pattern=cN,a._zod.values=new Set([void 0]),a._zod.optin="optional",a._zod.optout="optional",a._zod.parse=(b,c)=>{let d=b.value;return void 0===d||b.issues.push({expected:"undefined",code:"invalid_type",input:d,inst:a}),b}}),d6=ak("$ZodNull",(a,b)=>{dx.init(a,b),a._zod.pattern=cM,a._zod.values=new Set([null]),a._zod.parse=(b,c)=>{let d=b.value;return null===d||b.issues.push({expected:"null",code:"invalid_type",input:d,inst:a}),b}}),d7=ak("$ZodAny",(a,b)=>{dx.init(a,b),a._zod.parse=a=>a}),d8=ak("$ZodUnknown",(a,b)=>{dx.init(a,b),a._zod.parse=a=>a}),d9=ak("$ZodNever",(a,b)=>{dx.init(a,b),a._zod.parse=(b,c)=>(b.issues.push({expected:"never",code:"invalid_type",input:b.value,inst:a}),b)}),ea=ak("$ZodVoid",(a,b)=>{dx.init(a,b),a._zod.parse=(b,c)=>{let d=b.value;return void 0===d||b.issues.push({expected:"void",code:"invalid_type",input:d,inst:a}),b}}),eb=ak("$ZodDate",(a,b)=>{dx.init(a,b),a._zod.parse=(c,d)=>{if(b.coerce)try{c.value=new Date(c.value)}catch(a){}let e=c.value,f=e instanceof Date;return f&&!Number.isNaN(e.getTime())||c.issues.push({expected:"date",code:"invalid_type",input:e,...f?{received:"Invalid Date"}:{},inst:a}),c}});function ec(a,b,c){a.issues.length&&b.issues.push(...a9(c,a.issues)),b.value[c]=a.value}let ed=ak("$ZodArray",(a,b)=>{dx.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!Array.isArray(e))return c.issues.push({expected:"array",code:"invalid_type",input:e,inst:a}),c;c.value=Array(e.length);let f=[];for(let a=0;a<e.length;a++){let g=e[a],h=b.element._zod.run({value:g,issues:[]},d);h instanceof Promise?f.push(h.then(b=>ec(b,c,a))):ec(h,c,a)}return f.length?Promise.all(f).then(()=>c):c}});function ee(a,b,c,d){a.issues.length&&b.issues.push(...a9(c,a.issues)),void 0===a.value?c in d&&(b.value[c]=void 0):b.value[c]=a.value}function ef(a){let b=Object.keys(a.shape);for(let c of b)if(!a.shape[c]._zod.traits.has("$ZodType"))throw Error(`Invalid element at key "${c}": expected a Zod schema`);let c=a$(a.shape);return{...a,keys:b,keySet:new Set(b),numKeys:b.length,optionalKeys:new Set(c)}}function eg(a,b,c,d,e,f){let g=[],h=e.keySet,i=e.catchall._zod,j=i.def.type;for(let e of Object.keys(b)){if(h.has(e))continue;if("never"===j){g.push(e);continue}let f=i.run({value:b[e],issues:[]},d);f instanceof Promise?a.push(f.then(a=>ee(a,c,e,b))):ee(f,c,e,b)}return(g.length&&c.issues.push({code:"unrecognized_keys",keys:g,input:b,inst:f}),a.length)?Promise.all(a).then(()=>c):c}let eh=ak("$ZodObject",(a,b)=>{let c;dx.init(a,b);let d=ay(()=>ef(b));aD(a._zod,"propValues",()=>{let a=b.shape,c={};for(let b in a){let d=a[b]._zod;if(d.values)for(let a of(c[b]??(c[b]=new Set),d.values))c[b].add(a)}return c});let e=b.catchall;a._zod.parse=(b,f)=>{c??(c=d.value);let g=b.value;if(!aN(g))return b.issues.push({expected:"object",code:"invalid_type",input:g,inst:a}),b;b.value={};let h=[],i=c.shape;for(let a of c.keys){let c=i[a]._zod.run({value:g[a],issues:[]},f);c instanceof Promise?h.push(c.then(c=>ee(c,b,a,g))):ee(c,b,a,g)}return e?eg(h,g,b,f,d.value,a):h.length?Promise.all(h).then(()=>b):b}}),ei=ak("$ZodObjectJIT",(a,b)=>{let c,d;eh.init(a,b);let e=a._zod.parse,f=ay(()=>ef(b)),g=!ao.jitless,h=g&&aO.value,i=b.catchall;a._zod.parse=(j,k)=>{d??(d=f.value);let l=j.value;return aN(l)?g&&h&&k?.async===!1&&!0!==k.jitless?(c||(c=(a=>{let b=new dv(["shape","payload","ctx"]),c=f.value,d=a=>{let b=aL(a);return`shape[${b}]._zod.run({ value: input[${b}], issues: [] }, ctx)`};b.write("const input = payload.value;");let e=Object.create(null),g=0;for(let a of c.keys)e[a]=`key_${g++}`;for(let a of(b.write("const newResult = {}"),c.keys)){let c=e[a],f=aL(a);b.write(`const ${c} = ${d(a)};`),b.write(`
        if (${c}.issues.length) {
          payload.issues = payload.issues.concat(${c}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${f}, ...iss.path] : [${f}]
          })));
        }
        
        if (${c}.value === undefined) {
          if (${f} in input) {
            newResult[${f}] = undefined;
          }
        } else {
          newResult[${f}] = ${c}.value;
        }
      `)}b.write("payload.value = newResult;"),b.write("return payload;");let h=b.compile();return(b,c)=>h(a,b,c)})(b.shape)),j=c(j,k),i)?eg([],l,j,k,d,a):j:e(j,k):(j.issues.push({expected:"object",code:"invalid_type",input:l,inst:a}),j)}});function ej(a,b,c,d){for(let c of a)if(0===c.issues.length)return b.value=c.value,b;let e=a.filter(a=>!a8(a));return 1===e.length?(b.value=e[0].value,e[0]):(b.issues.push({code:"invalid_union",input:b.value,inst:c,errors:a.map(a=>a.issues.map(a=>bb(a,d,ap())))}),b)}let ek=ak("$ZodUnion",(a,b)=>{dx.init(a,b),aD(a._zod,"optin",()=>b.options.some(a=>"optional"===a._zod.optin)?"optional":void 0),aD(a._zod,"optout",()=>b.options.some(a=>"optional"===a._zod.optout)?"optional":void 0),aD(a._zod,"values",()=>{if(b.options.every(a=>a._zod.values))return new Set(b.options.flatMap(a=>Array.from(a._zod.values)))}),aD(a._zod,"pattern",()=>{if(b.options.every(a=>a._zod.pattern)){let a=b.options.map(a=>a._zod.pattern);return RegExp(`^(${a.map(a=>aA(a.source)).join("|")})$`)}});let c=1===b.options.length,d=b.options[0]._zod.run;a._zod.parse=(e,f)=>{if(c)return d(e,f);let g=!1,h=[];for(let a of b.options){let b=a._zod.run({value:e.value,issues:[]},f);if(b instanceof Promise)h.push(b),g=!0;else{if(0===b.issues.length)return b;h.push(b)}}return g?Promise.all(h).then(b=>ej(b,e,a,f)):ej(h,e,a,f)}}),el=ak("$ZodDiscriminatedUnion",(a,b)=>{ek.init(a,b);let c=a._zod.parse;aD(a._zod,"propValues",()=>{let a={};for(let c of b.options){let d=c._zod.propValues;if(!d||0===Object.keys(d).length)throw Error(`Invalid discriminated union option at index "${b.options.indexOf(c)}"`);for(let[b,c]of Object.entries(d))for(let d of(a[b]||(a[b]=new Set),c))a[b].add(d)}return a});let d=ay(()=>{let a=b.options,c=new Map;for(let d of a){let a=d._zod.propValues?.[b.discriminator];if(!a||0===a.size)throw Error(`Invalid discriminated union option at index "${b.options.indexOf(d)}"`);for(let b of a){if(c.has(b))throw Error(`Duplicate discriminator value "${String(b)}"`);c.set(b,d)}}return c});a._zod.parse=(e,f)=>{let g=e.value;if(!aN(g))return e.issues.push({code:"invalid_type",expected:"object",input:g,inst:a}),e;let h=d.value.get(g?.[b.discriminator]);return h?h._zod.run(e,f):b.unionFallback?c(e,f):(e.issues.push({code:"invalid_union",errors:[],note:"No matching discriminator",discriminator:b.discriminator,input:g,path:[b.discriminator],inst:a}),e)}}),em=ak("$ZodIntersection",(a,b)=>{dx.init(a,b),a._zod.parse=(a,c)=>{let d=a.value,e=b.left._zod.run({value:d,issues:[]},c),f=b.right._zod.run({value:d,issues:[]},c);return e instanceof Promise||f instanceof Promise?Promise.all([e,f]).then(([b,c])=>en(a,b,c)):en(a,e,f)}});function en(a,b,c){if(b.issues.length&&a.issues.push(...b.issues),c.issues.length&&a.issues.push(...c.issues),a8(a))return a;let d=function a(b,c){if(b===c||b instanceof Date&&c instanceof Date&&+b==+c)return{valid:!0,data:b};if(aP(b)&&aP(c)){let d=Object.keys(c),e=Object.keys(b).filter(a=>-1!==d.indexOf(a)),f={...b,...c};for(let d of e){let e=a(b[d],c[d]);if(!e.valid)return{valid:!1,mergeErrorPath:[d,...e.mergeErrorPath]};f[d]=e.data}return{valid:!0,data:f}}if(Array.isArray(b)&&Array.isArray(c)){if(b.length!==c.length)return{valid:!1,mergeErrorPath:[]};let d=[];for(let e=0;e<b.length;e++){let f=a(b[e],c[e]);if(!f.valid)return{valid:!1,mergeErrorPath:[e,...f.mergeErrorPath]};d.push(f.data)}return{valid:!0,data:d}}return{valid:!1,mergeErrorPath:[]}}(b.value,c.value);if(!d.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(d.mergeErrorPath)}`);return a.value=d.data,a}let eo=ak("$ZodTuple",(a,b)=>{dx.init(a,b);let c=b.items,d=c.length-[...c].reverse().findIndex(a=>"optional"!==a._zod.optin);a._zod.parse=(e,f)=>{let g=e.value;if(!Array.isArray(g))return e.issues.push({input:g,inst:a,expected:"tuple",code:"invalid_type"}),e;e.value=[];let h=[];if(!b.rest){let b=g.length>c.length,f=g.length<d-1;if(b||f)return e.issues.push({...b?{code:"too_big",maximum:c.length}:{code:"too_small",minimum:c.length},input:g,inst:a,origin:"array"}),e}let i=-1;for(let a of c){if(++i>=g.length&&i>=d)continue;let b=a._zod.run({value:g[i],issues:[]},f);b instanceof Promise?h.push(b.then(a=>ep(a,e,i))):ep(b,e,i)}if(b.rest)for(let a of g.slice(c.length)){i++;let c=b.rest._zod.run({value:a,issues:[]},f);c instanceof Promise?h.push(c.then(a=>ep(a,e,i))):ep(c,e,i)}return h.length?Promise.all(h).then(()=>e):e}});function ep(a,b,c){a.issues.length&&b.issues.push(...a9(c,a.issues)),b.value[c]=a.value}let eq=ak("$ZodRecord",(a,b)=>{dx.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!aP(e))return c.issues.push({expected:"record",code:"invalid_type",input:e,inst:a}),c;let f=[];if(b.keyType._zod.values){let g,h=b.keyType._zod.values;for(let a of(c.value={},h))if("string"==typeof a||"number"==typeof a||"symbol"==typeof a){let g=b.valueType._zod.run({value:e[a],issues:[]},d);g instanceof Promise?f.push(g.then(b=>{b.issues.length&&c.issues.push(...a9(a,b.issues)),c.value[a]=b.value})):(g.issues.length&&c.issues.push(...a9(a,g.issues)),c.value[a]=g.value)}for(let a in e)h.has(a)||(g=g??[]).push(a);g&&g.length>0&&c.issues.push({code:"unrecognized_keys",input:e,inst:a,keys:g})}else for(let g of(c.value={},Reflect.ownKeys(e))){if("__proto__"===g)continue;let h=b.keyType._zod.run({value:g,issues:[]},d);if(h instanceof Promise)throw Error("Async schemas not supported in object keys currently");if(h.issues.length){c.issues.push({code:"invalid_key",origin:"record",issues:h.issues.map(a=>bb(a,d,ap())),input:g,path:[g],inst:a}),c.value[h.value]=h.value;continue}let i=b.valueType._zod.run({value:e[g],issues:[]},d);i instanceof Promise?f.push(i.then(a=>{a.issues.length&&c.issues.push(...a9(g,a.issues)),c.value[h.value]=a.value})):(i.issues.length&&c.issues.push(...a9(g,i.issues)),c.value[h.value]=i.value)}return f.length?Promise.all(f).then(()=>c):c}}),er=ak("$ZodMap",(a,b)=>{dx.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!(e instanceof Map))return c.issues.push({expected:"map",code:"invalid_type",input:e,inst:a}),c;let f=[];for(let[g,h]of(c.value=new Map,e)){let i=b.keyType._zod.run({value:g,issues:[]},d),j=b.valueType._zod.run({value:h,issues:[]},d);i instanceof Promise||j instanceof Promise?f.push(Promise.all([i,j]).then(([b,f])=>{es(b,f,c,g,e,a,d)})):es(i,j,c,g,e,a,d)}return f.length?Promise.all(f).then(()=>c):c}});function es(a,b,c,d,e,f,g){a.issues.length&&(aT.has(typeof d)?c.issues.push(...a9(d,a.issues)):c.issues.push({code:"invalid_key",origin:"map",input:e,inst:f,issues:a.issues.map(a=>bb(a,g,ap()))})),b.issues.length&&(aT.has(typeof d)?c.issues.push(...a9(d,b.issues)):c.issues.push({origin:"map",code:"invalid_element",input:e,inst:f,key:d,issues:b.issues.map(a=>bb(a,g,ap()))})),c.value.set(a.value,b.value)}let et=ak("$ZodSet",(a,b)=>{dx.init(a,b),a._zod.parse=(c,d)=>{let e=c.value;if(!(e instanceof Set))return c.issues.push({input:e,inst:a,expected:"set",code:"invalid_type"}),c;let f=[];for(let a of(c.value=new Set,e)){let e=b.valueType._zod.run({value:a,issues:[]},d);e instanceof Promise?f.push(e.then(a=>eu(a,c))):eu(e,c)}return f.length?Promise.all(f).then(()=>c):c}});function eu(a,b){a.issues.length&&b.issues.push(...a.issues),b.value.add(a.value)}let ev=ak("$ZodEnum",(a,b)=>{dx.init(a,b);let c=av(b.entries),d=new Set(c);a._zod.values=d,a._zod.pattern=RegExp(`^(${c.filter(a=>aT.has(typeof a)).map(a=>"string"==typeof a?aV(a):a.toString()).join("|")})$`),a._zod.parse=(b,e)=>{let f=b.value;return d.has(f)||b.issues.push({code:"invalid_value",values:c,input:f,inst:a}),b}}),ew=ak("$ZodLiteral",(a,b)=>{if(dx.init(a,b),0===b.values.length)throw Error("Cannot create literal schema with no valid values");a._zod.values=new Set(b.values),a._zod.pattern=RegExp(`^(${b.values.map(a=>"string"==typeof a?aV(a):a?aV(a.toString()):String(a)).join("|")})$`),a._zod.parse=(c,d)=>{let e=c.value;return a._zod.values.has(e)||c.issues.push({code:"invalid_value",values:b.values,input:e,inst:a}),c}}),ex=ak("$ZodFile",(a,b)=>{dx.init(a,b),a._zod.parse=(b,c)=>{let d=b.value;return d instanceof File||b.issues.push({expected:"file",code:"invalid_type",input:d,inst:a}),b}}),ey=ak("$ZodTransform",(a,b)=>{dx.init(a,b),a._zod.parse=(c,d)=>{if("backward"===d.direction)throw new an(a.constructor.name);let e=b.transform(c.value,c);if(d.async)return(e instanceof Promise?e:Promise.resolve(e)).then(a=>(c.value=a,c));if(e instanceof Promise)throw new am;return c.value=e,c}});function ez(a,b){return a.issues.length&&void 0===b?{issues:[],value:void 0}:a}let eA=ak("$ZodOptional",(a,b)=>{dx.init(a,b),a._zod.optin="optional",a._zod.optout="optional",aD(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,void 0]):void 0),aD(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${aA(a.source)})?$`):void 0}),a._zod.parse=(a,c)=>{if("optional"===b.innerType._zod.optin){let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(b=>ez(b,a.value)):ez(d,a.value)}return void 0===a.value?a:b.innerType._zod.run(a,c)}}),eB=ak("$ZodNullable",(a,b)=>{dx.init(a,b),aD(a._zod,"optin",()=>b.innerType._zod.optin),aD(a._zod,"optout",()=>b.innerType._zod.optout),aD(a._zod,"pattern",()=>{let a=b.innerType._zod.pattern;return a?RegExp(`^(${aA(a.source)}|null)$`):void 0}),aD(a._zod,"values",()=>b.innerType._zod.values?new Set([...b.innerType._zod.values,null]):void 0),a._zod.parse=(a,c)=>null===a.value?a:b.innerType._zod.run(a,c)}),eC=ak("$ZodDefault",(a,b)=>{dx.init(a,b),a._zod.optin="optional",aD(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{if("backward"===c.direction)return b.innerType._zod.run(a,c);if(void 0===a.value)return a.value=b.defaultValue,a;let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(a=>eD(a,b)):eD(d,b)}});function eD(a,b){return void 0===a.value&&(a.value=b.defaultValue),a}let eE=ak("$ZodPrefault",(a,b)=>{dx.init(a,b),a._zod.optin="optional",aD(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>("backward"===c.direction||void 0===a.value&&(a.value=b.defaultValue),b.innerType._zod.run(a,c))}),eF=ak("$ZodNonOptional",(a,b)=>{dx.init(a,b),aD(a._zod,"values",()=>{let a=b.innerType._zod.values;return a?new Set([...a].filter(a=>void 0!==a)):void 0}),a._zod.parse=(c,d)=>{let e=b.innerType._zod.run(c,d);return e instanceof Promise?e.then(b=>eG(b,a)):eG(e,a)}});function eG(a,b){return a.issues.length||void 0!==a.value||a.issues.push({code:"invalid_type",expected:"nonoptional",input:a.value,inst:b}),a}let eH=ak("$ZodSuccess",(a,b)=>{dx.init(a,b),a._zod.parse=(a,c)=>{if("backward"===c.direction)throw new an("ZodSuccess");let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(b=>(a.value=0===b.issues.length,a)):(a.value=0===d.issues.length,a)}}),eI=ak("$ZodCatch",(a,b)=>{dx.init(a,b),aD(a._zod,"optin",()=>b.innerType._zod.optin),aD(a._zod,"optout",()=>b.innerType._zod.optout),aD(a._zod,"values",()=>b.innerType._zod.values),a._zod.parse=(a,c)=>{if("backward"===c.direction)return b.innerType._zod.run(a,c);let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(d=>(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>bb(a,c,ap()))},input:a.value}),a.issues=[]),a)):(a.value=d.value,d.issues.length&&(a.value=b.catchValue({...a,error:{issues:d.issues.map(a=>bb(a,c,ap()))},input:a.value}),a.issues=[]),a)}}),eJ=ak("$ZodNaN",(a,b)=>{dx.init(a,b),a._zod.parse=(b,c)=>("number"==typeof b.value&&Number.isNaN(b.value)||b.issues.push({input:b.value,inst:a,expected:"nan",code:"invalid_type"}),b)}),eK=ak("$ZodPipe",(a,b)=>{dx.init(a,b),aD(a._zod,"values",()=>b.in._zod.values),aD(a._zod,"optin",()=>b.in._zod.optin),aD(a._zod,"optout",()=>b.out._zod.optout),aD(a._zod,"propValues",()=>b.in._zod.propValues),a._zod.parse=(a,c)=>{if("backward"===c.direction){let d=b.out._zod.run(a,c);return d instanceof Promise?d.then(a=>eL(a,b.in,c)):eL(d,b.in,c)}let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>eL(a,b.out,c)):eL(d,b.out,c)}});function eL(a,b,c){return a.issues.length?(a.aborted=!0,a):b._zod.run({value:a.value,issues:a.issues},c)}let eM=ak("$ZodCodec",(a,b)=>{dx.init(a,b),aD(a._zod,"values",()=>b.in._zod.values),aD(a._zod,"optin",()=>b.in._zod.optin),aD(a._zod,"optout",()=>b.out._zod.optout),aD(a._zod,"propValues",()=>b.in._zod.propValues),a._zod.parse=(a,c)=>{if("forward"===(c.direction||"forward")){let d=b.in._zod.run(a,c);return d instanceof Promise?d.then(a=>eN(a,b,c)):eN(d,b,c)}{let d=b.out._zod.run(a,c);return d instanceof Promise?d.then(a=>eN(a,b,c)):eN(d,b,c)}}});function eN(a,b,c){if(a.issues.length)return a.aborted=!0,a;if("forward"===(c.direction||"forward")){let d=b.transform(a.value,a);return d instanceof Promise?d.then(d=>eO(a,d,b.out,c)):eO(a,d,b.out,c)}{let d=b.reverseTransform(a.value,a);return d instanceof Promise?d.then(d=>eO(a,d,b.in,c)):eO(a,d,b.in,c)}}function eO(a,b,c,d){return a.issues.length?(a.aborted=!0,a):c._zod.run({value:b,issues:a.issues},d)}let eP=ak("$ZodReadonly",(a,b)=>{dx.init(a,b),aD(a._zod,"propValues",()=>b.innerType._zod.propValues),aD(a._zod,"values",()=>b.innerType._zod.values),aD(a._zod,"optin",()=>b.innerType._zod.optin),aD(a._zod,"optout",()=>b.innerType._zod.optout),a._zod.parse=(a,c)=>{if("backward"===c.direction)return b.innerType._zod.run(a,c);let d=b.innerType._zod.run(a,c);return d instanceof Promise?d.then(eQ):eQ(d)}});function eQ(a){return a.value=Object.freeze(a.value),a}let eR=ak("$ZodTemplateLiteral",(a,b)=>{dx.init(a,b);let c=[];for(let a of b.parts)if("object"==typeof a&&null!==a){if(!a._zod.pattern)throw Error(`Invalid template literal part, no pattern found: ${[...a._zod.traits].shift()}`);let b=a._zod.pattern instanceof RegExp?a._zod.pattern.source:a._zod.pattern;if(!b)throw Error(`Invalid template literal part: ${a._zod.traits}`);let d=+!!b.startsWith("^"),e=b.endsWith("$")?b.length-1:b.length;c.push(b.slice(d,e))}else if(null===a||aU.has(typeof a))c.push(aV(`${a}`));else throw Error(`Invalid template literal part: ${a}`);a._zod.pattern=RegExp(`^${c.join("")}$`),a._zod.parse=(c,d)=>("string"!=typeof c.value?c.issues.push({input:c.value,inst:a,expected:"template_literal",code:"invalid_type"}):(a._zod.pattern.lastIndex=0,a._zod.pattern.test(c.value)||c.issues.push({input:c.value,inst:a,code:"invalid_format",format:b.format??"template_literal",pattern:a._zod.pattern.source})),c)}),eS=ak("$ZodFunction",(a,b)=>(dx.init(a,b),a._def=b,a._zod.def=b,a.implement=b=>{if("function"!=typeof b)throw Error("implement() must be called with a function");return function(...c){let d=Reflect.apply(b,this,a._def.input?bw(a._def.input,c):c);return a._def.output?bw(a._def.output,d):d}},a.implementAsync=b=>{if("function"!=typeof b)throw Error("implementAsync() must be called with a function");return async function(...c){let d=a._def.input?await by(a._def.input,c):c,e=await Reflect.apply(b,this,d);return a._def.output?await by(a._def.output,e):e}},a._zod.parse=(b,c)=>("function"!=typeof b.value?b.issues.push({code:"invalid_type",expected:"function",input:b.value,inst:a}):a._def.output&&"promise"===a._def.output._zod.def.type?b.value=a.implementAsync(b.value):b.value=a.implement(b.value),b),a.input=(...b)=>{let c=a.constructor;return new c(Array.isArray(b[0])?{type:"function",input:new eo({type:"tuple",items:b[0],rest:b[1]}),output:a._def.output}:{type:"function",input:b[0],output:a._def.output})},a.output=b=>new a.constructor({type:"function",input:a._def.input,output:b}),a)),eT=ak("$ZodPromise",(a,b)=>{dx.init(a,b),a._zod.parse=(a,c)=>Promise.resolve(a.value).then(a=>b.innerType._zod.run({value:a,issues:[]},c))}),eU=ak("$ZodLazy",(a,b)=>{dx.init(a,b),aD(a._zod,"innerType",()=>b.getter()),aD(a._zod,"pattern",()=>a._zod.innerType._zod.pattern),aD(a._zod,"propValues",()=>a._zod.innerType._zod.propValues),aD(a._zod,"optin",()=>a._zod.innerType._zod.optin??void 0),aD(a._zod,"optout",()=>a._zod.innerType._zod.optout??void 0),a._zod.parse=(b,c)=>a._zod.innerType._zod.run(b,c)}),eV=ak("$ZodCustom",(a,b)=>{c6.init(a,b),dx.init(a,b),a._zod.parse=(a,b)=>a,a._zod.check=c=>{let d=c.value,e=b.fn(d);if(e instanceof Promise)return e.then(b=>eW(b,c,d,a));eW(e,c,d,a)}});function eW(a,b,c,d){if(!a){let a={code:"custom",input:c,inst:d,path:[...d._zod.def.path??[]],continue:!d._zod.def.abort};d._zod.def.params&&(a.params=d._zod.def.params),b.issues.push(be(a))}}a.i(96686),a.i(60009),a.i(2381),a.i(23215);var eX=a.i(9346),eY=a.i(10395);function eZ(){return{localeError:(()=>{let a={string:{unit:"حرف",verb:"أن يحوي"},file:{unit:"بايت",verb:"أن يحوي"},array:{unit:"عنصر",verb:"أن يحوي"},set:{unit:"عنصر",verb:"أن يحوي"}},b={regex:"مدخل",email:"بريد إلكتروني",url:"رابط",emoji:"إيموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاريخ ووقت بمعيار ISO",date:"تاريخ بمعيار ISO",time:"وقت بمعيار ISO",duration:"مدة بمعيار ISO",ipv4:"عنوان IPv4",ipv6:"عنوان IPv6",cidrv4:"مدى عناوين بصيغة IPv4",cidrv6:"مدى عناوين بصيغة IPv6",base64:"نَص بترميز base64-encoded",base64url:"نَص بترميز base64url-encoded",json_string:"نَص على هيئة JSON",e164:"رقم هاتف بمعيار E.164",jwt:"JWT",template_literal:"مدخل"};return c=>{switch(c.code){case"invalid_type":return`مدخلات غير مقبولة: يفترض إدخال ${c.expected}، ولكن تم إدخال ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`مدخلات غير مقبولة: يفترض إدخال ${aZ(c.values[0])}`;return`اختيار غير مقبول: يتوقع انتقاء أحد هذه الخيارات: ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return` أكبر من اللازم: يفترض أن تكون ${c.origin??"القيمة"} ${b} ${c.maximum.toString()} ${d.unit??"عنصر"}`;return`أكبر من اللازم: يفترض أن تكون ${c.origin??"القيمة"} ${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`أصغر من اللازم: يفترض لـ ${c.origin} أن يكون ${b} ${c.minimum.toString()} ${d.unit}`;return`أصغر من اللازم: يفترض لـ ${c.origin} أن يكون ${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`نَص غير مقبول: يجب أن يبدأ بـ "${c.prefix}"`;if("ends_with"===c.format)return`نَص غير مقبول: يجب أن ينتهي بـ "${c.suffix}"`;if("includes"===c.format)return`نَص غير مقبول: يجب أن يتضمَّن "${c.includes}"`;if("regex"===c.format)return`نَص غير مقبول: يجب أن يطابق النمط ${c.pattern}`;return`${b[c.format]??c.format} غير مقبول`;case"not_multiple_of":return`رقم غير مقبول: يجب أن يكون من مضاعفات ${c.divisor}`;case"unrecognized_keys":return`معرف${c.keys.length>1?"ات":""} غريب${c.keys.length>1?"ة":""}: ${aw(c.keys,"، ")}`;case"invalid_key":return`معرف غير مقبول في ${c.origin}`;case"invalid_union":default:return"مدخل غير مقبول";case"invalid_element":return`مدخل غير مقبول في ${c.origin}`}}})()}}function e$(){return{localeError:(()=>{let a={string:{unit:"simvol",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"element",verb:"olmalıdır"},set:{unit:"element",verb:"olmalıdır"}},b={regex:"input",email:"email address",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datetime",date:"ISO date",time:"ISO time",duration:"ISO duration",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded string",base64url:"base64url-encoded string",json_string:"JSON string",e164:"E.164 number",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Yanlış dəyər: g\xf6zlənilən ${c.expected}, daxil olan ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Yanlış dəyər: g\xf6zlənilən ${aZ(c.values[0])}`;return`Yanlış se\xe7im: aşağıdakılardan biri olmalıdır: ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`\xc7ox b\xf6y\xfck: g\xf6zlənilən ${c.origin??"dəyər"} ${b}${c.maximum.toString()} ${d.unit??"element"}`;return`\xc7ox b\xf6y\xfck: g\xf6zlənilən ${c.origin??"dəyər"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`\xc7ox ki\xe7ik: g\xf6zlənilən ${c.origin} ${b}${c.minimum.toString()} ${d.unit}`;return`\xc7ox ki\xe7ik: g\xf6zlənilən ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Yanlış mətn: "${c.prefix}" ilə başlamalıdır`;if("ends_with"===c.format)return`Yanlış mətn: "${c.suffix}" ilə bitməlidir`;if("includes"===c.format)return`Yanlış mətn: "${c.includes}" daxil olmalıdır`;if("regex"===c.format)return`Yanlış mətn: ${c.pattern} şablonuna uyğun olmalıdır`;return`Yanlış ${b[c.format]??c.format}`;case"not_multiple_of":return`Yanlış ədəd: ${c.divisor} ilə b\xf6l\xfcnə bilən olmalıdır`;case"unrecognized_keys":return`Tanınmayan a\xe7ar${c.keys.length>1?"lar":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`${c.origin} daxilində yanlış a\xe7ar`;case"invalid_union":return"Yanlış dəyər";case"invalid_element":return`${c.origin} daxilində yanlış dəyər`;default:return`Yanlış dəyər`}}})()}}function e_(a,b,c,d){let e=Math.abs(a),f=e%10,g=e%100;return g>=11&&g<=19?d:1===f?b:f>=2&&f<=4?c:d}function e0(){return{localeError:(()=>{let a={string:{unit:{one:"сімвал",few:"сімвалы",many:"сімвалаў"},verb:"мець"},array:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},set:{unit:{one:"элемент",few:"элементы",many:"элементаў"},verb:"мець"},file:{unit:{one:"байт",few:"байты",many:"байтаў"},verb:"мець"}},b={regex:"увод",email:"email адрас",url:"URL",emoji:"эмодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата і час",date:"ISO дата",time:"ISO час",duration:"ISO працягласць",ipv4:"IPv4 адрас",ipv6:"IPv6 адрас",cidrv4:"IPv4 дыяпазон",cidrv6:"IPv6 дыяпазон",base64:"радок у фармаце base64",base64url:"радок у фармаце base64url",json_string:"JSON радок",e164:"нумар E.164",jwt:"JWT",template_literal:"увод"};return c=>{switch(c.code){case"invalid_type":return`Няправільны ўвод: чакаўся ${c.expected}, атрымана ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"лік";case"object":if(Array.isArray(a))return"масіў";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Няправільны ўвод: чакалася ${aZ(c.values[0])}`;return`Няправільны варыянт: чакаўся адзін з ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d){let a=e_(Number(c.maximum),d.unit.one,d.unit.few,d.unit.many);return`Занадта вялікі: чакалася, што ${c.origin??"значэнне"} павінна ${d.verb} ${b}${c.maximum.toString()} ${a}`}return`Занадта вялікі: чакалася, што ${c.origin??"значэнне"} павінна быць ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d){let a=e_(Number(c.minimum),d.unit.one,d.unit.few,d.unit.many);return`Занадта малы: чакалася, што ${c.origin} павінна ${d.verb} ${b}${c.minimum.toString()} ${a}`}return`Занадта малы: чакалася, што ${c.origin} павінна быць ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Няправільны радок: павінен пачынацца з "${c.prefix}"`;if("ends_with"===c.format)return`Няправільны радок: павінен заканчвацца на "${c.suffix}"`;if("includes"===c.format)return`Няправільны радок: павінен змяшчаць "${c.includes}"`;if("regex"===c.format)return`Няправільны радок: павінен адпавядаць шаблону ${c.pattern}`;return`Няправільны ${b[c.format]??c.format}`;case"not_multiple_of":return`Няправільны лік: павінен быць кратным ${c.divisor}`;case"unrecognized_keys":return`Нераспазнаны ${c.keys.length>1?"ключы":"ключ"}: ${aw(c.keys,", ")}`;case"invalid_key":return`Няправільны ключ у ${c.origin}`;case"invalid_union":return"Няправільны ўвод";case"invalid_element":return`Няправільнае значэнне ў ${c.origin}`;default:return`Няправільны ўвод`}}})()}}function e1(){return{localeError:(()=>{let a={string:{unit:"caràcters",verb:"contenir"},file:{unit:"bytes",verb:"contenir"},array:{unit:"elements",verb:"contenir"},set:{unit:"elements",verb:"contenir"}},b={regex:"entrada",email:"adreça electrònica",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i hora ISO",date:"data ISO",time:"hora ISO",duration:"durada ISO",ipv4:"adreça IPv4",ipv6:"adreça IPv6",cidrv4:"rang IPv4",cidrv6:"rang IPv6",base64:"cadena codificada en base64",base64url:"cadena codificada en base64url",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return c=>{switch(c.code){case"invalid_type":return`Tipus inv\xe0lid: s'esperava ${c.expected}, s'ha rebut ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Valor inv\xe0lid: s'esperava ${aZ(c.values[0])}`;return`Opci\xf3 inv\xe0lida: s'esperava una de ${aw(c.values," o ")}`;case"too_big":{let b=c.inclusive?"com a màxim":"menys de",d=a[c.origin]??null;if(d)return`Massa gran: s'esperava que ${c.origin??"el valor"} contingu\xe9s ${b} ${c.maximum.toString()} ${d.unit??"elements"}`;return`Massa gran: s'esperava que ${c.origin??"el valor"} fos ${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?"com a mínim":"més de",d=a[c.origin]??null;if(d)return`Massa petit: s'esperava que ${c.origin} contingu\xe9s ${b} ${c.minimum.toString()} ${d.unit}`;return`Massa petit: s'esperava que ${c.origin} fos ${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Format inv\xe0lid: ha de comen\xe7ar amb "${c.prefix}"`;if("ends_with"===c.format)return`Format inv\xe0lid: ha d'acabar amb "${c.suffix}"`;if("includes"===c.format)return`Format inv\xe0lid: ha d'incloure "${c.includes}"`;if("regex"===c.format)return`Format inv\xe0lid: ha de coincidir amb el patr\xf3 ${c.pattern}`;return`Format inv\xe0lid per a ${b[c.format]??c.format}`;case"not_multiple_of":return`N\xfamero inv\xe0lid: ha de ser m\xfaltiple de ${c.divisor}`;case"unrecognized_keys":return`Clau${c.keys.length>1?"s":""} no reconeguda${c.keys.length>1?"s":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`Clau inv\xe0lida a ${c.origin}`;case"invalid_union":return"Entrada invàlida";case"invalid_element":return`Element inv\xe0lid a ${c.origin}`;default:return`Entrada inv\xe0lida`}}})()}}function e2(){return{localeError:(()=>{let a={string:{unit:"znaků",verb:"mít"},file:{unit:"bajtů",verb:"mít"},array:{unit:"prvků",verb:"mít"},set:{unit:"prvků",verb:"mít"}},b={regex:"regulární výraz",email:"e-mailová adresa",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"datum a čas ve formátu ISO",date:"datum ve formátu ISO",time:"čas ve formátu ISO",duration:"doba trvání ISO",ipv4:"IPv4 adresa",ipv6:"IPv6 adresa",cidrv4:"rozsah IPv4",cidrv6:"rozsah IPv6",base64:"řetězec zakódovaný ve formátu base64",base64url:"řetězec zakódovaný ve formátu base64url",json_string:"řetězec ve formátu JSON",e164:"číslo E.164",jwt:"JWT",template_literal:"vstup"};return c=>{switch(c.code){case"invalid_type":return`Neplatn\xfd vstup: oček\xe1v\xe1no ${c.expected}, obdrženo ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"číslo";case"string":return"řetězec";case"boolean":return"boolean";case"bigint":return"bigint";case"function":return"funkce";case"symbol":return"symbol";case"undefined":return"undefined";case"object":if(Array.isArray(a))return"pole";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Neplatn\xfd vstup: oček\xe1v\xe1no ${aZ(c.values[0])}`;return`Neplatn\xe1 možnost: oček\xe1v\xe1na jedna z hodnot ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Hodnota je př\xedliš velk\xe1: ${c.origin??"hodnota"} mus\xed m\xedt ${b}${c.maximum.toString()} ${d.unit??"prvků"}`;return`Hodnota je př\xedliš velk\xe1: ${c.origin??"hodnota"} mus\xed b\xfdt ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Hodnota je př\xedliš mal\xe1: ${c.origin??"hodnota"} mus\xed m\xedt ${b}${c.minimum.toString()} ${d.unit??"prvků"}`;return`Hodnota je př\xedliš mal\xe1: ${c.origin??"hodnota"} mus\xed b\xfdt ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Neplatn\xfd řetězec: mus\xed zač\xednat na "${c.prefix}"`;if("ends_with"===c.format)return`Neplatn\xfd řetězec: mus\xed končit na "${c.suffix}"`;if("includes"===c.format)return`Neplatn\xfd řetězec: mus\xed obsahovat "${c.includes}"`;if("regex"===c.format)return`Neplatn\xfd řetězec: mus\xed odpov\xeddat vzoru ${c.pattern}`;return`Neplatn\xfd form\xe1t ${b[c.format]??c.format}`;case"not_multiple_of":return`Neplatn\xe9 č\xedslo: mus\xed b\xfdt n\xe1sobkem ${c.divisor}`;case"unrecognized_keys":return`Nezn\xe1m\xe9 kl\xedče: ${aw(c.keys,", ")}`;case"invalid_key":return`Neplatn\xfd kl\xedč v ${c.origin}`;case"invalid_union":return"Neplatný vstup";case"invalid_element":return`Neplatn\xe1 hodnota v ${c.origin}`;default:return`Neplatn\xfd vstup`}}})()}}function e3(){return{localeError:(()=>{let a={string:{unit:"tegn",verb:"havde"},file:{unit:"bytes",verb:"havde"},array:{unit:"elementer",verb:"indeholdt"},set:{unit:"elementer",verb:"indeholdt"}},b={string:"streng",number:"tal",boolean:"boolean",array:"liste",object:"objekt",set:"sæt",file:"fil"},c={regex:"input",email:"e-mailadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslæt",date:"ISO-dato",time:"ISO-klokkeslæt",duration:"ISO-varighed",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodet streng",base64url:"base64url-kodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return d=>{var e,f,g,h;switch(d.code){case"invalid_type":return`Ugyldigt input: forventede ${b[e=d.expected]??e}, fik ${b[f=(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"tal";case"object":if(Array.isArray(a))return"liste";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name;return"objekt"}return b})(d.input)]??f}`;case"invalid_value":if(1===d.values.length)return`Ugyldig v\xe6rdi: forventede ${aZ(d.values[0])}`;return`Ugyldigt valg: forventede en af f\xf8lgende ${aw(d.values,"|")}`;case"too_big":{let c=d.inclusive?"<=":"<",e=a[d.origin]??null,f=b[g=d.origin]??g;if(e)return`For stor: forventede ${f??"value"} ${e.verb} ${c} ${d.maximum.toString()} ${e.unit??"elementer"}`;return`For stor: forventede ${f??"value"} havde ${c} ${d.maximum.toString()}`}case"too_small":{let c=d.inclusive?">=":">",e=a[d.origin]??null,f=b[h=d.origin]??h;if(e)return`For lille: forventede ${f} ${e.verb} ${c} ${d.minimum.toString()} ${e.unit}`;return`For lille: forventede ${f} havde ${c} ${d.minimum.toString()}`}case"invalid_format":if("starts_with"===d.format)return`Ugyldig streng: skal starte med "${d.prefix}"`;if("ends_with"===d.format)return`Ugyldig streng: skal ende med "${d.suffix}"`;if("includes"===d.format)return`Ugyldig streng: skal indeholde "${d.includes}"`;if("regex"===d.format)return`Ugyldig streng: skal matche m\xf8nsteret ${d.pattern}`;return`Ugyldig ${c[d.format]??d.format}`;case"not_multiple_of":return`Ugyldigt tal: skal v\xe6re deleligt med ${d.divisor}`;case"unrecognized_keys":return`${d.keys.length>1?"Ukendte nøgler":"Ukendt nøgle"}: ${aw(d.keys,", ")}`;case"invalid_key":return`Ugyldig n\xf8gle i ${d.origin}`;case"invalid_union":return"Ugyldigt input: matcher ingen af de tilladte typer";case"invalid_element":return`Ugyldig v\xe6rdi i ${d.origin}`;default:return"Ugyldigt input"}}})()}}function e4(){return{localeError:(()=>{let a={string:{unit:"Zeichen",verb:"zu haben"},file:{unit:"Bytes",verb:"zu haben"},array:{unit:"Elemente",verb:"zu haben"},set:{unit:"Elemente",verb:"zu haben"}},b={regex:"Eingabe",email:"E-Mail-Adresse",url:"URL",emoji:"Emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-Datum und -Uhrzeit",date:"ISO-Datum",time:"ISO-Uhrzeit",duration:"ISO-Dauer",ipv4:"IPv4-Adresse",ipv6:"IPv6-Adresse",cidrv4:"IPv4-Bereich",cidrv6:"IPv6-Bereich",base64:"Base64-codierter String",base64url:"Base64-URL-codierter String",json_string:"JSON-String",e164:"E.164-Nummer",jwt:"JWT",template_literal:"Eingabe"};return c=>{switch(c.code){case"invalid_type":return`Ung\xfcltige Eingabe: erwartet ${c.expected}, erhalten ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"Zahl";case"object":if(Array.isArray(a))return"Array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ung\xfcltige Eingabe: erwartet ${aZ(c.values[0])}`;return`Ung\xfcltige Option: erwartet eine von ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Zu gro\xdf: erwartet, dass ${c.origin??"Wert"} ${b}${c.maximum.toString()} ${d.unit??"Elemente"} hat`;return`Zu gro\xdf: erwartet, dass ${c.origin??"Wert"} ${b}${c.maximum.toString()} ist`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Zu klein: erwartet, dass ${c.origin} ${b}${c.minimum.toString()} ${d.unit} hat`;return`Zu klein: erwartet, dass ${c.origin} ${b}${c.minimum.toString()} ist`}case"invalid_format":if("starts_with"===c.format)return`Ung\xfcltiger String: muss mit "${c.prefix}" beginnen`;if("ends_with"===c.format)return`Ung\xfcltiger String: muss mit "${c.suffix}" enden`;if("includes"===c.format)return`Ung\xfcltiger String: muss "${c.includes}" enthalten`;if("regex"===c.format)return`Ung\xfcltiger String: muss dem Muster ${c.pattern} entsprechen`;return`Ung\xfcltig: ${b[c.format]??c.format}`;case"not_multiple_of":return`Ung\xfcltige Zahl: muss ein Vielfaches von ${c.divisor} sein`;case"unrecognized_keys":return`${c.keys.length>1?"Unbekannte Schlüssel":"Unbekannter Schlüssel"}: ${aw(c.keys,", ")}`;case"invalid_key":return`Ung\xfcltiger Schl\xfcssel in ${c.origin}`;case"invalid_union":return"Ungültige Eingabe";case"invalid_element":return`Ung\xfcltiger Wert in ${c.origin}`;default:return`Ung\xfcltige Eingabe`}}})()}}function e5(){return{localeError:(()=>{let a={string:{unit:"karaktrojn",verb:"havi"},file:{unit:"bajtojn",verb:"havi"},array:{unit:"elementojn",verb:"havi"},set:{unit:"elementojn",verb:"havi"}},b={regex:"enigo",email:"retadreso",url:"URL",emoji:"emoĝio",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datotempo",date:"ISO-dato",time:"ISO-tempo",duration:"ISO-daŭro",ipv4:"IPv4-adreso",ipv6:"IPv6-adreso",cidrv4:"IPv4-rango",cidrv6:"IPv6-rango",base64:"64-ume kodita karaktraro",base64url:"URL-64-ume kodita karaktraro",json_string:"JSON-karaktraro",e164:"E.164-nombro",jwt:"JWT",template_literal:"enigo"};return c=>{switch(c.code){case"invalid_type":return`Nevalida enigo: atendiĝis ${c.expected}, riceviĝis ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nombro";case"object":if(Array.isArray(a))return"tabelo";if(null===a)return"senvalora";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Nevalida enigo: atendiĝis ${aZ(c.values[0])}`;return`Nevalida opcio: atendiĝis unu el ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Tro granda: atendiĝis ke ${c.origin??"valoro"} havu ${b}${c.maximum.toString()} ${d.unit??"elementojn"}`;return`Tro granda: atendiĝis ke ${c.origin??"valoro"} havu ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Tro malgranda: atendiĝis ke ${c.origin} havu ${b}${c.minimum.toString()} ${d.unit}`;return`Tro malgranda: atendiĝis ke ${c.origin} estu ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Nevalida karaktraro: devas komenciĝi per "${c.prefix}"`;if("ends_with"===c.format)return`Nevalida karaktraro: devas finiĝi per "${c.suffix}"`;if("includes"===c.format)return`Nevalida karaktraro: devas inkluzivi "${c.includes}"`;if("regex"===c.format)return`Nevalida karaktraro: devas kongrui kun la modelo ${c.pattern}`;return`Nevalida ${b[c.format]??c.format}`;case"not_multiple_of":return`Nevalida nombro: devas esti oblo de ${c.divisor}`;case"unrecognized_keys":return`Nekonata${c.keys.length>1?"j":""} ŝlosilo${c.keys.length>1?"j":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`Nevalida ŝlosilo en ${c.origin}`;case"invalid_union":default:return"Nevalida enigo";case"invalid_element":return`Nevalida valoro en ${c.origin}`}}})()}}function e6(){return{localeError:(()=>{let a={string:{unit:"caracteres",verb:"tener"},file:{unit:"bytes",verb:"tener"},array:{unit:"elementos",verb:"tener"},set:{unit:"elementos",verb:"tener"}},b={regex:"entrada",email:"dirección de correo electrónico",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"fecha y hora ISO",date:"fecha ISO",time:"hora ISO",duration:"duración ISO",ipv4:"dirección IPv4",ipv6:"dirección IPv6",cidrv4:"rango IPv4",cidrv6:"rango IPv6",base64:"cadena codificada en base64",base64url:"URL codificada en base64",json_string:"cadena JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return c=>{switch(c.code){case"invalid_type":return`Entrada inv\xe1lida: se esperaba ${c.expected}, recibido ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"número";case"object":if(Array.isArray(a))return"arreglo";if(null===a)return"nulo";if(Object.getPrototypeOf(a)!==Object.prototype)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Entrada inv\xe1lida: se esperaba ${aZ(c.values[0])}`;return`Opci\xf3n inv\xe1lida: se esperaba una de ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Demasiado grande: se esperaba que ${c.origin??"valor"} tuviera ${b}${c.maximum.toString()} ${d.unit??"elementos"}`;return`Demasiado grande: se esperaba que ${c.origin??"valor"} fuera ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Demasiado peque\xf1o: se esperaba que ${c.origin} tuviera ${b}${c.minimum.toString()} ${d.unit}`;return`Demasiado peque\xf1o: se esperaba que ${c.origin} fuera ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Cadena inv\xe1lida: debe comenzar con "${c.prefix}"`;if("ends_with"===c.format)return`Cadena inv\xe1lida: debe terminar en "${c.suffix}"`;if("includes"===c.format)return`Cadena inv\xe1lida: debe incluir "${c.includes}"`;if("regex"===c.format)return`Cadena inv\xe1lida: debe coincidir con el patr\xf3n ${c.pattern}`;return`Inv\xe1lido ${b[c.format]??c.format}`;case"not_multiple_of":return`N\xfamero inv\xe1lido: debe ser m\xfaltiplo de ${c.divisor}`;case"unrecognized_keys":return`Llave${c.keys.length>1?"s":""} desconocida${c.keys.length>1?"s":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`Llave inv\xe1lida en ${c.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inv\xe1lido en ${c.origin}`;default:return`Entrada inv\xe1lida`}}})()}}function e7(){return{localeError:(()=>{let a={string:{unit:"کاراکتر",verb:"داشته باشد"},file:{unit:"بایت",verb:"داشته باشد"},array:{unit:"آیتم",verb:"داشته باشد"},set:{unit:"آیتم",verb:"داشته باشد"}},b={regex:"ورودی",email:"آدرس ایمیل",url:"URL",emoji:"ایموجی",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"تاریخ و زمان ایزو",date:"تاریخ ایزو",time:"زمان ایزو",duration:"مدت زمان ایزو",ipv4:"IPv4 آدرس",ipv6:"IPv6 آدرس",cidrv4:"IPv4 دامنه",cidrv6:"IPv6 دامنه",base64:"base64-encoded رشته",base64url:"base64url-encoded رشته",json_string:"JSON رشته",e164:"E.164 عدد",jwt:"JWT",template_literal:"ورودی"};return c=>{switch(c.code){case"invalid_type":return`ورودی نامعتبر: می‌بایست ${c.expected} می‌بود، ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"عدد";case"object":if(Array.isArray(a))return"آرایه";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} دریافت شد`;case"invalid_value":if(1===c.values.length)return`ورودی نامعتبر: می‌بایست ${aZ(c.values[0])} می‌بود`;return`گزینه نامعتبر: می‌بایست یکی از ${aw(c.values,"|")} می‌بود`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`خیلی بزرگ: ${c.origin??"مقدار"} باید ${b}${c.maximum.toString()} ${d.unit??"عنصر"} باشد`;return`خیلی بزرگ: ${c.origin??"مقدار"} باید ${b}${c.maximum.toString()} باشد`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`خیلی کوچک: ${c.origin} باید ${b}${c.minimum.toString()} ${d.unit} باشد`;return`خیلی کوچک: ${c.origin} باید ${b}${c.minimum.toString()} باشد`}case"invalid_format":if("starts_with"===c.format)return`رشته نامعتبر: باید با "${c.prefix}" شروع شود`;if("ends_with"===c.format)return`رشته نامعتبر: باید با "${c.suffix}" تمام شود`;if("includes"===c.format)return`رشته نامعتبر: باید شامل "${c.includes}" باشد`;if("regex"===c.format)return`رشته نامعتبر: باید با الگوی ${c.pattern} مطابقت داشته باشد`;return`${b[c.format]??c.format} نامعتبر`;case"not_multiple_of":return`عدد نامعتبر: باید مضرب ${c.divisor} باشد`;case"unrecognized_keys":return`کلید${c.keys.length>1?"های":""} ناشناس: ${aw(c.keys,", ")}`;case"invalid_key":return`کلید ناشناس در ${c.origin}`;case"invalid_union":default:return`ورودی نامعتبر`;case"invalid_element":return`مقدار نامعتبر در ${c.origin}`}}})()}}function e8(){return{localeError:(()=>{let a={string:{unit:"merkkiä",subject:"merkkijonon"},file:{unit:"tavua",subject:"tiedoston"},array:{unit:"alkiota",subject:"listan"},set:{unit:"alkiota",subject:"joukon"},number:{unit:"",subject:"luvun"},bigint:{unit:"",subject:"suuren kokonaisluvun"},int:{unit:"",subject:"kokonaisluvun"},date:{unit:"",subject:"päivämäärän"}},b={regex:"säännöllinen lauseke",email:"sähköpostiosoite",url:"URL-osoite",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-aikaleima",date:"ISO-päivämäärä",time:"ISO-aika",duration:"ISO-kesto",ipv4:"IPv4-osoite",ipv6:"IPv6-osoite",cidrv4:"IPv4-alue",cidrv6:"IPv6-alue",base64:"base64-koodattu merkkijono",base64url:"base64url-koodattu merkkijono",json_string:"JSON-merkkijono",e164:"E.164-luku",jwt:"JWT",template_literal:"templaattimerkkijono"};return c=>{switch(c.code){case"invalid_type":return`Virheellinen tyyppi: odotettiin ${c.expected}, oli ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Virheellinen sy\xf6te: t\xe4ytyy olla ${aZ(c.values[0])}`;return`Virheellinen valinta: t\xe4ytyy olla yksi seuraavista: ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Liian suuri: ${d.subject} t\xe4ytyy olla ${b}${c.maximum.toString()} ${d.unit}`.trim();return`Liian suuri: arvon t\xe4ytyy olla ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Liian pieni: ${d.subject} t\xe4ytyy olla ${b}${c.minimum.toString()} ${d.unit}`.trim();return`Liian pieni: arvon t\xe4ytyy olla ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy alkaa "${c.prefix}"`;if("ends_with"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy loppua "${c.suffix}"`;if("includes"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy sis\xe4lt\xe4\xe4 "${c.includes}"`;if("regex"===c.format)return`Virheellinen sy\xf6te: t\xe4ytyy vastata s\xe4\xe4nn\xf6llist\xe4 lauseketta ${c.pattern}`;return`Virheellinen ${b[c.format]??c.format}`;case"not_multiple_of":return`Virheellinen luku: t\xe4ytyy olla luvun ${c.divisor} monikerta`;case"unrecognized_keys":return`${c.keys.length>1?"Tuntemattomat avaimet":"Tuntematon avain"}: ${aw(c.keys,", ")}`;case"invalid_key":return"Virheellinen avain tietueessa";case"invalid_union":return"Virheellinen unioni";case"invalid_element":return"Virheellinen arvo joukossa";default:return`Virheellinen sy\xf6te`}}})()}}function e9(){return{localeError:(()=>{let a={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}},b={regex:"entrée",email:"adresse e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date et heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return c=>{switch(c.code){case"invalid_type":return`Entr\xe9e invalide : ${c.expected} attendu, ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nombre";case"object":if(Array.isArray(a))return"tableau";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} re\xe7u`;case"invalid_value":if(1===c.values.length)return`Entr\xe9e invalide : ${aZ(c.values[0])} attendu`;return`Option invalide : une valeur parmi ${aw(c.values,"|")} attendue`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Trop grand : ${c.origin??"valeur"} doit ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"élément(s)"}`;return`Trop grand : ${c.origin??"valeur"} doit \xeatre ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Trop petit : ${c.origin} doit ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Trop petit : ${c.origin} doit \xeatre ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Cha\xeene invalide : doit commencer par "${c.prefix}"`;if("ends_with"===c.format)return`Cha\xeene invalide : doit se terminer par "${c.suffix}"`;if("includes"===c.format)return`Cha\xeene invalide : doit inclure "${c.includes}"`;if("regex"===c.format)return`Cha\xeene invalide : doit correspondre au mod\xe8le ${c.pattern}`;return`${b[c.format]??c.format} invalide`;case"not_multiple_of":return`Nombre invalide : doit \xeatre un multiple de ${c.divisor}`;case"unrecognized_keys":return`Cl\xe9${c.keys.length>1?"s":""} non reconnue${c.keys.length>1?"s":""} : ${aw(c.keys,", ")}`;case"invalid_key":return`Cl\xe9 invalide dans ${c.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${c.origin}`;default:return`Entr\xe9e invalide`}}})()}}function fa(){return{localeError:(()=>{let a={string:{unit:"caractères",verb:"avoir"},file:{unit:"octets",verb:"avoir"},array:{unit:"éléments",verb:"avoir"},set:{unit:"éléments",verb:"avoir"}},b={regex:"entrée",email:"adresse courriel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"date-heure ISO",date:"date ISO",time:"heure ISO",duration:"durée ISO",ipv4:"adresse IPv4",ipv6:"adresse IPv6",cidrv4:"plage IPv4",cidrv6:"plage IPv6",base64:"chaîne encodée en base64",base64url:"chaîne encodée en base64url",json_string:"chaîne JSON",e164:"numéro E.164",jwt:"JWT",template_literal:"entrée"};return c=>{switch(c.code){case"invalid_type":return`Entr\xe9e invalide : attendu ${c.expected}, re\xe7u ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Entr\xe9e invalide : attendu ${aZ(c.values[0])}`;return`Option invalide : attendu l'une des valeurs suivantes ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"≤":"<",d=a[c.origin]??null;if(d)return`Trop grand : attendu que ${c.origin??"la valeur"} ait ${b}${c.maximum.toString()} ${d.unit}`;return`Trop grand : attendu que ${c.origin??"la valeur"} soit ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?"≥":">",d=a[c.origin]??null;if(d)return`Trop petit : attendu que ${c.origin} ait ${b}${c.minimum.toString()} ${d.unit}`;return`Trop petit : attendu que ${c.origin} soit ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Cha\xeene invalide : doit commencer par "${c.prefix}"`;if("ends_with"===c.format)return`Cha\xeene invalide : doit se terminer par "${c.suffix}"`;if("includes"===c.format)return`Cha\xeene invalide : doit inclure "${c.includes}"`;if("regex"===c.format)return`Cha\xeene invalide : doit correspondre au motif ${c.pattern}`;return`${b[c.format]??c.format} invalide`;case"not_multiple_of":return`Nombre invalide : doit \xeatre un multiple de ${c.divisor}`;case"unrecognized_keys":return`Cl\xe9${c.keys.length>1?"s":""} non reconnue${c.keys.length>1?"s":""} : ${aw(c.keys,", ")}`;case"invalid_key":return`Cl\xe9 invalide dans ${c.origin}`;case"invalid_union":return"Entrée invalide";case"invalid_element":return`Valeur invalide dans ${c.origin}`;default:return`Entr\xe9e invalide`}}})()}}function fb(){return{localeError:(()=>{let a={string:{unit:"אותיות",verb:"לכלול"},file:{unit:"בייטים",verb:"לכלול"},array:{unit:"פריטים",verb:"לכלול"},set:{unit:"פריטים",verb:"לכלול"}},b={regex:"קלט",email:"כתובת אימייל",url:"כתובת רשת",emoji:"אימוג'י",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"תאריך וזמן ISO",date:"תאריך ISO",time:"זמן ISO",duration:"משך זמן ISO",ipv4:"כתובת IPv4",ipv6:"כתובת IPv6",cidrv4:"טווח IPv4",cidrv6:"טווח IPv6",base64:"מחרוזת בבסיס 64",base64url:"מחרוזת בבסיס 64 לכתובות רשת",json_string:"מחרוזת JSON",e164:"מספר E.164",jwt:"JWT",template_literal:"קלט"};return c=>{switch(c.code){case"invalid_type":return`קלט לא תקין: צריך ${c.expected}, התקבל ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`קלט לא תקין: צריך ${aZ(c.values[0])}`;return`קלט לא תקין: צריך אחת מהאפשרויות  ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`גדול מדי: ${c.origin??"value"} צריך להיות ${b}${c.maximum.toString()} ${d.unit??"elements"}`;return`גדול מדי: ${c.origin??"value"} צריך להיות ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`קטן מדי: ${c.origin} צריך להיות ${b}${c.minimum.toString()} ${d.unit}`;return`קטן מדי: ${c.origin} צריך להיות ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`מחרוזת לא תקינה: חייבת להתחיל ב"${c.prefix}"`;if("ends_with"===c.format)return`מחרוזת לא תקינה: חייבת להסתיים ב "${c.suffix}"`;if("includes"===c.format)return`מחרוזת לא תקינה: חייבת לכלול "${c.includes}"`;if("regex"===c.format)return`מחרוזת לא תקינה: חייבת להתאים לתבנית ${c.pattern}`;return`${b[c.format]??c.format} לא תקין`;case"not_multiple_of":return`מספר לא תקין: חייב להיות מכפלה של ${c.divisor}`;case"unrecognized_keys":return`מפתח${c.keys.length>1?"ות":""} לא מזוה${c.keys.length>1?"ים":"ה"}: ${aw(c.keys,", ")}`;case"invalid_key":return`מפתח לא תקין ב${c.origin}`;case"invalid_union":return"קלט לא תקין";case"invalid_element":return`ערך לא תקין ב${c.origin}`;default:return`קלט לא תקין`}}})()}}function fc(){return{localeError:(()=>{let a={string:{unit:"karakter",verb:"legyen"},file:{unit:"byte",verb:"legyen"},array:{unit:"elem",verb:"legyen"},set:{unit:"elem",verb:"legyen"}},b={regex:"bemenet",email:"email cím",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO időbélyeg",date:"ISO dátum",time:"ISO idő",duration:"ISO időintervallum",ipv4:"IPv4 cím",ipv6:"IPv6 cím",cidrv4:"IPv4 tartomány",cidrv6:"IPv6 tartomány",base64:"base64-kódolt string",base64url:"base64url-kódolt string",json_string:"JSON string",e164:"E.164 szám",jwt:"JWT",template_literal:"bemenet"};return c=>{switch(c.code){case"invalid_type":return`\xc9rv\xe9nytelen bemenet: a v\xe1rt \xe9rt\xe9k ${c.expected}, a kapott \xe9rt\xe9k ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"szám";case"object":if(Array.isArray(a))return"tömb";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`\xc9rv\xe9nytelen bemenet: a v\xe1rt \xe9rt\xe9k ${aZ(c.values[0])}`;return`\xc9rv\xe9nytelen opci\xf3: valamelyik \xe9rt\xe9k v\xe1rt ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`T\xfal nagy: ${c.origin??"érték"} m\xe9rete t\xfal nagy ${b}${c.maximum.toString()} ${d.unit??"elem"}`;return`T\xfal nagy: a bemeneti \xe9rt\xe9k ${c.origin??"érték"} t\xfal nagy: ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`T\xfal kicsi: a bemeneti \xe9rt\xe9k ${c.origin} m\xe9rete t\xfal kicsi ${b}${c.minimum.toString()} ${d.unit}`;return`T\xfal kicsi: a bemeneti \xe9rt\xe9k ${c.origin} t\xfal kicsi ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`\xc9rv\xe9nytelen string: "${c.prefix}" \xe9rt\xe9kkel kell kezdődnie`;if("ends_with"===c.format)return`\xc9rv\xe9nytelen string: "${c.suffix}" \xe9rt\xe9kkel kell v\xe9gződnie`;if("includes"===c.format)return`\xc9rv\xe9nytelen string: "${c.includes}" \xe9rt\xe9ket kell tartalmaznia`;if("regex"===c.format)return`\xc9rv\xe9nytelen string: ${c.pattern} mint\xe1nak kell megfelelnie`;return`\xc9rv\xe9nytelen ${b[c.format]??c.format}`;case"not_multiple_of":return`\xc9rv\xe9nytelen sz\xe1m: ${c.divisor} t\xf6bbsz\xf6r\xf6s\xe9nek kell lennie`;case"unrecognized_keys":return`Ismeretlen kulcs${c.keys.length>1?"s":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`\xc9rv\xe9nytelen kulcs ${c.origin}`;case"invalid_union":return"Érvénytelen bemenet";case"invalid_element":return`\xc9rv\xe9nytelen \xe9rt\xe9k: ${c.origin}`;default:return`\xc9rv\xe9nytelen bemenet`}}})()}}function fd(){return{localeError:(()=>{let a={string:{unit:"karakter",verb:"memiliki"},file:{unit:"byte",verb:"memiliki"},array:{unit:"item",verb:"memiliki"},set:{unit:"item",verb:"memiliki"}},b={regex:"input",email:"alamat email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tanggal dan waktu format ISO",date:"tanggal format ISO",time:"jam format ISO",duration:"durasi format ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"rentang alamat IPv4",cidrv6:"rentang alamat IPv6",base64:"string dengan enkode base64",base64url:"string dengan enkode base64url",json_string:"string JSON",e164:"angka E.164",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Input tidak valid: diharapkan ${c.expected}, diterima ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Input tidak valid: diharapkan ${aZ(c.values[0])}`;return`Pilihan tidak valid: diharapkan salah satu dari ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Terlalu besar: diharapkan ${c.origin??"value"} memiliki ${b}${c.maximum.toString()} ${d.unit??"elemen"}`;return`Terlalu besar: diharapkan ${c.origin??"value"} menjadi ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Terlalu kecil: diharapkan ${c.origin} memiliki ${b}${c.minimum.toString()} ${d.unit}`;return`Terlalu kecil: diharapkan ${c.origin} menjadi ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`String tidak valid: harus dimulai dengan "${c.prefix}"`;if("ends_with"===c.format)return`String tidak valid: harus berakhir dengan "${c.suffix}"`;if("includes"===c.format)return`String tidak valid: harus menyertakan "${c.includes}"`;if("regex"===c.format)return`String tidak valid: harus sesuai pola ${c.pattern}`;return`${b[c.format]??c.format} tidak valid`;case"not_multiple_of":return`Angka tidak valid: harus kelipatan dari ${c.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali ${c.keys.length>1?"s":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`Kunci tidak valid di ${c.origin}`;case"invalid_union":default:return"Input tidak valid";case"invalid_element":return`Nilai tidak valid di ${c.origin}`}}})()}}function fe(){return{localeError:(()=>{let a={string:{unit:"stafi",verb:"að hafa"},file:{unit:"bæti",verb:"að hafa"},array:{unit:"hluti",verb:"að hafa"},set:{unit:"hluti",verb:"að hafa"}},b={regex:"gildi",email:"netfang",url:"vefslóð",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dagsetning og tími",date:"ISO dagsetning",time:"ISO tími",duration:"ISO tímalengd",ipv4:"IPv4 address",ipv6:"IPv6 address",cidrv4:"IPv4 range",cidrv6:"IPv6 range",base64:"base64-encoded strengur",base64url:"base64url-encoded strengur",json_string:"JSON strengur",e164:"E.164 tölugildi",jwt:"JWT",template_literal:"gildi"};return c=>{switch(c.code){case"invalid_type":return`Rangt gildi: \xde\xfa sl\xf3st inn ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"númer";case"object":if(Array.isArray(a))return"fylki";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} \xfear sem \xe1 a\xf0 vera ${c.expected}`;case"invalid_value":if(1===c.values.length)return`Rangt gildi: gert r\xe1\xf0 fyrir ${aZ(c.values[0])}`;return`\xd3gilt val: m\xe1 vera eitt af eftirfarandi ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Of st\xf3rt: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin??"gildi"} hafi ${b}${c.maximum.toString()} ${d.unit??"hluti"}`;return`Of st\xf3rt: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin??"gildi"} s\xe9 ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Of l\xedti\xf0: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin} hafi ${b}${c.minimum.toString()} ${d.unit}`;return`Of l\xedti\xf0: gert er r\xe1\xf0 fyrir a\xf0 ${c.origin} s\xe9 ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 byrja \xe1 "${c.prefix}"`;if("ends_with"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 enda \xe1 "${c.suffix}"`;if("includes"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 innihalda "${c.includes}"`;if("regex"===c.format)return`\xd3gildur strengur: ver\xf0ur a\xf0 fylgja mynstri ${c.pattern}`;return`Rangt ${b[c.format]??c.format}`;case"not_multiple_of":return`R\xf6ng tala: ver\xf0ur a\xf0 vera margfeldi af ${c.divisor}`;case"unrecognized_keys":return`\xd3\xfeekkt ${c.keys.length>1?"ir lyklar":"ur lykill"}: ${aw(c.keys,", ")}`;case"invalid_key":return`Rangur lykill \xed ${c.origin}`;case"invalid_union":default:return"Rangt gildi";case"invalid_element":return`Rangt gildi \xed ${c.origin}`}}})()}}function ff(){return{localeError:(()=>{let a={string:{unit:"caratteri",verb:"avere"},file:{unit:"byte",verb:"avere"},array:{unit:"elementi",verb:"avere"},set:{unit:"elementi",verb:"avere"}},b={regex:"input",email:"indirizzo email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e ora ISO",date:"data ISO",time:"ora ISO",duration:"durata ISO",ipv4:"indirizzo IPv4",ipv6:"indirizzo IPv6",cidrv4:"intervallo IPv4",cidrv6:"intervallo IPv6",base64:"stringa codificata in base64",base64url:"URL codificata in base64",json_string:"stringa JSON",e164:"numero E.164",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Input non valido: atteso ${c.expected}, ricevuto ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"numero";case"object":if(Array.isArray(a))return"vettore";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Input non valido: atteso ${aZ(c.values[0])}`;return`Opzione non valida: atteso uno tra ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Troppo grande: ${c.origin??"valore"} deve avere ${b}${c.maximum.toString()} ${d.unit??"elementi"}`;return`Troppo grande: ${c.origin??"valore"} deve essere ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Troppo piccolo: ${c.origin} deve avere ${b}${c.minimum.toString()} ${d.unit}`;return`Troppo piccolo: ${c.origin} deve essere ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Stringa non valida: deve iniziare con "${c.prefix}"`;if("ends_with"===c.format)return`Stringa non valida: deve terminare con "${c.suffix}"`;if("includes"===c.format)return`Stringa non valida: deve includere "${c.includes}"`;if("regex"===c.format)return`Stringa non valida: deve corrispondere al pattern ${c.pattern}`;return`Invalid ${b[c.format]??c.format}`;case"not_multiple_of":return`Numero non valido: deve essere un multiplo di ${c.divisor}`;case"unrecognized_keys":return`Chiav${c.keys.length>1?"i":"e"} non riconosciut${c.keys.length>1?"e":"a"}: ${aw(c.keys,", ")}`;case"invalid_key":return`Chiave non valida in ${c.origin}`;case"invalid_union":default:return"Input non valido";case"invalid_element":return`Valore non valido in ${c.origin}`}}})()}}function fg(){return{localeError:(()=>{let a={string:{unit:"文字",verb:"である"},file:{unit:"バイト",verb:"である"},array:{unit:"要素",verb:"である"},set:{unit:"要素",verb:"である"}},b={regex:"入力値",email:"メールアドレス",url:"URL",emoji:"絵文字",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日時",date:"ISO日付",time:"ISO時刻",duration:"ISO期間",ipv4:"IPv4アドレス",ipv6:"IPv6アドレス",cidrv4:"IPv4範囲",cidrv6:"IPv6範囲",base64:"base64エンコード文字列",base64url:"base64urlエンコード文字列",json_string:"JSON文字列",e164:"E.164番号",jwt:"JWT",template_literal:"入力値"};return c=>{switch(c.code){case"invalid_type":return`無効な入力: ${c.expected}が期待されましたが、${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"数値";case"object":if(Array.isArray(a))return"配列";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}が入力されました`;case"invalid_value":if(1===c.values.length)return`無効な入力: ${aZ(c.values[0])}が期待されました`;return`無効な選択: ${aw(c.values,"、")}のいずれかである必要があります`;case"too_big":{let b=c.inclusive?"以下である":"より小さい",d=a[c.origin]??null;if(d)return`大きすぎる値: ${c.origin??"値"}は${c.maximum.toString()}${d.unit??"要素"}${b}必要があります`;return`大きすぎる値: ${c.origin??"値"}は${c.maximum.toString()}${b}必要があります`}case"too_small":{let b=c.inclusive?"以上である":"より大きい",d=a[c.origin]??null;if(d)return`小さすぎる値: ${c.origin}は${c.minimum.toString()}${d.unit}${b}必要があります`;return`小さすぎる値: ${c.origin}は${c.minimum.toString()}${b}必要があります`}case"invalid_format":if("starts_with"===c.format)return`無効な文字列: "${c.prefix}"で始まる必要があります`;if("ends_with"===c.format)return`無効な文字列: "${c.suffix}"で終わる必要があります`;if("includes"===c.format)return`無効な文字列: "${c.includes}"を含む必要があります`;if("regex"===c.format)return`無効な文字列: パターン${c.pattern}に一致する必要があります`;return`無効な${b[c.format]??c.format}`;case"not_multiple_of":return`無効な数値: ${c.divisor}の倍数である必要があります`;case"unrecognized_keys":return`認識されていないキー${c.keys.length>1?"群":""}: ${aw(c.keys,"、")}`;case"invalid_key":return`${c.origin}内の無効なキー`;case"invalid_union":return"無効な入力";case"invalid_element":return`${c.origin}内の無効な値`;default:return`無効な入力`}}})()}}function fh(){return{localeError:(()=>{let a={string:{unit:"តួអក្សរ",verb:"គួរមាន"},file:{unit:"បៃ",verb:"គួរមាន"},array:{unit:"ធាតុ",verb:"គួរមាន"},set:{unit:"ធាតុ",verb:"គួរមាន"}},b={regex:"ទិន្នន័យបញ្ចូល",email:"អាសយដ្ឋានអ៊ីមែល",url:"URL",emoji:"សញ្ញាអារម្មណ៍",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"កាលបរិច្ឆេទ និងម៉ោង ISO",date:"កាលបរិច្ឆេទ ISO",time:"ម៉ោង ISO",duration:"រយៈពេល ISO",ipv4:"អាសយដ្ឋាន IPv4",ipv6:"អាសយដ្ឋាន IPv6",cidrv4:"ដែនអាសយដ្ឋាន IPv4",cidrv6:"ដែនអាសយដ្ឋាន IPv6",base64:"ខ្សែអក្សរអ៊ិកូដ base64",base64url:"ខ្សែអក្សរអ៊ិកូដ base64url",json_string:"ខ្សែអក្សរ JSON",e164:"លេខ E.164",jwt:"JWT",template_literal:"ទិន្នន័យបញ្ចូល"};return c=>{switch(c.code){case"invalid_type":return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${c.expected} ប៉ុន្តែទទួលបាន ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"មិនមែនជាលេខ (NaN)":"លេខ";case"object":if(Array.isArray(a))return"អារេ (Array)";if(null===a)return"គ្មានតម្លៃ (null)";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`ទិន្នន័យបញ្ចូលមិនត្រឹមត្រូវ៖ ត្រូវការ ${aZ(c.values[0])}`;return`ជម្រើសមិនត្រឹមត្រូវ៖ ត្រូវជាមួយក្នុងចំណោម ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`ធំពេក៖ ត្រូវការ ${c.origin??"តម្លៃ"} ${b} ${c.maximum.toString()} ${d.unit??"ធាតុ"}`;return`ធំពេក៖ ត្រូវការ ${c.origin??"តម្លៃ"} ${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`តូចពេក៖ ត្រូវការ ${c.origin} ${b} ${c.minimum.toString()} ${d.unit}`;return`តូចពេក៖ ត្រូវការ ${c.origin} ${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវចាប់ផ្តើមដោយ "${c.prefix}"`;if("ends_with"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវបញ្ចប់ដោយ "${c.suffix}"`;if("includes"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវមាន "${c.includes}"`;if("regex"===c.format)return`ខ្សែអក្សរមិនត្រឹមត្រូវ៖ ត្រូវតែផ្គូផ្គងនឹងទម្រង់ដែលបានកំណត់ ${c.pattern}`;return`មិនត្រឹមត្រូវ៖ ${b[c.format]??c.format}`;case"not_multiple_of":return`លេខមិនត្រឹមត្រូវ៖ ត្រូវតែជាពហុគុណនៃ ${c.divisor}`;case"unrecognized_keys":return`រកឃើញសោមិនស្គាល់៖ ${aw(c.keys,", ")}`;case"invalid_key":return`សោមិនត្រឹមត្រូវនៅក្នុង ${c.origin}`;case"invalid_union":default:return`ទិន្នន័យមិនត្រឹមត្រូវ`;case"invalid_element":return`ទិន្នន័យមិនត្រឹមត្រូវនៅក្នុង ${c.origin}`}}})()}}function fi(){return{localeError:(()=>{let a={string:{unit:"문자",verb:"to have"},file:{unit:"바이트",verb:"to have"},array:{unit:"개",verb:"to have"},set:{unit:"개",verb:"to have"}},b={regex:"입력",email:"이메일 주소",url:"URL",emoji:"이모지",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 날짜시간",date:"ISO 날짜",time:"ISO 시간",duration:"ISO 기간",ipv4:"IPv4 주소",ipv6:"IPv6 주소",cidrv4:"IPv4 범위",cidrv6:"IPv6 범위",base64:"base64 인코딩 문자열",base64url:"base64url 인코딩 문자열",json_string:"JSON 문자열",e164:"E.164 번호",jwt:"JWT",template_literal:"입력"};return c=>{switch(c.code){case"invalid_type":return`잘못된 입력: 예상 타입은 ${c.expected}, 받은 타입은 ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}입니다`;case"invalid_value":if(1===c.values.length)return`잘못된 입력: 값은 ${aZ(c.values[0])} 이어야 합니다`;return`잘못된 옵션: ${aw(c.values,"또는 ")} 중 하나여야 합니다`;case"too_big":{let b=c.inclusive?"이하":"미만",d="미만"===b?"이어야 합니다":"여야 합니다",e=a[c.origin]??null,f=e?.unit??"요소";if(e)return`${c.origin??"값"}이 너무 큽니다: ${c.maximum.toString()}${f} ${b}${d}`;return`${c.origin??"값"}이 너무 큽니다: ${c.maximum.toString()} ${b}${d}`}case"too_small":{let b=c.inclusive?"이상":"초과",d="이상"===b?"이어야 합니다":"여야 합니다",e=a[c.origin]??null,f=e?.unit??"요소";if(e)return`${c.origin??"값"}이 너무 작습니다: ${c.minimum.toString()}${f} ${b}${d}`;return`${c.origin??"값"}이 너무 작습니다: ${c.minimum.toString()} ${b}${d}`}case"invalid_format":if("starts_with"===c.format)return`잘못된 문자열: "${c.prefix}"(으)로 시작해야 합니다`;if("ends_with"===c.format)return`잘못된 문자열: "${c.suffix}"(으)로 끝나야 합니다`;if("includes"===c.format)return`잘못된 문자열: "${c.includes}"을(를) 포함해야 합니다`;if("regex"===c.format)return`잘못된 문자열: 정규식 ${c.pattern} 패턴과 일치해야 합니다`;return`잘못된 ${b[c.format]??c.format}`;case"not_multiple_of":return`잘못된 숫자: ${c.divisor}의 배수여야 합니다`;case"unrecognized_keys":return`인식할 수 없는 키: ${aw(c.keys,", ")}`;case"invalid_key":return`잘못된 키: ${c.origin}`;case"invalid_union":default:return`잘못된 입력`;case"invalid_element":return`잘못된 값: ${c.origin}`}}})()}}function fj(){return{localeError:(()=>{let a={string:{unit:"знаци",verb:"да имаат"},file:{unit:"бајти",verb:"да имаат"},array:{unit:"ставки",verb:"да имаат"},set:{unit:"ставки",verb:"да имаат"}},b={regex:"внес",email:"адреса на е-пошта",url:"URL",emoji:"емоџи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO датум и време",date:"ISO датум",time:"ISO време",duration:"ISO времетраење",ipv4:"IPv4 адреса",ipv6:"IPv6 адреса",cidrv4:"IPv4 опсег",cidrv6:"IPv6 опсег",base64:"base64-енкодирана низа",base64url:"base64url-енкодирана низа",json_string:"JSON низа",e164:"E.164 број",jwt:"JWT",template_literal:"внес"};return c=>{switch(c.code){case"invalid_type":return`Грешен внес: се очекува ${c.expected}, примено ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"број";case"object":if(Array.isArray(a))return"низа";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Invalid input: expected ${aZ(c.values[0])}`;return`Грешана опција: се очекува една ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Премногу голем: се очекува ${c.origin??"вредноста"} да има ${b}${c.maximum.toString()} ${d.unit??"елементи"}`;return`Премногу голем: се очекува ${c.origin??"вредноста"} да биде ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Премногу мал: се очекува ${c.origin} да има ${b}${c.minimum.toString()} ${d.unit}`;return`Премногу мал: се очекува ${c.origin} да биде ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Неважечка низа: мора да започнува со "${c.prefix}"`;if("ends_with"===c.format)return`Неважечка низа: мора да завршува со "${c.suffix}"`;if("includes"===c.format)return`Неважечка низа: мора да вклучува "${c.includes}"`;if("regex"===c.format)return`Неважечка низа: мора да одгоара на патернот ${c.pattern}`;return`Invalid ${b[c.format]??c.format}`;case"not_multiple_of":return`Грешен број: мора да биде делив со ${c.divisor}`;case"unrecognized_keys":return`${c.keys.length>1?"Непрепознаени клучеви":"Непрепознаен клуч"}: ${aw(c.keys,", ")}`;case"invalid_key":return`Грешен клуч во ${c.origin}`;case"invalid_union":return"Грешен внес";case"invalid_element":return`Грешна вредност во ${c.origin}`;default:return`Грешен внес`}}})()}}function fk(){return{localeError:(()=>{let a={string:{unit:"aksara",verb:"mempunyai"},file:{unit:"bait",verb:"mempunyai"},array:{unit:"elemen",verb:"mempunyai"},set:{unit:"elemen",verb:"mempunyai"}},b={regex:"input",email:"alamat e-mel",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"tarikh masa ISO",date:"tarikh ISO",time:"masa ISO",duration:"tempoh ISO",ipv4:"alamat IPv4",ipv6:"alamat IPv6",cidrv4:"julat IPv4",cidrv6:"julat IPv6",base64:"string dikodkan base64",base64url:"string dikodkan base64url",json_string:"string JSON",e164:"nombor E.164",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Input tidak sah: dijangka ${c.expected}, diterima ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nombor";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Input tidak sah: dijangka ${aZ(c.values[0])}`;return`Pilihan tidak sah: dijangka salah satu daripada ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Terlalu besar: dijangka ${c.origin??"nilai"} ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"elemen"}`;return`Terlalu besar: dijangka ${c.origin??"nilai"} adalah ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Terlalu kecil: dijangka ${c.origin} ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Terlalu kecil: dijangka ${c.origin} adalah ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`String tidak sah: mesti bermula dengan "${c.prefix}"`;if("ends_with"===c.format)return`String tidak sah: mesti berakhir dengan "${c.suffix}"`;if("includes"===c.format)return`String tidak sah: mesti mengandungi "${c.includes}"`;if("regex"===c.format)return`String tidak sah: mesti sepadan dengan corak ${c.pattern}`;return`${b[c.format]??c.format} tidak sah`;case"not_multiple_of":return`Nombor tidak sah: perlu gandaan ${c.divisor}`;case"unrecognized_keys":return`Kunci tidak dikenali: ${aw(c.keys,", ")}`;case"invalid_key":return`Kunci tidak sah dalam ${c.origin}`;case"invalid_union":default:return"Input tidak sah";case"invalid_element":return`Nilai tidak sah dalam ${c.origin}`}}})()}}function fl(){return{localeError:(()=>{let a={string:{unit:"tekens"},file:{unit:"bytes"},array:{unit:"elementen"},set:{unit:"elementen"}},b={regex:"invoer",email:"emailadres",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum en tijd",date:"ISO datum",time:"ISO tijd",duration:"ISO duur",ipv4:"IPv4-adres",ipv6:"IPv6-adres",cidrv4:"IPv4-bereik",cidrv6:"IPv6-bereik",base64:"base64-gecodeerde tekst",base64url:"base64 URL-gecodeerde tekst",json_string:"JSON string",e164:"E.164-nummer",jwt:"JWT",template_literal:"invoer"};return c=>{switch(c.code){case"invalid_type":return`Ongeldige invoer: verwacht ${c.expected}, ontving ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"getal";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ongeldige invoer: verwacht ${aZ(c.values[0])}`;return`Ongeldige optie: verwacht \xe9\xe9n van ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Te lang: verwacht dat ${c.origin??"waarde"} ${b}${c.maximum.toString()} ${d.unit??"elementen"} bevat`;return`Te lang: verwacht dat ${c.origin??"waarde"} ${b}${c.maximum.toString()} is`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Te kort: verwacht dat ${c.origin} ${b}${c.minimum.toString()} ${d.unit} bevat`;return`Te kort: verwacht dat ${c.origin} ${b}${c.minimum.toString()} is`}case"invalid_format":if("starts_with"===c.format)return`Ongeldige tekst: moet met "${c.prefix}" beginnen`;if("ends_with"===c.format)return`Ongeldige tekst: moet op "${c.suffix}" eindigen`;if("includes"===c.format)return`Ongeldige tekst: moet "${c.includes}" bevatten`;if("regex"===c.format)return`Ongeldige tekst: moet overeenkomen met patroon ${c.pattern}`;return`Ongeldig: ${b[c.format]??c.format}`;case"not_multiple_of":return`Ongeldig getal: moet een veelvoud van ${c.divisor} zijn`;case"unrecognized_keys":return`Onbekende key${c.keys.length>1?"s":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`Ongeldige key in ${c.origin}`;case"invalid_union":default:return"Ongeldige invoer";case"invalid_element":return`Ongeldige waarde in ${c.origin}`}}})()}}function fm(){return{localeError:(()=>{let a={string:{unit:"tegn",verb:"å ha"},file:{unit:"bytes",verb:"å ha"},array:{unit:"elementer",verb:"å inneholde"},set:{unit:"elementer",verb:"å inneholde"}},b={regex:"input",email:"e-postadresse",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO dato- og klokkeslett",date:"ISO-dato",time:"ISO-klokkeslett",duration:"ISO-varighet",ipv4:"IPv4-område",ipv6:"IPv6-område",cidrv4:"IPv4-spekter",cidrv6:"IPv6-spekter",base64:"base64-enkodet streng",base64url:"base64url-enkodet streng",json_string:"JSON-streng",e164:"E.164-nummer",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`Ugyldig input: forventet ${c.expected}, fikk ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"tall";case"object":if(Array.isArray(a))return"liste";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ugyldig verdi: forventet ${aZ(c.values[0])}`;return`Ugyldig valg: forventet en av ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`For stor(t): forventet ${c.origin??"value"} til \xe5 ha ${b}${c.maximum.toString()} ${d.unit??"elementer"}`;return`For stor(t): forventet ${c.origin??"value"} til \xe5 ha ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`For lite(n): forventet ${c.origin} til \xe5 ha ${b}${c.minimum.toString()} ${d.unit}`;return`For lite(n): forventet ${c.origin} til \xe5 ha ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Ugyldig streng: m\xe5 starte med "${c.prefix}"`;if("ends_with"===c.format)return`Ugyldig streng: m\xe5 ende med "${c.suffix}"`;if("includes"===c.format)return`Ugyldig streng: m\xe5 inneholde "${c.includes}"`;if("regex"===c.format)return`Ugyldig streng: m\xe5 matche m\xf8nsteret ${c.pattern}`;return`Ugyldig ${b[c.format]??c.format}`;case"not_multiple_of":return`Ugyldig tall: m\xe5 v\xe6re et multiplum av ${c.divisor}`;case"unrecognized_keys":return`${c.keys.length>1?"Ukjente nøkler":"Ukjent nøkkel"}: ${aw(c.keys,", ")}`;case"invalid_key":return`Ugyldig n\xf8kkel i ${c.origin}`;case"invalid_union":default:return"Ugyldig input";case"invalid_element":return`Ugyldig verdi i ${c.origin}`}}})()}}function fn(){return{localeError:(()=>{let a={string:{unit:"harf",verb:"olmalıdır"},file:{unit:"bayt",verb:"olmalıdır"},array:{unit:"unsur",verb:"olmalıdır"},set:{unit:"unsur",verb:"olmalıdır"}},b={regex:"giren",email:"epostagâh",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO hengâmı",date:"ISO tarihi",time:"ISO zamanı",duration:"ISO müddeti",ipv4:"IPv4 nişânı",ipv6:"IPv6 nişânı",cidrv4:"IPv4 menzili",cidrv6:"IPv6 menzili",base64:"base64-şifreli metin",base64url:"base64url-şifreli metin",json_string:"JSON metin",e164:"E.164 sayısı",jwt:"JWT",template_literal:"giren"};return c=>{switch(c.code){case"invalid_type":return`F\xe2sit giren: umulan ${c.expected}, alınan ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"numara";case"object":if(Array.isArray(a))return"saf";if(null===a)return"gayb";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`F\xe2sit giren: umulan ${aZ(c.values[0])}`;return`F\xe2sit tercih: m\xfbteberler ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Fazla b\xfcy\xfck: ${c.origin??"value"}, ${b}${c.maximum.toString()} ${d.unit??"elements"} sahip olmalıydı.`;return`Fazla b\xfcy\xfck: ${c.origin??"value"}, ${b}${c.maximum.toString()} olmalıydı.`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Fazla k\xfc\xe7\xfck: ${c.origin}, ${b}${c.minimum.toString()} ${d.unit} sahip olmalıydı.`;return`Fazla k\xfc\xe7\xfck: ${c.origin}, ${b}${c.minimum.toString()} olmalıydı.`}case"invalid_format":if("starts_with"===c.format)return`F\xe2sit metin: "${c.prefix}" ile başlamalı.`;if("ends_with"===c.format)return`F\xe2sit metin: "${c.suffix}" ile bitmeli.`;if("includes"===c.format)return`F\xe2sit metin: "${c.includes}" ihtiv\xe2 etmeli.`;if("regex"===c.format)return`F\xe2sit metin: ${c.pattern} nakşına uymalı.`;return`F\xe2sit ${b[c.format]??c.format}`;case"not_multiple_of":return`F\xe2sit sayı: ${c.divisor} katı olmalıydı.`;case"unrecognized_keys":return`Tanınmayan anahtar ${c.keys.length>1?"s":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`${c.origin} i\xe7in tanınmayan anahtar var.`;case"invalid_union":return"Giren tanınamadı.";case"invalid_element":return`${c.origin} i\xe7in tanınmayan kıymet var.`;default:return`Kıymet tanınamadı.`}}})()}}function fo(){return{localeError:(()=>{let a={string:{unit:"توکي",verb:"ولري"},file:{unit:"بایټس",verb:"ولري"},array:{unit:"توکي",verb:"ولري"},set:{unit:"توکي",verb:"ولري"}},b={regex:"ورودي",email:"بریښنالیک",url:"یو آر ال",emoji:"ایموجي",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"نیټه او وخت",date:"نېټه",time:"وخت",duration:"موده",ipv4:"د IPv4 پته",ipv6:"د IPv6 پته",cidrv4:"د IPv4 ساحه",cidrv6:"د IPv6 ساحه",base64:"base64-encoded متن",base64url:"base64url-encoded متن",json_string:"JSON متن",e164:"د E.164 شمېره",jwt:"JWT",template_literal:"ورودي"};return c=>{switch(c.code){case"invalid_type":return`ناسم ورودي: باید ${c.expected} وای, مګر ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"عدد";case"object":if(Array.isArray(a))return"ارې";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} ترلاسه شو`;case"invalid_value":if(1===c.values.length)return`ناسم ورودي: باید ${aZ(c.values[0])} وای`;return`ناسم انتخاب: باید یو له ${aw(c.values,"|")} څخه وای`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`ډیر لوی: ${c.origin??"ارزښت"} باید ${b}${c.maximum.toString()} ${d.unit??"عنصرونه"} ولري`;return`ډیر لوی: ${c.origin??"ارزښت"} باید ${b}${c.maximum.toString()} وي`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`ډیر کوچنی: ${c.origin} باید ${b}${c.minimum.toString()} ${d.unit} ولري`;return`ډیر کوچنی: ${c.origin} باید ${b}${c.minimum.toString()} وي`}case"invalid_format":if("starts_with"===c.format)return`ناسم متن: باید د "${c.prefix}" سره پیل شي`;if("ends_with"===c.format)return`ناسم متن: باید د "${c.suffix}" سره پای ته ورسيږي`;if("includes"===c.format)return`ناسم متن: باید "${c.includes}" ولري`;if("regex"===c.format)return`ناسم متن: باید د ${c.pattern} سره مطابقت ولري`;return`${b[c.format]??c.format} ناسم دی`;case"not_multiple_of":return`ناسم عدد: باید د ${c.divisor} مضرب وي`;case"unrecognized_keys":return`ناسم ${c.keys.length>1?"کلیډونه":"کلیډ"}: ${aw(c.keys,", ")}`;case"invalid_key":return`ناسم کلیډ په ${c.origin} کې`;case"invalid_union":default:return`ناسمه ورودي`;case"invalid_element":return`ناسم عنصر په ${c.origin} کې`}}})()}}function fp(){return{localeError:(()=>{let a={string:{unit:"znaków",verb:"mieć"},file:{unit:"bajtów",verb:"mieć"},array:{unit:"elementów",verb:"mieć"},set:{unit:"elementów",verb:"mieć"}},b={regex:"wyrażenie",email:"adres email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data i godzina w formacie ISO",date:"data w formacie ISO",time:"godzina w formacie ISO",duration:"czas trwania ISO",ipv4:"adres IPv4",ipv6:"adres IPv6",cidrv4:"zakres IPv4",cidrv6:"zakres IPv6",base64:"ciąg znaków zakodowany w formacie base64",base64url:"ciąg znaków zakodowany w formacie base64url",json_string:"ciąg znaków w formacie JSON",e164:"liczba E.164",jwt:"JWT",template_literal:"wejście"};return c=>{switch(c.code){case"invalid_type":return`Nieprawidłowe dane wejściowe: oczekiwano ${c.expected}, otrzymano ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"liczba";case"object":if(Array.isArray(a))return"tablica";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Nieprawidłowe dane wejściowe: oczekiwano ${aZ(c.values[0])}`;return`Nieprawidłowa opcja: oczekiwano jednej z wartości ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Za duża wartość: oczekiwano, że ${c.origin??"wartość"} będzie mieć ${b}${c.maximum.toString()} ${d.unit??"elementów"}`;return`Zbyt duż(y/a/e): oczekiwano, że ${c.origin??"wartość"} będzie wynosić ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Za mała wartość: oczekiwano, że ${c.origin??"wartość"} będzie mieć ${b}${c.minimum.toString()} ${d.unit??"elementów"}`;return`Zbyt mał(y/a/e): oczekiwano, że ${c.origin??"wartość"} będzie wynosić ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi zaczynać się od "${c.prefix}"`;if("ends_with"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi kończyć się na "${c.suffix}"`;if("includes"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi zawierać "${c.includes}"`;if("regex"===c.format)return`Nieprawidłowy ciąg znak\xf3w: musi odpowiadać wzorcowi ${c.pattern}`;return`Nieprawidłow(y/a/e) ${b[c.format]??c.format}`;case"not_multiple_of":return`Nieprawidłowa liczba: musi być wielokrotnością ${c.divisor}`;case"unrecognized_keys":return`Nierozpoznane klucze${c.keys.length>1?"s":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`Nieprawidłowy klucz w ${c.origin}`;case"invalid_union":return"Nieprawidłowe dane wejściowe";case"invalid_element":return`Nieprawidłowa wartość w ${c.origin}`;default:return`Nieprawidłowe dane wejściowe`}}})()}}function fq(){return{localeError:(()=>{let a={string:{unit:"caracteres",verb:"ter"},file:{unit:"bytes",verb:"ter"},array:{unit:"itens",verb:"ter"},set:{unit:"itens",verb:"ter"}},b={regex:"padrão",email:"endereço de e-mail",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"data e hora ISO",date:"data ISO",time:"hora ISO",duration:"duração ISO",ipv4:"endereço IPv4",ipv6:"endereço IPv6",cidrv4:"faixa de IPv4",cidrv6:"faixa de IPv6",base64:"texto codificado em base64",base64url:"URL codificada em base64",json_string:"texto JSON",e164:"número E.164",jwt:"JWT",template_literal:"entrada"};return c=>{switch(c.code){case"invalid_type":return`Tipo inv\xe1lido: esperado ${c.expected}, recebido ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"número";case"object":if(Array.isArray(a))return"array";if(null===a)return"nulo";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Entrada inv\xe1lida: esperado ${aZ(c.values[0])}`;return`Op\xe7\xe3o inv\xe1lida: esperada uma das ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Muito grande: esperado que ${c.origin??"valor"} tivesse ${b}${c.maximum.toString()} ${d.unit??"elementos"}`;return`Muito grande: esperado que ${c.origin??"valor"} fosse ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Muito pequeno: esperado que ${c.origin} tivesse ${b}${c.minimum.toString()} ${d.unit}`;return`Muito pequeno: esperado que ${c.origin} fosse ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Texto inv\xe1lido: deve come\xe7ar com "${c.prefix}"`;if("ends_with"===c.format)return`Texto inv\xe1lido: deve terminar com "${c.suffix}"`;if("includes"===c.format)return`Texto inv\xe1lido: deve incluir "${c.includes}"`;if("regex"===c.format)return`Texto inv\xe1lido: deve corresponder ao padr\xe3o ${c.pattern}`;return`${b[c.format]??c.format} inv\xe1lido`;case"not_multiple_of":return`N\xfamero inv\xe1lido: deve ser m\xfaltiplo de ${c.divisor}`;case"unrecognized_keys":return`Chave${c.keys.length>1?"s":""} desconhecida${c.keys.length>1?"s":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`Chave inv\xe1lida em ${c.origin}`;case"invalid_union":return"Entrada inválida";case"invalid_element":return`Valor inv\xe1lido em ${c.origin}`;default:return`Campo inv\xe1lido`}}})()}}function fr(a,b,c,d){let e=Math.abs(a),f=e%10,g=e%100;return g>=11&&g<=19?d:1===f?b:f>=2&&f<=4?c:d}function fs(){return{localeError:(()=>{let a={string:{unit:{one:"символ",few:"символа",many:"символов"},verb:"иметь"},file:{unit:{one:"байт",few:"байта",many:"байт"},verb:"иметь"},array:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"},set:{unit:{one:"элемент",few:"элемента",many:"элементов"},verb:"иметь"}},b={regex:"ввод",email:"email адрес",url:"URL",emoji:"эмодзи",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO дата и время",date:"ISO дата",time:"ISO время",duration:"ISO длительность",ipv4:"IPv4 адрес",ipv6:"IPv6 адрес",cidrv4:"IPv4 диапазон",cidrv6:"IPv6 диапазон",base64:"строка в формате base64",base64url:"строка в формате base64url",json_string:"JSON строка",e164:"номер E.164",jwt:"JWT",template_literal:"ввод"};return c=>{switch(c.code){case"invalid_type":return`Неверный ввод: ожидалось ${c.expected}, получено ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"число";case"object":if(Array.isArray(a))return"массив";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Неверный ввод: ожидалось ${aZ(c.values[0])}`;return`Неверный вариант: ожидалось одно из ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d){let a=fr(Number(c.maximum),d.unit.one,d.unit.few,d.unit.many);return`Слишком большое значение: ожидалось, что ${c.origin??"значение"} будет иметь ${b}${c.maximum.toString()} ${a}`}return`Слишком большое значение: ожидалось, что ${c.origin??"значение"} будет ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d){let a=fr(Number(c.minimum),d.unit.one,d.unit.few,d.unit.many);return`Слишком маленькое значение: ожидалось, что ${c.origin} будет иметь ${b}${c.minimum.toString()} ${a}`}return`Слишком маленькое значение: ожидалось, что ${c.origin} будет ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Неверная строка: должна начинаться с "${c.prefix}"`;if("ends_with"===c.format)return`Неверная строка: должна заканчиваться на "${c.suffix}"`;if("includes"===c.format)return`Неверная строка: должна содержать "${c.includes}"`;if("regex"===c.format)return`Неверная строка: должна соответствовать шаблону ${c.pattern}`;return`Неверный ${b[c.format]??c.format}`;case"not_multiple_of":return`Неверное число: должно быть кратным ${c.divisor}`;case"unrecognized_keys":return`Нераспознанн${c.keys.length>1?"ые":"ый"} ключ${c.keys.length>1?"и":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`Неверный ключ в ${c.origin}`;case"invalid_union":return"Неверные входные данные";case"invalid_element":return`Неверное значение в ${c.origin}`;default:return`Неверные входные данные`}}})()}}function ft(){return{localeError:(()=>{let a={string:{unit:"znakov",verb:"imeti"},file:{unit:"bajtov",verb:"imeti"},array:{unit:"elementov",verb:"imeti"},set:{unit:"elementov",verb:"imeti"}},b={regex:"vnos",email:"e-poštni naslov",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO datum in čas",date:"ISO datum",time:"ISO čas",duration:"ISO trajanje",ipv4:"IPv4 naslov",ipv6:"IPv6 naslov",cidrv4:"obseg IPv4",cidrv6:"obseg IPv6",base64:"base64 kodiran niz",base64url:"base64url kodiran niz",json_string:"JSON niz",e164:"E.164 številka",jwt:"JWT",template_literal:"vnos"};return c=>{switch(c.code){case"invalid_type":return`Neveljaven vnos: pričakovano ${c.expected}, prejeto ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"število";case"object":if(Array.isArray(a))return"tabela";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Neveljaven vnos: pričakovano ${aZ(c.values[0])}`;return`Neveljavna možnost: pričakovano eno izmed ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Preveliko: pričakovano, da bo ${c.origin??"vrednost"} imelo ${b}${c.maximum.toString()} ${d.unit??"elementov"}`;return`Preveliko: pričakovano, da bo ${c.origin??"vrednost"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Premajhno: pričakovano, da bo ${c.origin} imelo ${b}${c.minimum.toString()} ${d.unit}`;return`Premajhno: pričakovano, da bo ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Neveljaven niz: mora se začeti z "${c.prefix}"`;if("ends_with"===c.format)return`Neveljaven niz: mora se končati z "${c.suffix}"`;if("includes"===c.format)return`Neveljaven niz: mora vsebovati "${c.includes}"`;if("regex"===c.format)return`Neveljaven niz: mora ustrezati vzorcu ${c.pattern}`;return`Neveljaven ${b[c.format]??c.format}`;case"not_multiple_of":return`Neveljavno število: mora biti večkratnik ${c.divisor}`;case"unrecognized_keys":return`Neprepoznan${c.keys.length>1?"i ključi":" ključ"}: ${aw(c.keys,", ")}`;case"invalid_key":return`Neveljaven ključ v ${c.origin}`;case"invalid_union":default:return"Neveljaven vnos";case"invalid_element":return`Neveljavna vrednost v ${c.origin}`}}})()}}function fu(){return{localeError:(()=>{let a={string:{unit:"tecken",verb:"att ha"},file:{unit:"bytes",verb:"att ha"},array:{unit:"objekt",verb:"att innehålla"},set:{unit:"objekt",verb:"att innehålla"}},b={regex:"reguljärt uttryck",email:"e-postadress",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO-datum och tid",date:"ISO-datum",time:"ISO-tid",duration:"ISO-varaktighet",ipv4:"IPv4-intervall",ipv6:"IPv6-intervall",cidrv4:"IPv4-spektrum",cidrv6:"IPv6-spektrum",base64:"base64-kodad sträng",base64url:"base64url-kodad sträng",json_string:"JSON-sträng",e164:"E.164-nummer",jwt:"JWT",template_literal:"mall-literal"};return c=>{switch(c.code){case"invalid_type":return`Ogiltig inmatning: f\xf6rv\xe4ntat ${c.expected}, fick ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"antal";case"object":if(Array.isArray(a))return"lista";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ogiltig inmatning: f\xf6rv\xe4ntat ${aZ(c.values[0])}`;return`Ogiltigt val: f\xf6rv\xe4ntade en av ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`F\xf6r stor(t): f\xf6rv\xe4ntade ${c.origin??"värdet"} att ha ${b}${c.maximum.toString()} ${d.unit??"element"}`;return`F\xf6r stor(t): f\xf6rv\xe4ntat ${c.origin??"värdet"} att ha ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`F\xf6r lite(t): f\xf6rv\xe4ntade ${c.origin??"värdet"} att ha ${b}${c.minimum.toString()} ${d.unit}`;return`F\xf6r lite(t): f\xf6rv\xe4ntade ${c.origin??"värdet"} att ha ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Ogiltig str\xe4ng: m\xe5ste b\xf6rja med "${c.prefix}"`;if("ends_with"===c.format)return`Ogiltig str\xe4ng: m\xe5ste sluta med "${c.suffix}"`;if("includes"===c.format)return`Ogiltig str\xe4ng: m\xe5ste inneh\xe5lla "${c.includes}"`;if("regex"===c.format)return`Ogiltig str\xe4ng: m\xe5ste matcha m\xf6nstret "${c.pattern}"`;return`Ogiltig(t) ${b[c.format]??c.format}`;case"not_multiple_of":return`Ogiltigt tal: m\xe5ste vara en multipel av ${c.divisor}`;case"unrecognized_keys":return`${c.keys.length>1?"Okända nycklar":"Okänd nyckel"}: ${aw(c.keys,", ")}`;case"invalid_key":return`Ogiltig nyckel i ${c.origin??"värdet"}`;case"invalid_union":default:return"Ogiltig input";case"invalid_element":return`Ogiltigt v\xe4rde i ${c.origin??"värdet"}`}}})()}}function fv(){return{localeError:(()=>{let a={string:{unit:"எழுத்துக்கள்",verb:"கொண்டிருக்க வேண்டும்"},file:{unit:"பைட்டுகள்",verb:"கொண்டிருக்க வேண்டும்"},array:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"},set:{unit:"உறுப்புகள்",verb:"கொண்டிருக்க வேண்டும்"}},b={regex:"உள்ளீடு",email:"மின்னஞ்சல் முகவரி",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO தேதி நேரம்",date:"ISO தேதி",time:"ISO நேரம்",duration:"ISO கால அளவு",ipv4:"IPv4 முகவரி",ipv6:"IPv6 முகவரி",cidrv4:"IPv4 வரம்பு",cidrv6:"IPv6 வரம்பு",base64:"base64-encoded சரம்",base64url:"base64url-encoded சரம்",json_string:"JSON சரம்",e164:"E.164 எண்",jwt:"JWT",template_literal:"input"};return c=>{switch(c.code){case"invalid_type":return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${c.expected}, பெறப்பட்டது ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"எண் அல்லாதது":"எண்";case"object":if(Array.isArray(a))return"அணி";if(null===a)return"வெறுமை";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`தவறான உள்ளீடு: எதிர்பார்க்கப்பட்டது ${aZ(c.values[0])}`;return`தவறான விருப்பம்: எதிர்பார்க்கப்பட்டது ${aw(c.values,"|")} இல் ஒன்று`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${c.origin??"மதிப்பு"} ${b}${c.maximum.toString()} ${d.unit??"உறுப்புகள்"} ஆக இருக்க வேண்டும்`;return`மிக பெரியது: எதிர்பார்க்கப்பட்டது ${c.origin??"மதிப்பு"} ${b}${c.maximum.toString()} ஆக இருக்க வேண்டும்`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${c.origin} ${b}${c.minimum.toString()} ${d.unit} ஆக இருக்க வேண்டும்`;return`மிகச் சிறியது: எதிர்பார்க்கப்பட்டது ${c.origin} ${b}${c.minimum.toString()} ஆக இருக்க வேண்டும்`}case"invalid_format":if("starts_with"===c.format)return`தவறான சரம்: "${c.prefix}" இல் தொடங்க வேண்டும்`;if("ends_with"===c.format)return`தவறான சரம்: "${c.suffix}" இல் முடிவடைய வேண்டும்`;if("includes"===c.format)return`தவறான சரம்: "${c.includes}" ஐ உள்ளடக்க வேண்டும்`;if("regex"===c.format)return`தவறான சரம்: ${c.pattern} முறைபாட்டுடன் பொருந்த வேண்டும்`;return`தவறான ${b[c.format]??c.format}`;case"not_multiple_of":return`தவறான எண்: ${c.divisor} இன் பலமாக இருக்க வேண்டும்`;case"unrecognized_keys":return`அடையாளம் தெரியாத விசை${c.keys.length>1?"கள்":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`${c.origin} இல் தவறான விசை`;case"invalid_union":return"தவறான உள்ளீடு";case"invalid_element":return`${c.origin} இல் தவறான மதிப்பு`;default:return`தவறான உள்ளீடு`}}})()}}function fw(){return{localeError:(()=>{let a={string:{unit:"ตัวอักษร",verb:"ควรมี"},file:{unit:"ไบต์",verb:"ควรมี"},array:{unit:"รายการ",verb:"ควรมี"},set:{unit:"รายการ",verb:"ควรมี"}},b={regex:"ข้อมูลที่ป้อน",email:"ที่อยู่อีเมล",url:"URL",emoji:"อิโมจิ",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"วันที่เวลาแบบ ISO",date:"วันที่แบบ ISO",time:"เวลาแบบ ISO",duration:"ช่วงเวลาแบบ ISO",ipv4:"ที่อยู่ IPv4",ipv6:"ที่อยู่ IPv6",cidrv4:"ช่วง IP แบบ IPv4",cidrv6:"ช่วง IP แบบ IPv6",base64:"ข้อความแบบ Base64",base64url:"ข้อความแบบ Base64 สำหรับ URL",json_string:"ข้อความแบบ JSON",e164:"เบอร์โทรศัพท์ระหว่างประเทศ (E.164)",jwt:"โทเคน JWT",template_literal:"ข้อมูลที่ป้อน"};return c=>{switch(c.code){case"invalid_type":return`ประเภทข้อมูลไม่ถูกต้อง: ควรเป็น ${c.expected} แต่ได้รับ ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"ไม่ใช่ตัวเลข (NaN)":"ตัวเลข";case"object":if(Array.isArray(a))return"อาร์เรย์ (Array)";if(null===a)return"ไม่มีค่า (null)";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`ค่าไม่ถูกต้อง: ควรเป็น ${aZ(c.values[0])}`;return`ตัวเลือกไม่ถูกต้อง: ควรเป็นหนึ่งใน ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"ไม่เกิน":"น้อยกว่า",d=a[c.origin]??null;if(d)return`เกินกำหนด: ${c.origin??"ค่า"} ควรมี${b} ${c.maximum.toString()} ${d.unit??"รายการ"}`;return`เกินกำหนด: ${c.origin??"ค่า"} ควรมี${b} ${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?"อย่างน้อย":"มากกว่า",d=a[c.origin]??null;if(d)return`น้อยกว่ากำหนด: ${c.origin} ควรมี${b} ${c.minimum.toString()} ${d.unit}`;return`น้อยกว่ากำหนด: ${c.origin} ควรมี${b} ${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องขึ้นต้นด้วย "${c.prefix}"`;if("ends_with"===c.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องลงท้ายด้วย "${c.suffix}"`;if("includes"===c.format)return`รูปแบบไม่ถูกต้อง: ข้อความต้องมี "${c.includes}" อยู่ในข้อความ`;if("regex"===c.format)return`รูปแบบไม่ถูกต้อง: ต้องตรงกับรูปแบบที่กำหนด ${c.pattern}`;return`รูปแบบไม่ถูกต้อง: ${b[c.format]??c.format}`;case"not_multiple_of":return`ตัวเลขไม่ถูกต้อง: ต้องเป็นจำนวนที่หารด้วย ${c.divisor} ได้ลงตัว`;case"unrecognized_keys":return`พบคีย์ที่ไม่รู้จัก: ${aw(c.keys,", ")}`;case"invalid_key":return`คีย์ไม่ถูกต้องใน ${c.origin}`;case"invalid_union":return"ข้อมูลไม่ถูกต้อง: ไม่ตรงกับรูปแบบยูเนียนที่กำหนดไว้";case"invalid_element":return`ข้อมูลไม่ถูกต้องใน ${c.origin}`;default:return`ข้อมูลไม่ถูกต้อง`}}})()}}function fx(){return{localeError:(()=>{let a={string:{unit:"karakter",verb:"olmalı"},file:{unit:"bayt",verb:"olmalı"},array:{unit:"öğe",verb:"olmalı"},set:{unit:"öğe",verb:"olmalı"}},b={regex:"girdi",email:"e-posta adresi",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO tarih ve saat",date:"ISO tarih",time:"ISO saat",duration:"ISO süre",ipv4:"IPv4 adresi",ipv6:"IPv6 adresi",cidrv4:"IPv4 aralığı",cidrv6:"IPv6 aralığı",base64:"base64 ile şifrelenmiş metin",base64url:"base64url ile şifrelenmiş metin",json_string:"JSON dizesi",e164:"E.164 sayısı",jwt:"JWT",template_literal:"Şablon dizesi"};return c=>{switch(c.code){case"invalid_type":return`Ge\xe7ersiz değer: beklenen ${c.expected}, alınan ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Ge\xe7ersiz değer: beklenen ${aZ(c.values[0])}`;return`Ge\xe7ersiz se\xe7enek: aşağıdakilerden biri olmalı: ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`\xc7ok b\xfcy\xfck: beklenen ${c.origin??"değer"} ${b}${c.maximum.toString()} ${d.unit??"öğe"}`;return`\xc7ok b\xfcy\xfck: beklenen ${c.origin??"değer"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`\xc7ok k\xfc\xe7\xfck: beklenen ${c.origin} ${b}${c.minimum.toString()} ${d.unit}`;return`\xc7ok k\xfc\xe7\xfck: beklenen ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Ge\xe7ersiz metin: "${c.prefix}" ile başlamalı`;if("ends_with"===c.format)return`Ge\xe7ersiz metin: "${c.suffix}" ile bitmeli`;if("includes"===c.format)return`Ge\xe7ersiz metin: "${c.includes}" i\xe7ermeli`;if("regex"===c.format)return`Ge\xe7ersiz metin: ${c.pattern} desenine uymalı`;return`Ge\xe7ersiz ${b[c.format]??c.format}`;case"not_multiple_of":return`Ge\xe7ersiz sayı: ${c.divisor} ile tam b\xf6l\xfcnebilmeli`;case"unrecognized_keys":return`Tanınmayan anahtar${c.keys.length>1?"lar":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`${c.origin} i\xe7inde ge\xe7ersiz anahtar`;case"invalid_union":return"Geçersiz değer";case"invalid_element":return`${c.origin} i\xe7inde ge\xe7ersiz değer`;default:return`Ge\xe7ersiz değer`}}})()}}function fy(){return{localeError:(()=>{let a={string:{unit:"символів",verb:"матиме"},file:{unit:"байтів",verb:"матиме"},array:{unit:"елементів",verb:"матиме"},set:{unit:"елементів",verb:"матиме"}},b={regex:"вхідні дані",email:"адреса електронної пошти",url:"URL",emoji:"емодзі",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"дата та час ISO",date:"дата ISO",time:"час ISO",duration:"тривалість ISO",ipv4:"адреса IPv4",ipv6:"адреса IPv6",cidrv4:"діапазон IPv4",cidrv6:"діапазон IPv6",base64:"рядок у кодуванні base64",base64url:"рядок у кодуванні base64url",json_string:"рядок JSON",e164:"номер E.164",jwt:"JWT",template_literal:"вхідні дані"};return c=>{switch(c.code){case"invalid_type":return`Неправильні вхідні дані: очікується ${c.expected}, отримано ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"число";case"object":if(Array.isArray(a))return"масив";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Неправильні вхідні дані: очікується ${aZ(c.values[0])}`;return`Неправильна опція: очікується одне з ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Занадто велике: очікується, що ${c.origin??"значення"} ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"елементів"}`;return`Занадто велике: очікується, що ${c.origin??"значення"} буде ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Занадто мале: очікується, що ${c.origin} ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Занадто мале: очікується, що ${c.origin} буде ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Неправильний рядок: повинен починатися з "${c.prefix}"`;if("ends_with"===c.format)return`Неправильний рядок: повинен закінчуватися на "${c.suffix}"`;if("includes"===c.format)return`Неправильний рядок: повинен містити "${c.includes}"`;if("regex"===c.format)return`Неправильний рядок: повинен відповідати шаблону ${c.pattern}`;return`Неправильний ${b[c.format]??c.format}`;case"not_multiple_of":return`Неправильне число: повинно бути кратним ${c.divisor}`;case"unrecognized_keys":return`Нерозпізнаний ключ${c.keys.length>1?"і":""}: ${aw(c.keys,", ")}`;case"invalid_key":return`Неправильний ключ у ${c.origin}`;case"invalid_union":return"Неправильні вхідні дані";case"invalid_element":return`Неправильне значення у ${c.origin}`;default:return`Неправильні вхідні дані`}}})()}}function fz(){return{localeError:(()=>{let a={string:{unit:"حروف",verb:"ہونا"},file:{unit:"بائٹس",verb:"ہونا"},array:{unit:"آئٹمز",verb:"ہونا"},set:{unit:"آئٹمز",verb:"ہونا"}},b={regex:"ان پٹ",email:"ای میل ایڈریس",url:"یو آر ایل",emoji:"ایموجی",uuid:"یو یو آئی ڈی",uuidv4:"یو یو آئی ڈی وی 4",uuidv6:"یو یو آئی ڈی وی 6",nanoid:"نینو آئی ڈی",guid:"جی یو آئی ڈی",cuid:"سی یو آئی ڈی",cuid2:"سی یو آئی ڈی 2",ulid:"یو ایل آئی ڈی",xid:"ایکس آئی ڈی",ksuid:"کے ایس یو آئی ڈی",datetime:"آئی ایس او ڈیٹ ٹائم",date:"آئی ایس او تاریخ",time:"آئی ایس او وقت",duration:"آئی ایس او مدت",ipv4:"آئی پی وی 4 ایڈریس",ipv6:"آئی پی وی 6 ایڈریس",cidrv4:"آئی پی وی 4 رینج",cidrv6:"آئی پی وی 6 رینج",base64:"بیس 64 ان کوڈڈ سٹرنگ",base64url:"بیس 64 یو آر ایل ان کوڈڈ سٹرنگ",json_string:"جے ایس او این سٹرنگ",e164:"ای 164 نمبر",jwt:"جے ڈبلیو ٹی",template_literal:"ان پٹ"};return c=>{switch(c.code){case"invalid_type":return`غلط ان پٹ: ${c.expected} متوقع تھا، ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"نمبر";case"object":if(Array.isArray(a))return"آرے";if(null===a)return"نل";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)} موصول ہوا`;case"invalid_value":if(1===c.values.length)return`غلط ان پٹ: ${aZ(c.values[0])} متوقع تھا`;return`غلط آپشن: ${aw(c.values,"|")} میں سے ایک متوقع تھا`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`بہت بڑا: ${c.origin??"ویلیو"} کے ${b}${c.maximum.toString()} ${d.unit??"عناصر"} ہونے متوقع تھے`;return`بہت بڑا: ${c.origin??"ویلیو"} کا ${b}${c.maximum.toString()} ہونا متوقع تھا`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`بہت چھوٹا: ${c.origin} کے ${b}${c.minimum.toString()} ${d.unit} ہونے متوقع تھے`;return`بہت چھوٹا: ${c.origin} کا ${b}${c.minimum.toString()} ہونا متوقع تھا`}case"invalid_format":if("starts_with"===c.format)return`غلط سٹرنگ: "${c.prefix}" سے شروع ہونا چاہیے`;if("ends_with"===c.format)return`غلط سٹرنگ: "${c.suffix}" پر ختم ہونا چاہیے`;if("includes"===c.format)return`غلط سٹرنگ: "${c.includes}" شامل ہونا چاہیے`;if("regex"===c.format)return`غلط سٹرنگ: پیٹرن ${c.pattern} سے میچ ہونا چاہیے`;return`غلط ${b[c.format]??c.format}`;case"not_multiple_of":return`غلط نمبر: ${c.divisor} کا مضاعف ہونا چاہیے`;case"unrecognized_keys":return`غیر تسلیم شدہ کی${c.keys.length>1?"ز":""}: ${aw(c.keys,"، ")}`;case"invalid_key":return`${c.origin} میں غلط کی`;case"invalid_union":return"غلط ان پٹ";case"invalid_element":return`${c.origin} میں غلط ویلیو`;default:return`غلط ان پٹ`}}})()}}function fA(){return{localeError:(()=>{let a={string:{unit:"ký tự",verb:"có"},file:{unit:"byte",verb:"có"},array:{unit:"phần tử",verb:"có"},set:{unit:"phần tử",verb:"có"}},b={regex:"đầu vào",email:"địa chỉ email",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ngày giờ ISO",date:"ngày ISO",time:"giờ ISO",duration:"khoảng thời gian ISO",ipv4:"địa chỉ IPv4",ipv6:"địa chỉ IPv6",cidrv4:"dải IPv4",cidrv6:"dải IPv6",base64:"chuỗi mã hóa base64",base64url:"chuỗi mã hóa base64url",json_string:"chuỗi JSON",e164:"số E.164",jwt:"JWT",template_literal:"đầu vào"};return c=>{switch(c.code){case"invalid_type":return`Đầu v\xe0o kh\xf4ng hợp lệ: mong đợi ${c.expected}, nhận được ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"số";case"object":if(Array.isArray(a))return"mảng";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`Đầu v\xe0o kh\xf4ng hợp lệ: mong đợi ${aZ(c.values[0])}`;return`T\xf9y chọn kh\xf4ng hợp lệ: mong đợi một trong c\xe1c gi\xe1 trị ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`Qu\xe1 lớn: mong đợi ${c.origin??"giá trị"} ${d.verb} ${b}${c.maximum.toString()} ${d.unit??"phần tử"}`;return`Qu\xe1 lớn: mong đợi ${c.origin??"giá trị"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`Qu\xe1 nhỏ: mong đợi ${c.origin} ${d.verb} ${b}${c.minimum.toString()} ${d.unit}`;return`Qu\xe1 nhỏ: mong đợi ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải bắt đầu bằng "${c.prefix}"`;if("ends_with"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải kết th\xfac bằng "${c.suffix}"`;if("includes"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải bao gồm "${c.includes}"`;if("regex"===c.format)return`Chuỗi kh\xf4ng hợp lệ: phải khớp với mẫu ${c.pattern}`;return`${b[c.format]??c.format} kh\xf4ng hợp lệ`;case"not_multiple_of":return`Số kh\xf4ng hợp lệ: phải l\xe0 bội số của ${c.divisor}`;case"unrecognized_keys":return`Kh\xf3a kh\xf4ng được nhận dạng: ${aw(c.keys,", ")}`;case"invalid_key":return`Kh\xf3a kh\xf4ng hợp lệ trong ${c.origin}`;case"invalid_union":return"Đầu vào không hợp lệ";case"invalid_element":return`Gi\xe1 trị kh\xf4ng hợp lệ trong ${c.origin}`;default:return`Đầu v\xe0o kh\xf4ng hợp lệ`}}})()}}function fB(){return{localeError:(()=>{let a={string:{unit:"字符",verb:"包含"},file:{unit:"字节",verb:"包含"},array:{unit:"项",verb:"包含"},set:{unit:"项",verb:"包含"}},b={regex:"输入",email:"电子邮件",url:"URL",emoji:"表情符号",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO日期时间",date:"ISO日期",time:"ISO时间",duration:"ISO时长",ipv4:"IPv4地址",ipv6:"IPv6地址",cidrv4:"IPv4网段",cidrv6:"IPv6网段",base64:"base64编码字符串",base64url:"base64url编码字符串",json_string:"JSON字符串",e164:"E.164号码",jwt:"JWT",template_literal:"输入"};return c=>{switch(c.code){case"invalid_type":return`无效输入：期望 ${c.expected}，实际接收 ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"非数字(NaN)":"数字";case"object":if(Array.isArray(a))return"数组";if(null===a)return"空值(null)";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`无效输入：期望 ${aZ(c.values[0])}`;return`无效选项：期望以下之一 ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`数值过大：期望 ${c.origin??"值"} ${b}${c.maximum.toString()} ${d.unit??"个元素"}`;return`数值过大：期望 ${c.origin??"值"} ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`数值过小：期望 ${c.origin} ${b}${c.minimum.toString()} ${d.unit}`;return`数值过小：期望 ${c.origin} ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`无效字符串：必须以 "${c.prefix}" 开头`;if("ends_with"===c.format)return`无效字符串：必须以 "${c.suffix}" 结尾`;if("includes"===c.format)return`无效字符串：必须包含 "${c.includes}"`;if("regex"===c.format)return`无效字符串：必须满足正则表达式 ${c.pattern}`;return`无效${b[c.format]??c.format}`;case"not_multiple_of":return`无效数字：必须是 ${c.divisor} 的倍数`;case"unrecognized_keys":return`出现未知的键(key): ${aw(c.keys,", ")}`;case"invalid_key":return`${c.origin} 中的键(key)无效`;case"invalid_union":return"无效输入";case"invalid_element":return`${c.origin} 中包含无效值(value)`;default:return`无效输入`}}})()}}function fC(){return{localeError:(()=>{let a={string:{unit:"字元",verb:"擁有"},file:{unit:"位元組",verb:"擁有"},array:{unit:"項目",verb:"擁有"},set:{unit:"項目",verb:"擁有"}},b={regex:"輸入",email:"郵件地址",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"ISO 日期時間",date:"ISO 日期",time:"ISO 時間",duration:"ISO 期間",ipv4:"IPv4 位址",ipv6:"IPv6 位址",cidrv4:"IPv4 範圍",cidrv6:"IPv6 範圍",base64:"base64 編碼字串",base64url:"base64url 編碼字串",json_string:"JSON 字串",e164:"E.164 數值",jwt:"JWT",template_literal:"輸入"};return c=>{switch(c.code){case"invalid_type":return`無效的輸入值：預期為 ${c.expected}，但收到 ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"number";case"object":if(Array.isArray(a))return"array";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`無效的輸入值：預期為 ${aZ(c.values[0])}`;return`無效的選項：預期為以下其中之一 ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`數值過大：預期 ${c.origin??"值"} 應為 ${b}${c.maximum.toString()} ${d.unit??"個元素"}`;return`數值過大：預期 ${c.origin??"值"} 應為 ${b}${c.maximum.toString()}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`數值過小：預期 ${c.origin} 應為 ${b}${c.minimum.toString()} ${d.unit}`;return`數值過小：預期 ${c.origin} 應為 ${b}${c.minimum.toString()}`}case"invalid_format":if("starts_with"===c.format)return`無效的字串：必須以 "${c.prefix}" 開頭`;if("ends_with"===c.format)return`無效的字串：必須以 "${c.suffix}" 結尾`;if("includes"===c.format)return`無效的字串：必須包含 "${c.includes}"`;if("regex"===c.format)return`無效的字串：必須符合格式 ${c.pattern}`;return`無效的 ${b[c.format]??c.format}`;case"not_multiple_of":return`無效的數字：必須為 ${c.divisor} 的倍數`;case"unrecognized_keys":return`無法識別的鍵值${c.keys.length>1?"們":""}：${aw(c.keys,"、")}`;case"invalid_key":return`${c.origin} 中有無效的鍵值`;case"invalid_union":return"無效的輸入值";case"invalid_element":return`${c.origin} 中有無效的值`;default:return`無效的輸入值`}}})()}}function fD(){return{localeError:(()=>{let a={string:{unit:"àmi",verb:"ní"},file:{unit:"bytes",verb:"ní"},array:{unit:"nkan",verb:"ní"},set:{unit:"nkan",verb:"ní"}},b={regex:"ẹ̀rọ ìbáwọlé",email:"àdírẹ́sì ìmẹ́lì",url:"URL",emoji:"emoji",uuid:"UUID",uuidv4:"UUIDv4",uuidv6:"UUIDv6",nanoid:"nanoid",guid:"GUID",cuid:"cuid",cuid2:"cuid2",ulid:"ULID",xid:"XID",ksuid:"KSUID",datetime:"àkókò ISO",date:"ọjọ́ ISO",time:"àkókò ISO",duration:"àkókò tó pé ISO",ipv4:"àdírẹ́sì IPv4",ipv6:"àdírẹ́sì IPv6",cidrv4:"àgbègbè IPv4",cidrv6:"àgbègbè IPv6",base64:"ọ̀rọ̀ tí a kọ́ ní base64",base64url:"ọ̀rọ̀ base64url",json_string:"ọ̀rọ̀ JSON",e164:"nọ́mbà E.164",jwt:"JWT",template_literal:"ẹ̀rọ ìbáwọlé"};return c=>{switch(c.code){case"invalid_type":return`\xccb\xe1wọl\xe9 aṣ\xecṣe: a n\xed l\xe1ti fi ${c.expected}, \xe0mọ̀ a r\xed ${(a=>{let b=typeof a;switch(b){case"number":return Number.isNaN(a)?"NaN":"nọ́mbà";case"object":if(Array.isArray(a))return"akopọ";if(null===a)return"null";if(Object.getPrototypeOf(a)!==Object.prototype&&a.constructor)return a.constructor.name}return b})(c.input)}`;case"invalid_value":if(1===c.values.length)return`\xccb\xe1wọl\xe9 aṣ\xecṣe: a n\xed l\xe1ti fi ${aZ(c.values[0])}`;return`\xc0ṣ\xe0y\xe0n aṣ\xecṣe: yan ọ̀kan l\xe1ra ${aw(c.values,"|")}`;case"too_big":{let b=c.inclusive?"<=":"<",d=a[c.origin]??null;if(d)return`T\xf3 pọ̀ j\xf9: a n\xed l\xe1ti jẹ́ p\xe9 ${c.origin??"iye"} ${d.verb} ${b}${c.maximum} ${d.unit}`;return`T\xf3 pọ̀ j\xf9: a n\xed l\xe1ti jẹ́ ${b}${c.maximum}`}case"too_small":{let b=c.inclusive?">=":">",d=a[c.origin]??null;if(d)return`K\xe9r\xe9 ju: a n\xed l\xe1ti jẹ́ p\xe9 ${c.origin} ${d.verb} ${b}${c.minimum} ${d.unit}`;return`K\xe9r\xe9 ju: a n\xed l\xe1ti jẹ́ ${b}${c.minimum}`}case"invalid_format":if("starts_with"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ bẹ̀rẹ̀ pẹ̀l\xfa "${c.prefix}"`;if("ends_with"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ par\xed pẹ̀l\xfa "${c.suffix}"`;if("includes"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ n\xed "${c.includes}"`;if("regex"===c.format)return`Ọ̀rọ̀ aṣ\xecṣe: gbọ́dọ̀ b\xe1 \xe0pẹẹrẹ mu ${c.pattern}`;return`Aṣ\xecṣe: ${b[c.format]??c.format}`;case"not_multiple_of":return`Nọ́mb\xe0 aṣ\xecṣe: gbọ́dọ̀ jẹ́ \xe8y\xe0 p\xedp\xedn ti ${c.divisor}`;case"unrecognized_keys":return`Bọt\xecn\xec \xe0\xecmọ̀: ${aw(c.keys,", ")}`;case"invalid_key":return`Bọt\xecn\xec aṣ\xecṣe n\xedn\xfa ${c.origin}`;case"invalid_union":default:return"Ìbáwọlé aṣìṣe";case"invalid_element":return`Iye aṣ\xecṣe n\xedn\xfa ${c.origin}`}}})()}}a.s(["ar",()=>eZ,"az",()=>e$,"be",()=>e0,"ca",()=>e1,"cs",()=>e2,"da",()=>e3,"de",()=>e4,"en",()=>b8,"eo",()=>e5,"es",()=>e6,"fa",()=>e7,"fi",()=>e8,"fr",()=>e9,"frCA",()=>fa,"he",()=>fb,"hu",()=>fc,"id",()=>fd,"is",()=>fe,"it",()=>ff,"ja",()=>fg,"kh",()=>fh,"ko",()=>fi,"mk",()=>fj,"ms",()=>fk,"nl",()=>fl,"no",()=>fm,"ota",()=>fn,"pl",()=>fp,"ps",()=>fo,"pt",()=>fq,"ru",()=>fs,"sl",()=>ft,"sv",()=>fu,"ta",()=>fv,"th",()=>fw,"tr",()=>fx,"ua",()=>fy,"ur",()=>fz,"vi",()=>fA,"yo",()=>fD,"zhCN",()=>fB,"zhTW",()=>fC],97840),a.s([],55828),a.i(55828);var fE=a.i(97840);a.s(["$ZodRegistry",()=>fH,"$input",()=>fG,"$output",()=>fF,"globalRegistry",()=>fJ,"registry",()=>fI],598);let fF=Symbol("ZodOutput"),fG=Symbol("ZodInput");class fH{constructor(){this._map=new Map,this._idmap=new Map}add(a,...b){let c=b[0];if(this._map.set(a,c),c&&"object"==typeof c&&"id"in c){if(this._idmap.has(c.id))throw Error(`ID ${c.id} already exists in the registry`);this._idmap.set(c.id,a)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(a){let b=this._map.get(a);return b&&"object"==typeof b&&"id"in b&&this._idmap.delete(b.id),this._map.delete(a),this}get(a){let b=a._zod.parent;if(b){let c={...this.get(b)??{}};delete c.id;let d={...c,...this._map.get(a)};return Object.keys(d).length?d:void 0}return this._map.get(a)}has(a){return this._map.has(a)}}function fI(){return new fH}let fJ=fI();function fK(a,b){return new a({type:"string",...aX(b)})}function fL(a,b){return new a({type:"string",coerce:!0,...aX(b)})}function fM(a,b){return new a({type:"string",format:"email",check:"string_format",abort:!1,...aX(b)})}function fN(a,b){return new a({type:"string",format:"guid",check:"string_format",abort:!1,...aX(b)})}function fO(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,...aX(b)})}function fP(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...aX(b)})}function fQ(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...aX(b)})}function fR(a,b){return new a({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...aX(b)})}function fS(a,b){return new a({type:"string",format:"url",check:"string_format",abort:!1,...aX(b)})}function fT(a,b){return new a({type:"string",format:"emoji",check:"string_format",abort:!1,...aX(b)})}function fU(a,b){return new a({type:"string",format:"nanoid",check:"string_format",abort:!1,...aX(b)})}function fV(a,b){return new a({type:"string",format:"cuid",check:"string_format",abort:!1,...aX(b)})}function fW(a,b){return new a({type:"string",format:"cuid2",check:"string_format",abort:!1,...aX(b)})}function fX(a,b){return new a({type:"string",format:"ulid",check:"string_format",abort:!1,...aX(b)})}function fY(a,b){return new a({type:"string",format:"xid",check:"string_format",abort:!1,...aX(b)})}function fZ(a,b){return new a({type:"string",format:"ksuid",check:"string_format",abort:!1,...aX(b)})}function f$(a,b){return new a({type:"string",format:"ipv4",check:"string_format",abort:!1,...aX(b)})}function f_(a,b){return new a({type:"string",format:"ipv6",check:"string_format",abort:!1,...aX(b)})}function f0(a,b){return new a({type:"string",format:"cidrv4",check:"string_format",abort:!1,...aX(b)})}function f1(a,b){return new a({type:"string",format:"cidrv6",check:"string_format",abort:!1,...aX(b)})}function f2(a,b){return new a({type:"string",format:"base64",check:"string_format",abort:!1,...aX(b)})}function f3(a,b){return new a({type:"string",format:"base64url",check:"string_format",abort:!1,...aX(b)})}function f4(a,b){return new a({type:"string",format:"e164",check:"string_format",abort:!1,...aX(b)})}function f5(a,b){return new a({type:"string",format:"jwt",check:"string_format",abort:!1,...aX(b)})}a.i(598),a.i(26553),a.s(["TimePrecision",()=>f6,"_any",()=>gr,"_array",()=>g$,"_base64",()=>f2,"_base64url",()=>f3,"_bigint",()=>gk,"_boolean",()=>gi,"_catch",()=>hg,"_check",()=>hp,"_cidrv4",()=>f0,"_cidrv6",()=>f1,"_coercedBigint",()=>gl,"_coercedBoolean",()=>gj,"_coercedDate",()=>gw,"_coercedNumber",()=>gc,"_coercedString",()=>fL,"_cuid",()=>fV,"_cuid2",()=>fW,"_custom",()=>hm,"_date",()=>gv,"_default",()=>hd,"_discriminatedUnion",()=>g0,"_e164",()=>f4,"_email",()=>fM,"_emoji",()=>fT,"_endsWith",()=>gS,"_enum",()=>g6,"_file",()=>g9,"_float32",()=>ge,"_float64",()=>gf,"_gt",()=>gA,"_gte",()=>gB,"_guid",()=>fN,"_includes",()=>gQ,"_int",()=>gd,"_int32",()=>gg,"_int64",()=>gm,"_intersection",()=>g1,"_ipv4",()=>f$,"_ipv6",()=>f_,"_isoDate",()=>f8,"_isoDateTime",()=>f7,"_isoDuration",()=>ga,"_isoTime",()=>f9,"_jwt",()=>f5,"_ksuid",()=>fZ,"_lazy",()=>hk,"_length",()=>gM,"_literal",()=>g8,"_lowercase",()=>gO,"_lt",()=>gy,"_lte",()=>gz,"_map",()=>g4,"_max",()=>gz,"_maxLength",()=>gK,"_maxSize",()=>gH,"_mime",()=>gU,"_min",()=>gB,"_minLength",()=>gL,"_minSize",()=>gI,"_multipleOf",()=>gG,"_nan",()=>gx,"_nanoid",()=>fU,"_nativeEnum",()=>g7,"_negative",()=>gD,"_never",()=>gt,"_nonnegative",()=>gF,"_nonoptional",()=>he,"_nonpositive",()=>gE,"_normalize",()=>gW,"_null",()=>gq,"_nullable",()=>hc,"_number",()=>gb,"_optional",()=>hb,"_overwrite",()=>gV,"_pipe",()=>hh,"_positive",()=>gC,"_promise",()=>hl,"_property",()=>gT,"_readonly",()=>hi,"_record",()=>g3,"_refine",()=>hn,"_regex",()=>gN,"_set",()=>g5,"_size",()=>gJ,"_startsWith",()=>gR,"_string",()=>fK,"_stringFormat",()=>hr,"_stringbool",()=>hq,"_success",()=>hf,"_superRefine",()=>ho,"_symbol",()=>go,"_templateLiteral",()=>hj,"_toLowerCase",()=>gY,"_toUpperCase",()=>gZ,"_transform",()=>ha,"_trim",()=>gX,"_tuple",()=>g2,"_uint32",()=>gh,"_uint64",()=>gn,"_ulid",()=>fX,"_undefined",()=>gp,"_union",()=>g_,"_unknown",()=>gs,"_uppercase",()=>gP,"_url",()=>fS,"_uuid",()=>fO,"_uuidv4",()=>fP,"_uuidv6",()=>fQ,"_uuidv7",()=>fR,"_void",()=>gu,"_xid",()=>fY],65838);let f6={Any:null,Minute:-1,Second:0,Millisecond:3,Microsecond:6};function f7(a,b){return new a({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...aX(b)})}function f8(a,b){return new a({type:"string",format:"date",check:"string_format",...aX(b)})}function f9(a,b){return new a({type:"string",format:"time",check:"string_format",precision:null,...aX(b)})}function ga(a,b){return new a({type:"string",format:"duration",check:"string_format",...aX(b)})}function gb(a,b){return new a({type:"number",checks:[],...aX(b)})}function gc(a,b){return new a({type:"number",coerce:!0,checks:[],...aX(b)})}function gd(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"safeint",...aX(b)})}function ge(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"float32",...aX(b)})}function gf(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"float64",...aX(b)})}function gg(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"int32",...aX(b)})}function gh(a,b){return new a({type:"number",check:"number_format",abort:!1,format:"uint32",...aX(b)})}function gi(a,b){return new a({type:"boolean",...aX(b)})}function gj(a,b){return new a({type:"boolean",coerce:!0,...aX(b)})}function gk(a,b){return new a({type:"bigint",...aX(b)})}function gl(a,b){return new a({type:"bigint",coerce:!0,...aX(b)})}function gm(a,b){return new a({type:"bigint",check:"bigint_format",abort:!1,format:"int64",...aX(b)})}function gn(a,b){return new a({type:"bigint",check:"bigint_format",abort:!1,format:"uint64",...aX(b)})}function go(a,b){return new a({type:"symbol",...aX(b)})}function gp(a,b){return new a({type:"undefined",...aX(b)})}function gq(a,b){return new a({type:"null",...aX(b)})}function gr(a){return new a({type:"any"})}function gs(a){return new a({type:"unknown"})}function gt(a,b){return new a({type:"never",...aX(b)})}function gu(a,b){return new a({type:"void",...aX(b)})}function gv(a,b){return new a({type:"date",...aX(b)})}function gw(a,b){return new a({type:"date",coerce:!0,...aX(b)})}function gx(a,b){return new a({type:"nan",...aX(b)})}function gy(a,b){return new c8({check:"less_than",...aX(b),value:a,inclusive:!1})}function gz(a,b){return new c8({check:"less_than",...aX(b),value:a,inclusive:!0})}function gA(a,b){return new c9({check:"greater_than",...aX(b),value:a,inclusive:!1})}function gB(a,b){return new c9({check:"greater_than",...aX(b),value:a,inclusive:!0})}function gC(a){return gA(0,a)}function gD(a){return gy(0,a)}function gE(a){return gz(0,a)}function gF(a){return gB(0,a)}function gG(a,b){return new da({check:"multiple_of",...aX(b),value:a})}function gH(a,b){return new dd({check:"max_size",...aX(b),maximum:a})}function gI(a,b){return new de({check:"min_size",...aX(b),minimum:a})}function gJ(a,b){return new df({check:"size_equals",...aX(b),size:a})}function gK(a,b){return new dg({check:"max_length",...aX(b),maximum:a})}function gL(a,b){return new dh({check:"min_length",...aX(b),minimum:a})}function gM(a,b){return new di({check:"length_equals",...aX(b),length:a})}function gN(a,b){return new dk({check:"string_format",format:"regex",...aX(b),pattern:a})}function gO(a){return new dl({check:"string_format",format:"lowercase",...aX(a)})}function gP(a){return new dm({check:"string_format",format:"uppercase",...aX(a)})}function gQ(a,b){return new dn({check:"string_format",format:"includes",...aX(b),includes:a})}function gR(a,b){return new dp({check:"string_format",format:"starts_with",...aX(b),prefix:a})}function gS(a,b){return new dq({check:"string_format",format:"ends_with",...aX(b),suffix:a})}function gT(a,b,c){return new ds({check:"property",property:a,schema:b,...aX(c)})}function gU(a,b){return new dt({check:"mime_type",mime:a,...aX(b)})}function gV(a){return new du({check:"overwrite",tx:a})}function gW(a){return gV(b=>b.normalize(a))}function gX(){return gV(a=>a.trim())}function gY(){return gV(a=>a.toLowerCase())}function gZ(){return gV(a=>a.toUpperCase())}function g$(a,b,c){return new a({type:"array",element:b,...aX(c)})}function g_(a,b,c){return new a({type:"union",options:b,...aX(c)})}function g0(a,b,c,d){return new a({type:"union",options:c,discriminator:b,...aX(d)})}function g1(a,b,c){return new a({type:"intersection",left:b,right:c})}function g2(a,b,c,d){let e=c instanceof dx,f=e?d:c;return new a({type:"tuple",items:b,rest:e?c:null,...aX(f)})}function g3(a,b,c,d){return new a({type:"record",keyType:b,valueType:c,...aX(d)})}function g4(a,b,c,d){return new a({type:"map",keyType:b,valueType:c,...aX(d)})}function g5(a,b,c){return new a({type:"set",valueType:b,...aX(c)})}function g6(a,b,c){return new a({type:"enum",entries:Array.isArray(b)?Object.fromEntries(b.map(a=>[a,a])):b,...aX(c)})}function g7(a,b,c){return new a({type:"enum",entries:b,...aX(c)})}function g8(a,b,c){return new a({type:"literal",values:Array.isArray(b)?b:[b],...aX(c)})}function g9(a,b){return new a({type:"file",...aX(b)})}function ha(a,b){return new a({type:"transform",transform:b})}function hb(a,b){return new a({type:"optional",innerType:b})}function hc(a,b){return new a({type:"nullable",innerType:b})}function hd(a,b,c){return new a({type:"default",innerType:b,get defaultValue(){return"function"==typeof c?c():aQ(c)}})}function he(a,b,c){return new a({type:"nonoptional",innerType:b,...aX(c)})}function hf(a,b){return new a({type:"success",innerType:b})}function hg(a,b,c){return new a({type:"catch",innerType:b,catchValue:"function"==typeof c?c:()=>c})}function hh(a,b,c){return new a({type:"pipe",in:b,out:c})}function hi(a,b){return new a({type:"readonly",innerType:b})}function hj(a,b,c){return new a({type:"template_literal",parts:b,...aX(c)})}function hk(a,b){return new a({type:"lazy",getter:b})}function hl(a,b){return new a({type:"promise",innerType:b})}function hm(a,b,c){let d=aX(c);return d.abort??(d.abort=!0),new a({type:"custom",check:"custom",fn:b,...d})}function hn(a,b,c){return new a({type:"custom",check:"custom",fn:b,...aX(c)})}function ho(a){let b=hp(c=>(c.addIssue=a=>{"string"==typeof a?c.issues.push(be(a,c.value,b._zod.def)):(a.fatal&&(a.continue=!1),a.code??(a.code="custom"),a.input??(a.input=c.value),a.inst??(a.inst=b),a.continue??(a.continue=!b._zod.def.abort),c.issues.push(be(a)))},a(c.value,c)));return b}function hp(a,b){let c=new c6({check:"custom",...aX(b)});return c._zod.check=a,c}function hq(a,b){let c=aX(b),d=c.truthy??["true","1","yes","on","y","enabled"],e=c.falsy??["false","0","no","off","n","disabled"];"sensitive"!==c.case&&(d=d.map(a=>"string"==typeof a?a.toLowerCase():a),e=e.map(a=>"string"==typeof a?a.toLowerCase():a));let f=new Set(d),g=new Set(e),h=a.Codec??eM,i=a.Boolean??d1,j=new h({type:"pipe",in:new(a.String??dy)({type:"string",error:c.error}),out:new i({type:"boolean",error:c.error}),transform:(a,b)=>{let d=a;return"sensitive"!==c.case&&(d=d.toLowerCase()),!!f.has(d)||!g.has(d)&&(b.issues.push({code:"invalid_value",expected:"stringbool",values:[...f,...g],input:b.value,inst:j,continue:!1}),{})},reverseTransform:(a,b)=>!0===a?d[0]||"true":e[0]||"false",error:c.error});return j}function hr(a,b,c,d={}){let e=aX(d),f={...aX(d),check:"string_format",type:"string",format:b,fn:"function"==typeof c?c:a=>c.test(a),...e};return c instanceof RegExp&&(f.pattern=c),new a(f)}a.i(65838),a.s(["JSONSchemaGenerator",()=>hs,"toJSONSchema",()=>ht],54443);class hs{constructor(a){this.counter=0,this.metadataRegistry=a?.metadata??fJ,this.target=a?.target??"draft-2020-12",this.unrepresentable=a?.unrepresentable??"throw",this.override=a?.override??(()=>{}),this.io=a?.io??"output",this.seen=new Map}process(a,b={path:[],schemaPath:[]}){var c;let d=a._zod.def,e=this.seen.get(a);if(e)return e.count++,b.schemaPath.includes(a)&&(e.cycle=b.path),e.schema;let f={schema:{},count:1,cycle:void 0,path:b.path};this.seen.set(a,f);let g=a._zod.toJSONSchema?.();if(g)f.schema=g;else{let c={...b,schemaPath:[...b.schemaPath,a],path:b.path},e=a._zod.parent;if(e)f.ref=e,this.process(e,c),this.seen.get(e).isParent=!0;else{let b=f.schema;switch(d.type){case"string":{b.type="string";let{minimum:c,maximum:d,format:e,patterns:g,contentEncoding:h}=a._zod.bag;if("number"==typeof c&&(b.minLength=c),"number"==typeof d&&(b.maxLength=d),e&&(b.format=({guid:"uuid",url:"uri",datetime:"date-time",json_string:"json-string",regex:""})[e]??e,""===b.format&&delete b.format),h&&(b.contentEncoding=h),g&&g.size>0){let a=[...g];1===a.length?b.pattern=a[0].source:a.length>1&&(f.schema.allOf=[...a.map(a=>({..."draft-7"===this.target||"draft-4"===this.target||"openapi-3.0"===this.target?{type:"string"}:{},pattern:a.source}))])}break}case"number":{let{minimum:c,maximum:d,format:e,multipleOf:f,exclusiveMaximum:g,exclusiveMinimum:h}=a._zod.bag;"string"==typeof e&&e.includes("int")?b.type="integer":b.type="number","number"==typeof h&&("draft-4"===this.target||"openapi-3.0"===this.target?(b.minimum=h,b.exclusiveMinimum=!0):b.exclusiveMinimum=h),"number"==typeof c&&(b.minimum=c,"number"==typeof h&&"draft-4"!==this.target&&(h>=c?delete b.minimum:delete b.exclusiveMinimum)),"number"==typeof g&&("draft-4"===this.target||"openapi-3.0"===this.target?(b.maximum=g,b.exclusiveMaximum=!0):b.exclusiveMaximum=g),"number"==typeof d&&(b.maximum=d,"number"==typeof g&&"draft-4"!==this.target&&(g<=d?delete b.maximum:delete b.exclusiveMaximum)),"number"==typeof f&&(b.multipleOf=f);break}case"boolean":case"success":b.type="boolean";break;case"bigint":if("throw"===this.unrepresentable)throw Error("BigInt cannot be represented in JSON Schema");break;case"symbol":if("throw"===this.unrepresentable)throw Error("Symbols cannot be represented in JSON Schema");break;case"null":"openapi-3.0"===this.target?(b.type="string",b.nullable=!0,b.enum=[null]):b.type="null";break;case"any":case"unknown":break;case"undefined":if("throw"===this.unrepresentable)throw Error("Undefined cannot be represented in JSON Schema");break;case"void":if("throw"===this.unrepresentable)throw Error("Void cannot be represented in JSON Schema");break;case"never":b.not={};break;case"date":if("throw"===this.unrepresentable)throw Error("Date cannot be represented in JSON Schema");break;case"array":{let{minimum:e,maximum:f}=a._zod.bag;"number"==typeof e&&(b.minItems=e),"number"==typeof f&&(b.maxItems=f),b.type="array",b.items=this.process(d.element,{...c,path:[...c.path,"items"]});break}case"object":{b.type="object",b.properties={};let a=d.shape;for(let d in a)b.properties[d]=this.process(a[d],{...c,path:[...c.path,"properties",d]});let e=new Set([...new Set(Object.keys(a))].filter(a=>{let b=d.shape[a]._zod;return"input"===this.io?void 0===b.optin:void 0===b.optout}));e.size>0&&(b.required=Array.from(e)),d.catchall?._zod.def.type==="never"?b.additionalProperties=!1:d.catchall?d.catchall&&(b.additionalProperties=this.process(d.catchall,{...c,path:[...c.path,"additionalProperties"]})):"output"===this.io&&(b.additionalProperties=!1);break}case"union":b.anyOf=d.options.map((a,b)=>this.process(a,{...c,path:[...c.path,"anyOf",b]}));break;case"intersection":{let a=this.process(d.left,{...c,path:[...c.path,"allOf",0]}),e=this.process(d.right,{...c,path:[...c.path,"allOf",1]}),f=a=>"allOf"in a&&1===Object.keys(a).length;b.allOf=[...f(a)?a.allOf:[a],...f(e)?e.allOf:[e]];break}case"tuple":{b.type="array";let e="draft-2020-12"===this.target?"prefixItems":"items",f="draft-2020-12"===this.target||"openapi-3.0"===this.target?"items":"additionalItems",g=d.items.map((a,b)=>this.process(a,{...c,path:[...c.path,e,b]})),h=d.rest?this.process(d.rest,{...c,path:[...c.path,f,..."openapi-3.0"===this.target?[d.items.length]:[]]}):null;"draft-2020-12"===this.target?(b.prefixItems=g,h&&(b.items=h)):"openapi-3.0"===this.target?(b.items={anyOf:g},h&&b.items.anyOf.push(h),b.minItems=g.length,h||(b.maxItems=g.length)):(b.items=g,h&&(b.additionalItems=h));let{minimum:i,maximum:j}=a._zod.bag;"number"==typeof i&&(b.minItems=i),"number"==typeof j&&(b.maxItems=j);break}case"record":b.type="object",("draft-7"===this.target||"draft-2020-12"===this.target)&&(b.propertyNames=this.process(d.keyType,{...c,path:[...c.path,"propertyNames"]})),b.additionalProperties=this.process(d.valueType,{...c,path:[...c.path,"additionalProperties"]});break;case"map":if("throw"===this.unrepresentable)throw Error("Map cannot be represented in JSON Schema");break;case"set":if("throw"===this.unrepresentable)throw Error("Set cannot be represented in JSON Schema");break;case"enum":{let a=av(d.entries);a.every(a=>"number"==typeof a)&&(b.type="number"),a.every(a=>"string"==typeof a)&&(b.type="string"),b.enum=a;break}case"literal":{let a=[];for(let b of d.values)if(void 0===b){if("throw"===this.unrepresentable)throw Error("Literal `undefined` cannot be represented in JSON Schema")}else if("bigint"==typeof b)if("throw"===this.unrepresentable)throw Error("BigInt literals cannot be represented in JSON Schema");else a.push(Number(b));else a.push(b);if(0===a.length);else if(1===a.length){let c=a[0];b.type=null===c?"null":typeof c,"draft-4"===this.target||"openapi-3.0"===this.target?b.enum=[c]:b.const=c}else a.every(a=>"number"==typeof a)&&(b.type="number"),a.every(a=>"string"==typeof a)&&(b.type="string"),a.every(a=>"boolean"==typeof a)&&(b.type="string"),a.every(a=>null===a)&&(b.type="null"),b.enum=a;break}case"file":{let c={type:"string",format:"binary",contentEncoding:"binary"},{minimum:d,maximum:e,mime:f}=a._zod.bag;void 0!==d&&(c.minLength=d),void 0!==e&&(c.maxLength=e),f?1===f.length?(c.contentMediaType=f[0],Object.assign(b,c)):b.anyOf=f.map(a=>({...c,contentMediaType:a})):Object.assign(b,c);break}case"transform":if("throw"===this.unrepresentable)throw Error("Transforms cannot be represented in JSON Schema");break;case"nullable":{let a=this.process(d.innerType,c);"openapi-3.0"===this.target?(f.ref=d.innerType,b.nullable=!0):b.anyOf=[a,{type:"null"}];break}case"nonoptional":case"promise":case"optional":this.process(d.innerType,c),f.ref=d.innerType;break;case"default":this.process(d.innerType,c),f.ref=d.innerType,b.default=JSON.parse(JSON.stringify(d.defaultValue));break;case"prefault":this.process(d.innerType,c),f.ref=d.innerType,"input"===this.io&&(b._prefault=JSON.parse(JSON.stringify(d.defaultValue)));break;case"catch":{let a;this.process(d.innerType,c),f.ref=d.innerType;try{a=d.catchValue(void 0)}catch{throw Error("Dynamic catch values are not supported in JSON Schema")}b.default=a;break}case"nan":if("throw"===this.unrepresentable)throw Error("NaN cannot be represented in JSON Schema");break;case"template_literal":{let c=a._zod.pattern;if(!c)throw Error("Pattern not found in template literal");b.type="string",b.pattern=c.source;break}case"pipe":{let a="input"===this.io?"transform"===d.in._zod.def.type?d.out:d.in:d.out;this.process(a,c),f.ref=a;break}case"readonly":this.process(d.innerType,c),f.ref=d.innerType,b.readOnly=!0;break;case"lazy":{let b=a._zod.innerType;this.process(b,c),f.ref=b;break}case"custom":if("throw"===this.unrepresentable)throw Error("Custom types cannot be represented in JSON Schema");break;case"function":if("throw"===this.unrepresentable)throw Error("Function types cannot be represented in JSON Schema")}}}let h=this.metadataRegistry.get(a);return h&&Object.assign(f.schema,h),"input"===this.io&&function a(b,c){let d=c??{seen:new Set};if(d.seen.has(b))return!1;d.seen.add(b);let e=b._zod.def;switch(e.type){case"string":case"number":case"bigint":case"boolean":case"date":case"symbol":case"undefined":case"null":case"any":case"unknown":case"never":case"void":case"literal":case"enum":case"nan":case"file":case"template_literal":case"custom":case"success":case"catch":case"function":return!1;case"array":return a(e.element,d);case"object":for(let b in e.shape)if(a(e.shape[b],d))return!0;return!1;case"union":for(let b of e.options)if(a(b,d))return!0;return!1;case"intersection":return a(e.left,d)||a(e.right,d);case"tuple":for(let b of e.items)if(a(b,d))return!0;if(e.rest&&a(e.rest,d))return!0;return!1;case"record":case"map":return a(e.keyType,d)||a(e.valueType,d);case"set":return a(e.valueType,d);case"promise":case"optional":case"nonoptional":case"nullable":case"readonly":case"default":case"prefault":return a(e.innerType,d);case"lazy":return a(e.getter(),d);case"transform":return!0;case"pipe":return a(e.in,d)||a(e.out,d)}throw Error(`Unknown schema type: ${e.type}`)}(a)&&(delete f.schema.examples,delete f.schema.default),"input"===this.io&&f.schema._prefault&&((c=f.schema).default??(c.default=f.schema._prefault)),delete f.schema._prefault,this.seen.get(a).schema}emit(a,b){let c={cycles:b?.cycles??"ref",reused:b?.reused??"inline",external:b?.external??void 0},d=this.seen.get(a);if(!d)throw Error("Unprocessed schema. This is a bug in Zod.");let e=a=>{let b="draft-2020-12"===this.target?"$defs":"definitions";if(c.external){let d=c.external.registry.get(a[0])?.id,e=c.external.uri??(a=>a);if(d)return{ref:e(d)};let f=a[1].defId??a[1].schema.id??`schema${this.counter++}`;return a[1].defId=f,{defId:f,ref:`${e("__shared")}#/${b}/${f}`}}if(a[1]===d)return{ref:"#"};let e=`#/${b}/`,f=a[1].schema.id??`__schema${this.counter++}`;return{defId:f,ref:e+f}},f=a=>{if(a[1].schema.$ref)return;let b=a[1],{ref:c,defId:d}=e(a);b.def={...b.schema},d&&(b.defId=d);let f=b.schema;for(let a in f)delete f[a];f.$ref=c};if("throw"===c.cycles)for(let a of this.seen.entries()){let b=a[1];if(b.cycle)throw Error(`Cycle detected: #/${b.cycle?.join("/")}/<root>

Set the \`cycles\` parameter to \`"ref"\` to resolve cyclical schemas with defs.`)}for(let b of this.seen.entries()){let d=b[1];if(a===b[0]){f(b);continue}if(c.external){let d=c.external.registry.get(b[0])?.id;if(a!==b[0]&&d){f(b);continue}}if(this.metadataRegistry.get(b[0])?.id||d.cycle||d.count>1&&"ref"===c.reused){f(b);continue}}let g=(a,b)=>{let c=this.seen.get(a),d=c.def??c.schema,e={...d};if(null===c.ref)return;let f=c.ref;if(c.ref=null,f){g(f,b);let a=this.seen.get(f).schema;a.$ref&&("draft-7"===b.target||"draft-4"===b.target||"openapi-3.0"===b.target)?(d.allOf=d.allOf??[],d.allOf.push(a)):(Object.assign(d,a),Object.assign(d,e))}c.isParent||this.override({zodSchema:a,jsonSchema:d,path:c.path??[]})};for(let a of[...this.seen.entries()].reverse())g(a[0],{target:this.target});let h={};if("draft-2020-12"===this.target?h.$schema="https://json-schema.org/draft/2020-12/schema":"draft-7"===this.target?h.$schema="http://json-schema.org/draft-07/schema#":"draft-4"===this.target?h.$schema="http://json-schema.org/draft-04/schema#":"openapi-3.0"===this.target||console.warn(`Invalid target: ${this.target}`),c.external?.uri){let b=c.external.registry.get(a)?.id;if(!b)throw Error("Schema is missing an `id` property");h.$id=c.external.uri(b)}Object.assign(h,d.def);let i=c.external?.defs??{};for(let a of this.seen.entries()){let b=a[1];b.def&&b.defId&&(i[b.defId]=b.def)}c.external||Object.keys(i).length>0&&("draft-2020-12"===this.target?h.$defs=i:h.definitions=i);try{return JSON.parse(JSON.stringify(h))}catch(a){throw Error("Error converting schema to JSON.")}}}function ht(a,b){if(a instanceof fH){let c=new hs(b),d={};for(let b of a._idmap.entries()){let[a,d]=b;c.process(d)}let e={},f={registry:a,uri:b?.uri,defs:d};for(let d of a._idmap.entries()){let[a,g]=d;e[a]=c.emit(g,{...b,external:f})}return Object.keys(d).length>0&&(e.__shared={["draft-2020-12"===c.target?"$defs":"definitions"]:d}),{schemas:e}}let c=new hs(b);return c.process(a),c.emit(a,b)}a.i(54443),a.s([],87384);var hu=a.i(87384),hv=a.i(98051);a.s(["ZodAny",()=>i4,"ZodArray",()=>je,"ZodBase64",()=>iy,"ZodBase64URL",()=>iA,"ZodBigInt",()=>iV,"ZodBigIntFormat",()=>iX,"ZodBoolean",()=>iT,"ZodCIDRv4",()=>iu,"ZodCIDRv6",()=>iw,"ZodCUID",()=>ie,"ZodCUID2",()=>ih,"ZodCatch",()=>jW,"ZodCodec",()=>j0,"ZodCustom",()=>kc,"ZodCustomStringFormat",()=>iG,"ZodDate",()=>jc,"ZodDefault",()=>jO,"ZodDiscriminatedUnion",()=>jn,"ZodE164",()=>iC,"ZodEmail",()=>h$,"ZodEmoji",()=>ia,"ZodEnum",()=>jA,"ZodFile",()=>jF,"ZodFunction",()=>ka,"ZodGUID",()=>h0,"ZodIPv4",()=>iq,"ZodIPv6",()=>is,"ZodIntersection",()=>jp,"ZodJWT",()=>iE,"ZodKSUID",()=>io,"ZodLazy",()=>j6,"ZodLiteral",()=>jD,"ZodMap",()=>jw,"ZodNaN",()=>jY,"ZodNanoID",()=>ic,"ZodNever",()=>i8,"ZodNonOptional",()=>jS,"ZodNull",()=>i2,"ZodNullable",()=>jL,"ZodNumber",()=>iL,"ZodNumberFormat",()=>iN,"ZodObject",()=>jh,"ZodOptional",()=>jJ,"ZodPipe",()=>j$,"ZodPrefault",()=>jQ,"ZodPromise",()=>j8,"ZodReadonly",()=>j2,"ZodRecord",()=>jt,"ZodSet",()=>jy,"ZodString",()=>hX,"ZodStringFormat",()=>hZ,"ZodSuccess",()=>jU,"ZodSymbol",()=>i$,"ZodTemplateLiteral",()=>j4,"ZodTransform",()=>jH,"ZodTuple",()=>jr,"ZodType",()=>hV,"ZodULID",()=>ij,"ZodURL",()=>h7,"ZodUUID",()=>h2,"ZodUndefined",()=>i0,"ZodUnion",()=>jl,"ZodUnknown",()=>i6,"ZodVoid",()=>ja,"ZodXID",()=>il,"_ZodString",()=>hW,"_default",()=>jP,"_function",()=>kb,"any",()=>i5,"array",()=>jf,"base64",()=>iz,"base64url",()=>iB,"bigint",()=>iW,"boolean",()=>iU,"catch",()=>jX,"check",()=>kd,"cidrv4",()=>iv,"cidrv6",()=>ix,"codec",()=>j1,"cuid",()=>ig,"cuid2",()=>ii,"custom",()=>ke,"date",()=>jd,"discriminatedUnion",()=>jo,"e164",()=>iD,"email",()=>h_,"emoji",()=>ib,"enum",()=>jB,"file",()=>jG,"float32",()=>iP,"float64",()=>iQ,"function",()=>kb,"guid",()=>h1,"hash",()=>iK,"hex",()=>iJ,"hostname",()=>iI,"httpUrl",()=>h9,"instanceof",()=>kh,"int",()=>iO,"int32",()=>iR,"int64",()=>iY,"intersection",()=>jq,"ipv4",()=>ir,"ipv6",()=>it,"json",()=>kj,"jwt",()=>iF,"keyof",()=>jg,"ksuid",()=>ip,"lazy",()=>j7,"literal",()=>jE,"looseObject",()=>jk,"map",()=>jx,"nan",()=>jZ,"nanoid",()=>id,"nativeEnum",()=>jC,"never",()=>i9,"nonoptional",()=>jT,"null",()=>i3,"nullable",()=>jM,"nullish",()=>jN,"number",()=>iM,"object",()=>ji,"optional",()=>jK,"partialRecord",()=>jv,"pipe",()=>j_,"prefault",()=>jR,"preprocess",()=>kk,"promise",()=>j9,"readonly",()=>j3,"record",()=>ju,"refine",()=>kf,"set",()=>jz,"strictObject",()=>jj,"string",()=>hY,"stringFormat",()=>iH,"stringbool",()=>ki,"success",()=>jV,"superRefine",()=>kg,"symbol",()=>i_,"templateLiteral",()=>j5,"transform",()=>jI,"tuple",()=>js,"uint32",()=>iS,"uint64",()=>iZ,"ulid",()=>ik,"undefined",()=>i1,"union",()=>jm,"unknown",()=>i7,"url",()=>h8,"uuid",()=>h3,"uuidv4",()=>h4,"uuidv6",()=>h5,"uuidv7",()=>h6,"void",()=>jb,"xid",()=>im],47934);var hw=eY,hx=eX;a.s(["ZodISODate",()=>hA,"ZodISODateTime",()=>hy,"ZodISODuration",()=>hE,"ZodISOTime",()=>hC,"date",()=>hB,"datetime",()=>hz,"duration",()=>hF,"time",()=>hD],44496);let hy=ak("ZodISODateTime",(a,b)=>{dL.init(a,b),hZ.init(a,b)});function hz(a){return f7(hy,a)}let hA=ak("ZodISODate",(a,b)=>{dM.init(a,b),hZ.init(a,b)});function hB(a){return f8(hA,a)}let hC=ak("ZodISOTime",(a,b)=>{dN.init(a,b),hZ.init(a,b)});function hD(a){return f9(hC,a)}let hE=ak("ZodISODuration",(a,b)=>{dO.init(a,b),hZ.init(a,b)});function hF(a){return ga(hE,a)}a.s(["decode",()=>hO,"decodeAsync",()=>hQ,"encode",()=>hN,"encodeAsync",()=>hP,"parse",()=>hJ,"parseAsync",()=>hK,"safeDecode",()=>hS,"safeDecodeAsync",()=>hU,"safeEncode",()=>hR,"safeEncodeAsync",()=>hT,"safeParse",()=>hL,"safeParseAsync",()=>hM],12957),a.s(["ZodError",()=>hH,"ZodRealError",()=>hI],5288);let hG=(a,b)=>{bo.init(a,b),a.name="ZodError",Object.defineProperties(a,{format:{value:b=>br(a,b)},flatten:{value:b=>bq(a,b)},addIssue:{value:b=>{a.issues.push(b),a.message=JSON.stringify(a.issues,ax,2)}},addIssues:{value:b=>{a.issues.push(...b),a.message=JSON.stringify(a.issues,ax,2)}},isEmpty:{get:()=>0===a.issues.length}})},hH=ak("ZodError",hG),hI=ak("ZodError",hG,{Parent:Error}),hJ=bv(hI),hK=bx(hI),hL=bz(hI),hM=bB(hI),hN=bD(hI),hO=bF(hI),hP=bH(hI),hQ=bJ(hI),hR=bL(hI),hS=bN(hI),hT=bP(hI),hU=bR(hI),hV=ak("ZodType",(a,b)=>(dx.init(a,b),a.def=b,a.type=b.type,Object.defineProperty(a,"_def",{value:b}),a.check=(...c)=>a.clone({...b,checks:[...b.checks??[],...c.map(a=>"function"==typeof a?{_zod:{check:a,def:{check:"custom"},onattach:[]}}:a)]}),a.clone=(b,c)=>aW(a,b,c),a.brand=()=>a,a.register=(b,c)=>(b.add(a,c),a),a.parse=(b,c)=>hJ(a,b,c,{callee:a.parse}),a.safeParse=(b,c)=>hL(a,b,c),a.parseAsync=async(b,c)=>hK(a,b,c,{callee:a.parseAsync}),a.safeParseAsync=async(b,c)=>hM(a,b,c),a.spa=a.safeParseAsync,a.encode=(b,c)=>hN(a,b,c),a.decode=(b,c)=>hO(a,b,c),a.encodeAsync=async(b,c)=>hP(a,b,c),a.decodeAsync=async(b,c)=>hQ(a,b,c),a.safeEncode=(b,c)=>hR(a,b,c),a.safeDecode=(b,c)=>hS(a,b,c),a.safeEncodeAsync=async(b,c)=>hT(a,b,c),a.safeDecodeAsync=async(b,c)=>hU(a,b,c),a.refine=(b,c)=>a.check(kf(b,c)),a.superRefine=b=>a.check(ho(b)),a.overwrite=b=>a.check(gV(b)),a.optional=()=>jK(a),a.nullable=()=>jM(a),a.nullish=()=>jK(jM(a)),a.nonoptional=b=>jT(a,b),a.array=()=>jf(a),a.or=b=>jm([a,b]),a.and=b=>jq(a,b),a.transform=b=>j_(a,jI(b)),a.default=b=>jP(a,b),a.prefault=b=>jR(a,b),a.catch=b=>jX(a,b),a.pipe=b=>j_(a,b),a.readonly=()=>j3(a),a.describe=b=>{let c=a.clone();return fJ.add(c,{description:b}),c},Object.defineProperty(a,"description",{get:()=>fJ.get(a)?.description,configurable:!0}),a.meta=(...b)=>{if(0===b.length)return fJ.get(a);let c=a.clone();return fJ.add(c,b[0]),c},a.isOptional=()=>a.safeParse(void 0).success,a.isNullable=()=>a.safeParse(null).success,a)),hW=ak("_ZodString",(a,b)=>{dy.init(a,b),hV.init(a,b);let c=a._zod.bag;a.format=c.format??null,a.minLength=c.minimum??null,a.maxLength=c.maximum??null,a.regex=(...b)=>a.check(gN(...b)),a.includes=(...b)=>a.check(gQ(...b)),a.startsWith=(...b)=>a.check(gR(...b)),a.endsWith=(...b)=>a.check(gS(...b)),a.min=(...b)=>a.check(gL(...b)),a.max=(...b)=>a.check(gK(...b)),a.length=(...b)=>a.check(gM(...b)),a.nonempty=(...b)=>a.check(gL(1,...b)),a.lowercase=b=>a.check(gO(b)),a.uppercase=b=>a.check(gP(b)),a.trim=()=>a.check(gX()),a.normalize=(...b)=>a.check(gW(...b)),a.toLowerCase=()=>a.check(gY()),a.toUpperCase=()=>a.check(gZ())}),hX=ak("ZodString",(a,b)=>{dy.init(a,b),hW.init(a,b),a.email=b=>a.check(fM(h$,b)),a.url=b=>a.check(fS(h7,b)),a.jwt=b=>a.check(f5(iE,b)),a.emoji=b=>a.check(fT(ia,b)),a.guid=b=>a.check(fN(h0,b)),a.uuid=b=>a.check(fO(h2,b)),a.uuidv4=b=>a.check(fP(h2,b)),a.uuidv6=b=>a.check(fQ(h2,b)),a.uuidv7=b=>a.check(fR(h2,b)),a.nanoid=b=>a.check(fU(ic,b)),a.guid=b=>a.check(fN(h0,b)),a.cuid=b=>a.check(fV(ie,b)),a.cuid2=b=>a.check(fW(ih,b)),a.ulid=b=>a.check(fX(ij,b)),a.base64=b=>a.check(f2(iy,b)),a.base64url=b=>a.check(f3(iA,b)),a.xid=b=>a.check(fY(il,b)),a.ksuid=b=>a.check(fZ(io,b)),a.ipv4=b=>a.check(f$(iq,b)),a.ipv6=b=>a.check(f_(is,b)),a.cidrv4=b=>a.check(f0(iu,b)),a.cidrv6=b=>a.check(f1(iw,b)),a.e164=b=>a.check(f4(iC,b)),a.datetime=b=>a.check(hz(b)),a.date=b=>a.check(hB(b)),a.time=b=>a.check(hD(b)),a.duration=b=>a.check(hF(b))});function hY(a){return fK(hX,a)}let hZ=ak("ZodStringFormat",(a,b)=>{dz.init(a,b),hW.init(a,b)}),h$=ak("ZodEmail",(a,b)=>{dC.init(a,b),hZ.init(a,b)});function h_(a){return fM(h$,a)}let h0=ak("ZodGUID",(a,b)=>{dA.init(a,b),hZ.init(a,b)});function h1(a){return fN(h0,a)}let h2=ak("ZodUUID",(a,b)=>{dB.init(a,b),hZ.init(a,b)});function h3(a){return fO(h2,a)}function h4(a){return fP(h2,a)}function h5(a){return fQ(h2,a)}function h6(a){return fR(h2,a)}let h7=ak("ZodURL",(a,b)=>{dD.init(a,b),hZ.init(a,b)});function h8(a){return fS(h7,a)}function h9(a){return fS(h7,{protocol:/^https?$/,hostname:hw.domain,...hx.normalizeParams(a)})}let ia=ak("ZodEmoji",(a,b)=>{dE.init(a,b),hZ.init(a,b)});function ib(a){return fT(ia,a)}let ic=ak("ZodNanoID",(a,b)=>{dF.init(a,b),hZ.init(a,b)});function id(a){return fU(ic,a)}let ie=ak("ZodCUID",(a,b)=>{dG.init(a,b),hZ.init(a,b)});function ig(a){return fV(ie,a)}let ih=ak("ZodCUID2",(a,b)=>{dH.init(a,b),hZ.init(a,b)});function ii(a){return fW(ih,a)}let ij=ak("ZodULID",(a,b)=>{dI.init(a,b),hZ.init(a,b)});function ik(a){return fX(ij,a)}let il=ak("ZodXID",(a,b)=>{dJ.init(a,b),hZ.init(a,b)});function im(a){return fY(il,a)}let io=ak("ZodKSUID",(a,b)=>{dK.init(a,b),hZ.init(a,b)});function ip(a){return fZ(io,a)}let iq=ak("ZodIPv4",(a,b)=>{dP.init(a,b),hZ.init(a,b)});function ir(a){return f$(iq,a)}let is=ak("ZodIPv6",(a,b)=>{dQ.init(a,b),hZ.init(a,b)});function it(a){return f_(is,a)}let iu=ak("ZodCIDRv4",(a,b)=>{dR.init(a,b),hZ.init(a,b)});function iv(a){return f0(iu,a)}let iw=ak("ZodCIDRv6",(a,b)=>{dS.init(a,b),hZ.init(a,b)});function ix(a){return f1(iw,a)}let iy=ak("ZodBase64",(a,b)=>{dU.init(a,b),hZ.init(a,b)});function iz(a){return f2(iy,a)}let iA=ak("ZodBase64URL",(a,b)=>{dW.init(a,b),hZ.init(a,b)});function iB(a){return f3(iA,a)}let iC=ak("ZodE164",(a,b)=>{dX.init(a,b),hZ.init(a,b)});function iD(a){return f4(iC,a)}let iE=ak("ZodJWT",(a,b)=>{dZ.init(a,b),hZ.init(a,b)});function iF(a){return f5(iE,a)}let iG=ak("ZodCustomStringFormat",(a,b)=>{d$.init(a,b),hZ.init(a,b)});function iH(a,b,c={}){return hr(iG,a,b,c)}function iI(a){return hr(iG,"hostname",hw.hostname,a)}function iJ(a){return hr(iG,"hex",hw.hex,a)}function iK(a,b){let c=b?.enc??"hex",d=`${a}_${c}`,e=hw[d];if(!e)throw Error(`Unrecognized hash format: ${d}`);return hr(iG,d,e,b)}let iL=ak("ZodNumber",(a,b)=>{d_.init(a,b),hV.init(a,b),a.gt=(b,c)=>a.check(gA(b,c)),a.gte=(b,c)=>a.check(gB(b,c)),a.min=(b,c)=>a.check(gB(b,c)),a.lt=(b,c)=>a.check(gy(b,c)),a.lte=(b,c)=>a.check(gz(b,c)),a.max=(b,c)=>a.check(gz(b,c)),a.int=b=>a.check(iO(b)),a.safe=b=>a.check(iO(b)),a.positive=b=>a.check(gA(0,b)),a.nonnegative=b=>a.check(gB(0,b)),a.negative=b=>a.check(gy(0,b)),a.nonpositive=b=>a.check(gz(0,b)),a.multipleOf=(b,c)=>a.check(gG(b,c)),a.step=(b,c)=>a.check(gG(b,c)),a.finite=()=>a;let c=a._zod.bag;a.minValue=Math.max(c.minimum??-1/0,c.exclusiveMinimum??-1/0)??null,a.maxValue=Math.min(c.maximum??1/0,c.exclusiveMaximum??1/0)??null,a.isInt=(c.format??"").includes("int")||Number.isSafeInteger(c.multipleOf??.5),a.isFinite=!0,a.format=c.format??null});function iM(a){return gb(iL,a)}let iN=ak("ZodNumberFormat",(a,b)=>{d0.init(a,b),iL.init(a,b)});function iO(a){return gd(iN,a)}function iP(a){return ge(iN,a)}function iQ(a){return gf(iN,a)}function iR(a){return gg(iN,a)}function iS(a){return gh(iN,a)}let iT=ak("ZodBoolean",(a,b)=>{d1.init(a,b),hV.init(a,b)});function iU(a){return gi(iT,a)}let iV=ak("ZodBigInt",(a,b)=>{d2.init(a,b),hV.init(a,b),a.gte=(b,c)=>a.check(gB(b,c)),a.min=(b,c)=>a.check(gB(b,c)),a.gt=(b,c)=>a.check(gA(b,c)),a.gte=(b,c)=>a.check(gB(b,c)),a.min=(b,c)=>a.check(gB(b,c)),a.lt=(b,c)=>a.check(gy(b,c)),a.lte=(b,c)=>a.check(gz(b,c)),a.max=(b,c)=>a.check(gz(b,c)),a.positive=b=>a.check(gA(BigInt(0),b)),a.negative=b=>a.check(gy(BigInt(0),b)),a.nonpositive=b=>a.check(gz(BigInt(0),b)),a.nonnegative=b=>a.check(gB(BigInt(0),b)),a.multipleOf=(b,c)=>a.check(gG(b,c));let c=a._zod.bag;a.minValue=c.minimum??null,a.maxValue=c.maximum??null,a.format=c.format??null});function iW(a){return gk(iV,a)}let iX=ak("ZodBigIntFormat",(a,b)=>{d3.init(a,b),iV.init(a,b)});function iY(a){return gm(iX,a)}function iZ(a){return gn(iX,a)}let i$=ak("ZodSymbol",(a,b)=>{d4.init(a,b),hV.init(a,b)});function i_(a){return go(i$,a)}let i0=ak("ZodUndefined",(a,b)=>{d5.init(a,b),hV.init(a,b)});function i1(a){return gp(i0,a)}let i2=ak("ZodNull",(a,b)=>{d6.init(a,b),hV.init(a,b)});function i3(a){return gq(i2,a)}let i4=ak("ZodAny",(a,b)=>{d7.init(a,b),hV.init(a,b)});function i5(){return gr(i4)}let i6=ak("ZodUnknown",(a,b)=>{d8.init(a,b),hV.init(a,b)});function i7(){return gs(i6)}let i8=ak("ZodNever",(a,b)=>{d9.init(a,b),hV.init(a,b)});function i9(a){return gt(i8,a)}let ja=ak("ZodVoid",(a,b)=>{ea.init(a,b),hV.init(a,b)});function jb(a){return gu(ja,a)}let jc=ak("ZodDate",(a,b)=>{eb.init(a,b),hV.init(a,b),a.min=(b,c)=>a.check(gB(b,c)),a.max=(b,c)=>a.check(gz(b,c));let c=a._zod.bag;a.minDate=c.minimum?new Date(c.minimum):null,a.maxDate=c.maximum?new Date(c.maximum):null});function jd(a){return gv(jc,a)}let je=ak("ZodArray",(a,b)=>{ed.init(a,b),hV.init(a,b),a.element=b.element,a.min=(b,c)=>a.check(gL(b,c)),a.nonempty=b=>a.check(gL(1,b)),a.max=(b,c)=>a.check(gK(b,c)),a.length=(b,c)=>a.check(gM(b,c)),a.unwrap=()=>a.element});function jf(a,b){return g$(je,a,b)}function jg(a){return jB(Object.keys(a._zod.def.shape))}let jh=ak("ZodObject",(a,b)=>{ei.init(a,b),hV.init(a,b),hx.defineLazy(a,"shape",()=>b.shape),a.keyof=()=>jB(Object.keys(a._zod.def.shape)),a.catchall=b=>a.clone({...a._zod.def,catchall:b}),a.passthrough=()=>a.clone({...a._zod.def,catchall:i7()}),a.loose=()=>a.clone({...a._zod.def,catchall:i7()}),a.strict=()=>a.clone({...a._zod.def,catchall:i9()}),a.strip=()=>a.clone({...a._zod.def,catchall:void 0}),a.extend=b=>hx.extend(a,b),a.safeExtend=b=>hx.safeExtend(a,b),a.merge=b=>hx.merge(a,b),a.pick=b=>hx.pick(a,b),a.omit=b=>hx.omit(a,b),a.partial=(...b)=>hx.partial(jJ,a,b[0]),a.required=(...b)=>hx.required(jS,a,b[0])});function ji(a,b){return new jh({type:"object",get shape(){return hx.assignProp(this,"shape",a?hx.objectClone(a):{}),this.shape},...hx.normalizeParams(b)})}function jj(a,b){return new jh({type:"object",get shape(){return hx.assignProp(this,"shape",hx.objectClone(a)),this.shape},catchall:i9(),...hx.normalizeParams(b)})}function jk(a,b){return new jh({type:"object",get shape(){return hx.assignProp(this,"shape",hx.objectClone(a)),this.shape},catchall:i7(),...hx.normalizeParams(b)})}let jl=ak("ZodUnion",(a,b)=>{ek.init(a,b),hV.init(a,b),a.options=b.options});function jm(a,b){return new jl({type:"union",options:a,...hx.normalizeParams(b)})}let jn=ak("ZodDiscriminatedUnion",(a,b)=>{jl.init(a,b),el.init(a,b)});function jo(a,b,c){return new jn({type:"union",options:b,discriminator:a,...hx.normalizeParams(c)})}let jp=ak("ZodIntersection",(a,b)=>{em.init(a,b),hV.init(a,b)});function jq(a,b){return new jp({type:"intersection",left:a,right:b})}let jr=ak("ZodTuple",(a,b)=>{eo.init(a,b),hV.init(a,b),a.rest=b=>a.clone({...a._zod.def,rest:b})});function js(a,b,c){let d=b instanceof dx,e=d?c:b;return new jr({type:"tuple",items:a,rest:d?b:null,...hx.normalizeParams(e)})}let jt=ak("ZodRecord",(a,b)=>{eq.init(a,b),hV.init(a,b),a.keyType=b.keyType,a.valueType=b.valueType});function ju(a,b,c){return new jt({type:"record",keyType:a,valueType:b,...hx.normalizeParams(c)})}function jv(a,b,c){let d=aW(a);return d._zod.values=void 0,new jt({type:"record",keyType:d,valueType:b,...hx.normalizeParams(c)})}let jw=ak("ZodMap",(a,b)=>{er.init(a,b),hV.init(a,b),a.keyType=b.keyType,a.valueType=b.valueType});function jx(a,b,c){return new jw({type:"map",keyType:a,valueType:b,...hx.normalizeParams(c)})}let jy=ak("ZodSet",(a,b)=>{et.init(a,b),hV.init(a,b),a.min=(...b)=>a.check(gI(...b)),a.nonempty=b=>a.check(gI(1,b)),a.max=(...b)=>a.check(gH(...b)),a.size=(...b)=>a.check(gJ(...b))});function jz(a,b){return new jy({type:"set",valueType:a,...hx.normalizeParams(b)})}let jA=ak("ZodEnum",(a,b)=>{ev.init(a,b),hV.init(a,b),a.enum=b.entries,a.options=Object.values(b.entries);let c=new Set(Object.keys(b.entries));a.extract=(a,d)=>{let e={};for(let d of a)if(c.has(d))e[d]=b.entries[d];else throw Error(`Key ${d} not found in enum`);return new jA({...b,checks:[],...hx.normalizeParams(d),entries:e})},a.exclude=(a,d)=>{let e={...b.entries};for(let b of a)if(c.has(b))delete e[b];else throw Error(`Key ${b} not found in enum`);return new jA({...b,checks:[],...hx.normalizeParams(d),entries:e})}});function jB(a,b){return new jA({type:"enum",entries:Array.isArray(a)?Object.fromEntries(a.map(a=>[a,a])):a,...hx.normalizeParams(b)})}function jC(a,b){return new jA({type:"enum",entries:a,...hx.normalizeParams(b)})}let jD=ak("ZodLiteral",(a,b)=>{ew.init(a,b),hV.init(a,b),a.values=new Set(b.values),Object.defineProperty(a,"value",{get(){if(b.values.length>1)throw Error("This schema contains multiple valid literal values. Use `.values` instead.");return b.values[0]}})});function jE(a,b){return new jD({type:"literal",values:Array.isArray(a)?a:[a],...hx.normalizeParams(b)})}let jF=ak("ZodFile",(a,b)=>{ex.init(a,b),hV.init(a,b),a.min=(b,c)=>a.check(gI(b,c)),a.max=(b,c)=>a.check(gH(b,c)),a.mime=(b,c)=>a.check(gU(Array.isArray(b)?b:[b],c))});function jG(a){return g9(jF,a)}let jH=ak("ZodTransform",(a,b)=>{ey.init(a,b),hV.init(a,b),a._zod.parse=(c,d)=>{if("backward"===d.direction)throw new an(a.constructor.name);c.addIssue=d=>{"string"==typeof d?c.issues.push(hx.issue(d,c.value,b)):(d.fatal&&(d.continue=!1),d.code??(d.code="custom"),d.input??(d.input=c.value),d.inst??(d.inst=a),c.issues.push(hx.issue(d)))};let e=b.transform(c.value,c);return e instanceof Promise?e.then(a=>(c.value=a,c)):(c.value=e,c)}});function jI(a){return new jH({type:"transform",transform:a})}let jJ=ak("ZodOptional",(a,b)=>{eA.init(a,b),hV.init(a,b),a.unwrap=()=>a._zod.def.innerType});function jK(a){return new jJ({type:"optional",innerType:a})}let jL=ak("ZodNullable",(a,b)=>{eB.init(a,b),hV.init(a,b),a.unwrap=()=>a._zod.def.innerType});function jM(a){return new jL({type:"nullable",innerType:a})}function jN(a){return jK(jM(a))}let jO=ak("ZodDefault",(a,b)=>{eC.init(a,b),hV.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeDefault=a.unwrap});function jP(a,b){return new jO({type:"default",innerType:a,get defaultValue(){return"function"==typeof b?b():hx.shallowClone(b)}})}let jQ=ak("ZodPrefault",(a,b)=>{eE.init(a,b),hV.init(a,b),a.unwrap=()=>a._zod.def.innerType});function jR(a,b){return new jQ({type:"prefault",innerType:a,get defaultValue(){return"function"==typeof b?b():hx.shallowClone(b)}})}let jS=ak("ZodNonOptional",(a,b)=>{eF.init(a,b),hV.init(a,b),a.unwrap=()=>a._zod.def.innerType});function jT(a,b){return new jS({type:"nonoptional",innerType:a,...hx.normalizeParams(b)})}let jU=ak("ZodSuccess",(a,b)=>{eH.init(a,b),hV.init(a,b),a.unwrap=()=>a._zod.def.innerType});function jV(a){return new jU({type:"success",innerType:a})}let jW=ak("ZodCatch",(a,b)=>{eI.init(a,b),hV.init(a,b),a.unwrap=()=>a._zod.def.innerType,a.removeCatch=a.unwrap});function jX(a,b){return new jW({type:"catch",innerType:a,catchValue:"function"==typeof b?b:()=>b})}let jY=ak("ZodNaN",(a,b)=>{eJ.init(a,b),hV.init(a,b)});function jZ(a){return gx(jY,a)}let j$=ak("ZodPipe",(a,b)=>{eK.init(a,b),hV.init(a,b),a.in=b.in,a.out=b.out});function j_(a,b){return new j$({type:"pipe",in:a,out:b})}let j0=ak("ZodCodec",(a,b)=>{j$.init(a,b),eM.init(a,b)});function j1(a,b,c){return new j0({type:"pipe",in:a,out:b,transform:c.decode,reverseTransform:c.encode})}let j2=ak("ZodReadonly",(a,b)=>{eP.init(a,b),hV.init(a,b),a.unwrap=()=>a._zod.def.innerType});function j3(a){return new j2({type:"readonly",innerType:a})}let j4=ak("ZodTemplateLiteral",(a,b)=>{eR.init(a,b),hV.init(a,b)});function j5(a,b){return new j4({type:"template_literal",parts:a,...hx.normalizeParams(b)})}let j6=ak("ZodLazy",(a,b)=>{eU.init(a,b),hV.init(a,b),a.unwrap=()=>a._zod.def.getter()});function j7(a){return new j6({type:"lazy",getter:a})}let j8=ak("ZodPromise",(a,b)=>{eT.init(a,b),hV.init(a,b),a.unwrap=()=>a._zod.def.innerType});function j9(a){return new j8({type:"promise",innerType:a})}let ka=ak("ZodFunction",(a,b)=>{eS.init(a,b),hV.init(a,b)});function kb(a){return new ka({type:"function",input:Array.isArray(a?.input)?js(a?.input):a?.input??jf(i7()),output:a?.output??i7()})}let kc=ak("ZodCustom",(a,b)=>{eV.init(a,b),hV.init(a,b)});function kd(a){let b=new c6({check:"custom"});return b._zod.check=a,b}function ke(a,b){return hm(kc,a??(()=>!0),b)}function kf(a,b={}){return hn(kc,a,b)}function kg(a){return ho(a)}function kh(a,b={error:`Input not instance of ${a.name}`}){let c=new kc({type:"custom",check:"custom",fn:b=>b instanceof a,abort:!0,...hx.normalizeParams(b)});return c._zod.bag.Class=a,c}let ki=(...a)=>hq({Codec:j0,Boolean:iT,String:hX},...a);function kj(a){let b=j7(()=>jm([hY(a),iM(),iU(),i3(),jf(b),ju(hY(),b)]));return b}function kk(a,b){return j_(jI(a),b)}a.i(47934),a.s(["endsWith",()=>gS,"gt",()=>gA,"gte",()=>gB,"includes",()=>gQ,"length",()=>gM,"lowercase",()=>gO,"lt",()=>gy,"lte",()=>gz,"maxLength",()=>gK,"maxSize",()=>gH,"mime",()=>gU,"minLength",()=>gL,"minSize",()=>gI,"multipleOf",()=>gG,"negative",()=>gD,"nonnegative",()=>gF,"nonpositive",()=>gE,"normalize",()=>gW,"overwrite",()=>gV,"positive",()=>gC,"property",()=>gT,"regex",()=>gN,"size",()=>gJ,"startsWith",()=>gR,"toLowerCase",()=>gY,"toUpperCase",()=>gZ,"trim",()=>gX,"uppercase",()=>gP],32261),a.s([],85210),a.i(85210),a.i(32261),a.i(5288),a.i(12957),a.s(["$brand",()=>al,"ZodFirstPartyTypeKind",()=>b,"ZodIssueCode",()=>kl,"config",()=>ap,"getErrorMap",()=>kn,"setErrorMap",()=>km],56282),a.s(["ZodFirstPartyTypeKind",()=>b,"ZodIssueCode",()=>kl,"getErrorMap",()=>kn,"setErrorMap",()=>km],58737);let kl={invalid_type:"invalid_type",too_big:"too_big",too_small:"too_small",invalid_format:"invalid_format",not_multiple_of:"not_multiple_of",unrecognized_keys:"unrecognized_keys",invalid_union:"invalid_union",invalid_key:"invalid_key",invalid_element:"invalid_element",invalid_value:"invalid_value",custom:"custom"};function km(a){ap({customError:a})}function kn(){return ap().customError}b||(b={}),a.i(58737),a.i(56282);var hw=eY,hx=eX,ko=fE,kp=a.i(44496);function kq(a){return fL(hX,a)}function kr(a){return gc(iL,a)}function ks(a){return gj(iT,a)}function kt(a){return gl(iV,a)}function ku(a){return gw(jc,a)}a.s(["bigint",()=>kt,"boolean",()=>ks,"date",()=>ku,"number",()=>kr,"string",()=>kq],94423);var kv=a.i(94423),kw=a.i(99333),kw=kw;let kx=kw.object({email:kw.email("Please enter a valid email address")});var ky=a.i(16218),kz=(0,ky.createServerReference)("401553368a882499aede4e05ebfdb3ca7882231805",ky.callServer,void 0,ky.findSourceMapURL,"subscribe"),kA=a.i(40695);function kB({className:a,type:b,...d}){return(0,c.jsx)("input",{className:(0,bZ.cn)("flex h-9 w-full min-w-0 rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-xs outline-none transition-[color,box-shadow] selection:bg-primary selection:text-primary-foreground file:inline-flex file:h-7 file:border-0 file:bg-transparent file:font-medium file:text-foreground file:text-sm placeholder:text-muted-foreground disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm dark:bg-input/30","focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50","aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40",a),"data-slot":"input",type:b,...d})}function kC(){let[a,b]=(0,d.useState)(!1),[k,n]=(0,d.useState)(!1),s=function(a={}){let b=d.default.useRef(void 0),c=d.default.useRef(void 0),[k,n]=d.default.useState({isDirty:!1,isValidating:!1,isLoading:G(a.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:a.errors||{},disabled:a.disabled||!1,isReady:!1,defaultValues:G(a.defaultValues)?void 0:a.defaultValues});if(!b.current)if(a.formControl)b.current={...a.formControl,formState:k},a.defaultValues&&!G(a.defaultValues)&&a.formControl.reset(a.defaultValues,a.resetOptions);else{let{formControl:c,...d}=function(a={}){let b,c={...ad,...a},d={submitCount:0,isDirty:!1,isReady:!1,isLoading:G(c.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:c.errors||{},disabled:c.disabled||!1},k={},n=(g(c.defaultValues)||g(c.values))&&j(c.defaultValues||c.values)||{},s=c.shouldUnregister?{}:j(n),t={action:!1,mount:!1,watch:!1},u={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},v=0,w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},x={...w},z={array:E(),state:E()},B=c.criteriaMode===r.all,C=async a=>{if(!c.disabled&&(w.isValid||x.isValid||a)){let a=c.resolver?F((await N()).errors):await Q(k,!0);a!==d.isValid&&z.state.next({isValid:a})}},J=(a,b)=>{!c.disabled&&(w.isValidating||w.validatingFields||x.isValidating||x.validatingFields)&&((a||Array.from(u.mount)).forEach(a=>{a&&(b?p(d.validatingFields,a,b):I(d.validatingFields,a))}),z.state.next({validatingFields:d.validatingFields,isValidating:!F(d.validatingFields)}))},K=(a,b,c,d)=>{let e=o(k,a);if(e){let f=o(s,a,l(c)?o(n,a):c);l(f)||d&&d.defaultChecked||b?p(s,a,b?f:S(e._f)):_(a,f),t.mount&&C()}},M=(a,b,e,f,g)=>{let h=!1,i=!1,j={name:a};if(!c.disabled){if(!e||f){(w.isDirty||x.isDirty)&&(i=d.isDirty,d.isDirty=j.isDirty=R(),h=i!==j.isDirty);let c=A(o(n,a),b);i=!!o(d.dirtyFields,a),c?I(d.dirtyFields,a):p(d.dirtyFields,a,!0),j.dirtyFields=d.dirtyFields,h=h||(w.dirtyFields||x.dirtyFields)&&!c!==i}if(e){let b=o(d.touchedFields,a);b||(p(d.touchedFields,a,e),j.touchedFields=d.touchedFields,h=h||(w.touchedFields||x.touchedFields)&&b!==e)}h&&g&&z.state.next(j)}return h?j:{}},N=async a=>{J(a,!0);let b=await c.resolver(s,c.context,((a,b,c,d)=>{let e={};for(let c of a){let a=o(b,c);a&&p(e,c,a._f)}return{criteriaMode:c,names:[...a],fields:e,shouldUseNativeValidation:d}})(a||u.mount,k,c.criteriaMode,c.shouldUseNativeValidation));return J(a),b},O=async a=>{let{errors:b}=await N(a);if(a)for(let c of a){let a=o(b,c);a?p(d.errors,c,a):I(d.errors,c)}else d.errors=b;return b},Q=async(a,b,e={valid:!0})=>{for(let f in a){let g=a[f];if(g){let{_f:a,...h}=g;if(a){let h=u.array.has(a.name),i=g._f&&W(g._f);i&&w.validatingFields&&J([f],!0);let j=await ac(g,u.disabled,s,B,c.shouldUseNativeValidation&&!b,h);if(i&&w.validatingFields&&J([f]),j[a.name]&&(e.valid=!1,b))break;b||(o(j,a.name)?h?$(d.errors,j,a.name):p(d.errors,a.name,j[a.name]):I(d.errors,a.name))}F(h)||await Q(h,b,e)}}return e.valid},R=(a,b)=>!c.disabled&&(a&&b&&p(s,a,b),!A(ah(),n)),V=(a,b,c)=>y(a,u,{...t.mount?s:l(b)?n:"string"==typeof a?{[a]:b}:b},c,b),_=(a,b,c={})=>{let d=o(k,a),e=b;if(d){let c=d._f;c&&(c.disabled||p(s,a,P(b,c)),c.ref,e=b,"select-multiple"===c.ref.type?[...c.ref.options].forEach(a=>a.selected=e.includes(a.value)):c.refs?"checkbox"===c.ref.type?c.refs.forEach(a=>{a.defaultChecked&&a.disabled||(Array.isArray(e)?a.checked=!!e.find(b=>b===a.value):a.checked=e===a.value||!!e)}):c.refs.forEach(a=>a.checked=a.value===e):"file"===c.ref.type?c.ref.value="":(c.ref.value=e,c.ref.type||z.state.next({name:a,values:j(s)})))}(c.shouldDirty||c.shouldTouch)&&M(a,e,c.shouldTouch,c.shouldDirty,!0),c.shouldValidate&&ag(a)},aa=(a,b,c)=>{for(let d in b){if(!b.hasOwnProperty(d))return;let f=b[d],h=a+"."+d,i=o(k,h);(u.array.has(a)||g(f)||i&&!i._f)&&!e(f)?aa(h,f,c):_(h,f,c)}},ab=(a,b,c={})=>{let e=o(k,a),g=u.array.has(a),h=j(b);p(s,a,h),g?(z.array.next({name:a,values:j(s)}),(w.isDirty||w.dirtyFields||x.isDirty||x.dirtyFields)&&c.shouldDirty&&z.state.next({name:a,dirtyFields:L(n,s),isDirty:R(a,h)})):!e||e._f||f(h)?_(a,h,c):aa(a,h,c),X(a,u)&&z.state.next({...d,name:a}),z.state.next({name:t.mount?a:void 0,values:j(s)})},ae=async a=>{t.mount=!0;let f=a.target,g=f.name,i=!0,l=o(k,g),m=a=>{i=Number.isNaN(a)||e(a)&&isNaN(a.getTime())||A(a,o(s,g,a))},n=U(c.mode),r=U(c.reValidateMode);if(l){let e,t,P,R=f.type?S(l._f):h(a),T=a.type===q.BLUR||a.type===q.FOCUS_OUT,U=!((P=l._f).mount&&(P.required||P.min||P.max||P.maxLength||P.minLength||P.pattern||P.validate))&&!c.resolver&&!o(d.errors,g)&&!l._f.deps||(y=T,D=o(d.touchedFields,g),E=d.isSubmitted,G=r,!(H=n).isOnAll&&(!E&&H.isOnTouch?!(D||y):(E?G.isOnBlur:H.isOnBlur)?!y:(E?!G.isOnChange:!H.isOnChange)||y)),V=X(g,u,T);p(s,g,R),T?f&&f.readOnly||(l._f.onBlur&&l._f.onBlur(a),b&&b(0)):l._f.onChange&&l._f.onChange(a);let W=M(g,R,T),Y=!F(W)||V;if(T||z.state.next({name:g,type:a.type,values:j(s)}),U)return(w.isValid||x.isValid)&&("onBlur"===c.mode?T&&C():T||C()),Y&&z.state.next({name:g,...V?{}:W});if(!T&&V&&z.state.next({...d}),c.resolver){let{errors:a}=await N([g]);if(m(R),i){let b=Z(d.errors,k,g),c=Z(a,k,b.name||g);e=c.error,g=c.name,t=F(a)}}else J([g],!0),e=(await ac(l,u.disabled,s,B,c.shouldUseNativeValidation))[g],J([g]),m(R),i&&(e?t=!1:(w.isValid||x.isValid)&&(t=await Q(k,!0)));if(i){l._f.deps&&ag(l._f.deps);var y,D,E,G,H,K=g,L=t,O=e;let a=o(d.errors,K),f=(w.isValid||x.isValid)&&"boolean"==typeof L&&d.isValid!==L;if(c.delayError&&O){let a;a=()=>{p(d.errors,K,O),z.state.next({errors:d.errors})},(b=b=>{clearTimeout(v),v=setTimeout(a,b)})(c.delayError)}else clearTimeout(v),b=null,O?p(d.errors,K,O):I(d.errors,K);if((O?!A(a,O):a)||!F(W)||f){let a={...W,...f&&"boolean"==typeof L?{isValid:L}:{},errors:d.errors,name:K};d={...d,...a},z.state.next(a)}}}},af=(a,b)=>{if(o(d.errors,b)&&a.focus)return a.focus(),1},ag=async(a,b={})=>{let e,f,g=D(a);if(c.resolver){let b=await O(l(a)?a:g);e=F(b),f=a?!g.some(a=>o(b,a)):e}else a?((f=(await Promise.all(g.map(async a=>{let b=o(k,a);return await Q(b&&b._f?{[a]:b}:b)}))).every(Boolean))||d.isValid)&&C():f=e=await Q(k);return z.state.next({..."string"!=typeof a||(w.isValid||x.isValid)&&e!==d.isValid?{}:{name:a},...c.resolver||!a?{isValid:e}:{},errors:d.errors}),b.shouldFocus&&!f&&Y(k,af,a?g:u.mount),f},ah=a=>{let b={...t.mount?s:n};return l(a)?b:"string"==typeof a?o(b,a):a.map(a=>o(b,a))},ai=(a,b)=>({invalid:!!o((b||d).errors,a),isDirty:!!o((b||d).dirtyFields,a),error:o((b||d).errors,a),isValidating:!!o(d.validatingFields,a),isTouched:!!o((b||d).touchedFields,a)}),aj=(a,b,c)=>{let e=(o(k,a,{_f:{}})._f||{}).ref,{ref:f,message:g,type:h,...i}=o(d.errors,a)||{};p(d.errors,a,{...i,...b,ref:e}),z.state.next({name:a,errors:d.errors,isValid:!1}),c&&c.shouldFocus&&e&&e.focus&&e.focus()},ak=a=>z.state.subscribe({next:b=>{let c,e,f;c=a.name,e=b.name,f=a.exact,(!c||!e||c===e||D(c).some(a=>a&&(f?a===e:a.startsWith(e)||e.startsWith(a))))&&((a,b,c,d)=>{c(a);let{name:e,...f}=a;return F(f)||Object.keys(f).length>=Object.keys(b).length||Object.keys(f).find(a=>b[a]===(!d||r.all))})(b,a.formState||w,as,a.reRenderRoot)&&a.callback({values:{...s},...d,...b,defaultValues:n})}}).unsubscribe,al=(a,b={})=>{for(let e of a?D(a):u.mount)u.mount.delete(e),u.array.delete(e),b.keepValue||(I(k,e),I(s,e)),b.keepError||I(d.errors,e),b.keepDirty||I(d.dirtyFields,e),b.keepTouched||I(d.touchedFields,e),b.keepIsValidating||I(d.validatingFields,e),c.shouldUnregister||b.keepDefaultValue||I(n,e);z.state.next({values:j(s)}),z.state.next({...d,...!b.keepDirty?{}:{isDirty:R()}}),b.keepIsValid||C()},am=({disabled:a,name:b})=>{("boolean"==typeof a&&t.mount||a||u.disabled.has(b))&&(a?u.disabled.add(b):u.disabled.delete(b))},an=(a,b={})=>{let d=o(k,a),e="boolean"==typeof b.disabled||"boolean"==typeof c.disabled;return(p(k,a,{...d||{},_f:{...d&&d._f?d._f:{ref:{name:a}},name:a,mount:!0,...b}}),u.mount.add(a),d)?am({disabled:"boolean"==typeof b.disabled?b.disabled:c.disabled,name:a}):K(a,!0,b.value),{...e?{disabled:b.disabled||c.disabled}:{},...c.progressive?{required:!!b.required,min:T(b.min),max:T(b.max),minLength:T(b.minLength),maxLength:T(b.maxLength),pattern:T(b.pattern)}:{},name:a,onChange:ae,onBlur:ae,ref:e=>{if(e){let c;an(a,b),d=o(k,a);let f=l(e.value)&&e.querySelectorAll&&e.querySelectorAll("input,select,textarea")[0]||e,g="radio"===(c=f).type||"checkbox"===c.type,h=d._f.refs||[];(g?h.find(a=>a===f):f===d._f.ref)||(p(k,a,{_f:{...d._f,...g?{refs:[...h.filter(H),f,...Array.isArray(o(n,a))?[{}]:[]],ref:{type:f.type,name:a}}:{ref:f}}}),K(a,!1,void 0,f))}else(d=o(k,a,{}))._f&&(d._f.mount=!1),(c.shouldUnregister||b.shouldUnregister)&&!(i(u.array,a)&&t.action)&&u.unMount.add(a)}}},ao=()=>c.shouldFocusError&&Y(k,af,u.mount),ap=(a,b)=>async e=>{let f;e&&(e.preventDefault&&e.preventDefault(),e.persist&&e.persist());let g=j(s);if(z.state.next({isSubmitting:!0}),c.resolver){let{errors:a,values:b}=await N();d.errors=a,g=j(b)}else await Q(k);if(u.disabled.size)for(let a of u.disabled)I(g,a);if(I(d.errors,"root"),F(d.errors)){z.state.next({errors:{}});try{await a(g,e)}catch(a){f=a}}else b&&await b({...d.errors},e),ao(),setTimeout(ao);if(z.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:F(d.errors)&&!f,submitCount:d.submitCount+1,errors:d.errors}),f)throw f},aq=(a,b={})=>{let e=a?j(a):n,f=j(e),g=F(a),h=g?n:f;if(b.keepDefaultValues||(n=e),!b.keepValues){if(b.keepDirtyValues)for(let a of Array.from(new Set([...u.mount,...Object.keys(L(n,s))])))o(d.dirtyFields,a)?p(h,a,o(s,a)):ab(a,o(h,a));else if(b.keepFieldsRef)for(let a of u.mount)ab(a,o(h,a));else k={};s=c.shouldUnregister?b.keepDefaultValues?j(n):{}:j(h),z.array.next({values:{...h}}),z.state.next({values:{...h}})}u={mount:b.keepDirtyValues?u.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},t.mount=!w.isValid||!!b.keepIsValid||!!b.keepDirtyValues,t.watch=!!c.shouldUnregister,z.state.next({submitCount:b.keepSubmitCount?d.submitCount:0,isDirty:!g&&(b.keepDirty?d.isDirty:!!(b.keepDefaultValues&&!A(a,n))),isSubmitted:!!b.keepIsSubmitted&&d.isSubmitted,dirtyFields:g?{}:b.keepDirtyValues?b.keepDefaultValues&&s?L(n,s):d.dirtyFields:b.keepDefaultValues&&a?L(n,a):b.keepDirty?d.dirtyFields:{},touchedFields:b.keepTouched?d.touchedFields:{},errors:b.keepErrors?d.errors:{},isSubmitSuccessful:!!b.keepIsSubmitSuccessful&&d.isSubmitSuccessful,isSubmitting:!1,defaultValues:n})},ar=(a,b)=>aq(G(a)?a(s):a,b),as=a=>{d={...d,...a}},at={control:{register:an,unregister:al,getFieldState:ai,handleSubmit:ap,setError:aj,_subscribe:ak,_runSchema:N,_focusError:ao,_getWatch:V,_getDirty:R,_setValid:C,_setFieldArray:(a,b=[],e,f,g=!0,h=!0)=>{if(f&&e&&!c.disabled){if(t.action=!0,h&&Array.isArray(o(k,a))){let b=e(o(k,a),f.argA,f.argB);g&&p(k,a,b)}if(h&&Array.isArray(o(d.errors,a))){let b,c=e(o(d.errors,a),f.argA,f.argB);g&&p(d.errors,a,c),m(o(b=d.errors,a)).length||I(b,a)}if((w.touchedFields||x.touchedFields)&&h&&Array.isArray(o(d.touchedFields,a))){let b=e(o(d.touchedFields,a),f.argA,f.argB);g&&p(d.touchedFields,a,b)}(w.dirtyFields||x.dirtyFields)&&(d.dirtyFields=L(n,s)),z.state.next({name:a,isDirty:R(a,b),dirtyFields:d.dirtyFields,errors:d.errors,isValid:d.isValid})}else p(s,a,b)},_setDisabledField:am,_setErrors:a=>{d.errors=a,z.state.next({errors:d.errors,isValid:!1})},_getFieldArray:a=>m(o(t.mount?s:n,a,c.shouldUnregister?o(n,a,[]):[])),_reset:aq,_resetDefaultValues:()=>G(c.defaultValues)&&c.defaultValues().then(a=>{ar(a,c.resetOptions),z.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let a of u.unMount){let b=o(k,a);b&&(b._f.refs?b._f.refs.every(a=>!H(a)):!H(b._f.ref))&&al(a)}u.unMount=new Set},_disableForm:a=>{"boolean"==typeof a&&(z.state.next({disabled:a}),Y(k,(b,c)=>{let d=o(k,c);d&&(b.disabled=d._f.disabled||a,Array.isArray(d._f.refs)&&d._f.refs.forEach(b=>{b.disabled=d._f.disabled||a}))},0,!1))},_subjects:z,_proxyFormState:w,get _fields(){return k},get _formValues(){return s},get _state(){return t},set _state(value){t=value},get _defaultValues(){return n},get _names(){return u},set _names(value){u=value},get _formState(){return d},get _options(){return c},set _options(value){c={...c,...value}}},subscribe:a=>(t.mount=!0,x={...x,...a.formState},ak({...a,formState:x})),trigger:ag,register:an,handleSubmit:ap,watch:(a,b)=>G(a)?z.state.subscribe({next:c=>"values"in c&&a(V(void 0,b),c)}):V(a,b,!0),setValue:ab,getValues:ah,reset:ar,resetField:(a,b={})=>{o(k,a)&&(l(b.defaultValue)?ab(a,j(o(n,a))):(ab(a,b.defaultValue),p(n,a,j(b.defaultValue))),b.keepTouched||I(d.touchedFields,a),b.keepDirty||(I(d.dirtyFields,a),d.isDirty=b.defaultValue?R(a,j(o(n,a))):R()),!b.keepError&&(I(d.errors,a),w.isValid&&C()),z.state.next({...d}))},clearErrors:a=>{a&&D(a).forEach(a=>I(d.errors,a)),z.state.next({errors:a?d.errors:{}})},unregister:al,setError:aj,setFocus:(a,b={})=>{let c=o(k,a),d=c&&c._f;if(d){let a=d.refs?d.refs[0]:d.ref;a.focus&&(a.focus(),b.shouldSelect&&G(a.select)&&a.select())}},getFieldState:ai};return{...at,formControl:at}}(a);b.current={...d,formState:k}}let s=b.current.control;return s._options=a,w(()=>{let a=s._subscribe({formState:s._proxyFormState,callback:()=>n({...s._formState}),reRenderRoot:!0});return n(a=>({...a,isReady:!0})),s._formState.isReady=!0,a},[s]),d.default.useEffect(()=>s._disableForm(a.disabled),[s,a.disabled]),d.default.useEffect(()=>{a.mode&&(s._options.mode=a.mode),a.reValidateMode&&(s._options.reValidateMode=a.reValidateMode)},[s,a.mode,a.reValidateMode]),d.default.useEffect(()=>{a.errors&&(s._setErrors(a.errors),s._focusError())},[s,a.errors]),d.default.useEffect(()=>{a.shouldUnregister&&s._subjects.state.next({values:s._getWatch()})},[s,a.shouldUnregister]),d.default.useEffect(()=>{if(s._proxyFormState.isDirty){let a=s._getDirty();a!==k.isDirty&&s._subjects.state.next({isDirty:a})}},[s,k.isDirty]),d.default.useEffect(()=>{a.values&&!A(a.values,c.current)?(s._reset(a.values,{keepFieldsRef:!0,...s._options.resetOptions}),c.current=a.values,n(a=>({...a}))):s._resetDefaultValues()},[s,a.values]),d.default.useEffect(()=>{s._state.mount||(s._setValid(),s._state.mount=!0),s._state.watch&&(s._state.watch=!1,s._subjects.state.next({...s._formState})),s._removeUnmounted()}),b.current.formState=v(k,s),b.current}({resolver:function(a,b,c){if(void 0===c&&(c={}),"_def"in a&&"object"==typeof a._def&&"typeName"in a._def)return function(b,d,e){try{return Promise.resolve(bT(function(){return Promise.resolve(a["sync"===c.mode?"parse":"parseAsync"](b,void 0)).then(function(a){return e.shouldUseNativeValidation&&af({},e),{errors:{},values:c.raw?Object.assign({},b):a}})},function(a){if(Array.isArray(null==a?void 0:a.issues))return{values:{},errors:ag(function(a,b){for(var c={};a.length;){var d=a[0],e=d.code,f=d.message,g=d.path.join(".");if(!c[g])if("unionErrors"in d){var h=d.unionErrors[0].errors[0];c[g]={message:h.message,type:h.code}}else c[g]={message:f,type:e};if("unionErrors"in d&&d.unionErrors.forEach(function(b){return b.errors.forEach(function(b){return a.push(b)})}),b){var i=c[g].types,j=i&&i[d.code];c[g]=C(g,b,c,e,j?[].concat(j,d.message):d.message)}a.shift()}return c}(a.errors,!e.shouldUseNativeValidation&&"all"===e.criteriaMode),e)};throw a}))}catch(a){return Promise.reject(a)}};if("_zod"in a&&"object"==typeof a._zod)return function(b,d,e){try{return Promise.resolve(bT(function(){return Promise.resolve(("sync"===c.mode?bw:by)(a,b,void 0)).then(function(a){return e.shouldUseNativeValidation&&af({},e),{errors:{},values:c.raw?Object.assign({},b):a}})},function(a){if(a instanceof bo)return{values:{},errors:ag(function(a,b){for(var c={};a.length;){var d=a[0],e=d.code,f=d.message,g=d.path.join(".");if(!c[g])if("invalid_union"===d.code&&d.errors.length>0){var h=d.errors[0][0];c[g]={message:h.message,type:h.code}}else c[g]={message:f,type:e};if("invalid_union"===d.code&&d.errors.forEach(function(b){return b.forEach(function(b){return a.push(b)})}),b){var i=c[g].types,j=i&&i[d.code];c[g]=C(g,b,c,e,j?[].concat(j,d.message):d.message)}a.shift()}return c}(a.issues,!e.shouldUseNativeValidation&&"all"===e.criteriaMode),e)};throw a}))}catch(a){return Promise.reject(a)}};throw Error("Invalid input: not a Zod schema")}(kx),defaultValues:{email:""}}),t=async a=>{b(!0);let c=kz(a);bV.toast.promise(c,{loading:"Subscribing..."});try{let a=await c;a?.success&&(n(!0),s.reset(),bV.toast.success("Subscribed successfully",{description:"You have been subscribed to Rathon."}))}catch{bV.toast.error("Failed to subscribe. Please try again.",{description:"There was an error subscribing to Rathon."})}finally{b(!1)}};return k?(0,c.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,c.jsx)("p",{className:"font-bold text-muted-foreground text-sm",children:"You have been subscribed to Rathon."}),(0,c.jsx)("p",{className:"text-muted-foreground text-sm",children:"Thank you for subscribing!"})]}):(0,c.jsx)(b_,{...s,children:(0,c.jsxs)("form",{className:"flex w-full max-w-sm items-center space-x-2",onSubmit:s.handleSubmit(t),children:[(0,c.jsx)(b1,{control:s.control,name:"email",render:({field:b})=>(0,c.jsxs)(b4,{className:"flex flex-col gap-5",children:[(0,c.jsx)(b5,{className:"sr-only font-bold",children:"Email"}),(0,c.jsx)(b6,{children:(0,c.jsx)(kB,{autoComplete:"email",disabled:a,placeholder:"<EMAIL>",type:"email",...b})}),(0,c.jsx)(b7,{})]})}),(0,c.jsx)("div",{className:"flex justify-end",children:(0,c.jsx)(kA.Button,{className:"bg-primary text-primary-foreground",disabled:a,size:"sm",type:"submit",children:a?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(bU,{className:"mr-2 h-4 w-4 animate-spin"}),"Subscribing..."]}):"Subscribe"})})]})})}},2485,(a,b,c)=>{b.exports=a.r(9022)},32514,a=>{"use strict";a.s(["default",()=>aG],32514);var b,c=a.i(69720);let d=(0,a.i(51827).default)("armchair",[["path",{d:"M19 9V6a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v3",key:"irtipd"}],["path",{d:"M3 16a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-5a2 2 0 0 0-4 0v1.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V11a2 2 0 0 0-4 0z",key:"1qyhux"}],["path",{d:"M5 18v2",key:"ppbyun"}],["path",{d:"M19 18v2",key:"gy7782"}]]);var e=a.i(3749),f=a.i(29611),g=a.i(97895),h=a.i(40695),i=a.i(2485),j=a.i(83312),k=a.i(21380),l=a.i(59653),m=a.i(53834),n=a.i(9806),o=a.i(39003),p=a.i(38442),q=a.i(91802),r=a.i(29239),s=a.i(97836),t=a.i(64098),u="dismissableLayer.update",v=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=f.forwardRef((a,d)=>{let{disableOutsidePointerEvents:e=!1,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:i,onInteractOutside:j,onDismiss:k,...n}=a,p=f.useContext(v),[q,r]=f.useState(null),s=q?.ownerDocument??globalThis?.document,[,w]=f.useState({}),z=(0,o.useComposedRefs)(d,a=>r(a)),A=Array.from(p.layers),[B]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=q?A.indexOf(q):-1,E=p.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=(0,t.useCallbackRef)(a),d=f.useRef(!1),e=f.useRef(()=>{});return f.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){y("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...p.branches].some(a=>a.contains(b));F&&!c&&(h?.(a),j?.(a),a.defaultPrevented||k?.())},s),H=function(a,b=globalThis?.document){let c=(0,t.useCallbackRef)(a),d=f.useRef(!1);return f.useEffect(()=>{let a=a=>{a.target&&!d.current&&y("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...p.branches].some(a=>a.contains(b))&&(i?.(a),j?.(a),a.defaultPrevented||k?.())},s);return!function(a,b=globalThis?.document){let c=(0,t.useCallbackRef)(a);f.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===p.layers.size-1&&(g?.(a),!a.defaultPrevented&&k&&(a.preventDefault(),k()))},s),f.useEffect(()=>{if(q)return e&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(b=s.body.style.pointerEvents,s.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(q)),p.layers.add(q),x(),()=>{e&&1===p.layersWithOutsidePointerEventsDisabled.size&&(s.body.style.pointerEvents=b)}},[q,s,e,p]),f.useEffect(()=>()=>{q&&(p.layers.delete(q),p.layersWithOutsidePointerEventsDisabled.delete(q),x())},[q,p]),f.useEffect(()=>{let a=()=>w({});return document.addEventListener(u,a),()=>document.removeEventListener(u,a)},[]),(0,c.jsx)(m.Primitive.div,{...n,ref:z,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:(0,l.composeEventHandlers)(a.onFocusCapture,H.onFocusCapture),onBlurCapture:(0,l.composeEventHandlers)(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:(0,l.composeEventHandlers)(a.onPointerDownCapture,G.onPointerDownCapture)})});function x(){let a=new CustomEvent(u);document.dispatchEvent(a)}function y(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,m.dispatchDiscreteCustomEvent)(e,f):e.dispatchEvent(f)}w.displayName="DismissableLayer",f.forwardRef((a,b)=>{let d=f.useContext(v),e=f.useRef(null),g=(0,o.useComposedRefs)(b,e);return f.useEffect(()=>{let a=e.current;if(a)return d.branches.add(a),()=>{d.branches.delete(a)}},[d.branches]),(0,c.jsx)(m.Primitive.div,{...a,ref:g})}).displayName="DismissableLayerBranch";var z=a.i(71120),A=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),B=f.forwardRef((a,b)=>(0,c.jsx)(m.Primitive.span,{...a,ref:b,style:{...A,...a.style}}));B.displayName="VisuallyHidden";var C="NavigationMenu",[D,E,F]=(0,s.createCollection)(C),[G,H,I]=(0,s.createCollection)(C),[J,K]=(0,k.createContextScope)(C,[F,I]),[L,M]=J(C),[N,O]=J(C),P=f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,value:e,onValueChange:g,defaultValue:h,delayDuration:i=200,skipDelayDuration:j=300,orientation:k="horizontal",dir:l,...q}=a,[r,s]=f.useState(null),t=(0,o.useComposedRefs)(b,a=>s(a)),u=(0,p.useDirection)(l),v=f.useRef(0),w=f.useRef(0),x=f.useRef(0),[y,z]=f.useState(!0),[A,B]=(0,n.useControllableState)({prop:e,onChange:a=>{let b=j>0;""!==a?(window.clearTimeout(x.current),b&&z(!1)):(window.clearTimeout(x.current),x.current=window.setTimeout(()=>z(!0),j)),g?.(a)},defaultProp:h??"",caller:C}),D=f.useCallback(()=>{window.clearTimeout(w.current),w.current=window.setTimeout(()=>B(""),150)},[B]),E=f.useCallback(a=>{window.clearTimeout(w.current),B(a)},[B]),F=f.useCallback(a=>{A===a?window.clearTimeout(w.current):v.current=window.setTimeout(()=>{window.clearTimeout(w.current),B(a)},i)},[A,B,i]);return f.useEffect(()=>()=>{window.clearTimeout(v.current),window.clearTimeout(w.current),window.clearTimeout(x.current)},[]),(0,c.jsx)(R,{scope:d,isRootMenu:!0,value:A,dir:u,orientation:k,rootNavigationMenu:r,onTriggerEnter:a=>{window.clearTimeout(v.current),y?F(a):E(a)},onTriggerLeave:()=>{window.clearTimeout(v.current),D()},onContentEnter:()=>window.clearTimeout(w.current),onContentLeave:D,onItemSelect:a=>{B(b=>b===a?"":a)},onItemDismiss:()=>B(""),children:(0,c.jsx)(m.Primitive.nav,{"aria-label":"Main","data-orientation":k,dir:u,...q,ref:t})})});P.displayName=C;var Q="NavigationMenuSub";f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,value:e,onValueChange:f,defaultValue:g,orientation:h="horizontal",...i}=a,j=M(Q,d),[k,l]=(0,n.useControllableState)({prop:e,onChange:f,defaultProp:g??"",caller:Q});return(0,c.jsx)(R,{scope:d,isRootMenu:!1,value:k,dir:j.dir,orientation:h,rootNavigationMenu:j.rootNavigationMenu,onTriggerEnter:a=>l(a),onItemSelect:a=>l(a),onItemDismiss:()=>l(""),children:(0,c.jsx)(m.Primitive.div,{"data-orientation":h,...i,ref:b})})}).displayName=Q;var R=a=>{let{scope:b,isRootMenu:d,rootNavigationMenu:e,dir:g,orientation:h,children:i,value:j,onItemSelect:k,onItemDismiss:l,onTriggerEnter:m,onTriggerLeave:n,onContentEnter:o,onContentLeave:p}=a,[q,s]=f.useState(null),[u,v]=f.useState(new Map),[w,x]=f.useState(null);return(0,c.jsx)(L,{scope:b,isRootMenu:d,rootNavigationMenu:e,value:j,previousValue:function(a){let b=f.useRef({value:a,previous:a});return f.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}(j),baseId:(0,r.useId)(),dir:g,orientation:h,viewport:q,onViewportChange:s,indicatorTrack:w,onIndicatorTrackChange:x,onTriggerEnter:(0,t.useCallbackRef)(m),onTriggerLeave:(0,t.useCallbackRef)(n),onContentEnter:(0,t.useCallbackRef)(o),onContentLeave:(0,t.useCallbackRef)(p),onItemSelect:(0,t.useCallbackRef)(k),onItemDismiss:(0,t.useCallbackRef)(l),onViewportContentChange:f.useCallback((a,b)=>{v(c=>(c.set(a,b),new Map(c)))},[]),onViewportContentRemove:f.useCallback(a=>{v(b=>b.has(a)?(b.delete(a),new Map(b)):b)},[]),children:(0,c.jsx)(D.Provider,{scope:b,children:(0,c.jsx)(N,{scope:b,items:u,children:i})})})},S="NavigationMenuList",T=f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,...e}=a,f=M(S,d),g=(0,c.jsx)(m.Primitive.ul,{"data-orientation":f.orientation,...e,ref:b});return(0,c.jsx)(m.Primitive.div,{style:{position:"relative"},ref:f.onIndicatorTrackChange,children:(0,c.jsx)(D.Slot,{scope:d,children:f.isRootMenu?(0,c.jsx)(ak,{asChild:!0,children:g}):g})})});T.displayName=S;var U="NavigationMenuItem",[V,W]=J(U),X=f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,value:e,...g}=a,h=(0,r.useId)(),i=f.useRef(null),j=f.useRef(null),k=f.useRef(null),l=f.useRef(()=>{}),n=f.useRef(!1),o=f.useCallback((a="start")=>{if(i.current){l.current();let b=an(i.current);b.length&&ao("start"===a?b:b.reverse())}},[]),p=f.useCallback(()=>{if(i.current){let a=an(i.current);a.length&&(l.current=function(a){return a.forEach(a=>{a.dataset.tabindex=a.getAttribute("tabindex")||"",a.setAttribute("tabindex","-1")}),()=>{a.forEach(a=>{let b=a.dataset.tabindex;a.setAttribute("tabindex",b)})}}(a))}},[]);return(0,c.jsx)(V,{scope:d,value:e||h||"LEGACY_REACT_AUTO_VALUE",triggerRef:j,contentRef:i,focusProxyRef:k,wasEscapeCloseRef:n,onEntryKeyDown:o,onFocusProxyEnter:o,onRootContentClose:p,onContentFocusOutside:p,children:(0,c.jsx)(m.Primitive.li,{...g,ref:b})})});X.displayName=U;var Y="NavigationMenuTrigger",Z=f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,disabled:e,...g}=a,h=M(Y,a.__scopeNavigationMenu),i=W(Y,a.__scopeNavigationMenu),j=f.useRef(null),k=(0,o.useComposedRefs)(j,i.triggerRef,b),n=ar(h.baseId,i.value),p=as(h.baseId,i.value),q=f.useRef(!1),r=f.useRef(!1),s=i.value===h.value;return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(D.ItemSlot,{scope:d,value:i.value,children:(0,c.jsx)(am,{asChild:!0,children:(0,c.jsx)(m.Primitive.button,{id:n,disabled:e,"data-disabled":e?"":void 0,"data-state":aq(s),"aria-expanded":s,"aria-controls":p,...g,ref:k,onPointerEnter:(0,l.composeEventHandlers)(a.onPointerEnter,()=>{r.current=!1,i.wasEscapeCloseRef.current=!1}),onPointerMove:(0,l.composeEventHandlers)(a.onPointerMove,at(()=>{e||r.current||i.wasEscapeCloseRef.current||q.current||(h.onTriggerEnter(i.value),q.current=!0)})),onPointerLeave:(0,l.composeEventHandlers)(a.onPointerLeave,at(()=>{e||(h.onTriggerLeave(),q.current=!1)})),onClick:(0,l.composeEventHandlers)(a.onClick,()=>{h.onItemSelect(i.value),r.current=s}),onKeyDown:(0,l.composeEventHandlers)(a.onKeyDown,a=>{let b={horizontal:"ArrowDown",vertical:"rtl"===h.dir?"ArrowLeft":"ArrowRight"}[h.orientation];s&&a.key===b&&(i.onEntryKeyDown(),a.preventDefault())})})})}),s&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(B,{"aria-hidden":!0,tabIndex:0,ref:i.focusProxyRef,onFocus:a=>{let b=i.contentRef.current,c=a.relatedTarget,d=c===j.current,e=b?.contains(c);(d||!e)&&i.onFocusProxyEnter(d?"start":"end")}}),h.viewport&&(0,c.jsx)("span",{"aria-owns":p})]})]})});Z.displayName=Y;var $="navigationMenu.linkSelect",_=f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,active:e,onSelect:f,...g}=a;return(0,c.jsx)(am,{asChild:!0,children:(0,c.jsx)(m.Primitive.a,{"data-active":e?"":void 0,"aria-current":e?"page":void 0,...g,ref:b,onClick:(0,l.composeEventHandlers)(a.onClick,a=>{let b=a.target,c=new CustomEvent($,{bubbles:!0,cancelable:!0});if(b.addEventListener($,a=>f?.(a),{once:!0}),(0,m.dispatchDiscreteCustomEvent)(b,c),!c.defaultPrevented&&!a.metaKey){let a=new CustomEvent(af,{bubbles:!0,cancelable:!0});(0,m.dispatchDiscreteCustomEvent)(b,a)}},{checkForDefaultPrevented:!1})})})});_.displayName="NavigationMenuLink";var aa="NavigationMenuIndicator";f.forwardRef((a,b)=>{let{forceMount:d,...e}=a,f=M(aa,a.__scopeNavigationMenu),g=!!f.value;return f.indicatorTrack?j.default.createPortal((0,c.jsx)(q.Presence,{present:d||g,children:(0,c.jsx)(ab,{...e,ref:b})}),f.indicatorTrack):null}).displayName=aa;var ab=f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,...e}=a,g=M(aa,d),h=E(d),[i,j]=f.useState(null),[k,l]=f.useState(null),n="horizontal"===g.orientation,o=!!g.value;f.useEffect(()=>{let a=h(),b=a.find(a=>a.value===g.value)?.ref.current;b&&j(b)},[h,g.value]);let p=()=>{i&&l({size:n?i.offsetWidth:i.offsetHeight,offset:n?i.offsetLeft:i.offsetTop})};return ap(i,p),ap(g.indicatorTrack,p),k?(0,c.jsx)(m.Primitive.div,{"aria-hidden":!0,"data-state":o?"visible":"hidden","data-orientation":g.orientation,...e,ref:b,style:{position:"absolute",...n?{left:0,width:k.size+"px",transform:`translateX(${k.offset}px)`}:{top:0,height:k.size+"px",transform:`translateY(${k.offset}px)`},...e.style}}):null}),ac="NavigationMenuContent",ad=f.forwardRef((a,b)=>{let{forceMount:d,...e}=a,f=M(ac,a.__scopeNavigationMenu),g=W(ac,a.__scopeNavigationMenu),h=(0,o.useComposedRefs)(g.contentRef,b),i=g.value===f.value,j={value:g.value,triggerRef:g.triggerRef,focusProxyRef:g.focusProxyRef,wasEscapeCloseRef:g.wasEscapeCloseRef,onContentFocusOutside:g.onContentFocusOutside,onRootContentClose:g.onRootContentClose,...e};return f.viewport?(0,c.jsx)(ae,{forceMount:d,...j,ref:h}):(0,c.jsx)(q.Presence,{present:d||i,children:(0,c.jsx)(ag,{"data-state":aq(i),...j,ref:h,onPointerEnter:(0,l.composeEventHandlers)(a.onPointerEnter,f.onContentEnter),onPointerLeave:(0,l.composeEventHandlers)(a.onPointerLeave,at(f.onContentLeave)),style:{pointerEvents:!i&&f.isRootMenu?"none":void 0,...j.style}})})});ad.displayName=ac;var ae=f.forwardRef((a,b)=>{let{onViewportContentChange:c,onViewportContentRemove:d}=M(ac,a.__scopeNavigationMenu);return(0,z.useLayoutEffect)(()=>{c(a.value,{ref:b,...a})},[a,b,c]),(0,z.useLayoutEffect)(()=>()=>d(a.value),[a.value,d]),null}),af="navigationMenu.rootContentDismiss",ag=f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,value:e,triggerRef:g,focusProxyRef:h,wasEscapeCloseRef:i,onRootContentClose:j,onContentFocusOutside:k,...m}=a,n=M(ac,d),p=f.useRef(null),q=(0,o.useComposedRefs)(p,b),r=ar(n.baseId,e),s=as(n.baseId,e),t=E(d),u=f.useRef(null),{onItemDismiss:v}=n;f.useEffect(()=>{let a=p.current;if(n.isRootMenu&&a){let b=()=>{v(),j(),a.contains(document.activeElement)&&g.current?.focus()};return a.addEventListener(af,b),()=>a.removeEventListener(af,b)}},[n.isRootMenu,a.value,g,v,j]);let x=f.useMemo(()=>{let a=t().map(a=>a.value);"rtl"===n.dir&&a.reverse();let b=a.indexOf(n.value),c=a.indexOf(n.previousValue),d=e===n.value,f=c===a.indexOf(e);if(!d&&!f)return u.current;let g=(()=>{if(b!==c){if(d&&-1!==c)return b>c?"from-end":"from-start";if(f&&-1!==b)return b>c?"to-start":"to-end"}return null})();return u.current=g,g},[n.previousValue,n.value,n.dir,t,e]);return(0,c.jsx)(ak,{asChild:!0,children:(0,c.jsx)(w,{id:s,"aria-labelledby":r,"data-motion":x,"data-orientation":n.orientation,...m,ref:q,disableOutsidePointerEvents:!1,onDismiss:()=>{let a=new Event(af,{bubbles:!0,cancelable:!0});p.current?.dispatchEvent(a)},onFocusOutside:(0,l.composeEventHandlers)(a.onFocusOutside,a=>{k();let b=a.target;n.rootNavigationMenu?.contains(b)&&a.preventDefault()}),onPointerDownOutside:(0,l.composeEventHandlers)(a.onPointerDownOutside,a=>{let b=a.target,c=t().some(a=>a.ref.current?.contains(b)),d=n.isRootMenu&&n.viewport?.contains(b);(c||d||!n.isRootMenu)&&a.preventDefault()}),onKeyDown:(0,l.composeEventHandlers)(a.onKeyDown,a=>{let b=a.altKey||a.ctrlKey||a.metaKey;if("Tab"===a.key&&!b){let b=an(a.currentTarget),c=document.activeElement,d=b.findIndex(a=>a===c);ao(a.shiftKey?b.slice(0,d).reverse():b.slice(d+1,b.length))?a.preventDefault():h.current?.focus()}}),onEscapeKeyDown:(0,l.composeEventHandlers)(a.onEscapeKeyDown,a=>{i.current=!0})})})}),ah="NavigationMenuViewport",ai=f.forwardRef((a,b)=>{let{forceMount:d,...e}=a,f=!!M(ah,a.__scopeNavigationMenu).value;return(0,c.jsx)(q.Presence,{present:d||f,children:(0,c.jsx)(aj,{...e,ref:b})})});ai.displayName=ah;var aj=f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,children:e,...g}=a,h=M(ah,d),i=(0,o.useComposedRefs)(b,h.onViewportChange),j=O(ac,a.__scopeNavigationMenu),[k,n]=f.useState(null),[p,r]=f.useState(null),s=k?k?.width+"px":void 0,t=k?k?.height+"px":void 0,u=!!h.value,v=u?h.value:h.previousValue;return ap(p,()=>{p&&n({width:p.offsetWidth,height:p.offsetHeight})}),(0,c.jsx)(m.Primitive.div,{"data-state":aq(u),"data-orientation":h.orientation,...g,ref:i,style:{pointerEvents:!u&&h.isRootMenu?"none":void 0,"--radix-navigation-menu-viewport-width":s,"--radix-navigation-menu-viewport-height":t,...g.style},onPointerEnter:(0,l.composeEventHandlers)(a.onPointerEnter,h.onContentEnter),onPointerLeave:(0,l.composeEventHandlers)(a.onPointerLeave,at(h.onContentLeave)),children:Array.from(j.items).map(([a,{ref:b,forceMount:d,...e}])=>{let f=v===a;return(0,c.jsx)(q.Presence,{present:d||f,children:(0,c.jsx)(ag,{...e,ref:(0,o.composeRefs)(b,a=>{f&&a&&r(a)})})},a)})})}),ak=f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,...e}=a,f=M("FocusGroup",d);return(0,c.jsx)(G.Provider,{scope:d,children:(0,c.jsx)(G.Slot,{scope:d,children:(0,c.jsx)(m.Primitive.div,{dir:f.dir,...e,ref:b})})})}),al=["ArrowRight","ArrowLeft","ArrowUp","ArrowDown"],am=f.forwardRef((a,b)=>{let{__scopeNavigationMenu:d,...e}=a,f=H(d),g=M("FocusGroupItem",d);return(0,c.jsx)(G.ItemSlot,{scope:d,children:(0,c.jsx)(m.Primitive.button,{...e,ref:b,onKeyDown:(0,l.composeEventHandlers)(a.onKeyDown,a=>{if(["Home","End",...al].includes(a.key)){let b=f().map(a=>a.ref.current);if(["rtl"===g.dir?"ArrowRight":"ArrowLeft","ArrowUp","End"].includes(a.key)&&b.reverse(),al.includes(a.key)){let c=b.indexOf(a.currentTarget);b=b.slice(c+1)}setTimeout(()=>ao(b)),a.preventDefault()}})})})});function an(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function ao(a){let b=document.activeElement;return a.some(a=>a===b||(a.focus(),document.activeElement!==b))}function ap(a,b){let c=(0,t.useCallbackRef)(b);(0,z.useLayoutEffect)(()=>{let b=0;if(a){let d=new ResizeObserver(()=>{cancelAnimationFrame(b),b=window.requestAnimationFrame(c)});return d.observe(a),()=>{window.cancelAnimationFrame(b),d.unobserve(a)}}},[a,c])}function aq(a){return a?"open":"closed"}function ar(a,b){return`${a}-trigger-${b}`}function as(a,b){return`${a}-content-${b}`}function at(a){return b=>"mouse"===b.pointerType?a(b):void 0}var au=a.i(42261),av=a.i(81533);function aw({className:a,children:b,viewport:d=!0,...e}){return(0,c.jsxs)(P,{className:(0,g.cn)("group/navigation-menu relative flex max-w-max flex-1 items-center justify-center",a),"data-slot":"navigation-menu","data-viewport":d,...e,children:[b,d&&(0,c.jsx)(aC,{})]})}function ax({className:a,...b}){return(0,c.jsx)(T,{className:(0,g.cn)("group flex flex-1 list-none items-center justify-center gap-1",a),"data-slot":"navigation-menu-list",...b})}function ay({className:a,...b}){return(0,c.jsx)(X,{className:(0,g.cn)("relative",a),"data-slot":"navigation-menu-item",...b})}let az=(0,au.cva)("group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 font-medium text-sm outline-none transition-[color,box-shadow] hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:outline-1 focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 data-[state=open]:bg-accent/50 data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:hover:bg-accent");function aA({className:a,children:b,...d}){return(0,c.jsxs)(Z,{className:(0,g.cn)(az(),"group",a),"data-slot":"navigation-menu-trigger",...d,children:[b," ",(0,c.jsx)(av.ChevronDownIcon,{"aria-hidden":"true",className:"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180"})]})}function aB({className:a,...b}){return(0,c.jsx)(ad,{className:(0,g.cn)("data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out md:absolute md:w-auto","group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 **:data-[slot=navigation-menu-link]:focus:outline-none **:data-[slot=navigation-menu-link]:focus:ring-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in",a),"data-slot":"navigation-menu-content",...b})}function aC({className:a,...b}){return(0,c.jsx)("div",{className:(0,g.cn)("absolute top-full left-0 isolate z-50 flex justify-center"),children:(0,c.jsx)(ai,{className:(0,g.cn)("data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full origin-top-center overflow-hidden rounded-2xl border-0 bg-popover text-popover-foreground shadow data-[state=closed]:animate-out data-[state=open]:animate-in md:w-[var(--radix-navigation-menu-viewport-width)]",a),"data-slot":"navigation-menu-viewport",...b})})}function aD({className:a,...b}){return(0,c.jsx)(_,{className:(0,g.cn)("flex flex-col gap-1 rounded-sm p-2 text-sm outline-none transition-all hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:outline-1 focus-visible:ring-[3px] focus-visible:ring-ring/50 data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent [&_svg:not([class*='size-'])]:size-4 [&_svg:not([class*='text-'])]:text-muted-foreground",a),"data-slot":"navigation-menu-link",...b})}var aE=a.i(91300);function aF({className:a,...b}){let d=(0,i.usePathname)();return(0,c.jsxs)("nav",{className:(0,g.cn)("items-center gap-0.5",a),...b,children:[(0,c.jsx)(aw,{children:(0,c.jsx)(ax,{children:(0,c.jsxs)(ay,{className:"rounded-2xl",children:[(0,c.jsx)(aA,{"aria-label":"Open blog links",className:"flex h-7 cursor-pointer items-center rounded-full bg-transparent px-3 py-4 font-normal text-md text-primary/80 transition-all duration-300 hover:bg-accent hover:text-primary dark:hover:bg-accent/50",children:"Features"}),(0,c.jsx)(aB,{className:"bg-muted/5",children:(0,c.jsxs)("div",{className:"grid w-[500px] p-4 lg:w-[600px]",children:[(0,c.jsx)("p",{className:"font-medium text-muted-foreground capitalize tracking-tighter",children:"Features"}),(0,c.jsx)("div",{className:"grid grid-cols-2 gap-6 py-6",children:aE.features.map(a=>(0,c.jsx)(aD,{asChild:!0,className:"group rounded-xl p-0 hover:bg-transparent",children:(0,c.jsx)(e.default,{href:"/",children:(0,c.jsxs)("div",{className:"flex items-center gap-4",children:[(0,c.jsx)("div",{className:"rounded-lg bg-muted p-3 transition-all duration-300 group-hover:bg-brand-500 dark:group-hover:bg-brand-500",children:(0,c.jsx)(a.icon,{className:"block size-5 transition-all duration-300 group-hover:text-white dark:group-hover:text-black"})}),(0,c.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,c.jsx)("div",{className:"font-medium text-md capitalize leading-none",children:a.title}),(0,c.jsx)("p",{className:"text-muted-foreground text-sm",children:a.description})]})]})})},a.title))})]})})]})})}),aE.navItems.map(a=>(0,c.jsx)(h.Button,{asChild:!0,className:"rounded-full bg-transparent hover:bg-transparent dark:hover:bg-transparent",size:"sm",variant:"ghost",children:(0,c.jsx)(e.default,{className:(0,g.cn)("font-normal text-md text-primary/80 transition-all duration-300 hover:text-primary",d===a.href&&"text-primary"),href:a.href,children:a.label})},a.href))]})}function aG(){let[a,b]=(0,f.useState)(!1);return(0,f.useEffect)(()=>{let a=()=>{b(window.scrollY>60)};return window.addEventListener("scroll",a),()=>window.removeEventListener("scroll",a)},[]),(0,c.jsx)("header",{className:"fixed top-4 right-0 left-0 z-50 flex w-full translate-y-0 justify-center opacity-100 transition-all duration-500 ease-out",children:(0,c.jsxs)("nav",{className:(0,g.cn)("relative mx-2 flex h-[60px] w-full max-w-6xl select-none items-center justify-between rounded-full bg-transparent px-1.5 py-2 transition-all duration-200 ease-in-out sm:mx-4 xl:grid xl:grid-cols-3 xl:gap-8",a&&"bg-background/40 backdrop-blur-lg supports-backdrop-blur:bg-background/90"),children:[(0,c.jsx)("div",{className:"flex items-center gap-4 xl:justify-start",children:(0,c.jsx)("div",{className:"flex translate-y-0 items-center gap-1.5 px-3 opacity-100 transition-all delay-75 duration-500 ease-out",children:(0,c.jsxs)(e.default,{className:"flex items-center gap-1.5",href:"/",children:[(0,c.jsx)(d,{className:"block size-6 text-brand-600"}),(0,c.jsx)("span",{className:"pb-[1.5px] font-medium text-lg",children:"Better Flow"})]})})}),(0,c.jsx)(aF,{className:"hidden lg:flex"}),(0,c.jsx)("div",{className:"flex items-center justify-end gap-2 xl:justify-end",children:(0,c.jsx)(h.Button,{className:"rounded-full bg-brand-500 hover:bg-brand-400",children:"Book Demo"})})]})})}}];

//# sourceMappingURL=_708c6159._.js.map