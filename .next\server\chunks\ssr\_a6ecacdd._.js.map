{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/features/book/cal-book.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Cal, { getCalApi } from '@calcom/embed-react';\r\nimport { useEffect } from 'react';\r\nexport default function CalBook() {\r\n  useEffect(() => {\r\n    (async () => {\r\n      const cal = await getCalApi({ namespace: 'better-flow' });\r\n      cal('ui', {\r\n        theme: 'dark',\r\n        cssVarsPerTheme: { dark: { 'cal-brand': '#ebff0a' } },\r\n        hideEventTypeDetails: false,\r\n        layout: 'month_view',\r\n      });\r\n    })();\r\n  }, []);\r\n  return (\r\n    <Cal\r\n      calLink=\"rathon-webdev/better-flow\"\r\n      config={{ layout: 'month_view', theme: 'dark' }}\r\n      namespace=\"better-flow\"\r\n      style={{ width: '100%', height: '100%', overflow: 'scroll' }}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAIe,SAAS;IACtB,IAAA,iVAAS,EAAC;QACR,CAAC;YACC,MAAM,MAAM,MAAM,IAAA,uRAAS,EAAC;gBAAE,WAAW;YAAc;YACvD,IAAI,MAAM;gBACR,OAAO;gBACP,iBAAiB;oBAAE,MAAM;wBAAE,aAAa;oBAAU;gBAAE;gBACpD,sBAAsB;gBACtB,QAAQ;YACV;QACF,CAAC;IACH,GAAG,EAAE;IACL,qBACE,6WAAC,qRAAG;QACF,SAAQ;QACR,QAAQ;YAAE,QAAQ;YAAc,OAAO;QAAO;QAC9C,WAAU;QACV,OAAO;YAAE,OAAO;YAAQ,QAAQ;YAAQ,UAAU;QAAS;;;;;;AAGjE", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/%40calcom%2Bembed-react%401.5.3_r_02a4af5fe065d44ebb2195a0c9f3b36b/node_modules/%40calcom/embed-react/dist/Cal.es.mjs"], "sourcesContent": ["import { useState as C, useEffect as g, useRef as p } from \"react\";\nimport { jsx as w } from \"react/jsx-runtime\";\nconst b = \"https://app.cal.com/embed/embed.js\";\nfunction m(s = b) {\n  (function(r, e, l) {\n    let t = function(n, i) {\n      n.q.push(i);\n    }, o = r.document;\n    r.Cal = r.Cal || function() {\n      let n = r.Cal, i = arguments;\n      if (n.loaded || (n.ns = {}, n.q = n.q || [], o.head.appendChild(o.createElement(\"script\")).src = e, n.loaded = !0), i[0] === l) {\n        const u = function() {\n          t(u, arguments);\n        }, c = i[1];\n        u.q = u.q || [], typeof c == \"string\" ? (n.ns[c] = n.ns[c] || u, t(n.ns[c], i), t(n, [\"initNamespace\", c])) : t(n, i);\n        return;\n      }\n      t(n, i);\n    };\n  })(\n    window,\n    //! Replace it with \"https://cal.com/embed.js\" or the URL where you have embed.js installed\n    s,\n    \"init\"\n  );\n  /*!  Copying ends here. */\n  return window.Cal;\n}\nm.toString();\nfunction q(s) {\n  const [r, e] = C();\n  return g(() => {\n    e(() => m(s));\n  }, []), r;\n}\nconst h = function(r) {\n  const {\n    calLink: e,\n    calOrigin: l,\n    namespace: t = \"\",\n    config: o,\n    initConfig: n = {},\n    embedJsUrl: i,\n    ...u\n  } = r;\n  if (!e)\n    throw new Error(\"calLink is required\");\n  const c = p(!1), a = q(i), f = p(null);\n  return g(() => {\n    if (!a || c.current || !f.current)\n      return;\n    c.current = !0;\n    const d = f.current;\n    t ? (a(\"init\", t, {\n      ...n,\n      origin: l\n    }), a.ns[t](\"inline\", {\n      elementOrSelector: d,\n      calLink: e,\n      config: o\n    })) : (a(\"init\", {\n      ...n,\n      origin: l\n    }), a(\"inline\", {\n      elementOrSelector: d,\n      calLink: e,\n      config: o\n    }));\n  }, [a, e, o, t, l, n]), a ? /* @__PURE__ */ w(\"div\", {\n    ref: f,\n    ...u\n  }) : null;\n}, R = h;\nfunction j(s) {\n  const r = typeof s == \"string\" ? { embedJsUrl: s } : s ?? {}, { namespace: e = \"\", embedJsUrl: l } = r;\n  return new Promise(function t(o) {\n    const n = m(l);\n    n(\"init\", e);\n    const i = e ? n.ns[e] : n;\n    if (!i) {\n      setTimeout(() => {\n        t(o);\n      }, 50);\n      return;\n    }\n    o(i);\n  });\n}\nexport {\n  R as default,\n  j as getCalApi\n};\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AACA,MAAM,IAAI;AACV,SAAS,EAAE,IAAI,CAAC;IACd,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;QACf,IAAI,IAAI,SAAS,CAAC,EAAE,CAAC;YACnB,EAAE,CAAC,CAAC,IAAI,CAAC;QACX,GAAG,IAAI,EAAE,QAAQ;QACjB,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI;YACf,IAAI,IAAI,EAAE,GAAG,EAAE,IAAI;YACnB,IAAI,EAAE,MAAM,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE,aAAa,CAAC,WAAW,GAAG,GAAG,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG;gBAC9H,MAAM,IAAI;oBACR,EAAE,GAAG;gBACP,GAAG,IAAI,CAAC,CAAC,EAAE;gBACX,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,OAAO,KAAK,WAAW,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,IAAI,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG;oBAAC;oBAAiB;iBAAE,CAAC,IAAI,EAAE,GAAG;gBACnH;YACF;YACA,EAAE,GAAG;QACP;IACF,CAAC,EACC,QACA,2FAA2F;IAC3F,GACA;IAEF,wBAAwB,GACxB,OAAO,OAAO,GAAG;AACnB;AACA,EAAE,QAAQ;AACV,SAAS,EAAE,CAAC;IACV,MAAM,CAAC,GAAG,EAAE,GAAG,IAAA,gVAAC;IAChB,OAAO,IAAA,iVAAC,EAAC;QACP,EAAE,IAAM,EAAE;IACZ,GAAG,EAAE,GAAG;AACV;AACA,MAAM,IAAI,SAAS,CAAC;IAClB,MAAM,EACJ,SAAS,CAAC,EACV,WAAW,CAAC,EACZ,WAAW,IAAI,EAAE,EACjB,QAAQ,CAAC,EACT,YAAY,IAAI,CAAC,CAAC,EAClB,YAAY,CAAC,EACb,GAAG,GACJ,GAAG;IACJ,IAAI,CAAC,GACH,MAAM,IAAI,MAAM;IAClB,MAAM,IAAI,IAAA,8UAAC,EAAC,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAA,8UAAC,EAAC;IACjC,OAAO,IAAA,iVAAC,EAAC;QACP,IAAI,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC,EAAE,OAAO,EAC/B;QACF,EAAE,OAAO,GAAG,CAAC;QACb,MAAM,IAAI,EAAE,OAAO;QACnB,IAAI,CAAC,EAAE,QAAQ,GAAG;YAChB,GAAG,CAAC;YACJ,QAAQ;QACV,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,UAAU;YACpB,mBAAmB;YACnB,SAAS;YACT,QAAQ;QACV,EAAE,IAAI,CAAC,EAAE,QAAQ;YACf,GAAG,CAAC;YACJ,QAAQ;QACV,IAAI,EAAE,UAAU;YACd,mBAAmB;YACnB,SAAS;YACT,QAAQ;QACV,EAAE;IACJ,GAAG;QAAC;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE,GAAG,IAAI,aAAa,GAAG,IAAA,6VAAC,EAAC,OAAO;QACnD,KAAK;QACL,GAAG,CAAC;IACN,KAAK;AACP,GAAG,IAAI;AACP,SAAS,EAAE,CAAC;IACV,MAAM,IAAI,OAAO,KAAK,WAAW;QAAE,YAAY;IAAE,IAAI,KAAK,CAAC,GAAG,EAAE,WAAW,IAAI,EAAE,EAAE,YAAY,CAAC,EAAE,GAAG;IACrG,OAAO,IAAI,QAAQ,SAAS,EAAE,CAAC;QAC7B,MAAM,IAAI,EAAE;QACZ,EAAE,QAAQ;QACV,MAAM,IAAI,IAAI,EAAE,EAAE,CAAC,EAAE,GAAG;QACxB,IAAI,CAAC,GAAG;YACN,WAAW;gBACT,EAAE;YACJ,GAAG;YACH;QACF;QACA,EAAE;IACJ;AACF", "ignoreList": [0], "debugId": null}}]}