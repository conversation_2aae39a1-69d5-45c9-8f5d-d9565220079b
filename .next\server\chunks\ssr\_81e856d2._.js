module.exports=[55289,(a,b,c)=>{"use strict";c._=function(a){return a&&a.__esModule?a:{default:a}}},12109,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"warnOnce",{enumerable:!0,get:function(){return d}});let d=a=>{}},47051,(a,b,c)=>{"use strict";function d(a){let{widthInt:b,heightInt:c,blurWidth:d,blurHeight:e,blurDataURL:f,objectFit:g}=a,h=d?40*d:b,i=e?40*e:c,j=h&&i?"viewBox='0 0 "+h+" "+i+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+j+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(j?"none":"contain"===g?"xMidYMid":"cover"===g?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+f+"'/%3E%3C/svg%3E"}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"getImageBlurSvg",{enumerable:!0,get:function(){return d}})},54613,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{VALID_LOADERS:function(){return d},imageConfigDefault:function(){return e}});let d=["default","imgix","cloudinary","akamai","custom"],e={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},47348,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"getImgProps",{enumerable:!0,get:function(){return i}}),a.r(12109);let d=a.r(47051),e=a.r(54613),f=["-moz-initial","fill","none","scale-down",void 0];function g(a){return void 0!==a.default}function h(a){return void 0===a?a:"number"==typeof a?Number.isFinite(a)?a:NaN:"string"==typeof a&&/^[0-9]+$/.test(a)?parseInt(a,10):NaN}function i(a,b){var c,i;let j,k,l,{src:m,sizes:n,unoptimized:o=!1,priority:p=!1,loading:q,className:r,quality:s,width:t,height:u,fill:v=!1,style:w,overrideSrc:x,onLoad:y,onLoadingComplete:z,placeholder:A="empty",blurDataURL:B,fetchPriority:C,decoding:D="async",layout:E,objectFit:F,objectPosition:G,lazyBoundary:H,lazyRoot:I,...J}=a,{imgConf:K,showAltText:L,blurComplete:M,defaultLoader:N}=b,O=K||e.imageConfigDefault;if("allSizes"in O)j=O;else{let a=[...O.deviceSizes,...O.imageSizes].sort((a,b)=>a-b),b=O.deviceSizes.sort((a,b)=>a-b),d=null==(c=O.qualities)?void 0:c.sort((a,b)=>a-b);j={...O,allSizes:a,deviceSizes:b,qualities:d}}if(void 0===N)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let P=J.loader||N;delete J.loader,delete J.srcSet;let Q="__next_img_default"in P;if(Q){if("custom"===j.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let a=P;P=b=>{let{config:c,...d}=b;return a(d)}}if(E){"fill"===E&&(v=!0);let a={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];a&&(w={...w,...a});let b={responsive:"100vw",fill:"100vw"}[E];b&&!n&&(n=b)}let R="",S=h(t),T=h(u);if((i=m)&&"object"==typeof i&&(g(i)||void 0!==i.src)){let a=g(m)?m.default:m;if(!a.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!a.height||!a.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(a)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(k=a.blurWidth,l=a.blurHeight,B=B||a.blurDataURL,R=a.src,!v)if(S||T){if(S&&!T){let b=S/a.width;T=Math.round(a.height*b)}else if(!S&&T){let b=T/a.height;S=Math.round(a.width*b)}}else S=a.width,T=a.height}let U=!p&&("lazy"===q||void 0===q);(!(m="string"==typeof m?m:R)||m.startsWith("data:")||m.startsWith("blob:"))&&(o=!0,U=!1),j.unoptimized&&(o=!0),Q&&!j.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(o=!0);let V=h(s),W=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:F,objectPosition:G}:{},L?{}:{color:"transparent"},w),X=M||"empty"===A?null:"blur"===A?'url("data:image/svg+xml;charset=utf-8,'+(0,d.getImageBlurSvg)({widthInt:S,heightInt:T,blurWidth:k,blurHeight:l,blurDataURL:B||"",objectFit:W.objectFit})+'")':'url("'+A+'")',Y=f.includes(W.objectFit)?"fill"===W.objectFit?"100% 100%":"cover":W.objectFit,Z=X?{backgroundSize:Y,backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:X}:{},$=function(a){let{config:b,src:c,unoptimized:d,width:e,quality:f,sizes:g,loader:h}=a;if(d)return{src:c,srcSet:void 0,sizes:void 0};let{widths:i,kind:j}=function(a,b,c){let{deviceSizes:d,allSizes:e}=a;if(c){let a=/(^|\s)(1?\d?\d)vw/g,b=[];for(let d;d=a.exec(c);)b.push(parseInt(d[2]));if(b.length){let a=.01*Math.min(...b);return{widths:e.filter(b=>b>=d[0]*a),kind:"w"}}return{widths:e,kind:"w"}}return"number"!=typeof b?{widths:d,kind:"w"}:{widths:[...new Set([b,2*b].map(a=>e.find(b=>b>=a)||e[e.length-1]))],kind:"x"}}(b,e,g),k=i.length-1;return{sizes:g||"w"!==j?g:"100vw",srcSet:i.map((a,d)=>h({config:b,src:c,quality:f,width:a})+" "+("w"===j?a:d+1)+j).join(", "),src:h({config:b,src:c,quality:f,width:i[k]})}}({config:j,src:m,unoptimized:o,width:S,quality:V,sizes:n,loader:P});return{props:{...J,loading:U?"lazy":q,fetchPriority:C,width:S,height:T,decoding:D,className:r,style:{...W,...Z},sizes:$.sizes,srcSet:$.srcSet,src:x||$.src},meta:{unoptimized:o,priority:p,placeholder:A,fill:v}}}},35530,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(62e3);a.n(d("[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/image-component.js <module evaluation>"))},79722,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(62e3);a.n(d("[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/image-component.js"))},10206,a=>{"use strict";a.i(35530);var b=a.i(79722);a.n(b)},98607,(a,b,c)=>{"use strict";function d(a){var b;let{config:c,src:d,width:e,quality:f}=a,g=f||(null==(b=c.qualities)?void 0:b.reduce((a,b)=>Math.abs(b-75)<Math.abs(a-75)?b:a))||75;return c.path+"?url="+encodeURIComponent(d)+"&w="+e+"&q="+g+(d.startsWith("/_next/static/media/"),"")}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return e}}),d.__next_img_default=!0;let e=d},65784,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{default:function(){return i},getImageProps:function(){return h}});let d=a.r(55289),e=a.r(47348),f=a.r(10206),g=d._(a.r(98607));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},4573,(a,b,c)=>{b.exports=a.r(65784)},15485,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(62e3);a.n(d("[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js <module evaluation>"))},24710,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(62e3);a.n(d("[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js"))},74464,a=>{"use strict";a.i(15485);var b=a.i(24710);a.n(b)},31255,a=>{a.v("/_next/static/media/gradient.ae841f47.webp")},58903,a=>{"use strict";a.s(["Separator",()=>b]);let b=(0,a.i(62e3).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/separator.tsx <module evaluation>","Separator")},19665,a=>{"use strict";a.s(["Separator",()=>b]);let b=(0,a.i(62e3).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/separator.tsx","Separator")},20407,a=>{"use strict";a.i(58903);var b=a.i(19665);a.n(b)},48329,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(62e3).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/components/layout/footer-subscribe-form.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/layout/footer-subscribe-form.tsx <module evaluation>","default")},20603,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(62e3).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/components/layout/footer-subscribe-form.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/layout/footer-subscribe-form.tsx","default")},67561,a=>{"use strict";a.i(48329);var b=a.i(20603);a.n(b)},47347,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(62e3).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/components/layout/site-header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/layout/site-header.tsx <module evaluation>","default")},6890,a=>{"use strict";a.s(["default",()=>b]);let b=(0,a.i(62e3).registerClientReference)(function(){throw Error("Attempted to call the default export of [project]/components/layout/site-header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/layout/site-header.tsx","default")},6562,a=>{"use strict";a.i(47347);var b=a.i(6890);a.n(b)},69486,a=>{"use strict";a.s(["default",()=>p],69486);var b=a.i(38470),c=a.i(4573);let d={src:a.i(31255).default,width:1728,height:891,blurWidth:8,blurHeight:4,blurDataURL:"data:image/webp;base64,UklGRhQBAABXRUJQVlA4TAgBAAAvB8AAEM1VICICHggACQAAAACyZwMAH2tMAhgAMAAAAAAAAAAAAACGAQAAAwMAAAAAAIDxzAMAAHggwCYAAAAA598fHXAMdIoKAgAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAADgHUHkgwCYAAAAA59+6pIACBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdSlAgAI4qnsgwCYAAAAA538AQAAEACEAAAAAAAAAAAAAAAAAAAAAAPQAAI4EIQqAQgrglmc9xW/28WYXHlv3kzH47wBrV4LGWDveISGYvTOj14TRMr0uRecyppCtNy8ZyXaheioPmTL/03rRCTSF7CDAtqSuZ12IfxSyRiA="};function e(){return(0,b.jsx)("div",{className:"-translate-x-1/2 pointer-events-none absolute top-0 left-1/2 w-full max-w-none overflow-hidden",children:(0,b.jsx)(c.default,{alt:"my image",className:"max-h-[900px] min-h-[500px] w-full",loading:"eager",role:"presentation",src:d})})}var f=a.i(43917);function g(){return(0,b.jsx)("div",{className:"overflow-hidden",children:(0,b.jsx)("div",{className:"relative w-full overflow-hidden bg-brand-600",children:(0,b.jsxs)("div",{className:"container mx-auto flex flex-col items-center justify-center px-4",children:[(0,b.jsxs)("div",{className:"mb-8 flex flex-col items-center justify-center gap-8",children:[(0,b.jsx)("h3",{className:"mb-4 pt-36 text-center font-aeonik-bold font-semibold text-3xl text-black sm:text-4xl md:text-5xl",children:"Available on Mac & Windows"}),(0,b.jsx)("div",{className:"mx-auto max-w-xs",children:(0,b.jsx)(f.Button,{className:"rounded-full bg-black text-white hover:bg-black/80",size:"lg",children:"Download for Mac"})})]}),(0,b.jsx)("div",{className:"-mb-32 w-full",children:(0,b.jsx)("h5",{className:"text-nowrap font-aeonik-bold text-[250px] text-black",children:"Better Flow"})})]})})})}let h=(0,a.i(47199).default)("armchair",[["path",{d:"M19 9V6a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v3",key:"irtipd"}],["path",{d:"M3 16a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-5a2 2 0 0 0-4 0v1.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V11a2 2 0 0 0-4 0z",key:"1qyhux"}],["path",{d:"M5 18v2",key:"ppbyun"}],["path",{d:"M19 18v2",key:"gy7782"}]]);var i=a.i(74464),j=a.i(20981);let k={links:{twitter:"https://x.com/rathonrw",linkedin:"https://www.linkedin.com/company/rathon",instagram:"https://www.instagram.com/rathonrw/",youtube:"https://www.youtube.com/@RathonRw"}};var l=a.i(20407),m=a.i(67561);function n(){return(0,b.jsx)("footer",{className:"border-grid border-t bg-my-background pt-16",id:"footer",children:(0,b.jsxs)("div",{className:"container relative",children:[(0,b.jsxs)("div",{className:"grid grid-cols-2 gap-10 md:grid-cols-4 md:px-4 lg:grid-cols-12",children:[(0,b.jsxs)("div",{className:"col-span-2 flex flex-col justify-between xl:col-span-3",children:[(0,b.jsxs)(i.default,{"aria-label":"go home",className:"flex size-fit items-center gap-1",href:"/",children:[(0,b.jsx)(h,{className:"block size-6 text-brand-600"}),(0,b.jsx)("span",{children:"Better Flow"})]}),(0,b.jsx)("div",{className:"my-5 w-fit font-normal text-md text-muted-foreground/50",children:"Highlight AI lets models understand your desktop activity. Get stuff done faster."}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(i.default,{className:"text-muted-foreground transition-colors duration-150 hover:text-primary",href:k.links.youtube,rel:"noreferrer",target:"_blank",children:(0,b.jsxs)("svg",{className:"size-5 fill-foreground",height:"24px",viewBox:"0 0 72 72",width:"24px",x:"0px",xmlns:"http://www.w3.org/2000/svg",y:"0px",children:[(0,b.jsx)("title",{children:"Youtube"}),(0,b.jsx)("path",{d:"M61.115,18.856C63.666,21.503,64,25.709,64,36s-0.334,14.497-2.885,17.144C58.563,55.791,55.906,56,36,56 s-22.563-0.209-25.115-2.856C8.334,50.497,8,46.291,8,36s0.334-14.497,2.885-17.144S16.094,16,36,16S58.563,16.209,61.115,18.856z M31.464,44.476l13.603-8.044l-13.603-7.918V44.476z"})]})}),(0,b.jsx)(l.Separator,{orientation:"vertical"}),(0,b.jsx)(i.default,{className:"text-muted-foreground transition-colors duration-150 hover:text-primary",href:k.links.instagram,rel:"noreferrer",target:"_blank",children:(0,b.jsxs)("svg",{className:"size-5 fill-foreground",height:"24px",viewBox:"0 0 64 64",width:"24px",x:"0px",xmlns:"http://www.w3.org/2000/svg",y:"0px",children:[(0,b.jsx)("title",{children:"Instagram"}),(0,b.jsx)("path",{d:"M 21.580078 7 C 13.541078 7 7 13.544938 7 21.585938 L 7 42.417969 C 7 50.457969 13.544938 57 21.585938 57 L 42.417969 57 C 50.457969 57 57 50.455062 57 42.414062 L 57 21.580078 C 57 13.541078 50.455062 7 42.414062 7 L 21.580078 7 z M 47 15 C 48.104 15 49 15.896 49 17 C 49 18.104 48.104 19 47 19 C 45.896 19 45 18.104 45 17 C 45 15.896 45.896 15 47 15 z M 32 19 C 39.17 19 45 24.83 45 32 C 45 39.17 39.169 45 32 45 C 24.83 45 19 39.169 19 32 C 19 24.831 24.83 19 32 19 z M 32 23 C 27.029 23 23 27.029 23 32 C 23 36.971 27.029 41 32 41 C 36.971 41 41 36.971 41 32 C 41 27.029 36.971 23 32 23 z"})]})}),(0,b.jsx)(l.Separator,{orientation:"vertical"}),(0,b.jsx)(i.default,{className:"text-muted-foreground transition-colors duration-150 hover:text-primary",href:k.links.twitter,rel:"noreferrer",target:"_blank",children:(0,b.jsxs)("svg",{className:"size-5 fill-foreground",height:"24px",viewBox:"0 0 24 24",width:"24px",xmlns:"http://www.w3.org/2000/svg",children:[(0,b.jsx)("title",{children:"Twitter"}),(0,b.jsx)("path",{d:"M 2.3671875 3 L 9.4628906 13.140625 L 2.7402344 21 L 5.3808594 21 L 10.644531 14.830078 L 14.960938 21 L 21.871094 21 L 14.449219 10.375 L 20.740234 3 L 18.140625 3 L 13.271484 8.6875 L 9.2988281 3 L 2.3671875 3 z M 6.2070312 5 L 8.2558594 5 L 18.033203 19 L 16.001953 19 L 6.2070312 5 z"})]})})]})]}),j.links.map(a=>(0,b.jsxs)("div",{className:"space-y-4 text-sm md:ml-5 lg:col-span-2",children:[(0,b.jsx)("span",{className:"block font-medium",children:a.group}),(0,b.jsx)("ul",{className:"space-y-3",children:a.items.map(a=>(0,b.jsx)("li",{children:(0,b.jsx)(i.default,{href:a.href,...a.external?{target:"_blank",rel:"noopener noreferrer"}:{},className:"block text-muted-foreground text-sm leading-5 duration-150 hover:text-primary",children:a.title})},a.title))})]},a.group)),(0,b.jsxs)("div",{className:"col-span-2 space-y-4 text-sm lg:col-span-3",children:[(0,b.jsx)("span",{className:"block font-medium",children:"Subscribe to our newsletter"}),(0,b.jsx)("div",{children:(0,b.jsx)("p",{className:"text-balance text-muted-foreground text-sm leading-5",children:"Stay updated on new releases and features, guides, and case studies."})}),(0,b.jsx)(m.default,{})]})]}),(0,b.jsx)("div",{className:"my-12 flex items-center justify-between gap-5",children:(0,b.jsx)("div",{className:"space-y-3",children:(0,b.jsxs)("p",{className:"text-balance text-muted-foreground text-sm leading-5",children:["© ",new Date().getFullYear()," Rathon, agency."]})})})]})})}var o=a.i(6562);function p({children:a}){return(0,b.jsxs)("div",{className:"container-wrapper dark relative z-10 flex min-h-svh flex-col bg-my-background text-white",children:[(0,b.jsx)(o.default,{}),(0,b.jsxs)("main",{className:"container relative flex flex-1 flex-col overflow-hidden",children:[(0,b.jsx)(e,{}),a]}),(0,b.jsx)(g,{}),(0,b.jsx)(n,{})]})}}];

//# sourceMappingURL=_81e856d2._.js.map