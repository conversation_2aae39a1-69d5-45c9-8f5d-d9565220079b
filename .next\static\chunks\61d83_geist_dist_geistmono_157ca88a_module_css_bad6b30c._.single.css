/* [project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistmono_157ca88a.module.css [app-client] (css) */
@font-face {
  font-family: GeistMono;
  src: url("../media/GeistMono_Variable.p.73882635.woff2") format("woff2");
  font-display: swap;
  font-weight: 100 900;
}

.geistmono_157ca88a-module__WVqpCG__className {
  font-family: GeistMono, ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace;
}

.geistmono_157ca88a-module__WVqpCG__variable {
  --font-geist-mono: "GeistM<PERSON>", ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace;
}

/*# sourceMappingURL=61d83_geist_dist_geistmono_157ca88a_module_css_bad6b30c._.single.css.map*/