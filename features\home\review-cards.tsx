import { Marquee } from '@/components/magicui/marquee';
import { reviews } from '@/config/docs';
import { ReviewCard } from './review-card';

const firstRow = reviews.slice(0, reviews.length / 2);
const secondRow = reviews.slice(reviews.length / 2);

export function ReviewCards() {
  return (
    <div className="relative hidden w-full flex-col items-center justify-center overflow-hidden md:flex">
      <Marquee className="[--duration:60s]" pauseOnHover>
        {firstRow.map((review) => (
          <ReviewCard key={review.name} {...review} />
        ))}
      </Marquee>
      <Marquee className="[--duration:60s]" pauseOnHover reverse>
        {secondRow.map((review) => (
          <ReviewCard key={review.name} {...review} />
        ))}
      </Marquee>
      <div className="pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r from-background" />
      <div className="pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l from-background" />
    </div>
  );
}
