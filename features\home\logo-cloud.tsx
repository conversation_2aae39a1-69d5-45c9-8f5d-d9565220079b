/** biome-ignore-all lint/performance/noImgElement: <explanation> */
import { InfiniteSlider } from '@/components/ui/infinite-slider';
import { ProgressiveBlur } from '@/components/ui/progressive-blur';

const logos = [
  {
    src: 'https://html.tailus.io/blocks/customers/nvidia.svg',
    alt: 'Nvidia Logo',
  },
  {
    src: 'https://html.tailus.io/blocks/customers/column.svg',
    alt: 'Column Logo',
  },
  {
    src: 'https://html.tailus.io/blocks/customers/github.svg',
    alt: 'GitHub Logo',
  },
  {
    src: 'https://html.tailus.io/blocks/customers/nike.svg',
    alt: 'Nike Logo',
  },
  {
    src: 'https://html.tailus.io/blocks/customers/lemonsqueezy.svg',
    alt: 'Lemon Squeezy Logo',
  },
  {
    src: 'https://html.tailus.io/blocks/customers/laravel.svg',
    alt: 'Laravel Logo',
  },
  {
    src: 'https://html.tailus.io/blocks/customers/lilly.svg',
    alt: 'Lilly Logo',
  },
  {
    src: 'https://html.tailus.io/blocks/customers/openai.svg',
    alt: 'OpenAI Logo',
  },
];
export default function LogoCloud() {
  return (
    <section className="overflow-hidden bg-background py-10">
      <div className="group relative m-auto max-w-7xl">
        <div className="relative">
          <InfiniteSlider gap={112} speed={40} speedOnHover={20}>
            {logos.map((logo) => (
              <div className="flex" key={logo.alt}>
                <img
                  alt={logo.alt}
                  className="mx-auto h-5 w-fit invert-75"
                  height="20"
                  src={logo.src}
                  width="auto"
                />
              </div>
            ))}
          </InfiniteSlider>

          <div className="absolute inset-y-0 left-0 w-20 bg-linear-to-r from-background" />
          <div className="absolute inset-y-0 right-0 w-20 bg-linear-to-l from-background" />
          <ProgressiveBlur
            blurIntensity={1}
            className="pointer-events-none absolute top-0 left-0 h-full w-20"
            direction="left"
          />
          <ProgressiveBlur
            blurIntensity={1}
            className="pointer-events-none absolute top-0 right-0 h-full w-20"
            direction="right"
          />
        </div>
      </div>
    </section>
  );
}
