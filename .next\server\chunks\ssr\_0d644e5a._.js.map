{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/public/gradient.webp.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 1728, height: 891, blurWidth: 8, blurHeight: 4, blurDataURL: \"data:image/webp;base64,UklGRhQBAABXRUJQVlA4TAgBAAAvB8AAEM1VICICHggACQAAAACyZwMAH2tMAhgAMAAAAAAAAAAAAACGAQAAAwMAAAAAAIDxzAMAAHggwCYAAAAA598fHXAMdIoKAgAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAADgHUHkgwCYAAAAA59+6pIACBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdSlAgAI4qnsgwCYAAAAA538AQAAEACEAAAAAAAAAAAAAAAAAAAAAAPQAAI4EIQqAQgrglmc9xW/28WYXHlv3kzH47wBrV4LGWDveISGYvTOj14TRMr0uRecyppCtNy8ZyXaheioPmTL/03rRCTSF7CDAtqSuZ12IfxSyRiA=\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,uHAAG;IAAE,OAAO;IAAM,QAAQ;IAAK,WAAW;IAAG,YAAY;IAAG,aAAa;AAAqZ", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/bg-cover.tsx"], "sourcesContent": ["import Image from 'next/image';\nimport gradient from '@/public/gradient.webp';\n\nexport default function BgCover() {\n  return (\n    <div className=\"-translate-x-1/2 pointer-events-none absolute top-0 left-1/2 w-full max-w-none overflow-hidden\">\n      <Image\n        alt=\"my image\"\n        className=\"max-h-[900px] min-h-[500px] w-full\"\n        loading=\"eager\"\n        role=\"presentation\"\n        src={gradient}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC,uQAAK;YACJ,KAAI;YACJ,WAAU;YACV,SAAQ;YACR,MAAK;YACL,KAAK,kSAAQ;;;;;;;;;;;AAIrB", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,oOAAO,EAAC,IAAA,8LAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from '@radix-ui/react-slot';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport type * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  \"inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium text-sm outline-none transition-all focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\n        destructive:\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40',\n        outline:\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:border-input dark:bg-input/30 dark:hover:bg-input/50',\n        secondary:\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\n        ghost:\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\n        sm: 'h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5',\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\n        icon: 'size-9',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<'button'> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean;\n  }) {\n  const Comp = asChild ? Slot : 'button';\n\n  return (\n    <Comp\n      className={cn(buttonVariants({ variant, size, className }))}\n      data-slot=\"button\"\n      {...props}\n    />\n  );\n}\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,IAAA,kPAAG,EACxB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,6SAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,WAAW,IAAA,kHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/footer-cta.tsx"], "sourcesContent": ["import { Button } from '../ui/button';\n\nexport default function FooterCta() {\n  return (\n    <div className=\"overflow-hidden\">\n      <div className=\"relative w-full overflow-hidden bg-brand-600\">\n        <div className=\"container mx-auto flex flex-col items-center justify-center px-4\">\n          <div className=\"mb-8 flex flex-col items-center justify-center gap-8\">\n            <h3 className=\"mb-4 pt-36 text-center font-aeonik-bold font-semibold text-3xl text-black sm:text-4xl md:text-5xl\">\n              Available on Mac &amp; Windows\n            </h3>\n            <div className=\"mx-auto max-w-xs\">\n              <Button\n                className=\"rounded-full bg-black text-white hover:bg-black/80\"\n                size={'lg'}\n              >\n                Download for Mac\n              </Button>\n            </div>\n          </div>\n          <div className=\"-mb-32 w-full\">\n            <h5 className=\"text-nowrap font-aeonik-bold text-[250px] text-black\">\n              Better Flow\n            </h5>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAG,WAAU;0CAAoG;;;;;;0CAGlH,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,qIAAM;oCACL,WAAU;oCACV,MAAM;8CACP;;;;;;;;;;;;;;;;;kCAKL,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAG,WAAU;sCAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF", "debugId": null}}, {"offset": {"line": 216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/config/docs.ts"], "sourcesContent": ["import {\n  ChartNoAxesCombinedIcon,\n  CircleCheckIcon,\n  DatabaseZapIcon,\n  HouseIcon,\n  ImageIcon,\n  MessageCircleIcon,\n  NotebookPenIcon,\n  PenLineIcon,\n  UsersIcon,\n} from 'lucide-react';\nimport type { TFeature, TFooterLink, TNavItem } from '@/types';\n\nexport const navItems: TNavItem[] = [\n  {\n    href: '/pricing',\n    label: 'Pricing',\n    icon: HouseIcon,\n  },\n  {\n    href: '/about',\n    label: 'About',\n    icon: NotebookPenIcon,\n  },\n  {\n    href: '/docs',\n    label: 'Docs',\n    icon: UsersIcon,\n  },\n  {\n    href: '/privacy',\n    label: 'Privacy',\n    icon: ImageIcon,\n  },\n];\n\nexport const footerLinks: TFooterLink[] = [\n  {\n    title: 'Newsroom',\n    links: [\n      { name: 'Latest News', href: '/', external: false },\n      { name: 'Top Stories', href: '/', external: false },\n      { name: \"Editor's Picks\", href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Company',\n    links: [\n      { name: 'About Us', href: '/', external: false },\n      { name: 'Careers', href: '/', external: false },\n      { name: 'Press', href: '/', external: false },\n      { name: 'Contact', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'For Business',\n    links: [\n      { name: 'Advertise with Us', href: '/', external: false },\n      { name: 'Media Kit', href: '/', external: false },\n      { name: 'Partner with Us', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'More',\n    links: [\n      { name: 'Newsletter', href: '/', external: false },\n      { name: 'Mobile App', href: '/', external: false },\n      { name: 'RSS Feeds', href: '/', external: false },\n      { name: 'Help Center', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Terms & Policies',\n    links: [\n      { name: 'Terms of Use', href: '/', external: false },\n      { name: 'Privacy Policy', href: '/', external: false },\n      { name: 'Cookie Policy', href: '/', external: false },\n      { name: 'Editorial Policy', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Safety',\n    links: [\n      { name: 'Fact-Checking', href: '/', external: false },\n      { name: 'Corrections', href: '/', external: false },\n      { name: 'Trust & Transparency', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Follow Us',\n    links: [\n      { name: 'Facebook', href: '/', external: true },\n      { name: 'Twitter', href: '/', external: true },\n      { name: 'Instagram', href: '/', external: true },\n      { name: 'YouTube', href: '/', external: true },\n    ],\n  },\n  {\n    title: 'Sections',\n    links: [\n      { name: 'Politics', href: '/', external: false },\n      { name: 'Business', href: '/', external: false },\n      { name: 'Technology', href: '/', external: false },\n      { name: 'Health', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Resources',\n    links: [\n      { name: 'Media Resources', href: '/', external: false },\n      { name: 'Author Guidelines', href: '/', external: false },\n      { name: 'News Archive', href: '/', external: false },\n    ],\n  },\n  {\n    title: 'Community',\n    links: [\n      { name: 'Events', href: '/', external: false },\n      { name: 'Reader Stories', href: '/', external: false },\n      { name: 'Submit News', href: '/', external: false },\n    ],\n  },\n];\n\nexport const features: TFeature[] = [\n  {\n    icon: MessageCircleIcon,\n    title: 'chat',\n    description: 'Chat with anyone in team.',\n  },\n  {\n    icon: PenLineIcon,\n    title: 'writing',\n    description: 'Notion like editor for writing.',\n  },\n  {\n    icon: CircleCheckIcon,\n    title: 'tasks',\n    description: 'Automated task tracking.',\n  },\n  {\n    icon: UsersIcon,\n    title: 'teams',\n    description: 'Collaborate with your team.',\n  },\n  {\n    icon: DatabaseZapIcon,\n    title: 'storage',\n    description: 'Unlimited storage for your files.',\n  },\n  {\n    icon: ChartNoAxesCombinedIcon,\n    title: 'analytics',\n    description: 'Easy to track your progress.',\n  },\n];\n\nexport const featuresCompare = [\n  {\n    feature: \"Doesn't train on your data\",\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: true,\n    Notion: true,\n  },\n  {\n    feature: 'Works across your entire computer',\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: true,\n    Notion: false,\n  },\n  {\n    feature: 'Always one click away',\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: true,\n    Notion: false,\n  },\n  {\n    feature: 'Custom actions and automations',\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: true,\n    Notion: false,\n  },\n  {\n    feature: \"Understands anything you're looking at\",\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: false,\n    Notion: false,\n  },\n  {\n    feature: 'Integrated audio transcription',\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: false,\n    Notion: false,\n  },\n  {\n    feature: 'Built for both Mac & Windows',\n    Highlight: true,\n    ChatGPT: false,\n    Claude: false,\n    Raycast: false,\n    Notion: false,\n  },\n];\n\nexport const reviews = [\n  {\n    name: 'Eric Glyman',\n    username: 'Co-Founder at Ramp',\n    body: \"I've never seen anything like this before. It's amazing. I love it.\",\n    img: 'https://avatar.vercel.sh/jack',\n  },\n  {\n    name: 'Eric James',\n    username: 'Co-Founder at Ramp',\n    body: \"I don't know what to say. I'm speechless. This is amazing.\",\n    img: 'https://avatar.vercel.sh/jill',\n  },\n  {\n    name: 'Eric Kagabo',\n    username: 'Co-Founder at Ramp',\n    body: \"I'm at a loss for words. This is amazing. I love it.\",\n    img: 'https://avatar.vercel.sh/john',\n  },\n  {\n    name: 'Eric Mugisha',\n    username: 'Co-Founder at Ramp',\n    body: \"I'm at a loss for words. This is amazing. I love it.\",\n    img: 'https://avatar.vercel.sh/jane',\n  },\n  {\n    name: 'Eric David',\n    username: 'Co-Founder at Ramp',\n    body: \"I'm at a loss for words. This is amazing. I love it.\",\n    img: 'https://avatar.vercel.sh/jenny',\n  },\n  {\n    name: 'Eric Tony',\n    username: 'Co-Founder at Ramp',\n    body: \"I'm at a loss for words. This is amazing. I love it.\",\n    img: 'https://avatar.vercel.sh/james',\n  },\n];\n\nexport const links = [\n  {\n    group: 'Company',\n    items: [\n      {\n        title: 'About',\n        href: '/about',\n        external: false,\n      },\n      {\n        title: 'Blog',\n        href: '/blogs',\n        external: false,\n      },\n      {\n        title: 'Pricing',\n        href: '/#pricing',\n        external: false,\n      },\n      {\n        title: 'Book a Call',\n        href: '/book',\n        external: false,\n      },\n    ],\n  },\n  {\n    group: 'Resources',\n    items: [\n      {\n        title: 'Careers',\n        href: '/contact',\n        external: false,\n      },\n\n      {\n        title: 'Support',\n        href: '/book',\n        external: false,\n      },\n      {\n        title: 'Sitemap',\n        href: '/sitemap.xml',\n        external: false,\n      },\n      {\n        title: 'llm.txt',\n        href: '/llm.txt',\n        external: false,\n      },\n    ],\n  },\n  {\n    group: 'Legal',\n    items: [\n      {\n        title: 'Privacy',\n        href: '/privacy',\n        external: false,\n      },\n      {\n        title: 'Terms',\n        href: '/terms',\n        external: false,\n      },\n    ],\n  },\n];\n\nexport const plans = [\n  {\n    id: 'free',\n    name: 'Free',\n    price: {\n      monthly: 0,\n      yearly: 0,\n    },\n    description:\n      'The perfect starting place for your web app or personal project.',\n    features: [\n      '50 API calls / month',\n      '60 second checks',\n      'Single-user account',\n      '5 monitors',\n      'Basic email support',\n    ],\n    cta: 'Get started for free',\n  },\n  {\n    id: 'pro',\n    name: 'Pro',\n    price: {\n      monthly: 90,\n      yearly: 75,\n    },\n    description: 'Everything you need to build and scale your business.',\n    features: [\n      'Unlimited API calls',\n      '30 second checks',\n      'Multi-user account',\n      '10 monitors',\n      'Priority email support',\n    ],\n    cta: 'Subscribe to Pro',\n    popular: true,\n  },\n  {\n    id: 'enterprise',\n    name: 'Enterprise',\n    price: {\n      monthly: 'Get in touch for pricing',\n      yearly: 'Get in touch for pricing',\n    },\n    description: 'Critical security, performance, observability and support.',\n    features: [\n      'You can DDOS our API.',\n      'Nano-second checks.',\n      'Invite your extended family.',\n      'Unlimited monitors.',\n      \"We'll sit on your desk.\",\n    ],\n    cta: 'Contact us',\n  },\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAaO,MAAM,WAAuB;IAClC;QACE,MAAM;QACN,OAAO;QACP,MAAM,ySAAS;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,+TAAe;IACvB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,ySAAS;IACjB;IACA;QACE,MAAM;QACN,OAAO;QACP,MAAM,ySAAS;IACjB;CACD;AAEM,MAAM,cAA6B;IACxC;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;SACtD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAM;YAC9C;gBAAE,MAAM;gBAAS,MAAM;gBAAK,UAAU;YAAM;YAC5C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAM;SAC/C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAqB,MAAM;gBAAK,UAAU;YAAM;YACxD;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAM;YAChD;gBAAE,MAAM;gBAAmB,MAAM;gBAAK,UAAU;YAAM;SACvD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAM;YAChD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;SACnD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAgB,MAAM;gBAAK,UAAU;YAAM;YACnD;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;YACrD;gBAAE,MAAM;gBAAiB,MAAM;gBAAK,UAAU;YAAM;YACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAK,UAAU;YAAM;SACxD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAiB,MAAM;gBAAK,UAAU;YAAM;YACpD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;YAClD;gBAAE,MAAM;gBAAwB,MAAM;gBAAK,UAAU;YAAM;SAC5D;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAK;YAC9C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAK;YAC7C;gBAAE,MAAM;gBAAa,MAAM;gBAAK,UAAU;YAAK;YAC/C;gBAAE,MAAM;gBAAW,MAAM;gBAAK,UAAU;YAAK;SAC9C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAY,MAAM;gBAAK,UAAU;YAAM;YAC/C;gBAAE,MAAM;gBAAc,MAAM;gBAAK,UAAU;YAAM;YACjD;gBAAE,MAAM;gBAAU,MAAM;gBAAK,UAAU;YAAM;SAC9C;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAmB,MAAM;gBAAK,UAAU;YAAM;YACtD;gBAAE,MAAM;gBAAqB,MAAM;gBAAK,UAAU;YAAM;YACxD;gBAAE,MAAM;gBAAgB,MAAM;gBAAK,UAAU;YAAM;SACpD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBAAE,MAAM;gBAAU,MAAM;gBAAK,UAAU;YAAM;YAC7C;gBAAE,MAAM;gBAAkB,MAAM;gBAAK,UAAU;YAAM;YACrD;gBAAE,MAAM;gBAAe,MAAM;gBAAK,UAAU;YAAM;SACnD;IACH;CACD;AAEM,MAAM,WAAuB;IAClC;QACE,MAAM,qUAAiB;QACvB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,mTAAW;QACjB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,+TAAe;QACrB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,ySAAS;QACf,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,+TAAe;QACrB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,+VAAuB;QAC7B,OAAO;QACP,aAAa;IACf;CACD;AAEM,MAAM,kBAAkB;IAC7B;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;CACD;AAEM,MAAM,UAAU;IACrB;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;IACA;QACE,MAAM;QACN,UAAU;QACV,MAAM;QACN,KAAK;IACP;CACD;AAEM,MAAM,QAAQ;IACnB;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YAEA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;IACH;IACA;QACE,OAAO;QACP,OAAO;YACL;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,UAAU;YACZ;SACD;IACH;CACD;AAEM,MAAM,QAAQ;IACnB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL,SAAS;YACT,QAAQ;QACV;QACA,aACE;QACF,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;IACP;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL,SAAS;YACT,QAAQ;QACV;QACA,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;IACX;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;YACL,SAAS;YACT,QAAQ;QACV;QACA,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;IACP;CACD", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/config/site.ts"], "sourcesContent": ["export const siteConfig = {\n  name: '<PERSON><PERSON>',\n  url: 'https://rathon-rw.vercel.app/', // Replace with production URL\n  ogImage: 'https://rathon-rw.vercel.app/opengraph-image.png',\n  description:\n    'We don’t just build websites. We craft bold brands and powerful platforms for businesses ready to stand out, scale up, and dominate the digital space.',\n  links: {\n    twitter: 'https://x.com/rathonrw',\n    linkedin: 'https://www.linkedin.com/company/rathon',\n    instagram: 'https://www.instagram.com/rathonrw/',\n    youtube: 'https://www.youtube.com/@RathonRw',\n  },\n  keywords: [\n    'Web Design',\n    'Web Development',\n    'UI/UX Design',\n    'SEO Optimization',\n    'Website Hosting',\n    'Website Maintenance',\n    'React',\n    'TypeScript',\n    'Tailwind CSS',\n    'Next.js',\n    'Landing Pages',\n    'Creative Agency',\n    'Frontend Development',\n    'Performance Optimization',\n    'Mobile Responsive Design',\n    'Modern Web Apps',\n    'Digital Branding',\n    'Design Systems',\n    'Careers in Tech',\n    'Contact Rathon',\n    'Book a Call',\n    'Support Resources',\n    'Video Guides',\n  ],\n};\n"], "names": [], "mappings": ";;;;AAAO,MAAM,aAAa;IACxB,MAAM;IACN,KAAK;IACL,SAAS;IACT,aACE;IACF,OAAO;QACL,SAAS;QACT,UAAU;QACV,WAAW;QACX,SAAS;IACX;IACA,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH", "debugId": null}}, {"offset": {"line": 789, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/separator.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/separator.tsx <module evaluation>\",\n    \"Separator\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,uYAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/ui/separator.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/separator.tsx\",\n    \"Separator\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,uYAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/footer-subscribe-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/layout/footer-subscribe-form.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/footer-subscribe-form.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/footer-subscribe-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/layout/footer-subscribe-form.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/footer-subscribe-form.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/site-footer.tsx"], "sourcesContent": ["import { ArmchairIcon } from 'lucide-react';\nimport Link from 'next/link';\nimport { links } from '@/config/docs';\nimport { siteConfig } from '@/config/site';\nimport { Separator } from '../ui/separator';\nimport FooterSubForm from './footer-subscribe-form';\n\nexport function SiteFooter() {\n  return (\n    <footer className=\"border-grid border-t bg-my-background pt-16\" id=\"footer\">\n      <div className=\"container relative\">\n        <div className=\"grid grid-cols-2 gap-10 md:grid-cols-4 md:px-4 lg:grid-cols-12\">\n          <div className=\"col-span-2 flex flex-col justify-between xl:col-span-3\">\n            <Link\n              aria-label=\"go home\"\n              className=\"flex size-fit items-center gap-1\"\n              href=\"/\"\n            >\n              <ArmchairIcon className=\"block size-6 text-brand-600\" />\n              <span>Better Flow</span>\n            </Link>\n            <div className=\"my-5 w-fit font-normal text-md text-muted-foreground/50\">\n              Highlight AI lets models understand your desktop activity. Get\n              stuff done faster.\n            </div>\n            <div className=\"flex gap-2\">\n              <Link\n                className=\"text-muted-foreground transition-colors duration-150 hover:text-primary\"\n                href={siteConfig.links.youtube}\n                rel=\"noreferrer\"\n                target=\"_blank\"\n              >\n                <svg\n                  className=\"size-5 fill-foreground\"\n                  height=\"24px\"\n                  viewBox=\"0 0 72 72\"\n                  width=\"24px\"\n                  x=\"0px\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  y=\"0px\"\n                >\n                  <title>Youtube</title>\n                  <path d=\"M61.115,18.856C63.666,21.503,64,25.709,64,36s-0.334,14.497-2.885,17.144C58.563,55.791,55.906,56,36,56\ts-22.563-0.209-25.115-2.856C8.334,50.497,8,46.291,8,36s0.334-14.497,2.885-17.144S16.094,16,36,16S58.563,16.209,61.115,18.856z M31.464,44.476l13.603-8.044l-13.603-7.918V44.476z\" />\n                </svg>\n              </Link>\n              <Separator orientation=\"vertical\" />\n              <Link\n                className=\"text-muted-foreground transition-colors duration-150 hover:text-primary\"\n                href={siteConfig.links.instagram}\n                rel=\"noreferrer\"\n                target=\"_blank\"\n              >\n                <svg\n                  className=\"size-5 fill-foreground\"\n                  height=\"24px\"\n                  viewBox=\"0 0 64 64\"\n                  width=\"24px\"\n                  x=\"0px\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  y=\"0px\"\n                >\n                  <title>Instagram</title>\n                  <path d=\"M 21.580078 7 C 13.541078 7 7 13.544938 7 21.585938 L 7 42.417969 C 7 50.457969 13.544938 57 21.585938 57 L 42.417969 57 C 50.457969 57 57 50.455062 57 42.414062 L 57 21.580078 C 57 13.541078 50.455062 7 42.414062 7 L 21.580078 7 z M 47 15 C 48.104 15 49 15.896 49 17 C 49 18.104 48.104 19 47 19 C 45.896 19 45 18.104 45 17 C 45 15.896 45.896 15 47 15 z M 32 19 C 39.17 19 45 24.83 45 32 C 45 39.17 39.169 45 32 45 C 24.83 45 19 39.169 19 32 C 19 24.831 24.83 19 32 19 z M 32 23 C 27.029 23 23 27.029 23 32 C 23 36.971 27.029 41 32 41 C 36.971 41 41 36.971 41 32 C 41 27.029 36.971 23 32 23 z\" />\n                </svg>\n              </Link>\n\n              <Separator orientation=\"vertical\" />\n              <Link\n                className=\"text-muted-foreground transition-colors duration-150 hover:text-primary\"\n                href={siteConfig.links.twitter}\n                rel=\"noreferrer\"\n                target=\"_blank\"\n              >\n                <svg\n                  className=\"size-5 fill-foreground\"\n                  height=\"24px\"\n                  viewBox=\"0 0 24 24\"\n                  width=\"24px\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <title>Twitter</title>\n                  <path d=\"M 2.3671875 3 L 9.4628906 13.140625 L 2.7402344 21 L 5.3808594 21 L 10.644531 14.830078 L 14.960938 21 L 21.871094 21 L 14.449219 10.375 L 20.740234 3 L 18.140625 3 L 13.271484 8.6875 L 9.2988281 3 L 2.3671875 3 z M 6.2070312 5 L 8.2558594 5 L 18.033203 19 L 16.001953 19 L 6.2070312 5 z\" />\n                </svg>\n              </Link>\n            </div>\n          </div>\n\n          {links.map((link) => (\n            <div\n              className=\"space-y-4 text-sm md:ml-5 lg:col-span-2\"\n              key={link.group}\n            >\n              <span className=\"block font-medium\">{link.group}</span>\n              <ul className=\"space-y-3\">\n                {link.items.map((item) => (\n                  <li key={item.title}>\n                    <Link\n                      href={item.href}\n                      {...(item.external\n                        ? {\n                            target: '_blank',\n                            rel: 'noopener noreferrer',\n                          }\n                        : {})}\n                      className=\"block text-muted-foreground text-sm leading-5 duration-150 hover:text-primary\"\n                    >\n                      {item.title}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n\n          <div className=\"col-span-2 space-y-4 text-sm lg:col-span-3\">\n            <span className=\"block font-medium\">\n              Subscribe to our newsletter\n            </span>\n            <div>\n              <p className=\"text-balance text-muted-foreground text-sm leading-5\">\n                Stay updated on new releases and features, guides, and case\n                studies.\n              </p>\n            </div>\n            <FooterSubForm />\n          </div>\n        </div>\n        <div className=\"my-12 flex items-center justify-between gap-5\">\n          <div className=\"space-y-3\">\n            <p className=\"text-balance text-muted-foreground text-sm leading-5\">\n              © {new Date().getFullYear()} Rathon, agency.\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEO,SAAS;IACd,qBACE,6WAAC;QAAO,WAAU;QAA8C,IAAG;kBACjE,cAAA,6WAAC;YAAI,WAAU;;8BACb,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,sSAAI;oCACH,cAAW;oCACX,WAAU;oCACV,MAAK;;sDAEL,6WAAC,kTAAY;4CAAC,WAAU;;;;;;sDACxB,6WAAC;sDAAK;;;;;;;;;;;;8CAER,6WAAC;oCAAI,WAAU;8CAA0D;;;;;;8CAIzE,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,sSAAI;4CACH,WAAU;4CACV,MAAM,4HAAU,CAAC,KAAK,CAAC,OAAO;4CAC9B,KAAI;4CACJ,QAAO;sDAEP,cAAA,6WAAC;gDACC,WAAU;gDACV,QAAO;gDACP,SAAQ;gDACR,OAAM;gDACN,GAAE;gDACF,OAAM;gDACN,GAAE;;kEAEF,6WAAC;kEAAM;;;;;;kEACP,6WAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,6WAAC,2IAAS;4CAAC,aAAY;;;;;;sDACvB,6WAAC,sSAAI;4CACH,WAAU;4CACV,MAAM,4HAAU,CAAC,KAAK,CAAC,SAAS;4CAChC,KAAI;4CACJ,QAAO;sDAEP,cAAA,6WAAC;gDACC,WAAU;gDACV,QAAO;gDACP,SAAQ;gDACR,OAAM;gDACN,GAAE;gDACF,OAAM;gDACN,GAAE;;kEAEF,6WAAC;kEAAM;;;;;;kEACP,6WAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAIZ,6WAAC,2IAAS;4CAAC,aAAY;;;;;;sDACvB,6WAAC,sSAAI;4CACH,WAAU;4CACV,MAAM,4HAAU,CAAC,KAAK,CAAC,OAAO;4CAC9B,KAAI;4CACJ,QAAO;sDAEP,cAAA,6WAAC;gDACC,WAAU;gDACV,QAAO;gDACP,SAAQ;gDACR,OAAM;gDACN,OAAM;;kEAEN,6WAAC;kEAAM;;;;;;kEACP,6WAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAMf,uHAAK,CAAC,GAAG,CAAC,CAAC,qBACV,6WAAC;gCACC,WAAU;;kDAGV,6WAAC;wCAAK,WAAU;kDAAqB,KAAK,KAAK;;;;;;kDAC/C,6WAAC;wCAAG,WAAU;kDACX,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,qBACf,6WAAC;0DACC,cAAA,6WAAC,sSAAI;oDACH,MAAM,KAAK,IAAI;oDACd,GAAI,KAAK,QAAQ,GACd;wDACE,QAAQ;wDACR,KAAK;oDACP,IACA,CAAC,CAAC;oDACN,WAAU;8DAET,KAAK,KAAK;;;;;;+CAXN,KAAK,KAAK;;;;;;;;;;;+BALlB,KAAK,KAAK;;;;;sCAwBnB,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAK,WAAU;8CAAoB;;;;;;8CAGpC,6WAAC;8CACC,cAAA,6WAAC;wCAAE,WAAU;kDAAuD;;;;;;;;;;;8CAKtE,6WAAC,+JAAa;;;;;;;;;;;;;;;;;8BAGlB,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAE,WAAU;;gCAAuD;gCAC/D,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1C", "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/site-header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/layout/site-header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/site-header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAqS,GAClU,mEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/components/layout/site-header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/layout/site-header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/site-header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,uYAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAiR,GAC9S,+CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1224, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1232, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/app/%28root%29/layout.tsx"], "sourcesContent": ["import BgCover from '@/components/layout/bg-cover';\nimport FooterC<PERSON> from '@/components/layout/footer-cta';\nimport { SiteFooter } from '@/components/layout/site-footer';\nimport SiteHeader from '@/components/layout/site-header';\n\nexport default function AppLayout({ children }: { children: React.ReactNode }) {\n  return (\n    <div className=\"container-wrapper dark relative z-10 flex min-h-svh flex-col bg-my-background text-white\">\n      <SiteHeader />\n      <main className=\"container relative flex flex-1 flex-col overflow-hidden\">\n        <BgCover />\n        {children}\n      </main>\n      <FooterCta />\n      <SiteFooter />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS,UAAU,EAAE,QAAQ,EAAiC;IAC3E,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,kJAAU;;;;;0BACX,6WAAC;gBAAK,WAAU;;kCACd,6WAAC,+IAAO;;;;;oBACP;;;;;;;0BAEH,6WAAC,iJAAS;;;;;0BACV,6WAAC,qJAAU;;;;;;;;;;;AAGjB", "debugId": null}}]}