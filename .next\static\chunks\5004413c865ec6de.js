(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,47163,7284,e=>{"use strict";function t(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,o,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(o=e(t[r]))&&(n&&(n+=" "),n+=o)}else for(o in t)t[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=t);return o}e.s(["cn",()=>ee],47163),e.s(["clsx",()=>t],7284);let r=(e,t)=>{var o;if(0===e.length)return t.classGroupId;let n=e[0],a=t.nextPart.get(n),i=a?r(e.slice(1),a):void 0;if(i)return i;if(0===t.validators.length)return;let s=e.join("-");return null==(o=t.validators.find(e=>{let{validator:t}=e;return t(s)}))?void 0:o.classGroupId},o=/^\[(.+)\]$/,n=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:a(t,e)).classGroupId=r;return}if("function"==typeof e)return i(e)?void n(e(o),t,r,o):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(e=>{let[i,s]=e;n(s,a(t,i),r,o)})})},a=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},i=e=>e.isThemeGetter,s=/\s+/;function l(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=c(e))&&(o&&(o+=" "),o+=t);return o}let c=e=>{let t;if("string"==typeof e)return e;let r="";for(let o=0;o<e.length;o++)e[o]&&(t=c(e[o]))&&(r&&(r+=" "),r+=t);return r},d=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},u=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,m=/^\((?:(\w[\w-]*):)?(.+)\)$/i,p=/^\d+\/\d+$/,f=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,h=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,g=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,b=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,v=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,y=e=>p.test(e),k=e=>!!e&&!Number.isNaN(Number(e)),w=e=>!!e&&Number.isInteger(Number(e)),x=e=>e.endsWith("%")&&k(e.slice(0,-1)),z=e=>f.test(e),C=()=>!0,M=e=>h.test(e)&&!g.test(e),N=()=>!1,A=e=>b.test(e),R=e=>v.test(e),E=e=>!I(e)&&!G(e),j=e=>$(e,K,N),I=e=>u.test(e),T=e=>$(e,Y,M),S=e=>$(e,Z,k),O=e=>$(e,B,N),P=e=>$(e,V,R),L=e=>$(e,X,A),G=e=>m.test(e),U=e=>q(e,Y),_=e=>q(e,J),W=e=>q(e,B),D=e=>q(e,K),F=e=>q(e,V),H=e=>q(e,X,!0),$=(e,t,r)=>{let o=u.exec(e);return!!o&&(o[1]?t(o[1]):r(o[2]))},q=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=m.exec(e);return!!o&&(o[1]?t(o[1]):r)},B=e=>"position"===e||"percentage"===e,V=e=>"image"===e||"url"===e,K=e=>"length"===e||"size"===e||"bg-size"===e,Y=e=>"length"===e,Z=e=>"number"===e,J=e=>"family-name"===e,X=e=>"shadow"===e;Symbol.toStringTag;let Q=function(e){let t,a,i;for(var c=arguments.length,d=Array(c>1?c-1:0),u=1;u<c;u++)d[u-1]=arguments[u];let m=function(s){let l;return a=(t={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,o=new Map,n=(n,a)=>{r.set(n,a),++t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}})((l=d.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:r}=e,o=e=>{let t,r,o=[],n=0,a=0,i=0;for(let r=0;r<e.length;r++){let s=e[r];if(0===n&&0===a){if(":"===s){o.push(e.slice(i,r)),i=r+1;continue}if("/"===s){t=r;continue}}"["===s?n++:"]"===s?n--:"("===s?a++:")"===s&&a--}let s=0===o.length?e:e.substring(i),l=(r=s).endsWith("!")?r.substring(0,r.length-1):r.startsWith("!")?r.substring(1):r;return{modifiers:o,hasImportantModifier:l!==s,baseClassName:l,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=o;o=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=o;o=t=>r({className:t,parseClassName:e})}return o})(l),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],o=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...o.sort(),e),o=[]):o.push(e)}),r.push(...o.sort()),r}})(l),...(e=>{let t=(e=>{let{theme:t,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(let e in r)n(r[e],o,e,t);return o})(e),{conflictingClassGroups:a,conflictingClassGroupModifiers:i}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),r(n,t)||(e=>{if(o.test(e)){let t=o.exec(e)[1],r=null==t?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}})(e)},getConflictingClassGroupIds:(e,t)=>{let r=a[e]||[];return t&&i[e]?[...r,...i[e]]:r}}})(l)}).cache.get,i=t.cache.set,m=p,p(s)};function p(e){let r=a(e);if(r)return r;let o=((e,t)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=t,i=[],l=e.trim().split(s),c="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:s,modifiers:d,hasImportantModifier:u,baseClassName:m,maybePostfixModifierPosition:p}=r(t);if(s){c=t+(c.length>0?" "+c:c);continue}let f=!!p,h=o(f?m.substring(0,p):m);if(!h){if(!f||!(h=o(m))){c=t+(c.length>0?" "+c:c);continue}f=!1}let g=a(d).join(":"),b=u?g+"!":g,v=b+h;if(i.includes(v))continue;i.push(v);let y=n(h,f);for(let e=0;e<y.length;++e){let t=y[e];i.push(b+t)}c=t+(c.length>0?" "+c:c)}return c})(e,t);return i(e,o),o}return function(){return m(l.apply(null,arguments))}}(()=>{let e=d("color"),t=d("font"),r=d("text"),o=d("font-weight"),n=d("tracking"),a=d("leading"),i=d("breakpoint"),s=d("container"),l=d("spacing"),c=d("radius"),u=d("shadow"),m=d("inset-shadow"),p=d("text-shadow"),f=d("drop-shadow"),h=d("blur"),g=d("perspective"),b=d("aspect"),v=d("ease"),M=d("animate"),N=()=>["auto","avoid","all","avoid-page","page","left","right","column"],A=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],R=()=>[...A(),G,I],$=()=>["auto","hidden","clip","visible","scroll"],q=()=>["auto","contain","none"],B=()=>[G,I,l],V=()=>[y,"full","auto",...B()],K=()=>[w,"none","subgrid",G,I],Y=()=>["auto",{span:["full",w,G,I]},w,G,I],Z=()=>[w,"auto",G,I],J=()=>["auto","min","max","fr",G,I],X=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...B()],et=()=>[y,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...B()],er=()=>[e,G,I],eo=()=>[...A(),W,O,{position:[G,I]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",D,j,{size:[G,I]}],ei=()=>[x,U,T],es=()=>["","none","full",c,G,I],el=()=>["",k,U,T],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[k,x,W,O],em=()=>["","none",h,G,I],ep=()=>["none",k,G,I],ef=()=>["none",k,G,I],eh=()=>[k,G,I],eg=()=>[y,"full",...B()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[z],breakpoint:[z],color:[C],container:[z],"drop-shadow":[z],ease:["in","out","in-out"],font:[E],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[z],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[z],shadow:[z],spacing:["px",k],text:[z],"text-shadow":[z],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",y,I,G,b]}],container:["container"],columns:[{columns:[k,I,G,s]}],"break-after":[{"break-after":N()}],"break-before":[{"break-before":N()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:R()}],overflow:[{overflow:$()}],"overflow-x":[{"overflow-x":$()}],"overflow-y":[{"overflow-y":$()}],overscroll:[{overscroll:q()}],"overscroll-x":[{"overscroll-x":q()}],"overscroll-y":[{"overscroll-y":q()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:V()}],"inset-x":[{"inset-x":V()}],"inset-y":[{"inset-y":V()}],start:[{start:V()}],end:[{end:V()}],top:[{top:V()}],right:[{right:V()}],bottom:[{bottom:V()}],left:[{left:V()}],visibility:["visible","invisible","collapse"],z:[{z:[w,"auto",G,I]}],basis:[{basis:[y,"full","auto",s,...B()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[k,y,"auto","initial","none",I]}],grow:[{grow:["",k,G,I]}],shrink:[{shrink:["",k,G,I]}],order:[{order:[w,"first","last","none",G,I]}],"grid-cols":[{"grid-cols":K()}],"col-start-end":[{col:Y()}],"col-start":[{"col-start":Z()}],"col-end":[{"col-end":Z()}],"grid-rows":[{"grid-rows":K()}],"row-start-end":[{row:Y()}],"row-start":[{"row-start":Z()}],"row-end":[{"row-end":Z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":J()}],"auto-rows":[{"auto-rows":J()}],gap:[{gap:B()}],"gap-x":[{"gap-x":B()}],"gap-y":[{"gap-y":B()}],"justify-content":[{justify:[...X(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...X()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":X()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:B()}],px:[{px:B()}],py:[{py:B()}],ps:[{ps:B()}],pe:[{pe:B()}],pt:[{pt:B()}],pr:[{pr:B()}],pb:[{pb:B()}],pl:[{pl:B()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":B()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":B()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[s,"screen",...et()]}],"min-w":[{"min-w":[s,"screen","none",...et()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[i]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,U,T]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,G,S]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",x,I]}],"font-family":[{font:[_,I,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,G,I]}],"line-clamp":[{"line-clamp":[k,"none",G,S]}],leading:[{leading:[a,...B()]}],"list-image":[{"list-image":["none",G,I]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",G,I]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[k,"from-font","auto",G,T]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[k,"auto",G,I]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:B()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",G,I]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",G,I]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},w,G,I],radial:["",G,I],conic:[w,G,I]},F,P]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ei()}],"gradient-via-pos":[{via:ei()}],"gradient-to-pos":[{to:ei()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[k,G,I]}],"outline-w":[{outline:["",k,U,T]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",u,H,L]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",m,H,L]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[k,T]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",p,H,L]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[k,G,I]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[k]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[G,I]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":A()}],"mask-image-conic-pos":[{"mask-conic":[k]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",G,I]}],filter:[{filter:["","none",G,I]}],blur:[{blur:em()}],brightness:[{brightness:[k,G,I]}],contrast:[{contrast:[k,G,I]}],"drop-shadow":[{"drop-shadow":["","none",f,H,L]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",k,G,I]}],"hue-rotate":[{"hue-rotate":[k,G,I]}],invert:[{invert:["",k,G,I]}],saturate:[{saturate:[k,G,I]}],sepia:[{sepia:["",k,G,I]}],"backdrop-filter":[{"backdrop-filter":["","none",G,I]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[k,G,I]}],"backdrop-contrast":[{"backdrop-contrast":[k,G,I]}],"backdrop-grayscale":[{"backdrop-grayscale":["",k,G,I]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[k,G,I]}],"backdrop-invert":[{"backdrop-invert":["",k,G,I]}],"backdrop-opacity":[{"backdrop-opacity":[k,G,I]}],"backdrop-saturate":[{"backdrop-saturate":[k,G,I]}],"backdrop-sepia":[{"backdrop-sepia":["",k,G,I]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":B()}],"border-spacing-x":[{"border-spacing-x":B()}],"border-spacing-y":[{"border-spacing-y":B()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",G,I]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[k,"initial",G,I]}],ease:[{ease:["linear","initial",v,G,I]}],delay:[{delay:[k,G,I]}],animate:[{animate:["none",M,G,I]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,G,I]}],"perspective-origin":[{"perspective-origin":R()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eh()}],"skew-x":[{"skew-x":eh()}],"skew-y":[{"skew-y":eh()}],transform:[{transform:[G,I,"","none","gpu","cpu"]}],"transform-origin":[{origin:R()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",G,I]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":B()}],"scroll-mx":[{"scroll-mx":B()}],"scroll-my":[{"scroll-my":B()}],"scroll-ms":[{"scroll-ms":B()}],"scroll-me":[{"scroll-me":B()}],"scroll-mt":[{"scroll-mt":B()}],"scroll-mr":[{"scroll-mr":B()}],"scroll-mb":[{"scroll-mb":B()}],"scroll-ml":[{"scroll-ml":B()}],"scroll-p":[{"scroll-p":B()}],"scroll-px":[{"scroll-px":B()}],"scroll-py":[{"scroll-py":B()}],"scroll-ps":[{"scroll-ps":B()}],"scroll-pe":[{"scroll-pe":B()}],"scroll-pt":[{"scroll-pt":B()}],"scroll-pr":[{"scroll-pr":B()}],"scroll-pb":[{"scroll-pb":B()}],"scroll-pl":[{"scroll-pl":B()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",G,I]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[k,U,T,S]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ee(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return Q(t(r))}},86981,e=>{"use strict";e.s(["default",()=>i],86981);var t=e.i(6943);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,t.forwardRef)((e,r)=>{let{color:a="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:c="",children:d,iconNode:u,...m}=e;return(0,t.createElement)("svg",{ref:r,...n,width:i,height:i,stroke:a,strokeWidth:l?24*Number(s)/Number(i):s,className:o("lucide",c),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(m)&&{"aria-hidden":"true"},...m},[...u.map(e=>{let[r,o]=e;return(0,t.createElement)(r,o)}),...Array.isArray(d)?d:[d]])}),i=(e,n)=>{let i=(0,t.forwardRef)((i,s)=>{let{className:l,...c}=i;return(0,t.createElement)(a,{ref:s,iconNode:n,className:o("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),l),...c})});return i.displayName=r(e),i}},67881,94237,e=>{"use strict";e.s(["Button",()=>c],67881);var t=e.i(65830),r=e.i(81808);e.s(["cva",()=>i],94237);var o=e.i(7284);let n=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,a=o.clsx,i=(e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:s}=t,l=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],o=null==s?void 0:s[e];if(null===t)return null;let a=n(t)||n(o);return i[e][a]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return a(e,l,null==t||null==(o=t.compoundVariants)?void 0:o.reduce((e,t)=>{let{class:r,className:o,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...c}[t]):({...s,...c})[t]===r})?[...e,r,o]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)};var s=e.i(47163);let l=i("inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium text-sm outline-none transition-all focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:border-input dark:bg-input/30 dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:o,variant:n,size:a,asChild:i=!1,...c}=e,d=i?r.Slot:"button";return(0,t.jsx)(d,{className:(0,s.cn)(l({variant:n,size:a,className:o})),"data-slot":"button",...c})}},46511,e=>{"use strict";e.s(["createContextScope",()=>o]);var t=e.i(6943),r=e.i(65830);function o(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[],a=()=>{let r=n.map(e=>t.createContext(e));return function(o){let n=(null==o?void 0:o[e])||r;return t.useMemo(()=>({["__scope".concat(e)]:{...o,[e]:n}}),[o,n])}};return a.scopeName=e,[function(o,a){let i=t.createContext(a),s=n.length;n=[...n,a];let l=o=>{var n;let{scope:a,children:l,...c}=o,d=(null==a||null==(n=a[e])?void 0:n[s])||i,u=t.useMemo(()=>c,Object.values(c));return(0,r.jsx)(d.Provider,{value:u,children:l})};return l.displayName=o+"Provider",[l,function(r,n){var l;let c=(null==n||null==(l=n[e])?void 0:l[s])||i,d=t.useContext(c);if(d)return d;if(void 0!==a)return a;throw Error("`".concat(r,"` must be used within `").concat(o,"`"))}]},function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];let n=r[0];if(1===r.length)return n;let a=()=>{let e=r.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(r){let o=e.reduce((e,t)=>{let{useScope:o,scopeName:n}=t,a=o(r)["__scope".concat(n)];return{...e,...a}},{});return t.useMemo(()=>({["__scope".concat(n.scopeName)]:o}),[o])}};return a.scopeName=n.scopeName,a}(a,...o)]}},91967,e=>{"use strict";function t(e,t){let{checkForDefaultPrevented:r=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(o){if(null==e||e(o),!1===r||!o.defaultPrevented)return null==t?void 0:t(o)}}e.s(["composeEventHandlers",()=>t]),"undefined"!=typeof window&&window.document&&window.document.createElement},43272,e=>{"use strict";e.s(["useLayoutEffect",()=>o]);var t,r=e.i(6943),o=(null==(t=globalThis)?void 0:t.document)?r.useLayoutEffect:()=>{}},77406,e=>{"use strict";e.s(["useControllableState",()=>n],77406);var t=e.i(6943),r=e.i(43272);t[" useEffectEvent ".trim().toString()],t[" useInsertionEffect ".trim().toString()];var o=t[" useInsertionEffect ".trim().toString()]||r.useLayoutEffect;function n(e){let{prop:r,defaultProp:n,onChange:a=()=>{},caller:i}=e,[s,l,c]=function(e){let{defaultProp:r,onChange:n}=e,[a,i]=t.useState(r),s=t.useRef(a),l=t.useRef(n);return o(()=>{l.current=n},[n]),t.useEffect(()=>{if(s.current!==a){var e;null==(e=l.current)||e.call(l,a),s.current=a}},[a,s]),[a,i,l]}({defaultProp:n,onChange:a}),d=void 0!==r,u=d?r:s;{let e=t.useRef(void 0!==r);t.useEffect(()=>{let t=e.current;if(t!==d){let e=d?"controlled":"uncontrolled";console.warn("".concat(i," is changing from ").concat(t?"controlled":"uncontrolled"," to ").concat(e,". Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component."))}e.current=d},[d,i])}return[u,t.useCallback(e=>{if(d){let o="function"==typeof e?e(r):e;if(o!==r){var t;null==(t=c.current)||t.call(c,o)}}else l(e)},[d,r,l,c])]}Symbol("RADIX:SYNC_STATE")},77590,e=>{"use strict";e.s(["useDirection",()=>o]);var t=e.i(6943);e.i(65830);var r=t.createContext(void 0);function o(e){let o=t.useContext(r);return e||o||"ltr"}},61702,e=>{"use strict";e.s(["Presence",()=>n]);var t=e.i(6943),r=e.i(68768),o=e.i(43272),n=e=>{let{present:n,children:i}=e,s=function(e){var r,n;let[i,s]=t.useState(),l=t.useRef(null),c=t.useRef(e),d=t.useRef("none"),[u,m]=(r=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},r));return t.useEffect(()=>{let e=a(l.current);d.current="mounted"===u?e:"none"},[u]),(0,o.useLayoutEffect)(()=>{let t=l.current,r=c.current;if(r!==e){let o=d.current,n=a(t);e?m("MOUNT"):"none"===n||(null==t?void 0:t.display)==="none"?m("UNMOUNT"):r&&o!==n?m("ANIMATION_OUT"):m("UNMOUNT"),c.current=e}},[e,m]),(0,o.useLayoutEffect)(()=>{if(i){var e;let t,r=null!=(e=i.ownerDocument.defaultView)?e:window,o=e=>{let o=a(l.current).includes(CSS.escape(e.animationName));if(e.target===i&&o&&(m("ANIMATION_END"),!c.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},n=e=>{e.target===i&&(d.current=a(l.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",o),i.addEventListener("animationend",o),()=>{r.clearTimeout(t),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",o),i.removeEventListener("animationend",o)}}m("ANIMATION_END")},[i,m]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:t.useCallback(e=>{l.current=e?getComputedStyle(e):null,s(e)},[])}}(n),l="function"==typeof i?i({present:s.isPresent}):t.Children.only(i),c=(0,r.useComposedRefs)(s.ref,function(e){var t,r;let o=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,n=o&&"isReactWarning"in o&&o.isReactWarning;return n?e.ref:(n=(o=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in o&&o.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof i||s.isPresent?t.cloneElement(l,{ref:c}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}n.displayName="Presence"},24186,e=>{"use strict";e.s(["useId",()=>a]);var t=e.i(6943),r=e.i(43272),o=t[" useId ".trim().toString()]||(()=>void 0),n=0;function a(e){let[a,i]=t.useState(o());return(0,r.useLayoutEffect)(()=>{e||i(e=>null!=e?e:String(n++))},[e]),e||(a?"radix-".concat(a):"")}},52683,e=>{"use strict";function t(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function r(e,r){var o=t(e,r,"get");return o.get?o.get.call(e):o.value}function o(e,r,o){var n=t(e,r,"set");if(n.set)n.set.call(e,o);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=o}return o}e.s(["createCollection",()=>d],52683);var n,a=e.i(6943),i=e.i(46511),s=e.i(68768),l=e.i(81808),c=e.i(65830);function d(e){let t=e+"CollectionProvider",[r,o]=(0,i.createContextScope)(t),[n,d]=r(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:r}=e,o=a.default.useRef(null),i=a.default.useRef(new Map).current;return(0,c.jsx)(n,{scope:t,itemMap:i,collectionRef:o,children:r})};u.displayName=t;let m=e+"CollectionSlot",p=(0,l.createSlot)(m),f=a.default.forwardRef((e,t)=>{let{scope:r,children:o}=e,n=d(m,r),a=(0,s.useComposedRefs)(t,n.collectionRef);return(0,c.jsx)(p,{ref:a,children:o})});f.displayName=m;let h=e+"CollectionItemSlot",g="data-radix-collection-item",b=(0,l.createSlot)(h),v=a.default.forwardRef((e,t)=>{let{scope:r,children:o,...n}=e,i=a.default.useRef(null),l=(0,s.useComposedRefs)(t,i),u=d(h,r);return a.default.useEffect(()=>(u.itemMap.set(i,{ref:i,...n}),()=>void u.itemMap.delete(i))),(0,c.jsx)(b,{...{[g]:""},ref:l,children:o})});return v.displayName=h,[{Provider:u,Slot:f,ItemSlot:v},function(t){let r=d(e+"CollectionConsumer",t);return a.default.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(g,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},o]}var u=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,o=p(t),n=o>=0?o:r+o;return n<0||n>=r?-1:n}(e,t);return -1===r?void 0:e[r]}function p(e){return e!=e||0===e?0:Math.trunc(e)}n=new WeakMap,class e extends Map{set(e,t){return u.get(this)&&(this.has(e)?r(this,n)[r(this,n).indexOf(e)]=e:r(this,n).push(e)),super.set(e,t),this}insert(e,t,o){let a,i=this.has(t),s=r(this,n).length,l=p(e),c=l>=0?l:s+l,d=c<0||c>=s?-1:c;if(d===this.size||i&&d===this.size-1||-1===d)return this.set(t,o),this;let u=this.size+ +!i;l<0&&c++;let m=[...r(this,n)],f=!1;for(let e=c;e<u;e++)if(c===e){let r=m[e];m[e]===t&&(r=m[e+1]),i&&this.delete(t),a=this.get(r),this.set(t,o)}else{f||m[e-1]!==t||(f=!0);let r=m[f?e:e-1],o=a;a=this.get(r),this.delete(r),this.set(r,o)}return this}with(t,r,o){let n=new e(this);return n.insert(t,r,o),n}before(e){let t=r(this,n).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,o){let a=r(this,n).indexOf(e);return -1===a?this:this.insert(a,t,o)}after(e){let t=r(this,n).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,o){let a=r(this,n).indexOf(e);return -1===a?this:this.insert(a+1,t,o)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return o(this,n,[]),super.clear()}delete(e){let t=super.delete(e);return t&&r(this,n).splice(r(this,n).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=m(r(this,n),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=m(r(this,n),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return r(this,n).indexOf(e)}keyAt(e){return m(r(this,n),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let o=r+t;return o<0&&(o=0),o>=this.size&&(o=this.size-1),this.at(o)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let o=r+t;return o<0&&(o=0),o>=this.size&&(o=this.size-1),this.keyAt(o)}find(e,t){let r=0;for(let o of this){if(Reflect.apply(e,t,[o,r,this]))return o;r++}}findIndex(e,t){let r=0;for(let o of this){if(Reflect.apply(e,t,[o,r,this]))return r;r++}return -1}filter(t,r){let o=[],n=0;for(let e of this)Reflect.apply(t,r,[e,n,this])&&o.push(e),n++;return new e(o)}map(t,r){let o=[],n=0;for(let e of this)o.push([e[0],Reflect.apply(t,r,[e,n,this])]),n++;return new e(o)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[o,n]=t,a=0,i=null!=n?n:this.at(0);for(let e of this)i=0===a&&1===t.length?e:Reflect.apply(o,this,[i,e,a,this]),a++;return i}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[o,n]=t,a=null!=n?n:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);a=e===this.size-1&&1===t.length?r:Reflect.apply(o,this,[a,r,e,this])}return a}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),o=this.get(r);t.set(r,o)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=arguments[o];let n=[...this.entries()];return n.splice(...r),new e(n)}slice(t,r){let o=new e,n=this.size-1;if(void 0===t)return o;t<0&&(t+=this.size),void 0!==r&&r>0&&(n=r-1);for(let e=t;e<=n;e++){let t=this.keyAt(e),r=this.get(t);o.set(t,r)}return o}every(e,t){let r=0;for(let o of this){if(!Reflect.apply(e,t,[o,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let o of this){if(Reflect.apply(e,t,[o,r,this]))return!0;r++}return!1}constructor(e){super(e),function(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}(this,n,{writable:!0,value:void 0}),o(this,n,[...super.keys()]),u.set(this,!0)}}},70287,e=>{"use strict";e.s(["ChevronDownIcon",()=>t],70287);let t=(0,e.i(86981).default)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},94710,e=>{"use strict";e.s(["features",()=>m,"featuresCompare",()=>p,"navItems",()=>u,"plans",()=>h,"reviews",()=>f],94710);var t=e.i(86981);let r=(0,t.default)("chart-no-axes-combined",[["path",{d:"M12 16v5",key:"zza2cw"}],["path",{d:"M16 14v7",key:"1g90b9"}],["path",{d:"M20 10v11",key:"1iqoj0"}],["path",{d:"m22 3-8.646 8.646a.5.5 0 0 1-.708 0L9.354 8.354a.5.5 0 0 0-.707 0L2 15",key:"1fw8x9"}],["path",{d:"M4 18v3",key:"1yp0dc"}],["path",{d:"M8 14v7",key:"n3cwzv"}]]),o=(0,t.default)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),n=(0,t.default)("database-zap",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 15 21.84",key:"14ibmq"}],["path",{d:"M21 5V8",key:"1marbg"}],["path",{d:"M21 12L18 17H22L19 22",key:"zafso"}],["path",{d:"M3 12A9 3 0 0 0 14.59 14.87",key:"1y4wr8"}]]),a=(0,t.default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),i=(0,t.default)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),s=(0,t.default)("message-circle",[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]]),l=(0,t.default)("notebook-pen",[["path",{d:"M13.4 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-7.4",key:"re6nr2"}],["path",{d:"M2 6h4",key:"aawbzj"}],["path",{d:"M2 10h4",key:"l0bgd4"}],["path",{d:"M2 14h4",key:"1gsvsf"}],["path",{d:"M2 18h4",key:"1bu2t1"}],["path",{d:"M21.378 5.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"pqwjuv"}]]),c=(0,t.default)("pen-line",[["path",{d:"M13 21h8",key:"1jsn5i"}],["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),d=(0,t.default)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),u=[{href:"/pricing",label:"Pricing",icon:a},{href:"/about",label:"About",icon:l},{href:"/docs",label:"Docs",icon:d},{href:"/privacy",label:"Privacy",icon:i}],m=[{icon:s,title:"chat",description:"Chat with anyone in team."},{icon:c,title:"writing",description:"Notion like editor for writing."},{icon:o,title:"tasks",description:"Automated task tracking."},{icon:d,title:"teams",description:"Collaborate with your team."},{icon:n,title:"storage",description:"Unlimited storage for your files."},{icon:r,title:"analytics",description:"Easy to track your progress."}],p=[{feature:"Doesn't train on your data",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!0,Notion:!0},{feature:"Works across your entire computer",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!0,Notion:!1},{feature:"Always one click away",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!0,Notion:!1},{feature:"Custom actions and automations",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!0,Notion:!1},{feature:"Understands anything you're looking at",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!1,Notion:!1},{feature:"Integrated audio transcription",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!1,Notion:!1},{feature:"Built for both Mac & Windows",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!1,Notion:!1}],f=[{name:"Eric Glyman",username:"Co-Founder at Ramp",body:"I've never seen anything like this before. It's amazing. I love it.",img:"https://avatar.vercel.sh/jack"},{name:"Eric James",username:"Co-Founder at Ramp",body:"I don't know what to say. I'm speechless. This is amazing.",img:"https://avatar.vercel.sh/jill"},{name:"Eric Kagabo",username:"Co-Founder at Ramp",body:"I'm at a loss for words. This is amazing. I love it.",img:"https://avatar.vercel.sh/john"},{name:"Eric Mugisha",username:"Co-Founder at Ramp",body:"I'm at a loss for words. This is amazing. I love it.",img:"https://avatar.vercel.sh/jane"},{name:"Eric David",username:"Co-Founder at Ramp",body:"I'm at a loss for words. This is amazing. I love it.",img:"https://avatar.vercel.sh/jenny"},{name:"Eric Tony",username:"Co-Founder at Ramp",body:"I'm at a loss for words. This is amazing. I love it.",img:"https://avatar.vercel.sh/james"}],h=[{id:"free",name:"Free",price:{monthly:0,yearly:0},description:"The perfect starting place for your web app or personal project.",features:["50 API calls / month","60 second checks","Single-user account","5 monitors","Basic email support"],cta:"Get started for free"},{id:"pro",name:"Pro",price:{monthly:90,yearly:75},description:"Everything you need to build and scale your business.",features:["Unlimited API calls","30 second checks","Multi-user account","10 monitors","Priority email support"],cta:"Subscribe to Pro",popular:!0},{id:"enterprise",name:"Enterprise",price:{monthly:"Get in touch for pricing",yearly:"Get in touch for pricing"},description:"Critical security, performance, observability and support.",features:["You can DDOS our API.","Nano-second checks.","Invite your extended family.","Unlimited monitors.","We'll sit on your desk."],cta:"Contact us"}]}]);