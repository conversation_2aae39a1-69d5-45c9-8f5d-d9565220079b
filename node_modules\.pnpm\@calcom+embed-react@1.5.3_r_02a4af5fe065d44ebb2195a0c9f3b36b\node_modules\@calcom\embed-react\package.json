{"name": "@calcom/embed-react", "sideEffects": false, "version": "1.5.3", "description": "Embed Cal Link as a React Component", "license": "SEE LICENSE IN LICENSE", "repository": {"type": "git", "url": "https://github.com/calcom/cal.com", "directory": "packages/embeds/embed-react"}, "scripts": {"dev": "vite --port=3101 --open", "build": "rm -rf dist && vite build && cp ./dist/Cal.es.js ./dist/Cal.es.mjs && tsc --emitDeclarationOnly --declarationDir dist", "preview": "vite preview", "type-check": "tsc --pretty --noEmit", "type-check:ci": "tsc-absolute --pretty --noEmit", "lint": "eslint --ext .ts,.js,.tsx,.jsx ./src", "embed-tests": "yarn playwright test --config=./playwright/config/playwright.config.ts", "embed-tests-quick": "QUICK=true yarn embed-tests", "embed-tests-update-snapshots:ci": "yarn embed-tests-quick --update-snapshots", "packaged:tests": "cd test/packaged && yarn tsc --noEmit && yarn run -T test -- --packaged-embed-tests-only", "withEmbedPublishEnv": "NEXT_PUBLIC_EMBED_LIB_URL='https://app.cal.com/embed/embed.js' NEXT_PUBLIC_WEBAPP_URL='https://app.cal.com' yarn", "prepack": "yarn ../../../ lint --filter='@calcom/embed-react' && yarn withEmbedPublishEnv build && yarn packaged:tests", "embed-web-start": "yarn workspace @calcom/web start", "embed-dev": "yarn workspace @calcom/embed-react dev", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "main": "./dist/Cal.umd.js", "module": "./dist/Cal.es.mjs", "types": "./dist/embed-react/src/index.d.ts", "peerDependencies": {"react": "^18.2.0 || ^19.0.0", "react-dom": "^18.2.0 || ^19.0.0"}, "files": ["dist"], "exports": {".": {"types": "./dist/embed-react/src/index.d.ts", "import": "./dist/Cal.es.mjs", "require": "./dist/Cal.umd.js"}}, "devDependencies": {"@playwright/test": "^1.45.3", "@types/react": "18.0.26", "@types/react-dom": "^18.0.9", "@vitejs/plugin-react": "^2.2.0", "eslint": "^8.34.0", "npm-run-all": "^4.1.5", "typescript": "^4.9.4", "vite": "^4.5.2"}, "dependencies": {"@calcom/embed-core": "1.5.3", "@calcom/embed-snippet": "1.3.3"}}