/** biome-ignore-all lint/nursery/useImageSize: <explanation> */
/** biome-ignore-all lint/performance/noImgElement: <explanation> */
export default function Parteners() {
  return (
    <section>
      <div className="mb-2 flex flex-col items-center font-aeonik">
        <h2 className="text-center font-medium text-lg text-muted-foreground/50">
          Backed by our Partners at
        </h2>
      </div>
      <div className="mx-auto mb-[160px] grid max-w-2xl grid-cols-2 gap-4 rounded-xl">
        <div className="relative flex h-32 items-center justify-center">
          <a
            className="relative flex h-full w-full items-center justify-center"
            href="https://www.generalcatalyst.com/"
            rel="noopener noreferrer"
            target="_blank"
          >
            <img
              alt="Logo 1"
              className="max-h-32 max-w-full object-contain"
              src="https://cdn.highlightai.com/media/landing/misc/backed-by/general-catalyst-logo.webp"
            />
          </a>
        </div>
        <div className="relative flex h-32 items-center justify-center">
          <a
            className="relative flex h-full w-full items-center justify-center"
            href="https://www.generalcatalyst.com/"
            rel="noopener noreferrer"
            target="_blank"
          >
            <img
              alt="Logo 1"
              className="max-h-32 max-w-full object-contain"
              src="https://cdn.highlightai.com/media/landing/misc/backed-by/general-catalyst-logo.webp"
            />
          </a>
        </div>
      </div>
    </section>
  );
}
