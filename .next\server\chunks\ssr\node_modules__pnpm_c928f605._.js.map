{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/domelementtype%402.3.0/node_modules/domelementtype/lib/esm/index.js"], "sourcesContent": ["/** Types of elements found in htmlparser2's DOM */\nexport var ElementType;\n(function (ElementType) {\n    /** Type for the root element of a document */\n    ElementType[\"Root\"] = \"root\";\n    /** Type for Text */\n    ElementType[\"Text\"] = \"text\";\n    /** Type for <? ... ?> */\n    ElementType[\"Directive\"] = \"directive\";\n    /** Type for <!-- ... --> */\n    ElementType[\"Comment\"] = \"comment\";\n    /** Type for <script> tags */\n    ElementType[\"Script\"] = \"script\";\n    /** Type for <style> tags */\n    ElementType[\"Style\"] = \"style\";\n    /** Type for Any tag */\n    ElementType[\"Tag\"] = \"tag\";\n    /** Type for <![CDATA[ ... ]]> */\n    ElementType[\"CDATA\"] = \"cdata\";\n    /** Type for <!doctype ...> */\n    ElementType[\"Doctype\"] = \"doctype\";\n})(ElementType || (ElementType = {}));\n/**\n * Tests whether an element is a tag or not.\n *\n * @param elem Element to test\n */\nexport function isTag(elem) {\n    return (elem.type === ElementType.Tag ||\n        elem.type === ElementType.Script ||\n        elem.type === ElementType.Style);\n}\n// Exports for backwards compatibility\n/** Type for the root element of a document */\nexport const Root = ElementType.Root;\n/** Type for Text */\nexport const Text = ElementType.Text;\n/** Type for <? ... ?> */\nexport const Directive = ElementType.Directive;\n/** Type for <!-- ... --> */\nexport const Comment = ElementType.Comment;\n/** Type for <script> tags */\nexport const Script = ElementType.Script;\n/** Type for <style> tags */\nexport const Style = ElementType.Style;\n/** Type for Any tag */\nexport const Tag = ElementType.Tag;\n/** Type for <![CDATA[ ... ]]> */\nexport const CDATA = ElementType.CDATA;\n/** Type for <!doctype ...> */\nexport const Doctype = ElementType.Doctype;\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;;;;;;;;;;;;;;;;;;;;;AAC1C,IAAI;AACX,CAAC,SAAU,WAAW;IAClB,4CAA4C,GAC5C,WAAW,CAAC,OAAO,GAAG;IACtB,kBAAkB,GAClB,WAAW,CAAC,OAAO,GAAG;IACtB,uBAAuB,GACvB,WAAW,CAAC,YAAY,GAAG;IAC3B,0BAA0B,GAC1B,WAAW,CAAC,UAAU,GAAG;IACzB,2BAA2B,GAC3B,WAAW,CAAC,SAAS,GAAG;IACxB,0BAA0B,GAC1B,WAAW,CAAC,QAAQ,GAAG;IACvB,qBAAqB,GACrB,WAAW,CAAC,MAAM,GAAG;IACrB,+BAA+B,GAC/B,WAAW,CAAC,QAAQ,GAAG;IACvB,4BAA4B,GAC5B,WAAW,CAAC,UAAU,GAAG;AAC7B,CAAC,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;AAM5B,SAAS,MAAM,IAAI;IACtB,OAAQ,KAAK,IAAI,KAAK,YAAY,GAAG,IACjC,KAAK,IAAI,KAAK,YAAY,MAAM,IAChC,KAAK,IAAI,KAAK,YAAY,KAAK;AACvC;AAGO,MAAM,OAAO,YAAY,IAAI;AAE7B,MAAM,OAAO,YAAY,IAAI;AAE7B,MAAM,YAAY,YAAY,SAAS;AAEvC,MAAM,UAAU,YAAY,OAAO;AAEnC,MAAM,SAAS,YAAY,MAAM;AAEjC,MAAM,QAAQ,YAAY,KAAK;AAE/B,MAAM,MAAM,YAAY,GAAG;AAE3B,MAAM,QAAQ,YAAY,KAAK;AAE/B,MAAM,UAAU,YAAY,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/domhandler%405.0.3/node_modules/domhandler/lib/esm/node.js"], "sourcesContent": ["import { ElementType, isTag as isTagRaw } from \"domelementtype\";\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nexport class Node {\n    constructor() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    // Read-write aliases for properties\n    /**\n     * Same as {@link parent}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get parentNode() {\n        return this.parent;\n    }\n    set parentNode(parent) {\n        this.parent = parent;\n    }\n    /**\n     * Same as {@link prev}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get previousSibling() {\n        return this.prev;\n    }\n    set previousSibling(prev) {\n        this.prev = prev;\n    }\n    /**\n     * Same as {@link next}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nextSibling() {\n        return this.next;\n    }\n    set nextSibling(next) {\n        this.next = next;\n    }\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    cloneNode(recursive = false) {\n        return cloneNode(this, recursive);\n    }\n}\n/**\n * A node that contains some data.\n */\nexport class DataNode extends Node {\n    /**\n     * @param data The content of the data node\n     */\n    constructor(data) {\n        super();\n        this.data = data;\n    }\n    /**\n     * Same as {@link data}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nodeValue() {\n        return this.data;\n    }\n    set nodeValue(data) {\n        this.data = data;\n    }\n}\n/**\n * Text within the document.\n */\nexport class Text extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.Text;\n    }\n    get nodeType() {\n        return 3;\n    }\n}\n/**\n * Comments within the document.\n */\nexport class Comment extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.Comment;\n    }\n    get nodeType() {\n        return 8;\n    }\n}\n/**\n * Processing instructions, including doc types.\n */\nexport class ProcessingInstruction extends DataNode {\n    constructor(name, data) {\n        super(data);\n        this.name = name;\n        this.type = ElementType.Directive;\n    }\n    get nodeType() {\n        return 1;\n    }\n}\n/**\n * A `Node` that can have children.\n */\nexport class NodeWithChildren extends Node {\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    constructor(children) {\n        super();\n        this.children = children;\n    }\n    // Aliases\n    /** First child of the node. */\n    get firstChild() {\n        var _a;\n        return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n    }\n    /** Last child of the node. */\n    get lastChild() {\n        return this.children.length > 0\n            ? this.children[this.children.length - 1]\n            : null;\n    }\n    /**\n     * Same as {@link children}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get childNodes() {\n        return this.children;\n    }\n    set childNodes(children) {\n        this.children = children;\n    }\n}\nexport class CDATA extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.CDATA;\n    }\n    get nodeType() {\n        return 4;\n    }\n}\n/**\n * The root node of the document.\n */\nexport class Document extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = ElementType.Root;\n    }\n    get nodeType() {\n        return 9;\n    }\n}\n/**\n * An element within the DOM.\n */\nexport class Element extends NodeWithChildren {\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    constructor(name, attribs, children = [], type = name === \"script\"\n        ? ElementType.Script\n        : name === \"style\"\n            ? ElementType.Style\n            : ElementType.Tag) {\n        super(children);\n        this.name = name;\n        this.attribs = attribs;\n        this.type = type;\n    }\n    get nodeType() {\n        return 1;\n    }\n    // DOM Level 1 aliases\n    /**\n     * Same as {@link name}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get tagName() {\n        return this.name;\n    }\n    set tagName(name) {\n        this.name = name;\n    }\n    get attributes() {\n        return Object.keys(this.attribs).map((name) => {\n            var _a, _b;\n            return ({\n                name,\n                value: this.attribs[name],\n                namespace: (_a = this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                prefix: (_b = this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n            });\n        });\n    }\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nexport function isTag(node) {\n    return isTagRaw(node);\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nexport function isCDATA(node) {\n    return node.type === ElementType.CDATA;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nexport function isText(node) {\n    return node.type === ElementType.Text;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nexport function isComment(node) {\n    return node.type === ElementType.Comment;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nexport function isDirective(node) {\n    return node.type === ElementType.Directive;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nexport function isDocument(node) {\n    return node.type === ElementType.Root;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nexport function hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nexport function cloneNode(node, recursive = false) {\n    let result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Element(node.name, { ...node.attribs }, children);\n        children.forEach((child) => (child.parent = clone));\n        if (node.namespace != null) {\n            clone.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone[\"x-attribsNamespace\"] = { ...node[\"x-attribsNamespace\"] };\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone[\"x-attribsPrefix\"] = { ...node[\"x-attribsPrefix\"] };\n        }\n        result = clone;\n    }\n    else if (isCDATA(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new CDATA(children);\n        children.forEach((child) => (child.parent = clone));\n        result = clone;\n    }\n    else if (isDocument(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Document(children);\n        children.forEach((child) => (child.parent = clone));\n        if (node[\"x-mode\"]) {\n            clone[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone;\n    }\n    else if (isDirective(node)) {\n        const instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(`Not implemented yet: ${node.type}`);\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nfunction cloneChildren(childs) {\n    const children = childs.map((child) => cloneNode(child, true));\n    for (let i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAKO,MAAM;IACT,aAAc;QACV,uBAAuB,GACvB,IAAI,CAAC,MAAM,GAAG;QACd,qBAAqB,GACrB,IAAI,CAAC,IAAI,GAAG;QACZ,iBAAiB,GACjB,IAAI,CAAC,IAAI,GAAG;QACZ,yFAAyF,GACzF,IAAI,CAAC,UAAU,GAAG;QAClB,qFAAqF,GACrF,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,oCAAoC;IACpC;;;KAGC,GACD,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,IAAI,WAAW,MAAM,EAAE;QACnB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;;KAGC,GACD,IAAI,kBAAkB;QAClB,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,IAAI,gBAAgB,IAAI,EAAE;QACtB,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;;;KAGC,GACD,IAAI,cAAc;QACd,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,IAAI,YAAY,IAAI,EAAE;QAClB,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;;;;;KAKC,GACD,UAAU,YAAY,KAAK,EAAE;QACzB,OAAO,UAAU,IAAI,EAAE;IAC3B;AACJ;AAIO,MAAM,iBAAiB;IAC1B;;KAEC,GACD,YAAY,IAAI,CAAE;QACd,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;;;KAGC,GACD,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,IAAI,UAAU,IAAI,EAAE;QAChB,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AAIO,MAAM,aAAa;IACtB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG,+NAAW,CAAC,IAAI;IAChC;IACA,IAAI,WAAW;QACX,OAAO;IACX;AACJ;AAIO,MAAM,gBAAgB;IACzB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG,+NAAW,CAAC,OAAO;IACnC;IACA,IAAI,WAAW;QACX,OAAO;IACX;AACJ;AAIO,MAAM,8BAA8B;IACvC,YAAY,IAAI,EAAE,IAAI,CAAE;QACpB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG,+NAAW,CAAC,SAAS;IACrC;IACA,IAAI,WAAW;QACX,OAAO;IACX;AACJ;AAIO,MAAM,yBAAyB;IAClC;;KAEC,GACD,YAAY,QAAQ,CAAE;QAClB,KAAK;QACL,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,UAAU;IACV,6BAA6B,GAC7B,IAAI,aAAa;QACb,IAAI;QACJ,OAAO,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IACpE;IACA,4BAA4B,GAC5B,IAAI,YAAY;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,GACvC;IACV;IACA;;;KAGC,GACD,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,IAAI,WAAW,QAAQ,EAAE;QACrB,IAAI,CAAC,QAAQ,GAAG;IACpB;AACJ;AACO,MAAM,cAAc;IACvB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG,+NAAW,CAAC,KAAK;IACjC;IACA,IAAI,WAAW;QACX,OAAO;IACX;AACJ;AAIO,MAAM,iBAAiB;IAC1B,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,IAAI,GAAG,+NAAW,CAAC,IAAI;IAChC;IACA,IAAI,WAAW;QACX,OAAO;IACX;AACJ;AAIO,MAAM,gBAAgB;IACzB;;;;KAIC,GACD,YAAY,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,EAAE,OAAO,SAAS,WACpD,+NAAW,CAAC,MAAM,GAClB,SAAS,UACL,+NAAW,CAAC,KAAK,GACjB,+NAAW,CAAC,GAAG,CAAE;QACvB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,IAAI,WAAW;QACX,OAAO;IACX;IACA,sBAAsB;IACtB;;;KAGC,GACD,IAAI,UAAU;QACV,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,IAAI,QAAQ,IAAI,EAAE;QACd,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,IAAI,aAAa;QACb,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;YAClC,IAAI,IAAI;YACR,OAAQ;gBACJ;gBACA,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;gBACzB,WAAW,CAAC,KAAK,IAAI,CAAC,qBAAqB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,KAAK;gBAC1F,QAAQ,CAAC,KAAK,IAAI,CAAC,kBAAkB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,KAAK;YACxF;QACJ;IACJ;AACJ;AAKO,SAAS,MAAM,IAAI;IACtB,OAAO,IAAA,yNAAQ,EAAC;AACpB;AAKO,SAAS,QAAQ,IAAI;IACxB,OAAO,KAAK,IAAI,KAAK,+NAAW,CAAC,KAAK;AAC1C;AAKO,SAAS,OAAO,IAAI;IACvB,OAAO,KAAK,IAAI,KAAK,+NAAW,CAAC,IAAI;AACzC;AAKO,SAAS,UAAU,IAAI;IAC1B,OAAO,KAAK,IAAI,KAAK,+NAAW,CAAC,OAAO;AAC5C;AAKO,SAAS,YAAY,IAAI;IAC5B,OAAO,KAAK,IAAI,KAAK,+NAAW,CAAC,SAAS;AAC9C;AAKO,SAAS,WAAW,IAAI;IAC3B,OAAO,KAAK,IAAI,KAAK,+NAAW,CAAC,IAAI;AACzC;AAKO,SAAS,YAAY,IAAI;IAC5B,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM;AACtD;AAOO,SAAS,UAAU,IAAI,EAAE,YAAY,KAAK;IAC7C,IAAI;IACJ,IAAI,OAAO,OAAO;QACd,SAAS,IAAI,KAAK,KAAK,IAAI;IAC/B,OACK,IAAI,UAAU,OAAO;QACtB,SAAS,IAAI,QAAQ,KAAK,IAAI;IAClC,OACK,IAAI,MAAM,OAAO;QAClB,MAAM,WAAW,YAAY,cAAc,KAAK,QAAQ,IAAI,EAAE;QAC9D,MAAM,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE;YAAE,GAAG,KAAK,OAAO;QAAC,GAAG;QAC1D,SAAS,OAAO,CAAC,CAAC,QAAW,MAAM,MAAM,GAAG;QAC5C,IAAI,KAAK,SAAS,IAAI,MAAM;YACxB,MAAM,SAAS,GAAG,KAAK,SAAS;QACpC;QACA,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,KAAK,CAAC,qBAAqB,GAAG;gBAAE,GAAG,IAAI,CAAC,qBAAqB;YAAC;QAClE;QACA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,KAAK,CAAC,kBAAkB,GAAG;gBAAE,GAAG,IAAI,CAAC,kBAAkB;YAAC;QAC5D;QACA,SAAS;IACb,OACK,IAAI,QAAQ,OAAO;QACpB,MAAM,WAAW,YAAY,cAAc,KAAK,QAAQ,IAAI,EAAE;QAC9D,MAAM,QAAQ,IAAI,MAAM;QACxB,SAAS,OAAO,CAAC,CAAC,QAAW,MAAM,MAAM,GAAG;QAC5C,SAAS;IACb,OACK,IAAI,WAAW,OAAO;QACvB,MAAM,WAAW,YAAY,cAAc,KAAK,QAAQ,IAAI,EAAE;QAC9D,MAAM,QAAQ,IAAI,SAAS;QAC3B,SAAS,OAAO,CAAC,CAAC,QAAW,MAAM,MAAM,GAAG;QAC5C,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;QACpC;QACA,SAAS;IACb,OACK,IAAI,YAAY,OAAO;QACxB,MAAM,cAAc,IAAI,sBAAsB,KAAK,IAAI,EAAE,KAAK,IAAI;QAClE,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM;YACxB,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;YACtC,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;YAC9C,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;QAClD;QACA,SAAS;IACb,OACK;QACD,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE;IACvD;IACA,OAAO,UAAU,GAAG,KAAK,UAAU;IACnC,OAAO,QAAQ,GAAG,KAAK,QAAQ;IAC/B,IAAI,KAAK,kBAAkB,IAAI,MAAM;QACjC,OAAO,kBAAkB,GAAG,KAAK,kBAAkB;IACvD;IACA,OAAO;AACX;AACA,SAAS,cAAc,MAAM;IACzB,MAAM,WAAW,OAAO,GAAG,CAAC,CAAC,QAAU,UAAU,OAAO;IACxD,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACtC,QAAQ,CAAC,EAAE,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE;QAClC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG,QAAQ,CAAC,EAAE;IACtC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/domhandler%405.0.3/node_modules/domhandler/lib/esm/index.js"], "sourcesContent": ["import { ElementType } from \"domelementtype\";\nimport { Element, Text, Comment, CDATA, Document, ProcessingInstruction, } from \"./node.js\";\nexport * from \"./node.js\";\n// Default options\nconst defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nexport class DomHandler {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    constructor(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    onparserinit(parser) {\n        this.parser = parser;\n    }\n    // Resets the handler back to starting state\n    onreset() {\n        this.dom = [];\n        this.root = new Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    }\n    // Signals the handler that parsing is done\n    onend() {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    }\n    onerror(error) {\n        this.handleCallback(error);\n    }\n    onclosetag() {\n        this.lastNode = null;\n        const elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    }\n    onopentag(name, attribs) {\n        const type = this.options.xmlMode ? ElementType.Tag : undefined;\n        const element = new Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    }\n    ontext(data) {\n        const { lastNode } = this;\n        if (lastNode && lastNode.type === ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            const node = new Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    }\n    oncomment(data) {\n        if (this.lastNode && this.lastNode.type === ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        const node = new Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    }\n    oncommentend() {\n        this.lastNode = null;\n    }\n    oncdatastart() {\n        const text = new Text(\"\");\n        const node = new CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    }\n    oncdataend() {\n        this.lastNode = null;\n    }\n    onprocessinginstruction(name, data) {\n        const node = new ProcessingInstruction(name, data);\n        this.addNode(node);\n    }\n    handleCallback(error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    }\n    addNode(node) {\n        const parent = this.tagStack[this.tagStack.length - 1];\n        const previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    }\n}\nexport default DomHandler;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAEA,kBAAkB;AAClB,MAAM,cAAc;IAChB,kBAAkB;IAClB,gBAAgB;IAChB,SAAS;AACb;AACO,MAAM;IACT;;;;KAIC,GACD,YAAY,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAE;QACtC,4BAA4B,GAC5B,IAAI,CAAC,GAAG,GAAG,EAAE;QACb,iCAAiC,GACjC,IAAI,CAAC,IAAI,GAAG,IAAI,mNAAQ,CAAC,IAAI,CAAC,GAAG;QACjC,kDAAkD,GAClD,IAAI,CAAC,IAAI,GAAG;QACZ,wBAAwB,GACxB,IAAI,CAAC,QAAQ,GAAG;YAAC,IAAI,CAAC,IAAI;SAAC;QAC3B,gDAAgD,GAChD,IAAI,CAAC,QAAQ,GAAG;QAChB,qEAAqE,GACrE,IAAI,CAAC,MAAM,GAAG;QACd,kEAAkE;QAClE,IAAI,OAAO,YAAY,YAAY;YAC/B,YAAY;YACZ,UAAU;QACd;QACA,IAAI,OAAO,aAAa,UAAU;YAC9B,UAAU;YACV,WAAW;QACf;QACA,IAAI,CAAC,QAAQ,GAAG,aAAa,QAAQ,aAAa,KAAK,IAAI,WAAW;QACtE,IAAI,CAAC,OAAO,GAAG,YAAY,QAAQ,YAAY,KAAK,IAAI,UAAU;QAClE,IAAI,CAAC,SAAS,GAAG,cAAc,QAAQ,cAAc,KAAK,IAAI,YAAY;IAC9E;IACA,aAAa,MAAM,EAAE;QACjB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,4CAA4C;IAC5C,UAAU;QACN,IAAI,CAAC,GAAG,GAAG,EAAE;QACb,IAAI,CAAC,IAAI,GAAG,IAAI,mNAAQ,CAAC,IAAI,CAAC,GAAG;QACjC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;YAAC,IAAI,CAAC,IAAI;SAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,2CAA2C;IAC3C,QAAQ;QACJ,IAAI,IAAI,CAAC,IAAI,EACT;QACJ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,cAAc,CAAC;IACxB;IACA,QAAQ,KAAK,EAAE;QACX,IAAI,CAAC,cAAc,CAAC;IACxB;IACA,aAAa;QACT,IAAI,CAAC,QAAQ,GAAG;QAChB,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG;QAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC7B,KAAK,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ;QACxC;QACA,IAAI,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,SAAS,CAAC;IACvB;IACA,UAAU,IAAI,EAAE,OAAO,EAAE;QACrB,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,+NAAW,CAAC,GAAG,GAAG;QACtD,MAAM,UAAU,IAAI,kNAAO,CAAC,MAAM,SAAS,WAAW;QACtD,IAAI,CAAC,OAAO,CAAC;QACb,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACvB;IACA,OAAO,IAAI,EAAE;QACT,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI;QACzB,IAAI,YAAY,SAAS,IAAI,KAAK,+NAAW,CAAC,IAAI,EAAE;YAChD,SAAS,IAAI,IAAI;YACjB,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;gBAC7B,SAAS,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ;YAC5C;QACJ,OACK;YACD,MAAM,OAAO,IAAI,+MAAI,CAAC;YACtB,IAAI,CAAC,OAAO,CAAC;YACb,IAAI,CAAC,QAAQ,GAAG;QACpB;IACJ;IACA,UAAU,IAAI,EAAE;QACZ,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,+NAAW,CAAC,OAAO,EAAE;YAC7D,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI;YACtB;QACJ;QACA,MAAM,OAAO,IAAI,kNAAO,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC;QACb,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,eAAe;QACX,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,eAAe;QACX,MAAM,OAAO,IAAI,+MAAI,CAAC;QACtB,MAAM,OAAO,IAAI,gNAAK,CAAC;YAAC;SAAK;QAC7B,IAAI,CAAC,OAAO,CAAC;QACb,KAAK,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,aAAa;QACT,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,wBAAwB,IAAI,EAAE,IAAI,EAAE;QAChC,MAAM,OAAO,IAAI,gOAAqB,CAAC,MAAM;QAC7C,IAAI,CAAC,OAAO,CAAC;IACjB;IACA,eAAe,KAAK,EAAE;QAClB,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,YAAY;YACrC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,GAAG;QACjC,OACK,IAAI,OAAO;YACZ,MAAM;QACV;IACJ;IACA,QAAQ,IAAI,EAAE;QACV,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE;QACtD,MAAM,kBAAkB,OAAO,QAAQ,CAAC,OAAO,QAAQ,CAAC,MAAM,GAAG,EAAE;QACnE,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YAC/B,KAAK,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU;QAC5C;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC7B,KAAK,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ;QACxC;QACA,OAAO,QAAQ,CAAC,IAAI,CAAC;QACrB,IAAI,iBAAiB;YACjB,KAAK,IAAI,GAAG;YACZ,gBAAgB,IAAI,GAAG;QAC3B;QACA,KAAK,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;IACpB;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/leac%400.6.0/node_modules/leac/lib/leac.mjs"], "sourcesContent": ["const e=/\\n/g;function n(n){const o=[...n.matchAll(e)].map((e=>e.index||0));o.unshift(-1);const s=t(o,0,o.length);return e=>r(s,e)}function t(e,n,r){if(r-n==1)return{offset:e[n],index:n+1};const o=Math.ceil((n+r)/2),s=t(e,n,o),l=t(e,o,r);return{offset:s.offset,low:s,high:l}}function r(e,n){return function(e){return Object.prototype.hasOwnProperty.call(e,\"index\")}(e)?{line:e.index,column:n-e.offset}:r(e.high.offset<n?e.high:e.low,n)}function o(e,t=\"\",r={}){const o=\"string\"!=typeof t?t:r,l=\"string\"==typeof t?t:\"\",c=e.map(s),f=!!o.lineNumbers;return function(e,t=0){const r=f?n(e):()=>({line:0,column:0});let o=t;const s=[];e:for(;o<e.length;){let n=!1;for(const t of c){t.regex.lastIndex=o;const c=t.regex.exec(e);if(c&&c[0].length>0){if(!t.discard){const e=r(o),n=\"string\"==typeof t.replace?c[0].replace(new RegExp(t.regex.source,t.regex.flags),t.replace):c[0];s.push({state:l,name:t.name,text:n,offset:o,len:c[0].length,line:e.line,column:e.column})}if(o=t.regex.lastIndex,n=!0,t.push){const n=t.push(e,o);s.push(...n.tokens),o=n.offset}if(t.pop)break e;break}}if(!n)break}return{tokens:s,offset:o,complete:e.length<=o}}}function s(e,n){return{...e,regex:l(e,n)}}function l(e,n){if(0===e.name.length)throw new Error(`Rule #${n} has empty name, which is not allowed.`);if(function(e){return Object.prototype.hasOwnProperty.call(e,\"regex\")}(e))return function(e){if(e.global)throw new Error(`Regular expression /${e.source}/${e.flags} contains the global flag, which is not allowed.`);return e.sticky?e:new RegExp(e.source,e.flags+\"y\")}(e.regex);if(function(e){return Object.prototype.hasOwnProperty.call(e,\"str\")}(e)){if(0===e.str.length)throw new Error(`Rule #${n} (\"${e.name}\") has empty \"str\" property, which is not allowed.`);return new RegExp(c(e.str),\"y\")}return new RegExp(c(e.name),\"y\")}function c(e){return e.replace(/[-[\\]{}()*+!<=:?./\\\\^$|#\\s,]/g,\"\\\\$&\")}export{o as createLexer};\n"], "names": [], "mappings": ";;;;AAAA,MAAM,IAAE;AAAM,SAAS,EAAE,CAAC;IAAE,MAAM,IAAE;WAAI,EAAE,QAAQ,CAAC;KAAG,CAAC,GAAG,CAAE,CAAA,IAAG,EAAE,KAAK,IAAE;IAAI,EAAE,OAAO,CAAC,CAAC;IAAG,MAAM,IAAE,EAAE,GAAE,GAAE,EAAE,MAAM;IAAE,OAAO,CAAA,IAAG,EAAE,GAAE;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,IAAG,IAAE,KAAG,GAAE,OAAM;QAAC,QAAO,CAAC,CAAC,EAAE;QAAC,OAAM,IAAE;IAAC;IAAE,MAAM,IAAE,KAAK,IAAI,CAAC,CAAC,IAAE,CAAC,IAAE,IAAG,IAAE,EAAE,GAAE,GAAE,IAAG,IAAE,EAAE,GAAE,GAAE;IAAG,OAAM;QAAC,QAAO,EAAE,MAAM;QAAC,KAAI;QAAE,MAAK;IAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAO,SAAS,CAAC;QAAE,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAE;IAAQ,EAAE,KAAG;QAAC,MAAK,EAAE,KAAK;QAAC,QAAO,IAAE,EAAE,MAAM;IAAA,IAAE,EAAE,EAAE,IAAI,CAAC,MAAM,GAAC,IAAE,EAAE,IAAI,GAAC,EAAE,GAAG,EAAC;AAAE;AAAC,SAAS,EAAE,CAAC,EAAC,IAAE,EAAE,EAAC,IAAE,CAAC,CAAC;IAAE,MAAM,KAAE,YAAU,OAAO,IAAE,IAAE,GAAE,IAAE,YAAU,OAAO,IAAE,IAAE,IAAG,IAAE,EAAE,GAAG,CAAC,IAAG,IAAE,CAAC,CAAC,GAAE,WAAW;IAAC,OAAO,SAAS,CAAC,EAAC,IAAE,CAAC;QAAE,MAAM,IAAE,IAAE,EAAE,KAAG,IAAI,CAAC;gBAAC,MAAK;gBAAE,QAAO;YAAC,CAAC;QAAE,IAAI,IAAE;QAAE,MAAM,IAAE,EAAE;QAAC,GAAE,MAAK,IAAE,EAAE,MAAM,EAAE;YAAC,IAAI,IAAE,CAAC;YAAE,KAAI,MAAM,KAAK,EAAE;gBAAC,EAAE,KAAK,CAAC,SAAS,GAAC;gBAAE,MAAM,IAAE,EAAE,KAAK,CAAC,IAAI,CAAC;gBAAG,IAAG,KAAG,CAAC,CAAC,EAAE,CAAC,MAAM,GAAC,GAAE;oBAAC,IAAG,CAAC,EAAE,OAAO,EAAC;wBAAC,MAAM,IAAE,EAAE,IAAG,IAAE,YAAU,OAAO,EAAE,OAAO,GAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,OAAO,EAAE,KAAK,CAAC,MAAM,EAAC,EAAE,KAAK,CAAC,KAAK,GAAE,EAAE,OAAO,IAAE,CAAC,CAAC,EAAE;wBAAC,EAAE,IAAI,CAAC;4BAAC,OAAM;4BAAE,MAAK,EAAE,IAAI;4BAAC,MAAK;4BAAE,QAAO;4BAAE,KAAI,CAAC,CAAC,EAAE,CAAC,MAAM;4BAAC,MAAK,EAAE,IAAI;4BAAC,QAAO,EAAE,MAAM;wBAAA;oBAAE;oBAAC,IAAG,IAAE,EAAE,KAAK,CAAC,SAAS,EAAC,IAAE,CAAC,GAAE,EAAE,IAAI,EAAC;wBAAC,MAAM,IAAE,EAAE,IAAI,CAAC,GAAE;wBAAG,EAAE,IAAI,IAAI,EAAE,MAAM,GAAE,IAAE,EAAE,MAAM;oBAAA;oBAAC,IAAG,EAAE,GAAG,EAAC,MAAM;oBAAE;gBAAK;YAAC;YAAC,IAAG,CAAC,GAAE;QAAK;QAAC,OAAM;YAAC,QAAO;YAAE,QAAO;YAAE,UAAS,EAAE,MAAM,IAAE;QAAC;IAAC;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAM;QAAC,GAAG,CAAC;QAAC,OAAM,EAAE,GAAE;IAAE;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAG,MAAI,EAAE,IAAI,CAAC,MAAM,EAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,EAAE,sCAAsC,CAAC;IAAE,IAAG,SAAS,CAAC;QAAE,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAE;IAAQ,EAAE,IAAG,OAAO,SAAS,CAAC;QAAE,IAAG,EAAE,MAAM,EAAC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,gDAAgD,CAAC;QAAE,OAAO,EAAE,MAAM,GAAC,IAAE,IAAI,OAAO,EAAE,MAAM,EAAC,EAAE,KAAK,GAAC;IAAI,EAAE,EAAE,KAAK;IAAE,IAAG,SAAS,CAAC;QAAE,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAE;IAAM,EAAE,IAAG;QAAC,IAAG,MAAI,EAAE,GAAG,CAAC,MAAM,EAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,kDAAkD,CAAC;QAAE,OAAO,IAAI,OAAO,EAAE,EAAE,GAAG,GAAE;IAAI;IAAC,OAAO,IAAI,OAAO,EAAE,EAAE,IAAI,GAAE;AAAI;AAAC,SAAS,EAAE,CAAC;IAAE,OAAO,EAAE,OAAO,CAAC,iCAAgC;AAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/peberminta%400.9.0/node_modules/peberminta/lib/util.mjs"], "sourcesContent": ["function clamp(left, x, right) {\n    return Math.max(left, Math.min(x, right));\n}\nfunction escapeWhitespace(str) {\n    return str.replace(/(\\t)|(\\r)|(\\n)/g, (m, t, r) => t ? '\\\\t' : r ? '\\\\r' : '\\\\n');\n}\n\nexport { clamp, escapeWhitespace };\n"], "names": [], "mappings": ";;;;;;AAAA,SAAS,MAAM,IAAI,EAAE,CAAC,EAAE,KAAK;IACzB,OAAO,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,GAAG;AACtC;AACA,SAAS,iBAAiB,GAAG;IACzB,OAAO,IAAI,OAAO,CAAC,mBAAmB,CAAC,GAAG,GAAG,IAAM,IAAI,QAAQ,IAAI,QAAQ;AAC/E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/peberminta%400.9.0/node_modules/peberminta/lib/core.mjs"], "sourcesContent": ["import { clamp, escapeWhitespace } from './util.mjs';\n\nfunction emit(value) {\n    return (data, i) => ({\n        matched: true,\n        position: i,\n        value: value\n    });\n}\nfunction make(\nf) {\n    return (data, i) => ({\n        matched: true,\n        position: i,\n        value: f(data, i)\n    });\n}\nfunction action(\nf) {\n    return (data, i) => {\n        f(data, i);\n        return {\n            matched: true,\n            position: i,\n            value: null\n        };\n    };\n}\nfunction fail(\ndata, i) {\n    return { matched: false };\n}\nfunction error(message) {\n    return (data, i) => {\n        throw new Error((message instanceof Function) ? message(data, i) : message);\n    };\n}\nfunction token(\nonToken,\nonEnd) {\n    return (data, i) => {\n        let position = i;\n        let value = undefined;\n        if (i < data.tokens.length) {\n            value = onToken(data.tokens[i], data, i);\n            if (value !== undefined) {\n                position++;\n            }\n        }\n        else {\n            onEnd?.(data, i);\n        }\n        return (value === undefined)\n            ? { matched: false }\n            : {\n                matched: true,\n                position: position,\n                value: value\n            };\n    };\n}\nfunction any(data, i) {\n    return (i < data.tokens.length)\n        ? {\n            matched: true,\n            position: i + 1,\n            value: data.tokens[i]\n        }\n        : { matched: false };\n}\nfunction satisfy(\ntest) {\n    return (data, i) => (i < data.tokens.length && test(data.tokens[i], data, i))\n        ? {\n            matched: true,\n            position: i + 1,\n            value: data.tokens[i]\n        }\n        : { matched: false };\n}\nfunction mapInner(r, f) {\n    return (r.matched) ? ({\n        matched: true,\n        position: r.position,\n        value: f(r.value, r.position)\n    }) : r;\n}\nfunction mapOuter(r, f) {\n    return (r.matched) ? f(r) : r;\n}\nfunction map(p, mapper) {\n    return (data, i) => mapInner(p(data, i), (v, j) => mapper(v, data, i, j));\n}\nfunction map1(p,\nmapper) {\n    return (data, i) => mapOuter(p(data, i), (m) => mapper(m, data, i));\n}\nfunction peek(p, f) {\n    return (data, i) => {\n        const r = p(data, i);\n        f(r, data, i);\n        return r;\n    };\n}\nfunction option(p, def) {\n    return (data, i) => {\n        const r = p(data, i);\n        return (r.matched)\n            ? r\n            : {\n                matched: true,\n                position: i,\n                value: def\n            };\n    };\n}\nfunction not(p) {\n    return (data, i) => {\n        const r = p(data, i);\n        return (r.matched)\n            ? { matched: false }\n            : {\n                matched: true,\n                position: i,\n                value: true\n            };\n    };\n}\nfunction choice(...ps) {\n    return (data, i) => {\n        for (const p of ps) {\n            const result = p(data, i);\n            if (result.matched) {\n                return result;\n            }\n        }\n        return { matched: false };\n    };\n}\nfunction otherwise(pa, pb) {\n    return (data, i) => {\n        const r1 = pa(data, i);\n        return (r1.matched)\n            ? r1\n            : pb(data, i);\n    };\n}\nfunction longest(...ps) {\n    return (data, i) => {\n        let match = undefined;\n        for (const p of ps) {\n            const result = p(data, i);\n            if (result.matched && (!match || match.position < result.position)) {\n                match = result;\n            }\n        }\n        return match || { matched: false };\n    };\n}\nfunction takeWhile(p,\ntest) {\n    return (data, i) => {\n        const values = [];\n        let success = true;\n        do {\n            const r = p(data, i);\n            if (r.matched && test(r.value, values.length + 1, data, i, r.position)) {\n                values.push(r.value);\n                i = r.position;\n            }\n            else {\n                success = false;\n            }\n        } while (success);\n        return {\n            matched: true,\n            position: i,\n            value: values\n        };\n    };\n}\nfunction takeUntil(p,\ntest) {\n    return takeWhile(p, (value, n, data, i, j) => !test(value, n, data, i, j));\n}\nfunction takeWhileP(pValue, pTest) {\n    return takeWhile(pValue, (value, n, data, i) => pTest(data, i).matched);\n}\nfunction takeUntilP(pValue, pTest) {\n    return takeWhile(pValue, (value, n, data, i) => !pTest(data, i).matched);\n}\nfunction many(p) {\n    return takeWhile(p, () => true);\n}\nfunction many1(p) {\n    return ab(p, many(p), (head, tail) => [head, ...tail]);\n}\nfunction ab(pa, pb, join) {\n    return (data, i) => mapOuter(pa(data, i), (ma) => mapInner(pb(data, ma.position), (vb, j) => join(ma.value, vb, data, i, j)));\n}\nfunction left(pa, pb) {\n    return ab(pa, pb, (va) => va);\n}\nfunction right(pa, pb) {\n    return ab(pa, pb, (va, vb) => vb);\n}\nfunction abc(pa, pb, pc, join) {\n    return (data, i) => mapOuter(pa(data, i), (ma) => mapOuter(pb(data, ma.position), (mb) => mapInner(pc(data, mb.position), (vc, j) => join(ma.value, mb.value, vc, data, i, j))));\n}\nfunction middle(pa, pb, pc) {\n    return abc(pa, pb, pc, (ra, rb) => rb);\n}\nfunction all(...ps) {\n    return (data, i) => {\n        const result = [];\n        let position = i;\n        for (const p of ps) {\n            const r1 = p(data, position);\n            if (r1.matched) {\n                result.push(r1.value);\n                position = r1.position;\n            }\n            else {\n                return { matched: false };\n            }\n        }\n        return {\n            matched: true,\n            position: position,\n            value: result\n        };\n    };\n}\nfunction skip(...ps) {\n    return map(all(...ps), () => null);\n}\nfunction flatten(...ps) {\n    return flatten1(all(...ps));\n}\nfunction flatten1(p) {\n    return map(p, (vs) => vs.flatMap((v) => v));\n}\nfunction sepBy1(pValue, pSep) {\n    return ab(pValue, many(right(pSep, pValue)), (head, tail) => [head, ...tail]);\n}\nfunction sepBy(pValue, pSep) {\n    return otherwise(sepBy1(pValue, pSep), emit([]));\n}\nfunction chainReduce(acc,\nf) {\n    return (data, i) => {\n        let loop = true;\n        let acc1 = acc;\n        let pos = i;\n        do {\n            const r = f(acc1, data, pos)(data, pos);\n            if (r.matched) {\n                acc1 = r.value;\n                pos = r.position;\n            }\n            else {\n                loop = false;\n            }\n        } while (loop);\n        return {\n            matched: true,\n            position: pos,\n            value: acc1\n        };\n    };\n}\nfunction reduceLeft(acc, p,\nreducer) {\n    return chainReduce(acc, (acc) => map(p, (v, data, i, j) => reducer(acc, v, data, i, j)));\n}\nfunction reduceRight(p, acc,\nreducer) {\n    return map(many(p), (vs, data, i, j) => vs.reduceRight((acc, v) => reducer(v, acc, data, i, j), acc));\n}\nfunction leftAssoc1(pLeft, pOper) {\n    return chain(pLeft, (v0) => reduceLeft(v0, pOper, (acc, f) => f(acc)));\n}\nfunction rightAssoc1(pOper, pRight) {\n    return ab(reduceRight(pOper, (y) => y, (f, acc) => (y) => f(acc(y))), pRight, (f, v) => f(v));\n}\nfunction leftAssoc2(pLeft, pOper, pRight) {\n    return chain(pLeft, (v0) => reduceLeft(v0, ab(pOper, pRight, (f, y) => [f, y]), (acc, [f, y]) => f(acc, y)));\n}\nfunction rightAssoc2(pLeft, pOper, pRight) {\n    return ab(reduceRight(ab(pLeft, pOper, (x, f) => [x, f]), (y) => y, ([x, f], acc) => (y) => f(x, acc(y))), pRight, (f, v) => f(v));\n}\nfunction condition(cond, pTrue, pFalse) {\n    return (data, i) => (cond(data, i))\n        ? pTrue(data, i)\n        : pFalse(data, i);\n}\nfunction decide(p) {\n    return (data, i) => mapOuter(p(data, i), (m1) => m1.value(data, m1.position));\n}\nfunction chain(p,\nf) {\n    return (data, i) => mapOuter(p(data, i), (m1) => f(m1.value, data, i, m1.position)(data, m1.position));\n}\nfunction ahead(p) {\n    return (data, i) => mapOuter(p(data, i), (m1) => ({\n        matched: true,\n        position: i,\n        value: m1.value\n    }));\n}\nfunction recursive(f) {\n    return function (data, i) {\n        return f()(data, i);\n    };\n}\nfunction start(data, i) {\n    return (i !== 0)\n        ? { matched: false }\n        : {\n            matched: true,\n            position: i,\n            value: true\n        };\n}\nfunction end(data, i) {\n    return (i < data.tokens.length)\n        ? { matched: false }\n        : {\n            matched: true,\n            position: i,\n            value: true\n        };\n}\nfunction remainingTokensNumber(data, i) {\n    return data.tokens.length - i;\n}\nfunction parserPosition(data, i, formatToken, contextTokens = 3) {\n    const len = data.tokens.length;\n    const lowIndex = clamp(0, i - contextTokens, len - contextTokens);\n    const highIndex = clamp(contextTokens, i + 1 + contextTokens, len);\n    const tokensSlice = data.tokens.slice(lowIndex, highIndex);\n    const lines = [];\n    const indexWidth = String(highIndex - 1).length + 1;\n    if (i < 0) {\n        lines.push(`${String(i).padStart(indexWidth)} >>`);\n    }\n    if (0 < lowIndex) {\n        lines.push('...'.padStart(indexWidth + 6));\n    }\n    for (let j = 0; j < tokensSlice.length; j++) {\n        const index = lowIndex + j;\n        lines.push(`${String(index).padStart(indexWidth)} ${(index === i ? '>' : ' ')} ${escapeWhitespace(formatToken(tokensSlice[j]))}`);\n    }\n    if (highIndex < len) {\n        lines.push('...'.padStart(indexWidth + 6));\n    }\n    if (len <= i) {\n        lines.push(`${String(i).padStart(indexWidth)} >>`);\n    }\n    return lines.join('\\n');\n}\nfunction parse(parser, tokens, options, formatToken = JSON.stringify) {\n    const data = { tokens: tokens, options: options };\n    const result = parser(data, 0);\n    if (!result.matched) {\n        throw new Error('No match');\n    }\n    if (result.position < data.tokens.length) {\n        throw new Error(`Partial match. Parsing stopped at:\\n${parserPosition(data, result.position, formatToken)}`);\n    }\n    return result.value;\n}\nfunction tryParse(parser, tokens, options) {\n    const result = parser({ tokens: tokens, options: options }, 0);\n    return (result.matched)\n        ? result.value\n        : undefined;\n}\nfunction match(matcher, tokens, options) {\n    const result = matcher({ tokens: tokens, options: options }, 0);\n    return result.value;\n}\n\nexport { ab, abc, action, ahead, all, all as and, any, chain, chainReduce, choice, condition, decide, skip as discard, otherwise as eitherOr, emit, end, end as eof, error, fail, flatten, flatten1, left, leftAssoc1, leftAssoc2, longest, ahead as lookAhead, make, many, many1, map, map1, match, middle, not, emit as of, option, choice as or, otherwise, parse, parserPosition, peek, recursive, reduceLeft, reduceRight, remainingTokensNumber, right, rightAssoc1, rightAssoc2, satisfy, sepBy, sepBy1, skip, many1 as some, start, takeUntil, takeUntilP, takeWhile, takeWhileP, token, tryParse };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA,SAAS,KAAK,KAAK;IACf,OAAO,CAAC,MAAM,IAAM,CAAC;YACjB,SAAS;YACT,UAAU;YACV,OAAO;QACX,CAAC;AACL;AACA,SAAS,KACT,CAAC;IACG,OAAO,CAAC,MAAM,IAAM,CAAC;YACjB,SAAS;YACT,UAAU;YACV,OAAO,EAAE,MAAM;QACnB,CAAC;AACL;AACA,SAAS,OACT,CAAC;IACG,OAAO,CAAC,MAAM;QACV,EAAE,MAAM;QACR,OAAO;YACH,SAAS;YACT,UAAU;YACV,OAAO;QACX;IACJ;AACJ;AACA,SAAS,KACT,IAAI,EAAE,CAAC;IACH,OAAO;QAAE,SAAS;IAAM;AAC5B;AACA,SAAS,MAAM,OAAO;IAClB,OAAO,CAAC,MAAM;QACV,MAAM,IAAI,MAAM,AAAC,mBAAmB,WAAY,QAAQ,MAAM,KAAK;IACvE;AACJ;AACA,SAAS,MACT,OAAO,EACP,KAAK;IACD,OAAO,CAAC,MAAM;QACV,IAAI,WAAW;QACf,IAAI,QAAQ;QACZ,IAAI,IAAI,KAAK,MAAM,CAAC,MAAM,EAAE;YACxB,QAAQ,QAAQ,KAAK,MAAM,CAAC,EAAE,EAAE,MAAM;YACtC,IAAI,UAAU,WAAW;gBACrB;YACJ;QACJ,OACK;YACD,QAAQ,MAAM;QAClB;QACA,OAAO,AAAC,UAAU,YACZ;YAAE,SAAS;QAAM,IACjB;YACE,SAAS;YACT,UAAU;YACV,OAAO;QACX;IACR;AACJ;AACA,SAAS,IAAI,IAAI,EAAE,CAAC;IAChB,OAAO,AAAC,IAAI,KAAK,MAAM,CAAC,MAAM,GACxB;QACE,SAAS;QACT,UAAU,IAAI;QACd,OAAO,KAAK,MAAM,CAAC,EAAE;IACzB,IACE;QAAE,SAAS;IAAM;AAC3B;AACA,SAAS,QACT,IAAI;IACA,OAAO,CAAC,MAAM,IAAM,AAAC,IAAI,KAAK,MAAM,CAAC,MAAM,IAAI,KAAK,KAAK,MAAM,CAAC,EAAE,EAAE,MAAM,KACpE;YACE,SAAS;YACT,UAAU,IAAI;YACd,OAAO,KAAK,MAAM,CAAC,EAAE;QACzB,IACE;YAAE,SAAS;QAAM;AAC3B;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IAClB,OAAO,AAAC,EAAE,OAAO,GAAK;QAClB,SAAS;QACT,UAAU,EAAE,QAAQ;QACpB,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,QAAQ;IAChC,IAAK;AACT;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IAClB,OAAO,AAAC,EAAE,OAAO,GAAI,EAAE,KAAK;AAChC;AACA,SAAS,IAAI,CAAC,EAAE,MAAM;IAClB,OAAO,CAAC,MAAM,IAAM,SAAS,EAAE,MAAM,IAAI,CAAC,GAAG,IAAM,OAAO,GAAG,MAAM,GAAG;AAC1E;AACA,SAAS,KAAK,CAAC,EACf,MAAM;IACF,OAAO,CAAC,MAAM,IAAM,SAAS,EAAE,MAAM,IAAI,CAAC,IAAM,OAAO,GAAG,MAAM;AACpE;AACA,SAAS,KAAK,CAAC,EAAE,CAAC;IACd,OAAO,CAAC,MAAM;QACV,MAAM,IAAI,EAAE,MAAM;QAClB,EAAE,GAAG,MAAM;QACX,OAAO;IACX;AACJ;AACA,SAAS,OAAO,CAAC,EAAE,GAAG;IAClB,OAAO,CAAC,MAAM;QACV,MAAM,IAAI,EAAE,MAAM;QAClB,OAAO,AAAC,EAAE,OAAO,GACX,IACA;YACE,SAAS;YACT,UAAU;YACV,OAAO;QACX;IACR;AACJ;AACA,SAAS,IAAI,CAAC;IACV,OAAO,CAAC,MAAM;QACV,MAAM,IAAI,EAAE,MAAM;QAClB,OAAO,AAAC,EAAE,OAAO,GACX;YAAE,SAAS;QAAM,IACjB;YACE,SAAS;YACT,UAAU;YACV,OAAO;QACX;IACR;AACJ;AACA,SAAS,OAAO,GAAG,EAAE;IACjB,OAAO,CAAC,MAAM;QACV,KAAK,MAAM,KAAK,GAAI;YAChB,MAAM,SAAS,EAAE,MAAM;YACvB,IAAI,OAAO,OAAO,EAAE;gBAChB,OAAO;YACX;QACJ;QACA,OAAO;YAAE,SAAS;QAAM;IAC5B;AACJ;AACA,SAAS,UAAU,EAAE,EAAE,EAAE;IACrB,OAAO,CAAC,MAAM;QACV,MAAM,KAAK,GAAG,MAAM;QACpB,OAAO,AAAC,GAAG,OAAO,GACZ,KACA,GAAG,MAAM;IACnB;AACJ;AACA,SAAS,QAAQ,GAAG,EAAE;IAClB,OAAO,CAAC,MAAM;QACV,IAAI,QAAQ;QACZ,KAAK,MAAM,KAAK,GAAI;YAChB,MAAM,SAAS,EAAE,MAAM;YACvB,IAAI,OAAO,OAAO,IAAI,CAAC,CAAC,SAAS,MAAM,QAAQ,GAAG,OAAO,QAAQ,GAAG;gBAChE,QAAQ;YACZ;QACJ;QACA,OAAO,SAAS;YAAE,SAAS;QAAM;IACrC;AACJ;AACA,SAAS,UAAU,CAAC,EACpB,IAAI;IACA,OAAO,CAAC,MAAM;QACV,MAAM,SAAS,EAAE;QACjB,IAAI,UAAU;QACd,GAAG;YACC,MAAM,IAAI,EAAE,MAAM;YAClB,IAAI,EAAE,OAAO,IAAI,KAAK,EAAE,KAAK,EAAE,OAAO,MAAM,GAAG,GAAG,MAAM,GAAG,EAAE,QAAQ,GAAG;gBACpE,OAAO,IAAI,CAAC,EAAE,KAAK;gBACnB,IAAI,EAAE,QAAQ;YAClB,OACK;gBACD,UAAU;YACd;QACJ,QAAS,QAAS;QAClB,OAAO;YACH,SAAS;YACT,UAAU;YACV,OAAO;QACX;IACJ;AACJ;AACA,SAAS,UAAU,CAAC,EACpB,IAAI;IACA,OAAO,UAAU,GAAG,CAAC,OAAO,GAAG,MAAM,GAAG,IAAM,CAAC,KAAK,OAAO,GAAG,MAAM,GAAG;AAC3E;AACA,SAAS,WAAW,MAAM,EAAE,KAAK;IAC7B,OAAO,UAAU,QAAQ,CAAC,OAAO,GAAG,MAAM,IAAM,MAAM,MAAM,GAAG,OAAO;AAC1E;AACA,SAAS,WAAW,MAAM,EAAE,KAAK;IAC7B,OAAO,UAAU,QAAQ,CAAC,OAAO,GAAG,MAAM,IAAM,CAAC,MAAM,MAAM,GAAG,OAAO;AAC3E;AACA,SAAS,KAAK,CAAC;IACX,OAAO,UAAU,GAAG,IAAM;AAC9B;AACA,SAAS,MAAM,CAAC;IACZ,OAAO,GAAG,GAAG,KAAK,IAAI,CAAC,MAAM,OAAS;YAAC;eAAS;SAAK;AACzD;AACA,SAAS,GAAG,EAAE,EAAE,EAAE,EAAE,IAAI;IACpB,OAAO,CAAC,MAAM,IAAM,SAAS,GAAG,MAAM,IAAI,CAAC,KAAO,SAAS,GAAG,MAAM,GAAG,QAAQ,GAAG,CAAC,IAAI,IAAM,KAAK,GAAG,KAAK,EAAE,IAAI,MAAM,GAAG;AAC7H;AACA,SAAS,KAAK,EAAE,EAAE,EAAE;IAChB,OAAO,GAAG,IAAI,IAAI,CAAC,KAAO;AAC9B;AACA,SAAS,MAAM,EAAE,EAAE,EAAE;IACjB,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,KAAO;AAClC;AACA,SAAS,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI;IACzB,OAAO,CAAC,MAAM,IAAM,SAAS,GAAG,MAAM,IAAI,CAAC,KAAO,SAAS,GAAG,MAAM,GAAG,QAAQ,GAAG,CAAC,KAAO,SAAS,GAAG,MAAM,GAAG,QAAQ,GAAG,CAAC,IAAI,IAAM,KAAK,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,IAAI,MAAM,GAAG;AAC/K;AACA,SAAS,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE;IACtB,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAO;AACvC;AACA,SAAS,IAAI,GAAG,EAAE;IACd,OAAO,CAAC,MAAM;QACV,MAAM,SAAS,EAAE;QACjB,IAAI,WAAW;QACf,KAAK,MAAM,KAAK,GAAI;YAChB,MAAM,KAAK,EAAE,MAAM;YACnB,IAAI,GAAG,OAAO,EAAE;gBACZ,OAAO,IAAI,CAAC,GAAG,KAAK;gBACpB,WAAW,GAAG,QAAQ;YAC1B,OACK;gBACD,OAAO;oBAAE,SAAS;gBAAM;YAC5B;QACJ;QACA,OAAO;YACH,SAAS;YACT,UAAU;YACV,OAAO;QACX;IACJ;AACJ;AACA,SAAS,KAAK,GAAG,EAAE;IACf,OAAO,IAAI,OAAO,KAAK,IAAM;AACjC;AACA,SAAS,QAAQ,GAAG,EAAE;IAClB,OAAO,SAAS,OAAO;AAC3B;AACA,SAAS,SAAS,CAAC;IACf,OAAO,IAAI,GAAG,CAAC,KAAO,GAAG,OAAO,CAAC,CAAC,IAAM;AAC5C;AACA,SAAS,OAAO,MAAM,EAAE,IAAI;IACxB,OAAO,GAAG,QAAQ,KAAK,MAAM,MAAM,UAAU,CAAC,MAAM,OAAS;YAAC;eAAS;SAAK;AAChF;AACA,SAAS,MAAM,MAAM,EAAE,IAAI;IACvB,OAAO,UAAU,OAAO,QAAQ,OAAO,KAAK,EAAE;AAClD;AACA,SAAS,YAAY,GAAG,EACxB,CAAC;IACG,OAAO,CAAC,MAAM;QACV,IAAI,OAAO;QACX,IAAI,OAAO;QACX,IAAI,MAAM;QACV,GAAG;YACC,MAAM,IAAI,EAAE,MAAM,MAAM,KAAK,MAAM;YACnC,IAAI,EAAE,OAAO,EAAE;gBACX,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,QAAQ;YACpB,OACK;gBACD,OAAO;YACX;QACJ,QAAS,KAAM;QACf,OAAO;YACH,SAAS;YACT,UAAU;YACV,OAAO;QACX;IACJ;AACJ;AACA,SAAS,WAAW,GAAG,EAAE,CAAC,EAC1B,OAAO;IACH,OAAO,YAAY,KAAK,CAAC,MAAQ,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,IAAM,QAAQ,KAAK,GAAG,MAAM,GAAG;AACxF;AACA,SAAS,YAAY,CAAC,EAAE,GAAG,EAC3B,OAAO;IACH,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,MAAM,GAAG,IAAM,GAAG,WAAW,CAAC,CAAC,KAAK,IAAM,QAAQ,GAAG,KAAK,MAAM,GAAG,IAAI;AACpG;AACA,SAAS,WAAW,KAAK,EAAE,KAAK;IAC5B,OAAO,MAAM,OAAO,CAAC,KAAO,WAAW,IAAI,OAAO,CAAC,KAAK,IAAM,EAAE;AACpE;AACA,SAAS,YAAY,KAAK,EAAE,MAAM;IAC9B,OAAO,GAAG,YAAY,OAAO,CAAC,IAAM,GAAG,CAAC,GAAG,MAAQ,CAAC,IAAM,EAAE,IAAI,MAAM,QAAQ,CAAC,GAAG,IAAM,EAAE;AAC9F;AACA,SAAS,WAAW,KAAK,EAAE,KAAK,EAAE,MAAM;IACpC,OAAO,MAAM,OAAO,CAAC,KAAO,WAAW,IAAI,GAAG,OAAO,QAAQ,CAAC,GAAG,IAAM;gBAAC;gBAAG;aAAE,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,GAAK,EAAE,KAAK;AAC5G;AACA,SAAS,YAAY,KAAK,EAAE,KAAK,EAAE,MAAM;IACrC,OAAO,GAAG,YAAY,GAAG,OAAO,OAAO,CAAC,GAAG,IAAM;YAAC;YAAG;SAAE,GAAG,CAAC,IAAM,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,MAAQ,CAAC,IAAM,EAAE,GAAG,IAAI,MAAM,QAAQ,CAAC,GAAG,IAAM,EAAE;AACnI;AACA,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,MAAM;IAClC,OAAO,CAAC,MAAM,IAAM,AAAC,KAAK,MAAM,KAC1B,MAAM,MAAM,KACZ,OAAO,MAAM;AACvB;AACA,SAAS,OAAO,CAAC;IACb,OAAO,CAAC,MAAM,IAAM,SAAS,EAAE,MAAM,IAAI,CAAC,KAAO,GAAG,KAAK,CAAC,MAAM,GAAG,QAAQ;AAC/E;AACA,SAAS,MAAM,CAAC,EAChB,CAAC;IACG,OAAO,CAAC,MAAM,IAAM,SAAS,EAAE,MAAM,IAAI,CAAC,KAAO,EAAE,GAAG,KAAK,EAAE,MAAM,GAAG,GAAG,QAAQ,EAAE,MAAM,GAAG,QAAQ;AACxG;AACA,SAAS,MAAM,CAAC;IACZ,OAAO,CAAC,MAAM,IAAM,SAAS,EAAE,MAAM,IAAI,CAAC,KAAO,CAAC;gBAC9C,SAAS;gBACT,UAAU;gBACV,OAAO,GAAG,KAAK;YACnB,CAAC;AACL;AACA,SAAS,UAAU,CAAC;IAChB,OAAO,SAAU,IAAI,EAAE,CAAC;QACpB,OAAO,IAAI,MAAM;IACrB;AACJ;AACA,SAAS,MAAM,IAAI,EAAE,CAAC;IAClB,OAAO,AAAC,MAAM,IACR;QAAE,SAAS;IAAM,IACjB;QACE,SAAS;QACT,UAAU;QACV,OAAO;IACX;AACR;AACA,SAAS,IAAI,IAAI,EAAE,CAAC;IAChB,OAAO,AAAC,IAAI,KAAK,MAAM,CAAC,MAAM,GACxB;QAAE,SAAS;IAAM,IACjB;QACE,SAAS;QACT,UAAU;QACV,OAAO;IACX;AACR;AACA,SAAS,sBAAsB,IAAI,EAAE,CAAC;IAClC,OAAO,KAAK,MAAM,CAAC,MAAM,GAAG;AAChC;AACA,SAAS,eAAe,IAAI,EAAE,CAAC,EAAE,WAAW,EAAE,gBAAgB,CAAC;IAC3D,MAAM,MAAM,KAAK,MAAM,CAAC,MAAM;IAC9B,MAAM,WAAW,IAAA,0MAAK,EAAC,GAAG,IAAI,eAAe,MAAM;IACnD,MAAM,YAAY,IAAA,0MAAK,EAAC,eAAe,IAAI,IAAI,eAAe;IAC9D,MAAM,cAAc,KAAK,MAAM,CAAC,KAAK,CAAC,UAAU;IAChD,MAAM,QAAQ,EAAE;IAChB,MAAM,aAAa,OAAO,YAAY,GAAG,MAAM,GAAG;IAClD,IAAI,IAAI,GAAG;QACP,MAAM,IAAI,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC,YAAY,GAAG,CAAC;IACrD;IACA,IAAI,IAAI,UAAU;QACd,MAAM,IAAI,CAAC,MAAM,QAAQ,CAAC,aAAa;IAC3C;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QACzC,MAAM,QAAQ,WAAW;QACzB,MAAM,IAAI,CAAC,GAAG,OAAO,OAAO,QAAQ,CAAC,YAAY,CAAC,EAAG,UAAU,IAAI,MAAM,IAAK,CAAC,EAAE,IAAA,qNAAgB,EAAC,YAAY,WAAW,CAAC,EAAE,IAAI;IACpI;IACA,IAAI,YAAY,KAAK;QACjB,MAAM,IAAI,CAAC,MAAM,QAAQ,CAAC,aAAa;IAC3C;IACA,IAAI,OAAO,GAAG;QACV,MAAM,IAAI,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC,YAAY,GAAG,CAAC;IACrD;IACA,OAAO,MAAM,IAAI,CAAC;AACtB;AACA,SAAS,MAAM,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,KAAK,SAAS;IAChE,MAAM,OAAO;QAAE,QAAQ;QAAQ,SAAS;IAAQ;IAChD,MAAM,SAAS,OAAO,MAAM;IAC5B,IAAI,CAAC,OAAO,OAAO,EAAE;QACjB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,OAAO,QAAQ,GAAG,KAAK,MAAM,CAAC,MAAM,EAAE;QACtC,MAAM,IAAI,MAAM,CAAC,oCAAoC,EAAE,eAAe,MAAM,OAAO,QAAQ,EAAE,cAAc;IAC/G;IACA,OAAO,OAAO,KAAK;AACvB;AACA,SAAS,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO;IACrC,MAAM,SAAS,OAAO;QAAE,QAAQ;QAAQ,SAAS;IAAQ,GAAG;IAC5D,OAAO,AAAC,OAAO,OAAO,GAChB,OAAO,KAAK,GACZ;AACV;AACA,SAAS,MAAM,OAAO,EAAE,MAAM,EAAE,OAAO;IACnC,MAAM,SAAS,QAAQ;QAAE,QAAQ;QAAQ,SAAS;IAAQ,GAAG;IAC7D,OAAO,OAAO,KAAK;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1143, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/parseley%400.12.1/node_modules/parseley/lib/parseley.mjs"], "sourcesContent": ["import { createLexer } from 'leac';\nimport * as p from 'peberminta';\n\nvar ast = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst ws = `(?:[ \\\\t\\\\r\\\\n\\\\f]*)`;\nconst nl = `(?:\\\\n|\\\\r\\\\n|\\\\r|\\\\f)`;\nconst nonascii = `[^\\\\x00-\\\\x7F]`;\nconst unicode = `(?:\\\\\\\\[0-9a-f]{1,6}(?:\\\\r\\\\n|[ \\\\n\\\\r\\\\t\\\\f])?)`;\nconst escape = `(?:\\\\\\\\[^\\\\n\\\\r\\\\f0-9a-f])`;\nconst nmstart = `(?:[_a-z]|${nonascii}|${unicode}|${escape})`;\nconst nmchar = `(?:[_a-z0-9-]|${nonascii}|${unicode}|${escape})`;\nconst name = `(?:${nmchar}+)`;\nconst ident = `(?:[-]?${nmstart}${nmchar}*)`;\nconst string1 = `'([^\\\\n\\\\r\\\\f\\\\\\\\']|\\\\\\\\${nl}|${nonascii}|${unicode}|${escape})*'`;\nconst string2 = `\"([^\\\\n\\\\r\\\\f\\\\\\\\\"]|\\\\\\\\${nl}|${nonascii}|${unicode}|${escape})*\"`;\nconst lexSelector = createLexer([\n    { name: 'ws', regex: new RegExp(ws) },\n    { name: 'hash', regex: new RegExp(`#${name}`, 'i') },\n    { name: 'ident', regex: new RegExp(ident, 'i') },\n    { name: 'str1', regex: new RegExp(string1, 'i') },\n    { name: 'str2', regex: new RegExp(string2, 'i') },\n    { name: '*' },\n    { name: '.' },\n    { name: ',' },\n    { name: '[' },\n    { name: ']' },\n    { name: '=' },\n    { name: '>' },\n    { name: '|' },\n    { name: '+' },\n    { name: '~' },\n    { name: '^' },\n    { name: '$' },\n]);\nconst lexEscapedString = createLexer([\n    { name: 'unicode', regex: new RegExp(unicode, 'i') },\n    { name: 'escape', regex: new RegExp(escape, 'i') },\n    { name: 'any', regex: new RegExp('[\\\\s\\\\S]', 'i') }\n]);\nfunction sumSpec([a0, a1, a2], [b0, b1, b2]) {\n    return [a0 + b0, a1 + b1, a2 + b2];\n}\nfunction sumAllSpec(ss) {\n    return ss.reduce(sumSpec, [0, 0, 0]);\n}\nconst unicodeEscapedSequence_ = p.token((t) => t.name === 'unicode' ? String.fromCodePoint(parseInt(t.text.slice(1), 16)) : undefined);\nconst escapedSequence_ = p.token((t) => t.name === 'escape' ? t.text.slice(1) : undefined);\nconst anyChar_ = p.token((t) => t.name === 'any' ? t.text : undefined);\nconst escapedString_ = p.map(p.many(p.or(unicodeEscapedSequence_, escapedSequence_, anyChar_)), (cs) => cs.join(''));\nfunction unescape(escapedString) {\n    const lexerResult = lexEscapedString(escapedString);\n    const result = escapedString_({ tokens: lexerResult.tokens, options: undefined }, 0);\n    return result.value;\n}\nfunction literal(name) {\n    return p.token((t) => t.name === name ? true : undefined);\n}\nconst whitespace_ = p.token((t) => t.name === 'ws' ? null : undefined);\nconst optionalWhitespace_ = p.option(whitespace_, null);\nfunction optionallySpaced(parser) {\n    return p.middle(optionalWhitespace_, parser, optionalWhitespace_);\n}\nconst identifier_ = p.token((t) => t.name === 'ident' ? unescape(t.text) : undefined);\nconst hashId_ = p.token((t) => t.name === 'hash' ? unescape(t.text.slice(1)) : undefined);\nconst string_ = p.token((t) => t.name.startsWith('str') ? unescape(t.text.slice(1, -1)) : undefined);\nconst namespace_ = p.left(p.option(identifier_, ''), literal('|'));\nconst qualifiedName_ = p.eitherOr(p.ab(namespace_, identifier_, (ns, name) => ({ name: name, namespace: ns })), p.map(identifier_, (name) => ({ name: name, namespace: null })));\nconst uniSelector_ = p.eitherOr(p.ab(namespace_, literal('*'), (ns) => ({ type: 'universal', namespace: ns, specificity: [0, 0, 0] })), p.map(literal('*'), () => ({ type: 'universal', namespace: null, specificity: [0, 0, 0] })));\nconst tagSelector_ = p.map(qualifiedName_, ({ name, namespace }) => ({\n    type: 'tag',\n    name: name,\n    namespace: namespace,\n    specificity: [0, 0, 1]\n}));\nconst classSelector_ = p.ab(literal('.'), identifier_, (fullstop, name) => ({\n    type: 'class',\n    name: name,\n    specificity: [0, 1, 0]\n}));\nconst idSelector_ = p.map(hashId_, (name) => ({\n    type: 'id',\n    name: name,\n    specificity: [1, 0, 0]\n}));\nconst attrModifier_ = p.token((t) => {\n    if (t.name === 'ident') {\n        if (t.text === 'i' || t.text === 'I') {\n            return 'i';\n        }\n        if (t.text === 's' || t.text === 'S') {\n            return 's';\n        }\n    }\n    return undefined;\n});\nconst attrValue_ = p.eitherOr(p.ab(string_, p.option(p.right(optionalWhitespace_, attrModifier_), null), (v, mod) => ({ value: v, modifier: mod })), p.ab(identifier_, p.option(p.right(whitespace_, attrModifier_), null), (v, mod) => ({ value: v, modifier: mod })));\nconst attrMatcher_ = p.choice(p.map(literal('='), () => '='), p.ab(literal('~'), literal('='), () => '~='), p.ab(literal('|'), literal('='), () => '|='), p.ab(literal('^'), literal('='), () => '^='), p.ab(literal('$'), literal('='), () => '$='), p.ab(literal('*'), literal('='), () => '*='));\nconst attrPresenceSelector_ = p.abc(literal('['), optionallySpaced(qualifiedName_), literal(']'), (lbr, { name, namespace }) => ({\n    type: 'attrPresence',\n    name: name,\n    namespace: namespace,\n    specificity: [0, 1, 0]\n}));\nconst attrValueSelector_ = p.middle(literal('['), p.abc(optionallySpaced(qualifiedName_), attrMatcher_, optionallySpaced(attrValue_), ({ name, namespace }, matcher, { value, modifier }) => ({\n    type: 'attrValue',\n    name: name,\n    namespace: namespace,\n    matcher: matcher,\n    value: value,\n    modifier: modifier,\n    specificity: [0, 1, 0]\n})), literal(']'));\nconst attrSelector_ = p.eitherOr(attrPresenceSelector_, attrValueSelector_);\nconst typeSelector_ = p.eitherOr(uniSelector_, tagSelector_);\nconst subclassSelector_ = p.choice(idSelector_, classSelector_, attrSelector_);\nconst compoundSelector_ = p.map(p.eitherOr(p.flatten(typeSelector_, p.many(subclassSelector_)), p.many1(subclassSelector_)), (ss) => {\n    return {\n        type: 'compound',\n        list: ss,\n        specificity: sumAllSpec(ss.map(s => s.specificity))\n    };\n});\nconst combinator_ = p.choice(p.map(literal('>'), () => '>'), p.map(literal('+'), () => '+'), p.map(literal('~'), () => '~'), p.ab(literal('|'), literal('|'), () => '||'));\nconst combinatorSeparator_ = p.eitherOr(optionallySpaced(combinator_), p.map(whitespace_, () => ' '));\nconst complexSelector_ = p.leftAssoc2(compoundSelector_, p.map(combinatorSeparator_, (c) => (left, right) => ({\n    type: 'compound',\n    list: [...right.list, { type: 'combinator', combinator: c, left: left, specificity: left.specificity }],\n    specificity: sumSpec(left.specificity, right.specificity)\n})), compoundSelector_);\nconst listSelector_ = p.leftAssoc2(p.map(complexSelector_, (s) => ({ type: 'list', list: [s] })), p.map(optionallySpaced(literal(',')), () => (acc, next) => ({ type: 'list', list: [...acc.list, next] })), complexSelector_);\nfunction parse_(parser, str) {\n    if (!(typeof str === 'string' || str instanceof String)) {\n        throw new Error('Expected a selector string. Actual input is not a string!');\n    }\n    const lexerResult = lexSelector(str);\n    if (!lexerResult.complete) {\n        throw new Error(`The input \"${str}\" was only partially tokenized, stopped at offset ${lexerResult.offset}!\\n` +\n            prettyPrintPosition(str, lexerResult.offset));\n    }\n    const result = optionallySpaced(parser)({ tokens: lexerResult.tokens, options: undefined }, 0);\n    if (!result.matched) {\n        throw new Error(`No match for \"${str}\" input!`);\n    }\n    if (result.position < lexerResult.tokens.length) {\n        const token = lexerResult.tokens[result.position];\n        throw new Error(`The input \"${str}\" was only partially parsed, stopped at offset ${token.offset}!\\n` +\n            prettyPrintPosition(str, token.offset, token.len));\n    }\n    return result.value;\n}\nfunction prettyPrintPosition(str, offset, len = 1) {\n    return `${str.replace(/(\\t)|(\\r)|(\\n)/g, (m, t, r) => t ? '\\u2409' : r ? '\\u240d' : '\\u240a')}\\n${''.padEnd(offset)}${'^'.repeat(len)}`;\n}\nfunction parse(str) {\n    return parse_(listSelector_, str);\n}\nfunction parse1(str) {\n    return parse_(complexSelector_, str);\n}\n\nfunction serialize(selector) {\n    if (!selector.type) {\n        throw new Error('This is not an AST node.');\n    }\n    switch (selector.type) {\n        case 'universal':\n            return _serNs(selector.namespace) + '*';\n        case 'tag':\n            return _serNs(selector.namespace) + _serIdent(selector.name);\n        case 'class':\n            return '.' + _serIdent(selector.name);\n        case 'id':\n            return '#' + _serIdent(selector.name);\n        case 'attrPresence':\n            return `[${_serNs(selector.namespace)}${_serIdent(selector.name)}]`;\n        case 'attrValue':\n            return `[${_serNs(selector.namespace)}${_serIdent(selector.name)}${selector.matcher}\"${_serStr(selector.value)}\"${(selector.modifier ? selector.modifier : '')}]`;\n        case 'combinator':\n            return serialize(selector.left) + selector.combinator;\n        case 'compound':\n            return selector.list.reduce((acc, node) => {\n                if (node.type === 'combinator') {\n                    return serialize(node) + acc;\n                }\n                else {\n                    return acc + serialize(node);\n                }\n            }, '');\n        case 'list':\n            return selector.list.map(serialize).join(',');\n    }\n}\nfunction _serNs(ns) {\n    return (ns || ns === '')\n        ? _serIdent(ns) + '|'\n        : '';\n}\nfunction _codePoint(char) {\n    return `\\\\${char.codePointAt(0).toString(16)} `;\n}\nfunction _serIdent(str) {\n    return str.replace(\n    /(^[0-9])|(^-[0-9])|(^-$)|([-0-9a-zA-Z_]|[^\\x00-\\x7F])|(\\x00)|([\\x01-\\x1f]|\\x7f)|([\\s\\S])/g, (m, d1, d2, hy, safe, nl, ctrl, other) => d1 ? _codePoint(d1) :\n        d2 ? '-' + _codePoint(d2.slice(1)) :\n            hy ? '\\\\-' :\n                safe ? safe :\n                    nl ? '\\ufffd' :\n                        ctrl ? _codePoint(ctrl) :\n                            '\\\\' + other);\n}\nfunction _serStr(str) {\n    return str.replace(\n    /(\")|(\\\\)|(\\x00)|([\\x01-\\x1f]|\\x7f)/g, (m, dq, bs, nl, ctrl) => dq ? '\\\\\"' :\n        bs ? '\\\\\\\\' :\n            nl ? '\\ufffd' :\n                _codePoint(ctrl));\n}\nfunction normalize(selector) {\n    if (!selector.type) {\n        throw new Error('This is not an AST node.');\n    }\n    switch (selector.type) {\n        case 'compound': {\n            selector.list.forEach(normalize);\n            selector.list.sort((a, b) => _compareArrays(_getSelectorPriority(a), _getSelectorPriority(b)));\n            break;\n        }\n        case 'combinator': {\n            normalize(selector.left);\n            break;\n        }\n        case 'list': {\n            selector.list.forEach(normalize);\n            selector.list.sort((a, b) => (serialize(a) < serialize(b)) ? -1 : 1);\n            break;\n        }\n    }\n    return selector;\n}\nfunction _getSelectorPriority(selector) {\n    switch (selector.type) {\n        case 'universal':\n            return [1];\n        case 'tag':\n            return [1];\n        case 'id':\n            return [2];\n        case 'class':\n            return [3, selector.name];\n        case 'attrPresence':\n            return [4, serialize(selector)];\n        case 'attrValue':\n            return [5, serialize(selector)];\n        case 'combinator':\n            return [15, serialize(selector)];\n    }\n}\nfunction compareSelectors(a, b) {\n    return _compareArrays(a.specificity, b.specificity);\n}\nfunction compareSpecificity(a, b) {\n    return _compareArrays(a, b);\n}\nfunction _compareArrays(a, b) {\n    if (!Array.isArray(a) || !Array.isArray(b)) {\n        throw new Error('Arguments must be arrays.');\n    }\n    const shorter = (a.length < b.length) ? a.length : b.length;\n    for (let i = 0; i < shorter; i++) {\n        if (a[i] === b[i]) {\n            continue;\n        }\n        return (a[i] < b[i]) ? -1 : 1;\n    }\n    return a.length - b.length;\n}\n\nexport { ast as Ast, compareSelectors, compareSpecificity, normalize, parse, parse1, serialize };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEA,IAAI,MAAM,WAAW,GAAE,OAAO,MAAM,CAAC;IACjC,WAAW;AACf;AAEA,MAAM,KAAK,CAAC,oBAAoB,CAAC;AACjC,MAAM,KAAK,CAAC,sBAAsB,CAAC;AACnC,MAAM,WAAW,CAAC,cAAc,CAAC;AACjC,MAAM,UAAU,CAAC,gDAAgD,CAAC;AAClE,MAAM,SAAS,CAAC,0BAA0B,CAAC;AAC3C,MAAM,UAAU,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;AAC7D,MAAM,SAAS,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;AAChE,MAAM,OAAO,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC;AAC7B,MAAM,QAAQ,CAAC,OAAO,EAAE,UAAU,OAAO,EAAE,CAAC;AAC5C,MAAM,UAAU,CAAC,wBAAwB,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,CAAC;AACnF,MAAM,UAAU,CAAC,wBAAwB,EAAE,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE,QAAQ,CAAC,EAAE,OAAO,GAAG,CAAC;AACnF,MAAM,cAAc,IAAA,oMAAW,EAAC;IAC5B;QAAE,MAAM;QAAM,OAAO,IAAI,OAAO;IAAI;IACpC;QAAE,MAAM;QAAQ,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE;IAAK;IACnD;QAAE,MAAM;QAAS,OAAO,IAAI,OAAO,OAAO;IAAK;IAC/C;QAAE,MAAM;QAAQ,OAAO,IAAI,OAAO,SAAS;IAAK;IAChD;QAAE,MAAM;QAAQ,OAAO,IAAI,OAAO,SAAS;IAAK;IAChD;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;IACZ;QAAE,MAAM;IAAI;CACf;AACD,MAAM,mBAAmB,IAAA,oMAAW,EAAC;IACjC;QAAE,MAAM;QAAW,OAAO,IAAI,OAAO,SAAS;IAAK;IACnD;QAAE,MAAM;QAAU,OAAO,IAAI,OAAO,QAAQ;IAAK;IACjD;QAAE,MAAM;QAAO,OAAO,IAAI,OAAO,YAAY;IAAK;CACrD;AACD,SAAS,QAAQ,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,IAAI,GAAG;IACvC,OAAO;QAAC,KAAK;QAAI,KAAK;QAAI,KAAK;KAAG;AACtC;AACA,SAAS,WAAW,EAAE;IAClB,OAAO,GAAG,MAAM,CAAC,SAAS;QAAC;QAAG;QAAG;KAAE;AACvC;AACA,MAAM,0BAA0B,0MAAO,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,YAAY,OAAO,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO;AAC5H,MAAM,mBAAmB,0MAAO,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;AAChF,MAAM,WAAW,0MAAO,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,QAAQ,EAAE,IAAI,GAAG;AAC5D,MAAM,iBAAiB,wMAAK,CAAC,yMAAM,CAAC,uMAAI,CAAC,yBAAyB,kBAAkB,YAAY,CAAC,KAAO,GAAG,IAAI,CAAC;AAChH,SAAS,SAAS,aAAa;IAC3B,MAAM,cAAc,iBAAiB;IACrC,MAAM,SAAS,eAAe;QAAE,QAAQ,YAAY,MAAM;QAAE,SAAS;IAAU,GAAG;IAClF,OAAO,OAAO,KAAK;AACvB;AACA,SAAS,QAAQ,IAAI;IACjB,OAAO,0MAAO,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,OAAO,OAAO;AACnD;AACA,MAAM,cAAc,0MAAO,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,OAAO,OAAO;AAC5D,MAAM,sBAAsB,2MAAQ,CAAC,aAAa;AAClD,SAAS,iBAAiB,MAAM;IAC5B,OAAO,2MAAQ,CAAC,qBAAqB,QAAQ;AACjD;AACA,MAAM,cAAc,0MAAO,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,UAAU,SAAS,EAAE,IAAI,IAAI;AAC3E,MAAM,UAAU,0MAAO,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,SAAS,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;AAC/E,MAAM,UAAU,0MAAO,CAAC,CAAC,IAAM,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM;AAC1F,MAAM,aAAa,yMAAM,CAAC,2MAAQ,CAAC,aAAa,KAAK,QAAQ;AAC7D,MAAM,iBAAiB,6MAAU,CAAC,uMAAI,CAAC,YAAY,aAAa,CAAC,IAAI,OAAS,CAAC;QAAE,MAAM;QAAM,WAAW;IAAG,CAAC,IAAI,wMAAK,CAAC,aAAa,CAAC,OAAS,CAAC;QAAE,MAAM;QAAM,WAAW;IAAK,CAAC;AAC7K,MAAM,eAAe,6MAAU,CAAC,uMAAI,CAAC,YAAY,QAAQ,MAAM,CAAC,KAAO,CAAC;QAAE,MAAM;QAAa,WAAW;QAAI,aAAa;YAAC;YAAG;YAAG;SAAE;IAAC,CAAC,IAAI,wMAAK,CAAC,QAAQ,MAAM,IAAM,CAAC;QAAE,MAAM;QAAa,WAAW;QAAM,aAAa;YAAC;YAAG;YAAG;SAAE;IAAC,CAAC;AACjO,MAAM,eAAe,wMAAK,CAAC,gBAAgB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,GAAK,CAAC;QACjE,MAAM;QACN,MAAM;QACN,WAAW;QACX,aAAa;YAAC;YAAG;YAAG;SAAE;IAC1B,CAAC;AACD,MAAM,iBAAiB,uMAAI,CAAC,QAAQ,MAAM,aAAa,CAAC,UAAU,OAAS,CAAC;QACxE,MAAM;QACN,MAAM;QACN,aAAa;YAAC;YAAG;YAAG;SAAE;IAC1B,CAAC;AACD,MAAM,cAAc,wMAAK,CAAC,SAAS,CAAC,OAAS,CAAC;QAC1C,MAAM;QACN,MAAM;QACN,aAAa;YAAC;YAAG;YAAG;SAAE;IAC1B,CAAC;AACD,MAAM,gBAAgB,0MAAO,CAAC,CAAC;IAC3B,IAAI,EAAE,IAAI,KAAK,SAAS;QACpB,IAAI,EAAE,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,KAAK;YAClC,OAAO;QACX;QACA,IAAI,EAAE,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,KAAK;YAClC,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA,MAAM,aAAa,6MAAU,CAAC,uMAAI,CAAC,SAAS,2MAAQ,CAAC,0MAAO,CAAC,qBAAqB,gBAAgB,OAAO,CAAC,GAAG,MAAQ,CAAC;QAAE,OAAO;QAAG,UAAU;IAAI,CAAC,IAAI,uMAAI,CAAC,aAAa,2MAAQ,CAAC,0MAAO,CAAC,aAAa,gBAAgB,OAAO,CAAC,GAAG,MAAQ,CAAC;QAAE,OAAO;QAAG,UAAU;IAAI,CAAC;AACpQ,MAAM,eAAe,2MAAQ,CAAC,wMAAK,CAAC,QAAQ,MAAM,IAAM,MAAM,uMAAI,CAAC,QAAQ,MAAM,QAAQ,MAAM,IAAM,OAAO,uMAAI,CAAC,QAAQ,MAAM,QAAQ,MAAM,IAAM,OAAO,uMAAI,CAAC,QAAQ,MAAM,QAAQ,MAAM,IAAM,OAAO,uMAAI,CAAC,QAAQ,MAAM,QAAQ,MAAM,IAAM,OAAO,uMAAI,CAAC,QAAQ,MAAM,QAAQ,MAAM,IAAM;AAC7R,MAAM,wBAAwB,wMAAK,CAAC,QAAQ,MAAM,iBAAiB,iBAAiB,QAAQ,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,GAAK,CAAC;QAC7H,MAAM;QACN,MAAM;QACN,WAAW;QACX,aAAa;YAAC;YAAG;YAAG;SAAE;IAC1B,CAAC;AACD,MAAM,qBAAqB,2MAAQ,CAAC,QAAQ,MAAM,wMAAK,CAAC,iBAAiB,iBAAiB,cAAc,iBAAiB,aAAa,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAK,CAAC;QAC1L,MAAM;QACN,MAAM;QACN,WAAW;QACX,SAAS;QACT,OAAO;QACP,UAAU;QACV,aAAa;YAAC;YAAG;YAAG;SAAE;IAC1B,CAAC,IAAI,QAAQ;AACb,MAAM,gBAAgB,6MAAU,CAAC,uBAAuB;AACxD,MAAM,gBAAgB,6MAAU,CAAC,cAAc;AAC/C,MAAM,oBAAoB,2MAAQ,CAAC,aAAa,gBAAgB;AAChE,MAAM,oBAAoB,wMAAK,CAAC,6MAAU,CAAC,4MAAS,CAAC,eAAe,yMAAM,CAAC,qBAAqB,0MAAO,CAAC,qBAAqB,CAAC;IAC1H,OAAO;QACH,MAAM;QACN,MAAM;QACN,aAAa,WAAW,GAAG,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW;IACrD;AACJ;AACA,MAAM,cAAc,2MAAQ,CAAC,wMAAK,CAAC,QAAQ,MAAM,IAAM,MAAM,wMAAK,CAAC,QAAQ,MAAM,IAAM,MAAM,wMAAK,CAAC,QAAQ,MAAM,IAAM,MAAM,uMAAI,CAAC,QAAQ,MAAM,QAAQ,MAAM,IAAM;AACpK,MAAM,uBAAuB,6MAAU,CAAC,iBAAiB,cAAc,wMAAK,CAAC,aAAa,IAAM;AAChG,MAAM,mBAAmB,+MAAY,CAAC,mBAAmB,wMAAK,CAAC,sBAAsB,CAAC,IAAM,CAAC,MAAM,QAAU,CAAC;YAC1G,MAAM;YACN,MAAM;mBAAI,MAAM,IAAI;gBAAE;oBAAE,MAAM;oBAAc,YAAY;oBAAG,MAAM;oBAAM,aAAa,KAAK,WAAW;gBAAC;aAAE;YACvG,aAAa,QAAQ,KAAK,WAAW,EAAE,MAAM,WAAW;QAC5D,CAAC,IAAI;AACL,MAAM,gBAAgB,+MAAY,CAAC,wMAAK,CAAC,kBAAkB,CAAC,IAAM,CAAC;QAAE,MAAM;QAAQ,MAAM;YAAC;SAAE;IAAC,CAAC,IAAI,wMAAK,CAAC,iBAAiB,QAAQ,OAAO,IAAM,CAAC,KAAK,OAAS,CAAC;YAAE,MAAM;YAAQ,MAAM;mBAAI,IAAI,IAAI;gBAAE;aAAK;QAAC,CAAC,IAAI;AAC7M,SAAS,OAAO,MAAM,EAAE,GAAG;IACvB,IAAI,CAAC,CAAC,OAAO,QAAQ,YAAY,eAAe,MAAM,GAAG;QACrD,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,cAAc,YAAY;IAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;QACvB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,kDAAkD,EAAE,YAAY,MAAM,CAAC,GAAG,CAAC,GACzG,oBAAoB,KAAK,YAAY,MAAM;IACnD;IACA,MAAM,SAAS,iBAAiB,QAAQ;QAAE,QAAQ,YAAY,MAAM;QAAE,SAAS;IAAU,GAAG;IAC5F,IAAI,CAAC,OAAO,OAAO,EAAE;QACjB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,IAAI,QAAQ,CAAC;IAClD;IACA,IAAI,OAAO,QAAQ,GAAG,YAAY,MAAM,CAAC,MAAM,EAAE;QAC7C,MAAM,QAAQ,YAAY,MAAM,CAAC,OAAO,QAAQ,CAAC;QACjD,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,+CAA+C,EAAE,MAAM,MAAM,CAAC,GAAG,CAAC,GAChG,oBAAoB,KAAK,MAAM,MAAM,EAAE,MAAM,GAAG;IACxD;IACA,OAAO,OAAO,KAAK;AACvB;AACA,SAAS,oBAAoB,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC;IAC7C,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB,CAAC,GAAG,GAAG,IAAM,IAAI,WAAW,IAAI,WAAW,UAAU,EAAE,EAAE,GAAG,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,MAAM;AAC3I;AACA,SAAS,MAAM,GAAG;IACd,OAAO,OAAO,eAAe;AACjC;AACA,SAAS,OAAO,GAAG;IACf,OAAO,OAAO,kBAAkB;AACpC;AAEA,SAAS,UAAU,QAAQ;IACvB,IAAI,CAAC,SAAS,IAAI,EAAE;QAChB,MAAM,IAAI,MAAM;IACpB;IACA,OAAQ,SAAS,IAAI;QACjB,KAAK;YACD,OAAO,OAAO,SAAS,SAAS,IAAI;QACxC,KAAK;YACD,OAAO,OAAO,SAAS,SAAS,IAAI,UAAU,SAAS,IAAI;QAC/D,KAAK;YACD,OAAO,MAAM,UAAU,SAAS,IAAI;QACxC,KAAK;YACD,OAAO,MAAM,UAAU,SAAS,IAAI;QACxC,KAAK;YACD,OAAO,CAAC,CAAC,EAAE,OAAO,SAAS,SAAS,IAAI,UAAU,SAAS,IAAI,EAAE,CAAC,CAAC;QACvE,KAAK;YACD,OAAO,CAAC,CAAC,EAAE,OAAO,SAAS,SAAS,IAAI,UAAU,SAAS,IAAI,IAAI,SAAS,OAAO,CAAC,CAAC,EAAE,QAAQ,SAAS,KAAK,EAAE,CAAC,EAAG,SAAS,QAAQ,GAAG,SAAS,QAAQ,GAAG,GAAI,CAAC,CAAC;QACrK,KAAK;YACD,OAAO,UAAU,SAAS,IAAI,IAAI,SAAS,UAAU;QACzD,KAAK;YACD,OAAO,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK;gBAC9B,IAAI,KAAK,IAAI,KAAK,cAAc;oBAC5B,OAAO,UAAU,QAAQ;gBAC7B,OACK;oBACD,OAAO,MAAM,UAAU;gBAC3B;YACJ,GAAG;QACP,KAAK;YACD,OAAO,SAAS,IAAI,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC;IACjD;AACJ;AACA,SAAS,OAAO,EAAE;IACd,OAAO,AAAC,MAAM,OAAO,KACf,UAAU,MAAM,MAChB;AACV;AACA,SAAS,WAAW,IAAI;IACpB,OAAO,CAAC,EAAE,EAAE,KAAK,WAAW,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnD;AACA,SAAS,UAAU,GAAG;IAClB,OAAO,IAAI,OAAO,CAClB,6FAA6F,CAAC,GAAG,IAAI,IAAI,IAAI,MAAM,IAAI,MAAM,QAAU,KAAK,WAAW,MACnJ,KAAK,MAAM,WAAW,GAAG,KAAK,CAAC,MAC3B,KAAK,QACD,OAAO,OACH,KAAK,WACD,OAAO,WAAW,QACd,OAAO;AACnC;AACA,SAAS,QAAQ,GAAG;IAChB,OAAO,IAAI,OAAO,CAClB,uCAAuC,CAAC,GAAG,IAAI,IAAI,IAAI,OAAS,KAAK,QACjE,KAAK,SACD,KAAK,WACD,WAAW;AAC3B;AACA,SAAS,UAAU,QAAQ;IACvB,IAAI,CAAC,SAAS,IAAI,EAAE;QAChB,MAAM,IAAI,MAAM;IACpB;IACA,OAAQ,SAAS,IAAI;QACjB,KAAK;YAAY;gBACb,SAAS,IAAI,CAAC,OAAO,CAAC;gBACtB,SAAS,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,eAAe,qBAAqB,IAAI,qBAAqB;gBAC1F;YACJ;QACA,KAAK;YAAc;gBACf,UAAU,SAAS,IAAI;gBACvB;YACJ;QACA,KAAK;YAAQ;gBACT,SAAS,IAAI,CAAC,OAAO,CAAC;gBACtB,SAAS,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,AAAC,UAAU,KAAK,UAAU,KAAM,CAAC,IAAI;gBAClE;YACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,qBAAqB,QAAQ;IAClC,OAAQ,SAAS,IAAI;QACjB,KAAK;YACD,OAAO;gBAAC;aAAE;QACd,KAAK;YACD,OAAO;gBAAC;aAAE;QACd,KAAK;YACD,OAAO;gBAAC;aAAE;QACd,KAAK;YACD,OAAO;gBAAC;gBAAG,SAAS,IAAI;aAAC;QAC7B,KAAK;YACD,OAAO;gBAAC;gBAAG,UAAU;aAAU;QACnC,KAAK;YACD,OAAO;gBAAC;gBAAG,UAAU;aAAU;QACnC,KAAK;YACD,OAAO;gBAAC;gBAAI,UAAU;aAAU;IACxC;AACJ;AACA,SAAS,iBAAiB,CAAC,EAAE,CAAC;IAC1B,OAAO,eAAe,EAAE,WAAW,EAAE,EAAE,WAAW;AACtD;AACA,SAAS,mBAAmB,CAAC,EAAE,CAAC;IAC5B,OAAO,eAAe,GAAG;AAC7B;AACA,SAAS,eAAe,CAAC,EAAE,CAAC;IACxB,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,OAAO,CAAC,IAAI;QACxC,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,UAAU,AAAC,EAAE,MAAM,GAAG,EAAE,MAAM,GAAI,EAAE,MAAM,GAAG,EAAE,MAAM;IAC3D,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;QAC9B,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE;YACf;QACJ;QACA,OAAO,AAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAI,CAAC,IAAI;IAChC;IACA,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/selderee%400.11.0/node_modules/selderee/lib/selderee.mjs"], "sourcesContent": ["import * as parseley from 'parseley';\nimport { compareSpecificity } from 'parseley';\n\nvar Ast = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nvar Types = /*#__PURE__*/Object.freeze({\n    __proto__: null\n});\n\nconst treeify = (nodes) => '▽\\n' + treeifyArray(nodes, thinLines);\nconst thinLines = [['├─', '│ '], ['└─', '  ']];\nconst heavyLines = [['┠─', '┃ '], ['┖─', '  ']];\nconst doubleLines = [['╟─', '║ '], ['╙─', '  ']];\nfunction treeifyArray(nodes, tpl = heavyLines) {\n    return prefixItems(tpl, nodes.map(n => treeifyNode(n)));\n}\nfunction treeifyNode(node) {\n    switch (node.type) {\n        case 'terminal': {\n            const vctr = node.valueContainer;\n            return `◁ #${vctr.index} ${JSON.stringify(vctr.specificity)} ${vctr.value}`;\n        }\n        case 'tagName':\n            return `◻ Tag name\\n${treeifyArray(node.variants, doubleLines)}`;\n        case 'attrValue':\n            return `▣ Attr value: ${node.name}\\n${treeifyArray(node.matchers, doubleLines)}`;\n        case 'attrPresence':\n            return `◨ Attr presence: ${node.name}\\n${treeifyArray(node.cont)}`;\n        case 'pushElement':\n            return `◉ Push element: ${node.combinator}\\n${treeifyArray(node.cont, thinLines)}`;\n        case 'popElement':\n            return `◌ Pop element\\n${treeifyArray(node.cont, thinLines)}`;\n        case 'variant':\n            return `◇ = ${node.value}\\n${treeifyArray(node.cont)}`;\n        case 'matcher':\n            return `◈ ${node.matcher} \"${node.value}\"${node.modifier || ''}\\n${treeifyArray(node.cont)}`;\n    }\n}\nfunction prefixItems(tpl, items) {\n    return items\n        .map((item, i, { length }) => prefixItem(tpl, item, i === length - 1))\n        .join('\\n');\n}\nfunction prefixItem(tpl, item, tail = true) {\n    const tpl1 = tpl[tail ? 1 : 0];\n    return tpl1[0] + item.split('\\n').join('\\n' + tpl1[1]);\n}\n\nvar TreeifyBuilder = /*#__PURE__*/Object.freeze({\n    __proto__: null,\n    treeify: treeify\n});\n\nclass DecisionTree {\n    constructor(input) {\n        this.branches = weave(toAstTerminalPairs(input));\n    }\n    build(builder) {\n        return builder(this.branches);\n    }\n}\nfunction toAstTerminalPairs(array) {\n    const len = array.length;\n    const results = new Array(len);\n    for (let i = 0; i < len; i++) {\n        const [selectorString, val] = array[i];\n        const ast = preprocess(parseley.parse1(selectorString));\n        results[i] = {\n            ast: ast,\n            terminal: {\n                type: 'terminal',\n                valueContainer: { index: i, value: val, specificity: ast.specificity }\n            }\n        };\n    }\n    return results;\n}\nfunction preprocess(ast) {\n    reduceSelectorVariants(ast);\n    parseley.normalize(ast);\n    return ast;\n}\nfunction reduceSelectorVariants(ast) {\n    const newList = [];\n    ast.list.forEach(sel => {\n        switch (sel.type) {\n            case 'class':\n                newList.push({\n                    matcher: '~=',\n                    modifier: null,\n                    name: 'class',\n                    namespace: null,\n                    specificity: sel.specificity,\n                    type: 'attrValue',\n                    value: sel.name,\n                });\n                break;\n            case 'id':\n                newList.push({\n                    matcher: '=',\n                    modifier: null,\n                    name: 'id',\n                    namespace: null,\n                    specificity: sel.specificity,\n                    type: 'attrValue',\n                    value: sel.name,\n                });\n                break;\n            case 'combinator':\n                reduceSelectorVariants(sel.left);\n                newList.push(sel);\n                break;\n            case 'universal':\n                break;\n            default:\n                newList.push(sel);\n                break;\n        }\n    });\n    ast.list = newList;\n}\nfunction weave(items) {\n    const branches = [];\n    while (items.length) {\n        const topKind = findTopKey(items, (sel) => true, getSelectorKind);\n        const { matches, nonmatches, empty } = breakByKind(items, topKind);\n        items = nonmatches;\n        if (matches.length) {\n            branches.push(branchOfKind(topKind, matches));\n        }\n        if (empty.length) {\n            branches.push(...terminate(empty));\n        }\n    }\n    return branches;\n}\nfunction terminate(items) {\n    const results = [];\n    for (const item of items) {\n        const terminal = item.terminal;\n        if (terminal.type === 'terminal') {\n            results.push(terminal);\n        }\n        else {\n            const { matches, rest } = partition(terminal.cont, (node) => node.type === 'terminal');\n            matches.forEach((node) => results.push(node));\n            if (rest.length) {\n                terminal.cont = rest;\n                results.push(terminal);\n            }\n        }\n    }\n    return results;\n}\nfunction breakByKind(items, selectedKind) {\n    const matches = [];\n    const nonmatches = [];\n    const empty = [];\n    for (const item of items) {\n        const simpsels = item.ast.list;\n        if (simpsels.length) {\n            const isMatch = simpsels.some(node => getSelectorKind(node) === selectedKind);\n            (isMatch ? matches : nonmatches).push(item);\n        }\n        else {\n            empty.push(item);\n        }\n    }\n    return { matches, nonmatches, empty };\n}\nfunction getSelectorKind(sel) {\n    switch (sel.type) {\n        case 'attrPresence':\n            return `attrPresence ${sel.name}`;\n        case 'attrValue':\n            return `attrValue ${sel.name}`;\n        case 'combinator':\n            return `combinator ${sel.combinator}`;\n        default:\n            return sel.type;\n    }\n}\nfunction branchOfKind(kind, items) {\n    if (kind === 'tag') {\n        return tagNameBranch(items);\n    }\n    if (kind.startsWith('attrValue ')) {\n        return attrValueBranch(kind.substring(10), items);\n    }\n    if (kind.startsWith('attrPresence ')) {\n        return attrPresenceBranch(kind.substring(13), items);\n    }\n    if (kind === 'combinator >') {\n        return combinatorBranch('>', items);\n    }\n    if (kind === 'combinator +') {\n        return combinatorBranch('+', items);\n    }\n    throw new Error(`Unsupported selector kind: ${kind}`);\n}\nfunction tagNameBranch(items) {\n    const groups = spliceAndGroup(items, (x) => x.type === 'tag', (x) => x.name);\n    const variants = Object.entries(groups).map(([name, group]) => ({\n        type: 'variant',\n        value: name,\n        cont: weave(group.items)\n    }));\n    return {\n        type: 'tagName',\n        variants: variants\n    };\n}\nfunction attrPresenceBranch(name, items) {\n    for (const item of items) {\n        spliceSimpleSelector(item, (x) => (x.type === 'attrPresence') && (x.name === name));\n    }\n    return {\n        type: 'attrPresence',\n        name: name,\n        cont: weave(items)\n    };\n}\nfunction attrValueBranch(name, items) {\n    const groups = spliceAndGroup(items, (x) => (x.type === 'attrValue') && (x.name === name), (x) => `${x.matcher} ${x.modifier || ''} ${x.value}`);\n    const matchers = [];\n    for (const group of Object.values(groups)) {\n        const sel = group.oneSimpleSelector;\n        const predicate = getAttrPredicate(sel);\n        const continuation = weave(group.items);\n        matchers.push({\n            type: 'matcher',\n            matcher: sel.matcher,\n            modifier: sel.modifier,\n            value: sel.value,\n            predicate: predicate,\n            cont: continuation\n        });\n    }\n    return {\n        type: 'attrValue',\n        name: name,\n        matchers: matchers\n    };\n}\nfunction getAttrPredicate(sel) {\n    if (sel.modifier === 'i') {\n        const expected = sel.value.toLowerCase();\n        switch (sel.matcher) {\n            case '=':\n                return (actual) => expected === actual.toLowerCase();\n            case '~=':\n                return (actual) => actual.toLowerCase().split(/[ \\t]+/).includes(expected);\n            case '^=':\n                return (actual) => actual.toLowerCase().startsWith(expected);\n            case '$=':\n                return (actual) => actual.toLowerCase().endsWith(expected);\n            case '*=':\n                return (actual) => actual.toLowerCase().includes(expected);\n            case '|=':\n                return (actual) => {\n                    const lower = actual.toLowerCase();\n                    return (expected === lower) || (lower.startsWith(expected) && lower[expected.length] === '-');\n                };\n        }\n    }\n    else {\n        const expected = sel.value;\n        switch (sel.matcher) {\n            case '=':\n                return (actual) => expected === actual;\n            case '~=':\n                return (actual) => actual.split(/[ \\t]+/).includes(expected);\n            case '^=':\n                return (actual) => actual.startsWith(expected);\n            case '$=':\n                return (actual) => actual.endsWith(expected);\n            case '*=':\n                return (actual) => actual.includes(expected);\n            case '|=':\n                return (actual) => (expected === actual) || (actual.startsWith(expected) && actual[expected.length] === '-');\n        }\n    }\n}\nfunction combinatorBranch(combinator, items) {\n    const groups = spliceAndGroup(items, (x) => (x.type === 'combinator') && (x.combinator === combinator), (x) => parseley.serialize(x.left));\n    const leftItems = [];\n    for (const group of Object.values(groups)) {\n        const rightCont = weave(group.items);\n        const leftAst = group.oneSimpleSelector.left;\n        leftItems.push({\n            ast: leftAst,\n            terminal: { type: 'popElement', cont: rightCont }\n        });\n    }\n    return {\n        type: 'pushElement',\n        combinator: combinator,\n        cont: weave(leftItems)\n    };\n}\nfunction spliceAndGroup(items, predicate, keyCallback) {\n    const groups = {};\n    while (items.length) {\n        const bestKey = findTopKey(items, predicate, keyCallback);\n        const bestKeyPredicate = (sel) => predicate(sel) && keyCallback(sel) === bestKey;\n        const hasBestKeyPredicate = (item) => item.ast.list.some(bestKeyPredicate);\n        const { matches, rest } = partition1(items, hasBestKeyPredicate);\n        let oneSimpleSelector = null;\n        for (const item of matches) {\n            const splicedNode = spliceSimpleSelector(item, bestKeyPredicate);\n            if (!oneSimpleSelector) {\n                oneSimpleSelector = splicedNode;\n            }\n        }\n        if (oneSimpleSelector == null) {\n            throw new Error('No simple selector is found.');\n        }\n        groups[bestKey] = { oneSimpleSelector: oneSimpleSelector, items: matches };\n        items = rest;\n    }\n    return groups;\n}\nfunction spliceSimpleSelector(item, predicate) {\n    const simpsels = item.ast.list;\n    const matches = new Array(simpsels.length);\n    let firstIndex = -1;\n    for (let i = simpsels.length; i-- > 0;) {\n        if (predicate(simpsels[i])) {\n            matches[i] = true;\n            firstIndex = i;\n        }\n    }\n    if (firstIndex == -1) {\n        throw new Error(`Couldn't find the required simple selector.`);\n    }\n    const result = simpsels[firstIndex];\n    item.ast.list = simpsels.filter((sel, i) => !matches[i]);\n    return result;\n}\nfunction findTopKey(items, predicate, keyCallback) {\n    const candidates = {};\n    for (const item of items) {\n        const candidates1 = {};\n        for (const node of item.ast.list.filter(predicate)) {\n            candidates1[keyCallback(node)] = true;\n        }\n        for (const key of Object.keys(candidates1)) {\n            if (candidates[key]) {\n                candidates[key]++;\n            }\n            else {\n                candidates[key] = 1;\n            }\n        }\n    }\n    let topKind = '';\n    let topCounter = 0;\n    for (const entry of Object.entries(candidates)) {\n        if (entry[1] > topCounter) {\n            topKind = entry[0];\n            topCounter = entry[1];\n        }\n    }\n    return topKind;\n}\nfunction partition(src, predicate) {\n    const matches = [];\n    const rest = [];\n    for (const x of src) {\n        if (predicate(x)) {\n            matches.push(x);\n        }\n        else {\n            rest.push(x);\n        }\n    }\n    return { matches, rest };\n}\nfunction partition1(src, predicate) {\n    const matches = [];\n    const rest = [];\n    for (const x of src) {\n        if (predicate(x)) {\n            matches.push(x);\n        }\n        else {\n            rest.push(x);\n        }\n    }\n    return { matches, rest };\n}\n\nclass Picker {\n    constructor(f) {\n        this.f = f;\n    }\n    pickAll(el) {\n        return this.f(el);\n    }\n    pick1(el, preferFirst = false) {\n        const results = this.f(el);\n        const len = results.length;\n        if (len === 0) {\n            return null;\n        }\n        if (len === 1) {\n            return results[0].value;\n        }\n        const comparator = (preferFirst)\n            ? comparatorPreferFirst\n            : comparatorPreferLast;\n        let result = results[0];\n        for (let i = 1; i < len; i++) {\n            const next = results[i];\n            if (comparator(result, next)) {\n                result = next;\n            }\n        }\n        return result.value;\n    }\n}\nfunction comparatorPreferFirst(acc, next) {\n    const diff = compareSpecificity(next.specificity, acc.specificity);\n    return diff > 0 || (diff === 0 && next.index < acc.index);\n}\nfunction comparatorPreferLast(acc, next) {\n    const diff = compareSpecificity(next.specificity, acc.specificity);\n    return diff > 0 || (diff === 0 && next.index > acc.index);\n}\n\nexport { Ast, DecisionTree, Picker, TreeifyBuilder as Treeify, Types };\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;AAGA,IAAI,MAAM,WAAW,GAAE,OAAO,MAAM,CAAC;IACjC,WAAW;AACf;AAEA,IAAI,QAAQ,WAAW,GAAE,OAAO,MAAM,CAAC;IACnC,WAAW;AACf;AAEA,MAAM,UAAU,CAAC,QAAU,QAAQ,aAAa,OAAO;AACvD,MAAM,YAAY;IAAC;QAAC;QAAM;KAAK;IAAE;QAAC;QAAM;KAAK;CAAC;AAC9C,MAAM,aAAa;IAAC;QAAC;QAAM;KAAK;IAAE;QAAC;QAAM;KAAK;CAAC;AAC/C,MAAM,cAAc;IAAC;QAAC;QAAM;KAAK;IAAE;QAAC;QAAM;KAAK;CAAC;AAChD,SAAS,aAAa,KAAK,EAAE,MAAM,UAAU;IACzC,OAAO,YAAY,KAAK,MAAM,GAAG,CAAC,CAAA,IAAK,YAAY;AACvD;AACA,SAAS,YAAY,IAAI;IACrB,OAAQ,KAAK,IAAI;QACb,KAAK;YAAY;gBACb,MAAM,OAAO,KAAK,cAAc;gBAChC,OAAO,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,KAAK,WAAW,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE;YAC/E;QACA,KAAK;YACD,OAAO,CAAC,YAAY,EAAE,aAAa,KAAK,QAAQ,EAAE,cAAc;QACpE,KAAK;YACD,OAAO,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,aAAa,KAAK,QAAQ,EAAE,cAAc;QACpF,KAAK;YACD,OAAO,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,aAAa,KAAK,IAAI,GAAG;QACtE,KAAK;YACD,OAAO,CAAC,gBAAgB,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE,aAAa,KAAK,IAAI,EAAE,YAAY;QACtF,KAAK;YACD,OAAO,CAAC,eAAe,EAAE,aAAa,KAAK,IAAI,EAAE,YAAY;QACjE,KAAK;YACD,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,aAAa,KAAK,IAAI,GAAG;QAC1D,KAAK;YACD,OAAO,CAAC,EAAE,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,QAAQ,IAAI,GAAG,EAAE,EAAE,aAAa,KAAK,IAAI,GAAG;IACpG;AACJ;AACA,SAAS,YAAY,GAAG,EAAE,KAAK;IAC3B,OAAO,MACF,GAAG,CAAC,CAAC,MAAM,GAAG,EAAE,MAAM,EAAE,GAAK,WAAW,KAAK,MAAM,MAAM,SAAS,IAClE,IAAI,CAAC;AACd;AACA,SAAS,WAAW,GAAG,EAAE,IAAI,EAAE,OAAO,IAAI;IACtC,MAAM,OAAO,GAAG,CAAC,OAAO,IAAI,EAAE;IAC9B,OAAO,IAAI,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE;AACzD;AAEA,IAAI,iBAAiB,WAAW,GAAE,OAAO,MAAM,CAAC;IAC5C,WAAW;IACX,SAAS;AACb;AAEA,MAAM;IACF,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,QAAQ,GAAG,MAAM,mBAAmB;IAC7C;IACA,MAAM,OAAO,EAAE;QACX,OAAO,QAAQ,IAAI,CAAC,QAAQ;IAChC;AACJ;AACA,SAAS,mBAAmB,KAAK;IAC7B,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,UAAU,IAAI,MAAM;IAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC1B,MAAM,CAAC,gBAAgB,IAAI,GAAG,KAAK,CAAC,EAAE;QACtC,MAAM,MAAM,WAAW,4MAAe,CAAC;QACvC,OAAO,CAAC,EAAE,GAAG;YACT,KAAK;YACL,UAAU;gBACN,MAAM;gBACN,gBAAgB;oBAAE,OAAO;oBAAG,OAAO;oBAAK,aAAa,IAAI,WAAW;gBAAC;YACzE;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,WAAW,GAAG;IACnB,uBAAuB;IACvB,+MAAkB,CAAC;IACnB,OAAO;AACX;AACA,SAAS,uBAAuB,GAAG;IAC/B,MAAM,UAAU,EAAE;IAClB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAA;QACb,OAAQ,IAAI,IAAI;YACZ,KAAK;gBACD,QAAQ,IAAI,CAAC;oBACT,SAAS;oBACT,UAAU;oBACV,MAAM;oBACN,WAAW;oBACX,aAAa,IAAI,WAAW;oBAC5B,MAAM;oBACN,OAAO,IAAI,IAAI;gBACnB;gBACA;YACJ,KAAK;gBACD,QAAQ,IAAI,CAAC;oBACT,SAAS;oBACT,UAAU;oBACV,MAAM;oBACN,WAAW;oBACX,aAAa,IAAI,WAAW;oBAC5B,MAAM;oBACN,OAAO,IAAI,IAAI;gBACnB;gBACA;YACJ,KAAK;gBACD,uBAAuB,IAAI,IAAI;gBAC/B,QAAQ,IAAI,CAAC;gBACb;YACJ,KAAK;gBACD;YACJ;gBACI,QAAQ,IAAI,CAAC;gBACb;QACR;IACJ;IACA,IAAI,IAAI,GAAG;AACf;AACA,SAAS,MAAM,KAAK;IAChB,MAAM,WAAW,EAAE;IACnB,MAAO,MAAM,MAAM,CAAE;QACjB,MAAM,UAAU,WAAW,OAAO,CAAC,MAAQ,MAAM;QACjD,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,YAAY,OAAO;QAC1D,QAAQ;QACR,IAAI,QAAQ,MAAM,EAAE;YAChB,SAAS,IAAI,CAAC,aAAa,SAAS;QACxC;QACA,IAAI,MAAM,MAAM,EAAE;YACd,SAAS,IAAI,IAAI,UAAU;QAC/B;IACJ;IACA,OAAO;AACX;AACA,SAAS,UAAU,KAAK;IACpB,MAAM,UAAU,EAAE;IAClB,KAAK,MAAM,QAAQ,MAAO;QACtB,MAAM,WAAW,KAAK,QAAQ;QAC9B,IAAI,SAAS,IAAI,KAAK,YAAY;YAC9B,QAAQ,IAAI,CAAC;QACjB,OACK;YACD,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,UAAU,SAAS,IAAI,EAAE,CAAC,OAAS,KAAK,IAAI,KAAK;YAC3E,QAAQ,OAAO,CAAC,CAAC,OAAS,QAAQ,IAAI,CAAC;YACvC,IAAI,KAAK,MAAM,EAAE;gBACb,SAAS,IAAI,GAAG;gBAChB,QAAQ,IAAI,CAAC;YACjB;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,YAAY,KAAK,EAAE,YAAY;IACpC,MAAM,UAAU,EAAE;IAClB,MAAM,aAAa,EAAE;IACrB,MAAM,QAAQ,EAAE;IAChB,KAAK,MAAM,QAAQ,MAAO;QACtB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI;QAC9B,IAAI,SAAS,MAAM,EAAE;YACjB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,OAAQ,gBAAgB,UAAU;YAChE,CAAC,UAAU,UAAU,UAAU,EAAE,IAAI,CAAC;QAC1C,OACK;YACD,MAAM,IAAI,CAAC;QACf;IACJ;IACA,OAAO;QAAE;QAAS;QAAY;IAAM;AACxC;AACA,SAAS,gBAAgB,GAAG;IACxB,OAAQ,IAAI,IAAI;QACZ,KAAK;YACD,OAAO,CAAC,aAAa,EAAE,IAAI,IAAI,EAAE;QACrC,KAAK;YACD,OAAO,CAAC,UAAU,EAAE,IAAI,IAAI,EAAE;QAClC,KAAK;YACD,OAAO,CAAC,WAAW,EAAE,IAAI,UAAU,EAAE;QACzC;YACI,OAAO,IAAI,IAAI;IACvB;AACJ;AACA,SAAS,aAAa,IAAI,EAAE,KAAK;IAC7B,IAAI,SAAS,OAAO;QAChB,OAAO,cAAc;IACzB;IACA,IAAI,KAAK,UAAU,CAAC,eAAe;QAC/B,OAAO,gBAAgB,KAAK,SAAS,CAAC,KAAK;IAC/C;IACA,IAAI,KAAK,UAAU,CAAC,kBAAkB;QAClC,OAAO,mBAAmB,KAAK,SAAS,CAAC,KAAK;IAClD;IACA,IAAI,SAAS,gBAAgB;QACzB,OAAO,iBAAiB,KAAK;IACjC;IACA,IAAI,SAAS,gBAAgB;QACzB,OAAO,iBAAiB,KAAK;IACjC;IACA,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,MAAM;AACxD;AACA,SAAS,cAAc,KAAK;IACxB,MAAM,SAAS,eAAe,OAAO,CAAC,IAAM,EAAE,IAAI,KAAK,OAAO,CAAC,IAAM,EAAE,IAAI;IAC3E,MAAM,WAAW,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,MAAM,MAAM,GAAK,CAAC;YAC5D,MAAM;YACN,OAAO;YACP,MAAM,MAAM,MAAM,KAAK;QAC3B,CAAC;IACD,OAAO;QACH,MAAM;QACN,UAAU;IACd;AACJ;AACA,SAAS,mBAAmB,IAAI,EAAE,KAAK;IACnC,KAAK,MAAM,QAAQ,MAAO;QACtB,qBAAqB,MAAM,CAAC,IAAM,AAAC,EAAE,IAAI,KAAK,kBAAoB,EAAE,IAAI,KAAK;IACjF;IACA,OAAO;QACH,MAAM;QACN,MAAM;QACN,MAAM,MAAM;IAChB;AACJ;AACA,SAAS,gBAAgB,IAAI,EAAE,KAAK;IAChC,MAAM,SAAS,eAAe,OAAO,CAAC,IAAM,AAAC,EAAE,IAAI,KAAK,eAAiB,EAAE,IAAI,KAAK,MAAO,CAAC,IAAM,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,IAAI,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE;IAC/I,MAAM,WAAW,EAAE;IACnB,KAAK,MAAM,SAAS,OAAO,MAAM,CAAC,QAAS;QACvC,MAAM,MAAM,MAAM,iBAAiB;QACnC,MAAM,YAAY,iBAAiB;QACnC,MAAM,eAAe,MAAM,MAAM,KAAK;QACtC,SAAS,IAAI,CAAC;YACV,MAAM;YACN,SAAS,IAAI,OAAO;YACpB,UAAU,IAAI,QAAQ;YACtB,OAAO,IAAI,KAAK;YAChB,WAAW;YACX,MAAM;QACV;IACJ;IACA,OAAO;QACH,MAAM;QACN,MAAM;QACN,UAAU;IACd;AACJ;AACA,SAAS,iBAAiB,GAAG;IACzB,IAAI,IAAI,QAAQ,KAAK,KAAK;QACtB,MAAM,WAAW,IAAI,KAAK,CAAC,WAAW;QACtC,OAAQ,IAAI,OAAO;YACf,KAAK;gBACD,OAAO,CAAC,SAAW,aAAa,OAAO,WAAW;YACtD,KAAK;gBACD,OAAO,CAAC,SAAW,OAAO,WAAW,GAAG,KAAK,CAAC,UAAU,QAAQ,CAAC;YACrE,KAAK;gBACD,OAAO,CAAC,SAAW,OAAO,WAAW,GAAG,UAAU,CAAC;YACvD,KAAK;gBACD,OAAO,CAAC,SAAW,OAAO,WAAW,GAAG,QAAQ,CAAC;YACrD,KAAK;gBACD,OAAO,CAAC,SAAW,OAAO,WAAW,GAAG,QAAQ,CAAC;YACrD,KAAK;gBACD,OAAO,CAAC;oBACJ,MAAM,QAAQ,OAAO,WAAW;oBAChC,OAAO,AAAC,aAAa,SAAW,MAAM,UAAU,CAAC,aAAa,KAAK,CAAC,SAAS,MAAM,CAAC,KAAK;gBAC7F;QACR;IACJ,OACK;QACD,MAAM,WAAW,IAAI,KAAK;QAC1B,OAAQ,IAAI,OAAO;YACf,KAAK;gBACD,OAAO,CAAC,SAAW,aAAa;YACpC,KAAK;gBACD,OAAO,CAAC,SAAW,OAAO,KAAK,CAAC,UAAU,QAAQ,CAAC;YACvD,KAAK;gBACD,OAAO,CAAC,SAAW,OAAO,UAAU,CAAC;YACzC,KAAK;gBACD,OAAO,CAAC,SAAW,OAAO,QAAQ,CAAC;YACvC,KAAK;gBACD,OAAO,CAAC,SAAW,OAAO,QAAQ,CAAC;YACvC,KAAK;gBACD,OAAO,CAAC,SAAW,AAAC,aAAa,UAAY,OAAO,UAAU,CAAC,aAAa,MAAM,CAAC,SAAS,MAAM,CAAC,KAAK;QAChH;IACJ;AACJ;AACA,SAAS,iBAAiB,UAAU,EAAE,KAAK;IACvC,MAAM,SAAS,eAAe,OAAO,CAAC,IAAM,AAAC,EAAE,IAAI,KAAK,gBAAkB,EAAE,UAAU,KAAK,YAAa,CAAC,IAAM,+MAAkB,CAAC,EAAE,IAAI;IACxI,MAAM,YAAY,EAAE;IACpB,KAAK,MAAM,SAAS,OAAO,MAAM,CAAC,QAAS;QACvC,MAAM,YAAY,MAAM,MAAM,KAAK;QACnC,MAAM,UAAU,MAAM,iBAAiB,CAAC,IAAI;QAC5C,UAAU,IAAI,CAAC;YACX,KAAK;YACL,UAAU;gBAAE,MAAM;gBAAc,MAAM;YAAU;QACpD;IACJ;IACA,OAAO;QACH,MAAM;QACN,YAAY;QACZ,MAAM,MAAM;IAChB;AACJ;AACA,SAAS,eAAe,KAAK,EAAE,SAAS,EAAE,WAAW;IACjD,MAAM,SAAS,CAAC;IAChB,MAAO,MAAM,MAAM,CAAE;QACjB,MAAM,UAAU,WAAW,OAAO,WAAW;QAC7C,MAAM,mBAAmB,CAAC,MAAQ,UAAU,QAAQ,YAAY,SAAS;QACzE,MAAM,sBAAsB,CAAC,OAAS,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QACzD,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,WAAW,OAAO;QAC5C,IAAI,oBAAoB;QACxB,KAAK,MAAM,QAAQ,QAAS;YACxB,MAAM,cAAc,qBAAqB,MAAM;YAC/C,IAAI,CAAC,mBAAmB;gBACpB,oBAAoB;YACxB;QACJ;QACA,IAAI,qBAAqB,MAAM;YAC3B,MAAM,IAAI,MAAM;QACpB;QACA,MAAM,CAAC,QAAQ,GAAG;YAAE,mBAAmB;YAAmB,OAAO;QAAQ;QACzE,QAAQ;IACZ;IACA,OAAO;AACX;AACA,SAAS,qBAAqB,IAAI,EAAE,SAAS;IACzC,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI;IAC9B,MAAM,UAAU,IAAI,MAAM,SAAS,MAAM;IACzC,IAAI,aAAa,CAAC;IAClB,IAAK,IAAI,IAAI,SAAS,MAAM,EAAE,MAAM,GAAI;QACpC,IAAI,UAAU,QAAQ,CAAC,EAAE,GAAG;YACxB,OAAO,CAAC,EAAE,GAAG;YACb,aAAa;QACjB;IACJ;IACA,IAAI,cAAc,CAAC,GAAG;QAClB,MAAM,IAAI,MAAM,CAAC,2CAA2C,CAAC;IACjE;IACA,MAAM,SAAS,QAAQ,CAAC,WAAW;IACnC,KAAK,GAAG,CAAC,IAAI,GAAG,SAAS,MAAM,CAAC,CAAC,KAAK,IAAM,CAAC,OAAO,CAAC,EAAE;IACvD,OAAO;AACX;AACA,SAAS,WAAW,KAAK,EAAE,SAAS,EAAE,WAAW;IAC7C,MAAM,aAAa,CAAC;IACpB,KAAK,MAAM,QAAQ,MAAO;QACtB,MAAM,cAAc,CAAC;QACrB,KAAK,MAAM,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,WAAY;YAChD,WAAW,CAAC,YAAY,MAAM,GAAG;QACrC;QACA,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,aAAc;YACxC,IAAI,UAAU,CAAC,IAAI,EAAE;gBACjB,UAAU,CAAC,IAAI;YACnB,OACK;gBACD,UAAU,CAAC,IAAI,GAAG;YACtB;QACJ;IACJ;IACA,IAAI,UAAU;IACd,IAAI,aAAa;IACjB,KAAK,MAAM,SAAS,OAAO,OAAO,CAAC,YAAa;QAC5C,IAAI,KAAK,CAAC,EAAE,GAAG,YAAY;YACvB,UAAU,KAAK,CAAC,EAAE;YAClB,aAAa,KAAK,CAAC,EAAE;QACzB;IACJ;IACA,OAAO;AACX;AACA,SAAS,UAAU,GAAG,EAAE,SAAS;IAC7B,MAAM,UAAU,EAAE;IAClB,MAAM,OAAO,EAAE;IACf,KAAK,MAAM,KAAK,IAAK;QACjB,IAAI,UAAU,IAAI;YACd,QAAQ,IAAI,CAAC;QACjB,OACK;YACD,KAAK,IAAI,CAAC;QACd;IACJ;IACA,OAAO;QAAE;QAAS;IAAK;AAC3B;AACA,SAAS,WAAW,GAAG,EAAE,SAAS;IAC9B,MAAM,UAAU,EAAE;IAClB,MAAM,OAAO,EAAE;IACf,KAAK,MAAM,KAAK,IAAK;QACjB,IAAI,UAAU,IAAI;YACd,QAAQ,IAAI,CAAC;QACjB,OACK;YACD,KAAK,IAAI,CAAC;QACd;IACJ;IACA,OAAO;QAAE;QAAS;IAAK;AAC3B;AAEA,MAAM;IACF,YAAY,CAAC,CAAE;QACX,IAAI,CAAC,CAAC,GAAG;IACb;IACA,QAAQ,EAAE,EAAE;QACR,OAAO,IAAI,CAAC,CAAC,CAAC;IAClB;IACA,MAAM,EAAE,EAAE,cAAc,KAAK,EAAE;QAC3B,MAAM,UAAU,IAAI,CAAC,CAAC,CAAC;QACvB,MAAM,MAAM,QAAQ,MAAM;QAC1B,IAAI,QAAQ,GAAG;YACX,OAAO;QACX;QACA,IAAI,QAAQ,GAAG;YACX,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK;QAC3B;QACA,MAAM,aAAa,AAAC,cACd,wBACA;QACN,IAAI,SAAS,OAAO,CAAC,EAAE;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,OAAO,OAAO,CAAC,EAAE;YACvB,IAAI,WAAW,QAAQ,OAAO;gBAC1B,SAAS;YACb;QACJ;QACA,OAAO,OAAO,KAAK;IACvB;AACJ;AACA,SAAS,sBAAsB,GAAG,EAAE,IAAI;IACpC,MAAM,OAAO,IAAA,wNAAkB,EAAC,KAAK,WAAW,EAAE,IAAI,WAAW;IACjE,OAAO,OAAO,KAAM,SAAS,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK;AAC5D;AACA,SAAS,qBAAqB,GAAG,EAAE,IAAI;IACnC,MAAM,OAAO,IAAA,wNAAkB,EAAC,KAAK,WAAW,EAAE,IAAI,WAAW;IACjE,OAAO,OAAO,KAAM,SAAS,KAAK,KAAK,KAAK,GAAG,IAAI,KAAK;AAC5D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2057, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/%40selderee%2Bplugin-htmlparser2%400.11.0/node_modules/%40selderee/plugin-htmlparser2/lib/hp2-builder.mjs"], "sourcesContent": ["import { isTag } from 'domhandler';\nimport { Picker } from 'selderee';\n\nfunction hp2Builder(nodes) {\n    return new Picker(handleArray(nodes));\n}\nfunction handleArray(nodes) {\n    const matchers = nodes.map(handleNode);\n    return (el, ...tail) => matchers.flatMap(m => m(el, ...tail));\n}\nfunction handleNode(node) {\n    switch (node.type) {\n        case 'terminal': {\n            const result = [node.valueContainer];\n            return (el, ...tail) => result;\n        }\n        case 'tagName':\n            return handleTagName(node);\n        case 'attrValue':\n            return handleAttrValueName(node);\n        case 'attrPresence':\n            return handleAttrPresenceName(node);\n        case 'pushElement':\n            return handlePushElementNode(node);\n        case 'popElement':\n            return handlePopElementNode(node);\n    }\n}\nfunction handleTagName(node) {\n    const variants = {};\n    for (const variant of node.variants) {\n        variants[variant.value] = handleArray(variant.cont);\n    }\n    return (el, ...tail) => {\n        const continuation = variants[el.name];\n        return (continuation) ? continuation(el, ...tail) : [];\n    };\n}\nfunction handleAttrPresenceName(node) {\n    const attrName = node.name;\n    const continuation = handleArray(node.cont);\n    return (el, ...tail) => (Object.prototype.hasOwnProperty.call(el.attribs, attrName))\n        ? continuation(el, ...tail)\n        : [];\n}\nfunction handleAttrValueName(node) {\n    const callbacks = [];\n    for (const matcher of node.matchers) {\n        const predicate = matcher.predicate;\n        const continuation = handleArray(matcher.cont);\n        callbacks.push((attr, el, ...tail) => (predicate(attr) ? continuation(el, ...tail) : []));\n    }\n    const attrName = node.name;\n    return (el, ...tail) => {\n        const attr = el.attribs[attrName];\n        return (attr || attr === '')\n            ? callbacks.flatMap(cb => cb(attr, el, ...tail))\n            : [];\n    };\n}\nfunction handlePushElementNode(node) {\n    const continuation = handleArray(node.cont);\n    const leftElementGetter = (node.combinator === '+')\n        ? getPrecedingElement\n        : getParentElement;\n    return (el, ...tail) => {\n        const next = leftElementGetter(el);\n        if (next === null) {\n            return [];\n        }\n        return continuation(next, el, ...tail);\n    };\n}\nconst getPrecedingElement = (el) => {\n    const prev = el.prev;\n    if (prev === null) {\n        return null;\n    }\n    return (isTag(prev)) ? prev : getPrecedingElement(prev);\n};\nconst getParentElement = (el) => {\n    const parent = el.parent;\n    return (parent && isTag(parent)) ? parent : null;\n};\nfunction handlePopElementNode(node) {\n    const continuation = handleArray(node.cont);\n    return (el, next, ...tail) => continuation(next, ...tail);\n}\n\nexport { hp2Builder };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,SAAS,WAAW,KAAK;IACrB,OAAO,IAAI,4MAAM,CAAC,YAAY;AAClC;AACA,SAAS,YAAY,KAAK;IACtB,MAAM,WAAW,MAAM,GAAG,CAAC;IAC3B,OAAO,CAAC,IAAI,GAAG,OAAS,SAAS,OAAO,CAAC,CAAA,IAAK,EAAE,OAAO;AAC3D;AACA,SAAS,WAAW,IAAI;IACpB,OAAQ,KAAK,IAAI;QACb,KAAK;YAAY;gBACb,MAAM,SAAS;oBAAC,KAAK,cAAc;iBAAC;gBACpC,OAAO,CAAC,IAAI,GAAG,OAAS;YAC5B;QACA,KAAK;YACD,OAAO,cAAc;QACzB,KAAK;YACD,OAAO,oBAAoB;QAC/B,KAAK;YACD,OAAO,uBAAuB;QAClC,KAAK;YACD,OAAO,sBAAsB;QACjC,KAAK;YACD,OAAO,qBAAqB;IACpC;AACJ;AACA,SAAS,cAAc,IAAI;IACvB,MAAM,WAAW,CAAC;IAClB,KAAK,MAAM,WAAW,KAAK,QAAQ,CAAE;QACjC,QAAQ,CAAC,QAAQ,KAAK,CAAC,GAAG,YAAY,QAAQ,IAAI;IACtD;IACA,OAAO,CAAC,IAAI,GAAG;QACX,MAAM,eAAe,QAAQ,CAAC,GAAG,IAAI,CAAC;QACtC,OAAO,AAAC,eAAgB,aAAa,OAAO,QAAQ,EAAE;IAC1D;AACJ;AACA,SAAS,uBAAuB,IAAI;IAChC,MAAM,WAAW,KAAK,IAAI;IAC1B,MAAM,eAAe,YAAY,KAAK,IAAI;IAC1C,OAAO,CAAC,IAAI,GAAG,OAAS,AAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,OAAO,EAAE,YACpE,aAAa,OAAO,QACpB,EAAE;AACZ;AACA,SAAS,oBAAoB,IAAI;IAC7B,MAAM,YAAY,EAAE;IACpB,KAAK,MAAM,WAAW,KAAK,QAAQ,CAAE;QACjC,MAAM,YAAY,QAAQ,SAAS;QACnC,MAAM,eAAe,YAAY,QAAQ,IAAI;QAC7C,UAAU,IAAI,CAAC,CAAC,MAAM,IAAI,GAAG,OAAU,UAAU,QAAQ,aAAa,OAAO,QAAQ,EAAE;IAC3F;IACA,MAAM,WAAW,KAAK,IAAI;IAC1B,OAAO,CAAC,IAAI,GAAG;QACX,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS;QACjC,OAAO,AAAC,QAAQ,SAAS,KACnB,UAAU,OAAO,CAAC,CAAA,KAAM,GAAG,MAAM,OAAO,SACxC,EAAE;IACZ;AACJ;AACA,SAAS,sBAAsB,IAAI;IAC/B,MAAM,eAAe,YAAY,KAAK,IAAI;IAC1C,MAAM,oBAAoB,AAAC,KAAK,UAAU,KAAK,MACzC,sBACA;IACN,OAAO,CAAC,IAAI,GAAG;QACX,MAAM,OAAO,kBAAkB;QAC/B,IAAI,SAAS,MAAM;YACf,OAAO,EAAE;QACb;QACA,OAAO,aAAa,MAAM,OAAO;IACrC;AACJ;AACA,MAAM,sBAAsB,CAAC;IACzB,MAAM,OAAO,GAAG,IAAI;IACpB,IAAI,SAAS,MAAM;QACf,OAAO;IACX;IACA,OAAO,AAAC,IAAA,gNAAK,EAAC,QAAS,OAAO,oBAAoB;AACtD;AACA,MAAM,mBAAmB,CAAC;IACtB,MAAM,SAAS,GAAG,MAAM;IACxB,OAAO,AAAC,UAAU,IAAA,gNAAK,EAAC,UAAW,SAAS;AAChD;AACA,SAAS,qBAAqB,IAAI;IAC9B,MAAM,eAAe,YAAY,KAAK,IAAI;IAC1C,OAAO,CAAC,IAAI,MAAM,GAAG,OAAS,aAAa,SAAS;AACxD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2153, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/entities@4.5.0/node_modules/entities/lib/esm/generated/decode-data-html.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/entities/61afd4701eaa736978b13c7351cd3de9a96b04bc/src/", "sources": ["generated/decode-data-html.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,8CAA8C;;;;;uCAE/B,IAAI,WAAW,CAC1B,kBAAkB;AAClB,268CAA268C,CACt68C,KAAK,CAAC,EAAE,CAAC,CACT,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CACnC,CAAC", "debugId": null}}, {"offset": {"line": 2165, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/entities@4.5.0/node_modules/entities/lib/esm/generated/decode-data-xml.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/entities/61afd4701eaa736978b13c7351cd3de9a96b04bc/src/", "sources": ["generated/decode-data-xml.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,8CAA8C;;;;;uCAE/B,IAAI,WAAW,CAC1B,kBAAkB;AAClB,uFAAuF,CAClF,KAAK,CAAC,EAAE,CAAC,CACT,GAAG,CAAC,CAAC,CAAC,EAAE,CAAG,CAAD,AAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CACnC,CAAC", "debugId": null}}, {"offset": {"line": 2177, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/entities@4.5.0/node_modules/entities/lib/esm/decode_codepoint.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/entities/61afd4701eaa736978b13c7351cd3de9a96b04bc/src/", "sources": ["decode_codepoint.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,qHAAqH;;;;;;;;;;AAErH,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC;IACtB;QAAC,CAAC;QAAE,KAAK;KAAC;IACV,sDAAsD;IACtD;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,GAAG;KAAC;IACV;QAAC,GAAG;QAAE,GAAG;KAAC;CACb,CAAC,CAAC;AAKI,MAAM,aAAa,GACtB,iHAAiH;AACjH,CAAA,KAAA,MAAM,CAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KACpB,SAAU,SAAiB;IACvB,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,IAAI,SAAS,GAAG,MAAM,EAAE;QACpB,SAAS,IAAI,OAAO,CAAC;QACrB,MAAM,IAAI,MAAM,CAAC,YAAY,CACzB,AAAE,CAAD,QAAU,KAAK,EAAE,CAAC,EAAG,KAAK,CAAC,EAAG,MAAM,CACxC,CAAC;QACF,SAAS,GAAG,MAAM,GAAG,AAAC,SAAS,GAAG,KAAK,CAAC,CAAC;KAC5C;IAED,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IACzC,OAAO,MAAM,CAAC;AAClB,CAAC,CAAC;AAOA,SAAU,gBAAgB,CAAC,SAAiB;;IAC9C,IAAI,AAAC,SAAS,IAAI,MAAM,IAAI,SAAS,IAAI,MAAM,CAAC,GAAI,SAAS,GAAG,QAAQ,EAAE;QACtE,OAAO,MAAM,CAAC;KACjB;IAED,OAAO,CAAA,KAAA,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,SAAS,CAAC;AACjD,CAAC;AASa,SAAU,eAAe,CAAC,SAAiB;IACrD,OAAO,aAAa,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC;AACtD,CAAC", "debugId": null}}, {"offset": {"line": 2327, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/entities@4.5.0/node_modules/entities/lib/esm/decode.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/entities/61afd4701eaa736978b13c7351cd3de9a96b04bc/src/", "sources": ["decode.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,OAAO,cAAc,MAAM,iCAAiC,CAAC;AAC7D,OAAO,aAAa,MAAM,gCAAgC,CAAC;AAC3D,OAAO,eAAe,EAAE,EACpB,gBAAgB,EAChB,aAAa,GAChB,MAAM,uBAAuB,CAAC;;;;;;AAM/B,IAAW,SAaV;AAbD,CAAA,SAAW,SAAS;IAChB,SAAA,CAAA,SAAA,CAAA,MAAA,GAAA,GAAA,GAAA,KAAQ,CAAA;IACR,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAS,CAAA;IACT,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAW,CAAA;IACX,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAS,CAAA;IACT,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAS,CAAA;IACT,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAY,CAAA;IACZ,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,IAAA,GAAA,SAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,IAAA,GAAA,SAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,IAAA,GAAA,SAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAY,CAAA;IACZ,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAY,CAAA;IACZ,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAY,CAAA;AAChB,CAAC,EAbU,SAAS,IAAA,CAAT,SAAS,GAAA,CAAA,CAAA,GAanB;AAED,oFAAA,EAAsF,CACtF,MAAM,YAAY,GAAG,QAAQ,CAAC;AAE9B,IAAY,YAIX;AAJD,CAAA,SAAY,YAAY;IACpB,YAAA,CAAA,YAAA,CAAA,eAAA,GAAA,MAAA,GAAA,cAAoC,CAAA;IACpC,YAAA,CAAA,YAAA,CAAA,gBAAA,GAAA,MAAA,GAAA,eAAqC,CAAA;IACrC,YAAA,CAAA,YAAA,CAAA,aAAA,GAAA,IAAA,GAAA,YAAkC,CAAA;AACtC,CAAC,EAJW,YAAY,IAAA,CAAZ,YAAY,GAAA,CAAA,CAAA,GAIvB;AAED,SAAS,QAAQ,CAAC,IAAY;IAC1B,OAAO,IAAI,IAAI,SAAS,CAAC,IAAI,IAAI,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC;AAC5D,CAAC;AAED,SAAS,sBAAsB,CAAC,IAAY;IACxC,OAAO,AACH,AAAC,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,GACvD,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,CAC3D,CAAC;AACN,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAY;IACrC,OAAO,AACH,AAAC,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,GACvD,IAAI,IAAI,SAAS,CAAC,OAAO,IAAI,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,GACxD,QAAQ,CAAC,IAAI,CAAC,CACjB,CAAC;AACN,CAAC;AAED;;;;;GAKG,CACH,SAAS,6BAA6B,CAAC,IAAY;IAC/C,OAAO,IAAI,KAAK,SAAS,CAAC,MAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;AAClE,CAAC;AAED,IAAW,kBAMV;AAND,CAAA,SAAW,kBAAkB;IACzB,kBAAA,CAAA,kBAAA,CAAA,cAAA,GAAA,EAAA,GAAA,aAAW,CAAA;IACX,kBAAA,CAAA,kBAAA,CAAA,eAAA,GAAA,EAAA,GAAA,cAAY,CAAA;IACZ,kBAAA,CAAA,kBAAA,CAAA,iBAAA,GAAA,EAAA,GAAA,gBAAc,CAAA;IACd,kBAAA,CAAA,kBAAA,CAAA,aAAA,GAAA,EAAA,GAAA,YAAU,CAAA;IACV,kBAAA,CAAA,kBAAA,CAAA,cAAA,GAAA,EAAA,GAAA,aAAW,CAAA;AACf,CAAC,EANU,kBAAkB,IAAA,CAAlB,kBAAkB,GAAA,CAAA,CAAA,GAM5B;AAED,IAAY,YAOX;AAPD,CAAA,SAAY,YAAY;IACpB,4DAAA,EAA8D,CAC9D,YAAA,CAAA,YAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,qDAAA,EAAuD,CACvD,YAAA,CAAA,YAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,kEAAA,EAAoE,CACpE,YAAA,CAAA,YAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAa,CAAA;AACjB,CAAC,EAPW,YAAY,IAAA,CAAZ,YAAY,GAAA,CAAA,CAAA,GAOvB;AAgBK,MAAO,aAAa;IACtB,YACI,sCAAA,EAAwC,CACvB,UAAuB,EACxC;;;;;;;;OAQG,CACc,aAAqD,EACtE,8CAAA,EAAgD,CAC/B,MAA4B,CAAA;QAZ5B,IAAA,CAAA,UAAU,GAAV,UAAU,CAAa;QAUvB,IAAA,CAAA,aAAa,GAAb,aAAa,CAAwC;QAErD,IAAA,CAAA,MAAM,GAAN,MAAM,CAAsB;QAGjD,sCAAA,EAAwC,CAChC,IAAA,CAAA,KAAK,GAAG,kBAAkB,CAAC,WAAW,CAAC;QAC/C,2DAAA,EAA6D,CACrD,IAAA,CAAA,QAAQ,GAAG,CAAC,CAAC;QACrB;;;;;WAKG,CACK,IAAA,CAAA,MAAM,GAAG,CAAC,CAAC;QAEnB,0CAAA,EAA4C,CACpC,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QACtB,2DAAA,EAA6D,CACrD,IAAA,CAAA,MAAM,GAAG,CAAC,CAAC;QACnB,gDAAA,EAAkD,CAC1C,IAAA,CAAA,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC;IAnBtC,CAAC;IAqBJ,6CAAA,EAA+C,CAC/C,WAAW,CAAC,UAAwB,EAAA;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,WAAW,CAAC;QAC5C,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;IACtB,CAAC;IAED;;;;;;;;;;OAUG,CACH,KAAK,CAAC,GAAW,EAAE,MAAc,EAAA;QAC7B,OAAQ,IAAI,CAAC,KAAK,EAAE;YAChB,KAAK,kBAAkB,CAAC,WAAW,CAAC;gBAAC;oBACjC,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,GAAG,EAAE;wBAC1C,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,YAAY,CAAC;wBAC7C,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;wBACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;qBAClD;oBACD,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,WAAW,CAAC;oBAC5C,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;iBAC7C;YAED,KAAK,kBAAkB,CAAC,YAAY,CAAC;gBAAC;oBAClC,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;iBAC9C;YAED,KAAK,kBAAkB,CAAC,cAAc,CAAC;gBAAC;oBACpC,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;iBAChD;YAED,KAAK,kBAAkB,CAAC,UAAU,CAAC;gBAAC;oBAChC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;iBAC5C;YAED,KAAK,kBAAkB,CAAC,WAAW,CAAC;gBAAC;oBACjC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;iBAC7C;SACJ;IACL,CAAC;IAED;;;;;;;;OAQG,CACK,iBAAiB,CAAC,GAAW,EAAE,MAAc,EAAA;QACjD,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE;YACtB,OAAO,CAAC,CAAC,CAAC;SACb;QAED,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,KAAK,SAAS,CAAC,OAAO,EAAE;YAC/D,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,UAAU,CAAC;YAC3C,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;YACnB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;SAChD;QAED,IAAI,CAAC,KAAK,GAAG,kBAAkB,CAAC,cAAc,CAAC;QAC/C,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACjD,CAAC;IAEO,kBAAkB,CACtB,GAAW,EACX,KAAa,EACb,GAAW,EACX,IAAY,EAAA;QAEZ,IAAI,KAAK,KAAK,GAAG,EAAE;YACf,MAAM,UAAU,GAAG,GAAG,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,MAAM,GACP,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,GACxC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,QAAQ,IAAI,UAAU,CAAC;SAC/B;IACL,CAAC;IAED;;;;;;;;OAQG,CACK,eAAe,CAAC,GAAW,EAAE,MAAc,EAAA;QAC/C,MAAM,QAAQ,GAAG,MAAM,CAAC;QAExB,MAAO,MAAM,GAAG,GAAG,CAAC,MAAM,CAAE;YACxB,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACpC,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;gBAChD,MAAM,IAAI,CAAC,CAAC;aACf,MAAM;gBACH,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;gBACnD,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aAC1C;SACJ;QAED,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAEnD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;;;;;OAQG,CACK,mBAAmB,CAAC,GAAW,EAAE,MAAc,EAAA;QACnD,MAAM,QAAQ,GAAG,MAAM,CAAC;QAExB,MAAO,MAAM,GAAG,GAAG,CAAC,MAAM,CAAE;YACxB,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACpC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;gBAChB,MAAM,IAAI,CAAC,CAAC;aACf,MAAM;gBACH,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;gBACnD,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aAC1C;SACJ;QAED,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,CAAC;QAEnD,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;;;;;;;;;OAYG,CACK,iBAAiB,CAAC,MAAc,EAAE,cAAsB,EAAA;;QAC5D,yCAAyC;QACzC,IAAI,IAAI,CAAC,QAAQ,IAAI,cAAc,EAAE;YACjC,CAAA,KAAA,IAAI,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,0CAA0C,CACnD,IAAI,CAAC,QAAQ,CAChB,CAAC;YACF,OAAO,CAAC,CAAC;SACZ;QAED,kDAAkD;QAClD,IAAI,MAAM,KAAK,SAAS,CAAC,IAAI,EAAE;YAC3B,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;SACtB,MAAM,IAAI,IAAI,CAAC,UAAU,KAAK,YAAY,CAAC,MAAM,EAAE;YAChD,OAAO,CAAC,CAAC;SACZ;QAED,IAAI,CAAC,aAAa,KAAC,mOAAgB,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEjE,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,MAAM,KAAK,SAAS,CAAC,IAAI,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,uCAAuC,EAAE,CAAC;aACzD;YAED,IAAI,CAAC,MAAM,CAAC,iCAAiC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC9D;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;;;;;OAQG,CACK,gBAAgB,CAAC,GAAW,EAAE,MAAc,EAAA;QAChD,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAC5B,IAAI,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACzC,4EAA4E;QAC5E,IAAI,WAAW,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAE9D,MAAO,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAE;YACjD,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAEpC,IAAI,CAAC,SAAS,GAAG,eAAe,CAC5B,UAAU,EACV,OAAO,EACP,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,WAAW,CAAC,EACzC,IAAI,CACP,CAAC;YAEF,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE;gBACpB,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,IAEnB,IAAI,CAAC,UAAU,KAAK,YAAY,CAAC,SAAS,IACvC,8DAA8D;gBAC9D,CAAC,WAAW,KAAK,CAAC,IACd,6CAA6C;gBAC7C,6BAA6B,CAAC,IAAI,CAAC,CAAC,CAAC,EAC3C,CAAC,GACD,IAAI,CAAC,4BAA4B,EAAE,CAAC;aAC7C;YAED,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACrC,WAAW,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;YAE1D,kDAAkD;YAClD,IAAI,WAAW,KAAK,CAAC,EAAE;gBACnB,2DAA2D;gBAC3D,IAAI,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE;oBACzB,OAAO,IAAI,CAAC,mBAAmB,CAC3B,IAAI,CAAC,SAAS,EACd,WAAW,EACX,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAC9B,CAAC;iBACL;gBAED,2FAA2F;gBAC3F,IAAI,IAAI,CAAC,UAAU,KAAK,YAAY,CAAC,MAAM,EAAE;oBACzC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC;oBAC7B,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC;oBAC7B,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;iBACnB;aACJ;SACJ;QAED,OAAO,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;;;OAIG,CACK,4BAA4B,GAAA;;QAChC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAEpC,MAAM,WAAW,GACb,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;QAE3D,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7D,CAAA,KAAA,IAAI,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,uCAAuC,EAAE,CAAC;QAEvD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;;;;;;OAQG,CACK,mBAAmB,CACvB,MAAc,EACd,WAAmB,EACnB,QAAgB,EAAA;QAEhB,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAE5B,IAAI,CAAC,aAAa,CACd,WAAW,KAAK,CAAC,GACX,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,YAAY,GAC/C,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EAC5B,QAAQ,CACX,CAAC;QACF,IAAI,WAAW,KAAK,CAAC,EAAE;YACnB,0DAA0D;YAC1D,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SACxD;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;;;;OAMG,CACH,GAAG,GAAA;;QACC,OAAQ,IAAI,CAAC,KAAK,EAAE;YAChB,KAAK,kBAAkB,CAAC,WAAW,CAAC;gBAAC;oBACjC,sCAAsC;oBACtC,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,IACpB,CAAC,IAAI,CAAC,UAAU,KAAK,YAAY,CAAC,SAAS,IACvC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,SAAS,CAAC,GACjC,IAAI,CAAC,4BAA4B,EAAE,GACnC,CAAC,CAAC;iBACX;YACD,mDAAmD;YACnD,KAAK,kBAAkB,CAAC,cAAc,CAAC;gBAAC;oBACpC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACvC;YACD,KAAK,kBAAkB,CAAC,UAAU,CAAC;gBAAC;oBAChC,OAAO,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACvC;YACD,KAAK,kBAAkB,CAAC,YAAY,CAAC;gBAAC;oBAClC,CAAA,KAAA,IAAI,CAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,0CAA0C,CACnD,IAAI,CAAC,QAAQ,CAChB,CAAC;oBACF,OAAO,CAAC,CAAC;iBACZ;YACD,KAAK,kBAAkB,CAAC,WAAW,CAAC;gBAAC;oBACjC,iCAAiC;oBACjC,OAAO,CAAC,CAAC;iBACZ;SACJ;IACL,CAAC;CACJ;AAED;;;;;GAKG,CACH,SAAS,UAAU,CAAC,UAAuB;IACvC,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,MAAM,OAAO,GAAG,IAAI,aAAa,CAC7B,UAAU,EACV,CAAC,GAAG,EAAE,CAAI,CAAF,CAAC,CAAI,QAAI,gOAAa,EAAC,GAAG,CAAC,CAAC,CACvC,CAAC;IAEF,OAAO,SAAS,cAAc,CAC1B,GAAW,EACX,UAAwB;QAExB,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,MAAM,GAAG,CAAC,CAAC;QAEf,MAAO,CAAC,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,CAAE;YAC7C,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAEpC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAEhC,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CACrB,GAAG,EACH,eAAe;YACf,MAAM,GAAG,CAAC,CACb,CAAC;YAEF,IAAI,GAAG,GAAG,CAAC,EAAE;gBACT,SAAS,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;gBACnC,MAAM;aACT;YAED,SAAS,GAAG,MAAM,GAAG,GAAG,CAAC;YACzB,oDAAoD;YACpD,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;SAClD;QAED,MAAM,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAE1C,2DAA2D;QAC3D,GAAG,GAAG,EAAE,CAAC;QAET,OAAO,MAAM,CAAC;IAClB,CAAC,CAAC;AACN,CAAC;AAYK,SAAU,eAAe,CAC3B,UAAuB,EACvB,OAAe,EACf,OAAe,EACf,IAAY;IAEZ,MAAM,WAAW,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAChE,MAAM,UAAU,GAAG,OAAO,GAAG,YAAY,CAAC,UAAU,CAAC;IAErD,+CAA+C;IAC/C,IAAI,WAAW,KAAK,CAAC,EAAE;QACnB,OAAO,UAAU,KAAK,CAAC,IAAI,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACjE;IAED,kDAAkD;IAClD,IAAI,UAAU,EAAE;QACZ,MAAM,KAAK,GAAG,IAAI,GAAG,UAAU,CAAC;QAEhC,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,WAAW,GAClC,CAAC,CAAC,GACF,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;KACzC;IAED,kDAAkD;IAElD,mCAAmC;IACnC,IAAI,EAAE,GAAG,OAAO,CAAC;IACjB,IAAI,EAAE,GAAG,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;IAE9B,MAAO,EAAE,IAAI,EAAE,CAAE;QACb,MAAM,GAAG,GAAG,AAAC,EAAE,GAAG,EAAE,CAAC,IAAK,CAAC,CAAC;QAC5B,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;QAE/B,IAAI,MAAM,GAAG,IAAI,EAAE;YACf,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;SAChB,MAAM,IAAI,MAAM,GAAG,IAAI,EAAE;YACtB,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;SAChB,MAAM;YACH,OAAO,UAAU,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC;SACxC;KACJ;IAED,OAAO,CAAC,CAAC,CAAC;AACd,CAAC;AAED,MAAM,WAAW,GAAG,UAAU,CAAC,6OAAc,CAAC,CAAC;AAC/C,MAAM,UAAU,GAAG,UAAU,CAAC,4OAAa,CAAC,CAAC;AASvC,SAAU,UAAU,CAAC,GAAW,EAAE,IAAI,GAAG,YAAY,CAAC,MAAM;IAC9D,OAAO,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAClC,CAAC;AAQK,SAAU,mBAAmB,CAAC,GAAW;IAC3C,OAAO,WAAW,CAAC,GAAG,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;AACpD,CAAC;AAQK,SAAU,gBAAgB,CAAC,GAAW;IACxC,OAAO,WAAW,CAAC,GAAG,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AACjD,CAAC;AAQK,SAAU,SAAS,CAAC,GAAW;IACjC,OAAO,UAAU,CAAC,GAAG,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AAChD,CAAC", "debugId": null}}, {"offset": {"line": 2783, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Tokenizer.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/htmlparser2/c123610e003a1eaebc61febed01cabb6e41eb658/src/", "sources": ["Tokenizer.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EACH,cAAc,EACd,aAAa,EACb,YAAY,EACZ,eAAe,EACf,gBAAgB,GACnB,MAAM,wBAAwB,CAAC;;AAEhC,IAAW,SA4BV;AA5BD,CAAA,SAAW,SAAS;IAChB,SAAA,CAAA,SAAA,CAAA,MAAA,GAAA,EAAA,GAAA,KAAS,CAAA;IACT,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,GAAA,GAAA,SAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,WAAA,GAAA,GAAA,GAAA,UAAc,CAAA;IACd,SAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,GAAA,GAAA,gBAAoB,CAAA;IACpB,SAAA,CAAA,SAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAY,CAAA;IACZ,SAAA,CAAA,SAAA,CAAA,kBAAA,GAAA,GAAA,GAAA,iBAAsB,CAAA;IACtB,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,MAAA,GAAA,GAAA,GAAA,KAAU,CAAA;IACV,SAAA,CAAA,SAAA,CAAA,cAAA,GAAA,GAAA,GAAA,aAAkB,CAAA;IAClB,SAAA,CAAA,SAAA,CAAA,cAAA,GAAA,GAAA,GAAA,aAAkB,CAAA;IAClB,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAW,CAAA;IACX,SAAA,CAAA,SAAA,CAAA,QAAA,GAAA,GAAA,GAAA,OAAY,CAAA;IACZ,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAW,CAAA;IACX,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAW,CAAA;IACX,SAAA,CAAA,SAAA,CAAA,OAAA,GAAA,GAAA,GAAA,MAAW,CAAA;IACX,SAAA,CAAA,SAAA,CAAA,KAAA,GAAA,GAAA,GAAA,IAAS,CAAA;IACT,SAAA,CAAA,SAAA,CAAA,KAAA,GAAA,GAAA,GAAA,IAAS,CAAA;IACT,SAAA,CAAA,SAAA,CAAA,KAAA,GAAA,GAAA,GAAA,IAAS,CAAA;IACT,SAAA,CAAA,SAAA,CAAA,eAAA,GAAA,GAAA,GAAA,cAAmB,CAAA;IACnB,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,IAAA,GAAA,QAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,GAAA,GAAA,QAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,IAAA,GAAA,QAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,IAAA,GAAA,QAAa,CAAA;IACb,SAAA,CAAA,SAAA,CAAA,uBAAA,GAAA,GAAA,GAAA,sBAA2B,CAAA;AAC/B,CAAC,EA5BU,SAAS,IAAA,CAAT,SAAS,GAAA,CAAA,CAAA,GA4BnB;AAED,4CAAA,EAA8C,CAC9C,IAAW,KAyCV;AAzCD,CAAA,SAAW,KAAK;IACZ,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAQ,CAAA;IACR,KAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,EAAA,GAAA,eAAa,CAAA;IACb,KAAA,CAAA,KAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAS,CAAA;IACT,KAAA,CAAA,KAAA,CAAA,mBAAA,GAAA,EAAA,GAAA,kBAAgB,CAAA;IAChB,KAAA,CAAA,KAAA,CAAA,uBAAA,GAAA,EAAA,GAAA,sBAAoB,CAAA;IACpB,KAAA,CAAA,KAAA,CAAA,mBAAA,GAAA,EAAA,GAAA,kBAAgB,CAAA;IAChB,KAAA,CAAA,KAAA,CAAA,sBAAA,GAAA,EAAA,GAAA,qBAAmB,CAAA;IAEnB,aAAa;IACb,KAAA,CAAA,KAAA,CAAA,sBAAA,GAAA,EAAA,GAAA,qBAAmB,CAAA;IACnB,KAAA,CAAA,KAAA,CAAA,kBAAA,GAAA,EAAA,GAAA,iBAAe,CAAA;IACf,KAAA,CAAA,KAAA,CAAA,qBAAA,GAAA,GAAA,GAAA,oBAAkB,CAAA;IAClB,KAAA,CAAA,KAAA,CAAA,uBAAA,GAAA,GAAA,GAAA,sBAAoB,CAAA;IACpB,KAAA,CAAA,KAAA,CAAA,qBAAA,GAAA,GAAA,GAAA,oBAAkB,CAAA;IAClB,KAAA,CAAA,KAAA,CAAA,qBAAA,GAAA,GAAA,GAAA,oBAAkB,CAAA;IAClB,KAAA,CAAA,KAAA,CAAA,qBAAA,GAAA,GAAA,GAAA,oBAAkB,CAAA;IAElB,eAAe;IACf,KAAA,CAAA,KAAA,CAAA,oBAAA,GAAA,GAAA,GAAA,mBAAiB,CAAA;IACjB,KAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,GAAA,GAAA,eAAa,CAAA;IAEb,0BAA0B;IAC1B,KAAA,CAAA,KAAA,CAAA,0BAAA,GAAA,GAAA,GAAA,yBAAuB,CAAA;IAEvB,mBAAmB;IACnB,KAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,GAAA,GAAA,eAAa,CAAA;IACb,KAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,GAAA,GAAA,eAAa,CAAA;IACb,KAAA,CAAA,KAAA,CAAA,mBAAA,GAAA,GAAA,GAAA,kBAAgB,CAAA;IAChB,KAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,GAAA,GAAA,eAAa,CAAA;IAEb,eAAe;IACf,KAAA,CAAA,KAAA,CAAA,iBAAA,GAAA,GAAA,GAAA,gBAAc,CAAA;IACd,KAAA,CAAA,KAAA,CAAA,uBAAA,GAAA,GAAA,GAAA,sBAAoB,CAAA;IACpB,KAAA,CAAA,KAAA,CAAA,eAAA,GAAA,GAAA,GAAA,cAAY,CAAA;IAEZ,KAAA,CAAA,KAAA,CAAA,eAAA,GAAA,GAAA,GAAA,cAAY,CAAA;IACZ,KAAA,CAAA,KAAA,CAAA,sBAAA,GAAA,GAAA,GAAA,qBAAmB,CAAA;IACnB,KAAA,CAAA,KAAA,CAAA,gBAAA,GAAA,GAAA,GAAA,eAAa,CAAA;IACb,KAAA,CAAA,KAAA,CAAA,kBAAA,GAAA,GAAA,GAAA,iBAAe,CAAA;IACf,KAAA,CAAA,KAAA,CAAA,cAAA,GAAA,GAAA,GAAA,aAAW,CAAA;AACf,CAAC,EAzCU,KAAK,IAAA,CAAL,KAAK,GAAA,CAAA,CAAA,GAyCf;AAED,SAAS,YAAY,CAAC,CAAS;IAC3B,OAAO,AACH,CAAC,KAAK,SAAS,CAAC,KAAK,IACrB,CAAC,KAAK,SAAS,CAAC,OAAO,IACvB,CAAC,KAAK,SAAS,CAAC,GAAG,IACnB,CAAC,KAAK,SAAS,CAAC,QAAQ,IACxB,CAAC,KAAK,SAAS,CAAC,cAAc,CACjC,CAAC;AACN,CAAC;AAED,SAAS,iBAAiB,CAAC,CAAS;IAChC,OAAO,CAAC,KAAK,SAAS,CAAC,KAAK,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AAC1E,CAAC;AAED,SAAS,QAAQ,CAAC,CAAS;IACvB,OAAO,CAAC,IAAI,SAAS,CAAC,IAAI,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC;AACtD,CAAC;AAED,SAAS,YAAY,CAAC,CAAS;IAC3B,OAAO,AACH,AAAC,CAAC,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,GAC/C,CAAC,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,CACnD,CAAC;AACN,CAAC;AAED,SAAS,UAAU,CAAC,CAAS;IACzB,OAAO,AACH,AAAC,CAAC,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,GAC/C,CAAC,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,CACnD,CAAC;AACN,CAAC;AAED,IAAY,SAKX;AALD,CAAA,SAAY,SAAS;IACjB,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,EAAA,GAAA,SAAW,CAAA;IACX,SAAA,CAAA,SAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;IACZ,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;IACV,SAAA,CAAA,SAAA,CAAA,SAAA,GAAA,EAAA,GAAA,QAAU,CAAA;AACd,CAAC,EALW,SAAS,IAAA,CAAT,SAAS,GAAA,CAAA,CAAA,GAKpB;AAoBD;;;;;GAKG,CACH,MAAM,SAAS,GAAG;IACd,KAAK,EAAE,IAAI,UAAU,CAAC;QAAC,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;KAAC,CAAC;IAC3D,QAAQ,EAAE,IAAI,UAAU,CAAC;QAAC,IAAI;QAAE,IAAI;QAAE,IAAI;KAAC,CAAC;IAC5C,UAAU,EAAE,IAAI,UAAU,CAAC;QAAC,IAAI;QAAE,IAAI;QAAE,IAAI;KAAC,CAAC;IAC9C,SAAS,EAAE,IAAI,UAAU,CAAC;QAAC,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;KAAC,CAAC;IAC3E,QAAQ,EAAE,IAAI,UAAU,CAAC;QAAC,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;KAAC,CAAC;IACpE,QAAQ,EAAE,IAAI,UAAU,CAAC;QAAC,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;QAAE,IAAI;KAAC,CAAC,EAAE,YAAY;CACrF,CAAC;AAEY,MAAO,SAAS;IAsB1B,YACI,EACI,OAAO,GAAG,KAAK,EACf,cAAc,GAAG,IAAI,EACyB,EACjC,GAAc,CAAA;QAAd,IAAA,CAAA,GAAG,GAAH,GAAG,CAAW;QA1BnC,2CAAA,EAA6C,CACrC,IAAA,CAAA,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QAC3B,qBAAA,EAAuB,CACf,IAAA,CAAA,MAAM,GAAG,EAAE,CAAC;QACpB,+DAAA,EAAiE,CACzD,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACzB,kEAAA,EAAoE,CAC5D,IAAA,CAAA,KAAK,GAAG,CAAC,CAAC;QAClB,gIAAA,EAAkI,CAC1H,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;QAC/B,kEAAA,EAAoE,CAC5D,IAAA,CAAA,SAAS,GAAG,KAAK,CAAC;QAC1B,qDAAA,EAAuD,CAChD,IAAA,CAAA,OAAO,GAAG,IAAI,CAAC;QACtB,sCAAA,EAAwC,CAChC,IAAA,CAAA,MAAM,GAAG,CAAC,CAAC;QA+EX,IAAA,CAAA,eAAe,GAAe,SAAU,CAAC;QACzC,IAAA,CAAA,aAAa,GAAG,CAAC,CAAC;QA+WlB,IAAA,CAAA,SAAS,GAAG,CAAC,CAAC;QACd,IAAA,CAAA,WAAW,GAAG,CAAC,CAAC;QACxB,sFAAA,EAAwF,CAChF,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACjB,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QAtbrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,8RAAa,CAAC,CAAC,CAAC,iSAAc,CAAC;IAC/D,CAAC;IAEM,KAAK,GAAA;QACR,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,SAAU,CAAC;QAClC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IACpB,CAAC;IAEM,KAAK,CAAC,KAAa,EAAA;QACtB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAEM,GAAG,GAAA;QACN,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;IACpC,CAAC;IAEM,KAAK,GAAA;QACR,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;IACzB,CAAC;IAEM,MAAM,GAAA;QACT,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;YAC/C,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;IACL,CAAC;IAED;;OAEG,CACI,QAAQ,GAAA;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG,CACI,eAAe,GAAA;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAEO,SAAS,CAAC,CAAS,EAAA;QACvB,IACI,CAAC,KAAK,SAAS,CAAC,EAAE,IACjB,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAC5D;YACE,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE;gBAChC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;aAClD;YACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;SAClC,MAAM,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,SAAS,CAAC,GAAG,EAAE;YACnD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC;SACnC;IACL,CAAC;IAIO,yBAAyB,CAAC,CAAS,EAAA;QACvC,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QACjE,MAAM,OAAO,GAAG,KAAK,GAEf,iBAAiB,CAAC,CAAC,CAAC,GAEpB,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAE9D,IAAI,CAAC,OAAO,EAAE;YACV,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;SAC1B,MAAM,IAAI,CAAC,KAAK,EAAE;YACf,IAAI,CAAC,aAAa,EAAE,CAAC;YACrB,OAAO;SACV;QAED,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC;QAC7B,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED,iEAAA,EAAmE,CAC3D,iBAAiB,CAAC,CAAS,EAAA;QAC/B,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;YACpD,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;gBACvC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBAE3D,IAAI,IAAI,CAAC,YAAY,GAAG,SAAS,EAAE;oBAC/B,uDAAuD;oBACvD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;oBAC/B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;oBACvB,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;oBAC9C,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC;iBAC5B;gBAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;gBACvB,IAAI,CAAC,YAAY,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,qBAAqB;gBACxD,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;gBAC9B,OAAO,CAAC,8CAA8C;aACzD;YAED,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;SAC1B;QAED,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACzD,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC;SAC3B,MAAM,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;YACjC,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,CAAC,QAAQ,EAAE;gBAC7C,6CAA6C;gBAC7C,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,SAAS,CAAC,GAAG,EAAE;oBAC5C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC;iBACnC;aACJ,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;gBACzC,gDAAgD;gBAChD,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;aAC1B;SACJ,MAAM;YACH,6EAA6E;YAC7E,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,CAAC,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC;SACnD;IACL,CAAC;IAEO,kBAAkB,CAAC,CAAS,EAAA;QAChC,IAAI,CAAC,KAAK,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YAC3C,IAAI,EAAE,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE;gBACjD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC;gBACjC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,QAAQ,CAAC;gBAC1C,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;gBACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;aACtC;SACJ,MAAM;YACH,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B;SACzD;IACL,CAAC;IAED;;;;;OAKG,CACK,aAAa,CAAC,CAAS,EAAA;QAC3B,MAAO,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAE;YACpD,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACxD,OAAO,IAAI,CAAC;aACf;SACJ;QAED;;;;;WAKG,CACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAElD,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG,CACK,kBAAkB,CAAC,CAAS,EAAA;QAChC,IAAI,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YAChD,IAAI,EAAE,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;gBACtD,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,CAAC,QAAQ,EAAE;oBAC7C,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBACtD,MAAM;oBACH,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;iBACxD;gBAED,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;gBACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;gBACnC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;aAC3B;SACJ,MAAM,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,EAAE;YACjC,sDAAsD;YACtD,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC7C,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;aAC1B;SACJ,MAAM,IAAI,CAAC,KAAK,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,EAAE;YAC3D,uCAAuC;YACvC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;SAC1B;IACL,CAAC;IAED;;;;;OAKG,CACK,cAAc,CAAC,CAAS,EAAA;QAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAEO,YAAY,CAAC,QAAoB,EAAE,MAAc,EAAA;QACrD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;QAChC,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,oBAAoB,CAAC;IAC5C,CAAC;IAEO,kBAAkB,CAAC,CAAS,EAAA;QAChC,IAAI,CAAC,KAAK,SAAS,CAAC,eAAe,EAAE;YACjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,iBAAiB,CAAC;YACrC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACtC,MAAM,IAAI,CAAC,KAAK,SAAS,CAAC,YAAY,EAAE;YACrC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,uBAAuB,CAAC;YAC3C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACtC,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;YAC/B,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;YACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBAClD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;aAC5C,MAAM;gBACH,IAAI,CAAC,KAAK,GACN,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,GAC3C,KAAK,CAAC,cAAc,GACpB,KAAK,CAAC,SAAS,CAAC;aAC7B;SACJ,MAAM,IAAI,CAAC,KAAK,SAAS,CAAC,KAAK,EAAE;YAC9B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,oBAAoB,CAAC;SAC3C,MAAM;YACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACrB;IACL,CAAC;IACO,cAAc,CAAC,CAAS,EAAA;QAC5B,IAAI,iBAAiB,CAAC,CAAC,CAAC,EAAE;YACtB,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC;YACvC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;SACpC;IACL,CAAC;IACO,yBAAyB,CAAC,CAAS,EAAA;QACvC,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;QACjB,SAAS;SACZ,MAAM,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;SAC3B,MAAM;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAC7B,KAAK,CAAC,gBAAgB,GACtB,KAAK,CAAC,gBAAgB,CAAC;YAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;SAClC;IACL,CAAC;IACO,qBAAqB,CAAC,CAAS,EAAA;QACnC,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;YACvC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACnD,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC;YACvC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;SACpC;IACL,CAAC;IACO,wBAAwB,CAAC,CAAS,EAAA;QACtC,4BAA4B;QAC5B,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;YACxD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;YAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACtC;IACL,CAAC;IACO,wBAAwB,CAAC,CAAS,EAAA;QACtC,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,EAAE;YACpB,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC;gBAChC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;aAC1B,MAAM;gBACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;aAC3B;YACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;YAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACtC,MAAM,IAAI,CAAC,KAAK,SAAS,CAAC,KAAK,EAAE;YAC9B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,gBAAgB,CAAC;SACvC,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC;YACnC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;SAClC;IACL,CAAC;IACO,qBAAqB,CAAC,CAAS,EAAA;QACnC,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,EAAE;YACpB,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;YAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;YACnC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,4DAA4D;SACvF,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC;YACvC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;SACpC;IACL,CAAC;IACO,oBAAoB,CAAC,CAAS,EAAA;QAClC,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,IAAI,iBAAiB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,kBAAkB,CAAC;YACtC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;SACnC;IACL,CAAC;IACO,uBAAuB,CAAC,CAAS,EAAA;QACrC,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,EAAE;YACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,oBAAoB,CAAC;SAC3C,MAAM,IAAI,CAAC,KAAK,SAAS,CAAC,KAAK,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,EAAE;YACpD,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC;YACvC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;SACpC,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC;YACnC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;SAClC;IACL,CAAC;IACO,yBAAyB,CAAC,CAAS,EAAA;QACvC,IAAI,CAAC,KAAK,SAAS,CAAC,WAAW,EAAE;YAC7B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,kBAAkB,CAAC;YACtC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACtC,MAAM,IAAI,CAAC,KAAK,SAAS,CAAC,WAAW,EAAE;YACpC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,kBAAkB,CAAC;YACtC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACtC,MAAM,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;YAC/B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,kBAAkB,CAAC;YACtC,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC,kBAAkB;SAC5D;IACL,CAAC;IACO,sBAAsB,CAAC,CAAS,EAAE,KAAa,EAAA;QACnD,IACI,CAAC,KAAK,KAAK,IACV,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CACrD;YACE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,WAAW,CAChB,KAAK,KAAK,SAAS,CAAC,WAAW,GACzB,SAAS,CAAC,MAAM,GAChB,SAAS,CAAC,MAAM,EACtB,IAAI,CAAC,KAAK,CACb,CAAC;YACF,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC;SAC1C,MAAM,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,SAAS,CAAC,GAAG,EAAE;YACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;YAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC;SACnC;IACL,CAAC;IACO,iCAAiC,CAAC,CAAS,EAAA;QAC/C,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC;IAC1D,CAAC;IACO,iCAAiC,CAAC,CAAS,EAAA;QAC/C,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,SAAS,CAAC,WAAW,CAAC,CAAC;IAC1D,CAAC;IACO,6BAA6B,CAAC,CAAS,EAAA;QAC3C,IAAI,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,EAAE;YACvC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;YACvB,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC;YACvC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;SACpC,MAAM,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,SAAS,CAAC,GAAG,EAAE;YACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;YAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC;SACnC;IACL,CAAC;IACO,sBAAsB,CAAC,CAAS,EAAA;QACpC,IAAI,CAAC,KAAK,SAAS,CAAC,oBAAoB,EAAE;YACtC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC;YACjC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;SAC1B,MAAM;YACH,IAAI,CAAC,KAAK,GACN,CAAC,KAAK,SAAS,CAAC,IAAI,GACd,KAAK,CAAC,aAAa,GACnB,KAAK,CAAC,aAAa,CAAC;SACjC;IACL,CAAC;IACO,kBAAkB,CAAC,CAAS,EAAA;QAChC,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;YACxD,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACtD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACtC;IACL,CAAC;IACO,4BAA4B,CAAC,CAAS,EAAA;QAC1C,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;YACxD,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YAChE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACtC;IACL,CAAC;IACO,kBAAkB,CAAC,CAAS,EAAA;QAChC,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,EAAE;YACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC;YACjC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,UAAU,CAAC;YAC5C,mCAAmC;YACnC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;YACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACtC,MAAM;YACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC;SACpC;IACL,CAAC;IACO,qBAAqB,CAAC,CAAS,EAAA;QACnC,IAAI,CAAC,KAAK,SAAS,CAAC,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;YACxD,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YACrD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SACtC;IACL,CAAC;IACO,mBAAmB,CAAC,CAAS,EAAA;QACjC,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;QACvB,IAAI,KAAK,KAAK,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;YAClC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;SAC7C,MAAM,IAAI,KAAK,KAAK,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YACxC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;SAC5C,MAAM;YACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC;YAC7B,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B;SACrD;IACL,CAAC;IAQO,iBAAiB,CAAC,CAAS,EAAA;QAC/B,yCAAyC;QACzC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QAEtB,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE;YACxB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC;SAC1C,MAAM,IAAI,CAAC,KAAK,SAAS,CAAC,GAAG,EAAE;QAC5B,kEAAkE;SACrE,MAAM;YACH,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;YACnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACtC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;SAC9B;IACL,CAAC;IAEO,kBAAkB,CAAC,CAAS,EAAA;QAChC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC;QAEvB,IAAI,CAAC,SAAS,OAAG,wOAAe,EAC5B,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,GAAG,CAAC,EAClB,CAAC,CACJ,CAAC;QAEF,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE;YACpB,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,EAAE,CAAC;YACb,OAAO;SACV;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEnD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG,qOAAY,CAAC,YAAY,CAAC;QAE5D,kDAAkD;QAClD,IAAI,MAAM,EAAE;YACR,4EAA4E;YAC5E,MAAM,WAAW,GAAG,CAAC,MAAM,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC;YAEvC,mFAAmF;YACnF,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,EAAE;gBACnD,IAAI,CAAC,SAAS,IAAI,WAAW,CAAC;aACjC,MAAM;gBACH,kDAAkD;gBAClD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;gBAEvD,IAAI,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE;oBACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;iBACpD;gBAED,0DAA0D;gBAC1D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC;gBACnC,IAAI,CAAC,SAAS,IAAI,WAAW,CAAC;gBAC9B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;gBACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;gBAEnC,IAAI,WAAW,KAAK,CAAC,EAAE;oBACnB,IAAI,CAAC,eAAe,EAAE,CAAC;iBAC1B;aACJ;SACJ;IACL,CAAC;IAEO,eAAe,GAAA;QACnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;QAE5B,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE;YACzB,OAAO;SACV;QAED,MAAM,WAAW,GACb,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,qOAAY,CAAC,YAAY,CAAC,IAChE,EAAE,CAAC;QAEP,OAAQ,WAAW,EAAE;YACjB,KAAK,CAAC,CAAC;gBAAC;oBACJ,IAAI,CAAC,aAAa,CACd,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,GAC9B,CAAC,qOAAY,CAAC,YAAY,CACjC,CAAC;oBACF,MAAM;iBACT;YACD,KAAK,CAAC,CAAC;gBAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC3D,MAAM;iBACT;YACD,KAAK,CAAC,CAAC;gBAAC;oBACJ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC3D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;iBAC9D;SACJ;IACL,CAAC;IAEO,wBAAwB,CAAC,CAAS,EAAA;QACtC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE;YACjC,IAAI,CAAC,YAAY,EAAE,CAAC;YACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC;SAClC,MAAM;YACH,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,eAAe,CAAC;YACnC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;SAChC;IACL,CAAC;IAEO,iBAAiB,CAAC,MAAe,EAAA;QACrC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACvD,MAAM,WAAW,GACb,WAAW,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,CAAC,CAAC;QAE/D,IAAI,WAAW,KAAK,IAAI,CAAC,KAAK,EAAE;YAC5B,2BAA2B;YAC3B,IAAI,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE;gBACjC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;aACpD;YAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;YAChD,IAAI,CAAC,aAAa,KAAC,mOAAgB,EAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;SAC3D;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;IAChC,CAAC;IACO,oBAAoB,CAAC,CAAS,EAAA;QAClC,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,EAAE;YACtB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;SAChC,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAClE,IAAI,CAAC,YAAY,EAAE,CAAC;SACvB,MAAM;YACH,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC1B,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;aACjC,MAAM;gBACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;aAC/B;YACD,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;IACL,CAAC;IACO,gBAAgB,CAAC,CAAS,EAAA;QAC9B,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,EAAE;YACtB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;SAChC,MAAM,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE;YACpB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAClE,IAAI,CAAC,YAAY,EAAE,CAAC;SACvB,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;YACtB,IAAI,CAAC,YAAY,GACb,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;YAClE,IAAI,CAAC,YAAY,EAAE,CAAC;SACvB,MAAM;YACH,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBAC1B,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;aACjC,MAAM;gBACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC;aAC/B;YACD,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;IACL,CAAC;IAEO,iBAAiB,GAAA;QACrB,OAAO,AACH,CAAC,IAAI,CAAC,OAAO,IACb,CAAC,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,IAC1B,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,YAAY,CAAC,CAC7C,CAAC;IACN,CAAC;IAED;;OAEG,CACK,OAAO,GAAA;QACX,qEAAqE;QACrE,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,KAAK,EAAE;YAClD,IACI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,IACxB,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,KAAK,CAAC,CAAC,CACjE;gBACE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC/C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;aAClC,MAAM,IACH,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,kBAAkB,IACvC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,kBAAkB,IACvC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,kBAAkB,EACzC;gBACE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBACrD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC;aAClC;SACJ;IACL,CAAC;IAEO,cAAc,GAAA;QAClB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC;IACzE,CAAC;IAED;;;;OAIG,CACK,KAAK,GAAA;QACT,MAAO,IAAI,CAAC,cAAc,EAAE,CAAE;YAC1B,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;YAC3D,OAAQ,IAAI,CAAC,KAAK,EAAE;gBAChB,KAAK,KAAK,CAAC,IAAI,CAAC;oBAAC;wBACb,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wBAClB,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,oBAAoB,CAAC;oBAAC;wBAC7B,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;wBAClC,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,YAAY,CAAC;oBAAC;wBACrB,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;wBAC1B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,aAAa,CAAC;oBAAC;wBACtB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;wBAC3B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,kBAAkB,CAAC;oBAAC;wBAC3B,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC;wBAC1C,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,eAAe,CAAC;oBAAC;wBACxB,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;wBAC7B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,aAAa,CAAC;oBAAC;wBACtB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;wBAC3B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,gBAAgB,CAAC;oBAAC;wBACzB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;wBAC9B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,mBAAmB,CAAC;oBAAC;wBAC5B,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;wBACjC,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,SAAS,CAAC;oBAAC;wBAClB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;wBACvB,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,gBAAgB,CAAC;oBAAC;wBACzB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;wBAC9B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,aAAa,CAAC;oBAAC;wBACtB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;wBAC3B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,kBAAkB,CAAC;oBAAC;wBAC3B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;wBAChC,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,kBAAkB,CAAC;oBAAC;wBAC3B,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC,CAAC;wBAC1C,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,oBAAoB,CAAC;oBAAC;wBAC7B,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;wBAClC,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,oBAAoB,CAAC;oBAAC;wBAC7B,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;wBAClC,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,mBAAmB,CAAC;oBAAC;wBAC5B,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;wBACjC,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,cAAc,CAAC;oBAAC;wBACvB,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;wBAC5B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,kBAAkB,CAAC;oBAAC;wBAC3B,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC;wBACtC,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,gBAAgB,CAAC;oBAAC;wBACzB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;wBAC9B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,aAAa,CAAC;oBAAC;wBACtB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;wBAC3B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,iBAAiB,CAAC;oBAAC;wBAC1B,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;wBAC/B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,aAAa,CAAC;oBAAC;wBACtB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;wBAC3B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,uBAAuB,CAAC;oBAAC;wBAChC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC;wBACrC,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,aAAa,CAAC;oBAAC;wBACtB,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;wBAC3B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,YAAY,CAAC;oBAAC;wBACrB,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;wBAC1B,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,WAAW,CAAC;oBAAC;wBACpB,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;wBACzB,MAAM;qBACT;gBACD,KAAK,KAAK,CAAC,eAAe,CAAC;oBAAC;wBACxB,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC;wBAC7B,MAAM;qBACT;gBACD,OAAO,CAAC;oBAAC;wBACL,8CAA8C;wBAC9C,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC;qBACpC;aACJ;YACD,IAAI,CAAC,KAAK,EAAE,CAAC;SAChB;QACD,IAAI,CAAC,OAAO,EAAE,CAAC;IACnB,CAAC;IAEO,MAAM,GAAA;QACV,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,aAAa,EAAE;YACpC,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;QAED,0DAA0D;QAC1D,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,EAAE;YAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC7B;QACD,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC;IACrB,CAAC;IAED,8BAAA,EAAgC,CACxB,kBAAkB,GAAA;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAClD,IAAI,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,aAAa,EAAE;YACpC,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,CAAC,QAAQ,EAAE;gBAC7C,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;aACpD,MAAM;gBACH,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;aACtD;SACJ,MAAM,IACH,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,eAAe,IACpC,IAAI,CAAC,iBAAiB,EAAE,EAC1B;YACE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC9B,4CAA4C;SAC/C,MAAM,IACH,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,IAChC,IAAI,CAAC,iBAAiB,EAAE,EAC1B;YACE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAC9B,4CAA4C;SAC/C,MAAM,IACH,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,SAAS,IAC9B,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,mBAAmB,IACxC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,oBAAoB,IACzC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,kBAAkB,IACvC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,eAAe,IACpC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,kBAAkB,IACvC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,kBAAkB,IACvC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,kBAAkB,IACvC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,gBAAgB,EACvC;QACE;;;eAGG,EACN,MAAM;YACH,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;SAChD;IACL,CAAC;IAEO,WAAW,CAAC,KAAa,EAAE,QAAgB,EAAA;QAC/C,IACI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,IAC7B,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,YAAY,EACvC;YACE,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;SAC1C,MAAM;YACH,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;SACpC;IACL,CAAC;IACO,aAAa,CAAC,EAAU,EAAA;QAC5B,IACI,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,IAC7B,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,YAAY,EACvC;YACE,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;SAC/B,MAAM;YACH,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;SAC7B;IACL,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 3686, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/Parser.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/htmlparser2/c123610e003a1eaebc61febed01cabb6e41eb658/src/", "sources": ["Parser.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;AAAA,OAAO,SAAS,EAAE,EAAa,SAAS,EAAE,MAAM,gBAAgB,CAAC;;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;;;AAEvD,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC;IACrB,OAAO;IACP,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,UAAU;CACb,CAAC,CAAC;AACH,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC;IAAC,GAAG;CAAC,CAAC,CAAC;AAC5B,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC;IAAC,OAAO;IAAE,OAAO;CAAC,CAAC,CAAC;AACrD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC;IAAC,IAAI;IAAE,IAAI;CAAC,CAAC,CAAC;AACtC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC;IAAC,IAAI;IAAE,IAAI;CAAC,CAAC,CAAC;AAEtC,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAsB;IAClD;QAAC,IAAI;QAAE,IAAI,GAAG,CAAC;YAAC,IAAI;YAAE,IAAI;YAAE,IAAI;SAAC,CAAC;KAAC;IACnC;QAAC,IAAI;QAAE,IAAI,GAAG,CAAC;YAAC,IAAI;SAAC,CAAC;KAAC;IACvB;QAAC,IAAI;QAAE,IAAI,GAAG,CAAC;YAAC,OAAO;YAAE,IAAI;YAAE,IAAI;SAAC,CAAC;KAAC;IACtC;QAAC,MAAM;QAAE,IAAI,GAAG,CAAC;YAAC,MAAM;YAAE,MAAM;YAAE,QAAQ;SAAC,CAAC;KAAC;IAC7C;QAAC,IAAI;QAAE,IAAI,GAAG,CAAC;YAAC,IAAI;SAAC,CAAC;KAAC;IACvB;QAAC,GAAG;QAAE,IAAI;KAAC;IACX;QAAC,IAAI;QAAE,IAAI;KAAC;IACZ;QAAC,IAAI;QAAE,IAAI;KAAC;IACZ;QAAC,IAAI;QAAE,IAAI;KAAC;IACZ;QAAC,IAAI;QAAE,IAAI;KAAC;IACZ;QAAC,IAAI;QAAE,IAAI;KAAC;IACZ;QAAC,IAAI;QAAE,IAAI;KAAC;IACZ;QAAC,QAAQ;QAAE,QAAQ;KAAC;IACpB;QAAC,OAAO;QAAE,QAAQ;KAAC;IACnB;QAAC,QAAQ;QAAE,QAAQ;KAAC;IACpB;QAAC,QAAQ;QAAE,QAAQ;KAAC;IACpB;QAAC,UAAU;QAAE,QAAQ;KAAC;IACtB;QAAC,UAAU;QAAE,QAAQ;KAAC;IACtB;QAAC,QAAQ;QAAE,IAAI,GAAG,CAAC;YAAC,QAAQ;SAAC,CAAC;KAAC;IAC/B;QAAC,UAAU;QAAE,IAAI,GAAG,CAAC;YAAC,UAAU;YAAE,QAAQ;SAAC,CAAC;KAAC;IAC7C;QAAC,IAAI;QAAE,OAAO;KAAC;IACf;QAAC,IAAI;QAAE,OAAO;KAAC;IACf;QAAC,SAAS;QAAE,IAAI;KAAC;IACjB;QAAC,SAAS;QAAE,IAAI;KAAC;IACjB;QAAC,OAAO;QAAE,IAAI;KAAC;IACf;QAAC,YAAY;QAAE,IAAI;KAAC;IACpB;QAAC,SAAS;QAAE,IAAI;KAAC;IACjB;QAAC,KAAK;QAAE,IAAI;KAAC;IACb;QAAC,IAAI;QAAE,IAAI;KAAC;IACZ;QAAC,UAAU;QAAE,IAAI;KAAC;IAClB;QAAC,YAAY;QAAE,IAAI;KAAC;IACpB;QAAC,QAAQ;QAAE,IAAI;KAAC;IAChB;QAAC,QAAQ;QAAE,IAAI;KAAC;IAChB;QAAC,MAAM;QAAE,IAAI;KAAC;IACd;QAAC,QAAQ;QAAE,IAAI;KAAC;IAChB;QAAC,IAAI;QAAE,IAAI;KAAC;IACZ;QAAC,MAAM;QAAE,IAAI;KAAC;IACd;QAAC,KAAK;QAAE,IAAI;KAAC;IACb;QAAC,IAAI;QAAE,IAAI;KAAC;IACZ;QAAC,KAAK;QAAE,IAAI;KAAC;IACb;QAAC,SAAS;QAAE,IAAI;KAAC;IACjB;QAAC,OAAO;QAAE,IAAI;KAAC;IACf;QAAC,IAAI;QAAE,IAAI;KAAC;IACZ;QAAC,IAAI;QAAE,OAAO;KAAC;IACf;QAAC,IAAI;QAAE,OAAO;KAAC;IACf;QAAC,OAAO;QAAE,gBAAgB;KAAC;IAC3B;QAAC,OAAO;QAAE,gBAAgB;KAAC;CAC9B,CAAC,CAAC;AAEH,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC;IACzB,MAAM;IACN,MAAM;IACN,UAAU;IACV,IAAI;IACJ,KAAK;IACL,SAAS;IACT,OAAO;IACP,OAAO;IACP,IAAI;IACJ,KAAK;IACL,OAAO;IACP,SAAS;IACT,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;IACP,KAAK;CACR,CAAC,CAAC;AAEH,MAAM,sBAAsB,GAAG,IAAI,GAAG,CAAC;IAAC,MAAM;IAAE,KAAK;CAAC,CAAC,CAAC;AAExD,MAAM,uBAAuB,GAAG,IAAI,GAAG,CAAC;IACpC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,gBAAgB;IAChB,eAAe;IACf,MAAM;IACN,OAAO;CACV,CAAC,CAAC;AA+FH,MAAM,SAAS,GAAG,OAAO,CAAC;AAEpB,MAAO,MAAM;IA6Bf,YACI,GAA6B,EACZ,UAAyB,CAAA,CAAE,CAAA;;QAA3B,IAAA,CAAA,OAAO,GAAP,OAAO,CAAoB;QA9BhD,uCAAA,EAAyC,CAClC,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QACtB,qCAAA,EAAuC,CAChC,IAAA,CAAA,QAAQ,GAAG,CAAC,CAAC;QACpB;;;WAGG,CACK,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QAEjB,IAAA,CAAA,OAAO,GAAG,EAAE,CAAC;QACb,IAAA,CAAA,UAAU,GAAG,EAAE,CAAC;QAChB,IAAA,CAAA,WAAW,GAAG,EAAE,CAAC;QACjB,IAAA,CAAA,OAAO,GAAqC,IAAI,CAAC;QACxC,IAAA,CAAA,KAAK,GAAa,EAAE,CAAC;QACrB,IAAA,CAAA,cAAc,GAAc,EAAE,CAAC;QAM/B,IAAA,CAAA,OAAO,GAAa,EAAE,CAAC;QAChC,IAAA,CAAA,YAAY,GAAG,CAAC,CAAC;QACzB,gFAAA,EAAkF,CAC1E,IAAA,CAAA,UAAU,GAAG,CAAC,CAAC;QACvB,gFAAA,EAAkF,CAC1E,IAAA,CAAA,KAAK,GAAG,KAAK,CAAC;QAMlB,IAAI,CAAC,GAAG,GAAG,GAAG,KAAA,QAAH,GAAG,KAAA,KAAA,IAAH,GAAG,GAAI,CAAA,CAAE,CAAC;QACrB,IAAI,CAAC,iBAAiB,GAAG,CAAA,KAAA,OAAO,CAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACnE,IAAI,CAAC,uBAAuB,GACxB,CAAA,KAAA,OAAO,CAAC,uBAAuB,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QACxD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAA,KAAA,OAAO,CAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,yNAAS,CAAC,CACjD,IAAI,CAAC,OAAO,EACZ,IAAI,CACP,CAAC;QACF,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,YAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,2BAA2B;IAE3B,cAAA,EAAgB,CAChB,MAAM,CAAC,KAAa,EAAE,QAAgB,EAAA;;QAClC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC5C,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC;QAC7B,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,CAAC,CAAC;QACxB,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;IAC/B,CAAC;IAED,cAAA,EAAgB,CAChB,YAAY,CAAC,EAAU,EAAA;;QACnB;;;WAGG,CACH,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE,CAAC;QAC/C,IAAI,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,CAAC;QAC1B,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,QAAG,gOAAa,EAAC,EAAE,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAES,aAAa,CAAC,IAAY,EAAA;QAChC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED,cAAA,EAAgB,CAChB,aAAa,CAAC,KAAa,EAAE,QAAgB,EAAA;QACzC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAEO,WAAW,CAAC,IAAY,EAAA;;QAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC;QACpC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QAEpB,MAAM,YAAY,GACd,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAExD,IAAI,YAAY,EAAE;YACd,MACI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IACrB,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CACrD;gBACE,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,CAAC;gBAClC,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,OAAO,EAAE,IAAI,CAAC,CAAC;aACxC;SACJ;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACtB,IAAI,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAClC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAClC,MAAM,IAAI,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBAC1C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACnC;SACJ;QACD,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,CAAC,CAAC;QAC/B,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,GAAG,CAAA,CAAE,CAAC;IAC9C,CAAC;IAEO,UAAU,CAAC,SAAkB,EAAA;;QACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC;QAEpC,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAC5D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACvB;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACzD,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACtB,CAAC;IAED,cAAA,EAAgB,CAChB,YAAY,CAAC,QAAgB,EAAA;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAEvB,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,UAAU,CAAC,KAAa,EAAE,QAAgB,EAAA;;QACtC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;SAC7B;QAED,IACI,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,IAChC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,EACnC;YACE,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACzC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;gBACZ,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;oBACrB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;oBACpC,MAAO,KAAK,EAAE,CAAE;wBACZ,6CAA6C;wBAC7C,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC;qBACvD;iBACJ,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;aAClC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,GAAG,EAAE;gBAC9C,6BAA6B;gBAC7B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aAC9B;SACJ,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,KAAK,IAAI,EAAE;YAC/C,oFAAoF;YACpF,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,aAAa,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,CAAC,CAAC;YAC/B,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,EAAE,CAAA,CAAE,EAAE,IAAI,CAAC,CAAC;YACrC,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,EAAE,KAAK,CAAC,CAAC;SACtC;QAED,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,gBAAgB,CAAC,QAAgB,EAAA;QAC7B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IACI,IAAI,CAAC,OAAO,CAAC,OAAO,IACpB,IAAI,CAAC,OAAO,CAAC,oBAAoB,IACjC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,EACrD;YACE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAE5B,iCAAiC;YACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;SAClC,MAAM;YACH,gDAAgD;YAChD,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;SAC/B;IACL,CAAC;IAEO,eAAe,CAAC,aAAsB,EAAA;;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;QAE/B,oDAAoD;QACpD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;YAC5C,uEAAuE;YACvE,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,EAAE,CAAC,aAAa,CAAC,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;SACpB;IACL,CAAC;IAED,cAAA,EAAgB,CAChB,YAAY,CAAC,KAAa,EAAE,QAAgB,EAAA;QACxC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE5C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,uBAAuB,GACxC,IAAI,CAAC,WAAW,EAAE,GAClB,IAAI,CAAC;IACf,CAAC;IAED,cAAA,EAAgB,CAChB,YAAY,CAAC,KAAa,EAAE,QAAgB,EAAA;QACxC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED,cAAA,EAAgB,CAChB,cAAc,CAAC,EAAU,EAAA;QACrB,IAAI,CAAC,WAAW,QAAI,gOAAa,EAAC,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,cAAA,EAAgB,CAChB,WAAW,CAAC,KAAgB,EAAE,QAAgB,EAAA;;QAC1C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,WAAW,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAChB,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,WAAW,EAChB,KAAK,KAAK,2NAAS,CAAC,MAAM,GACpB,GAAG,GACH,KAAK,KAAK,2NAAS,CAAC,MAAM,GAC1B,GAAG,GACH,KAAK,KAAK,2NAAS,CAAC,OAAO,GAC3B,SAAS,GACT,IAAI,CACb,CAAC;QAEF,IACI,IAAI,CAAC,OAAO,IACZ,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,EACtE;YACE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;SACpD;QACD,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;IAC1B,CAAC;IAEO,kBAAkB,CAAC,KAAa,EAAA;QACpC,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QACtC,IAAI,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;SAC7B;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,cAAA,EAAgB,CAChB,aAAa,CAAC,KAAa,EAAE,QAAgB,EAAA;QACzC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE7C,IAAI,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC5C,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA,CAAA,EAAI,IAAI,EAAE,EAAE,CAAA,CAAA,EAAI,KAAK,EAAE,CAAC,CAAC;SAC7D;QAED,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,uBAAuB,CAAC,KAAa,EAAE,QAAgB,EAAA;QACnD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAE7C,IAAI,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE;YAClC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC5C,IAAI,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAA,CAAA,EAAI,IAAI,EAAE,EAAE,CAAA,CAAA,EAAI,KAAK,EAAE,CAAC,CAAC;SAC7D;QAED,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,SAAS,CAAC,KAAa,EAAE,QAAgB,EAAE,MAAc,EAAA;;QACrD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAEzB,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC;QAC9D,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,YAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAI,CAAC;QAE1B,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,OAAO,CAAC,KAAa,EAAE,QAAgB,EAAE,MAAc,EAAA;;QACnD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC;QAEtD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YACrD,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,YAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAI,CAAC;YAC1B,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,MAAM,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,KAAK,CAAC,CAAC;YACzB,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,UAAU,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAI,CAAC;SAC3B,MAAM;YACH,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,SAAS,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,CAAA,OAAA,EAAU,KAAK,CAAA,EAAA,CAAI,CAAC,CAAC;YAC1C,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,YAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAI,CAAC;SAC7B;QAED,iCAAiC;QACjC,IAAI,CAAC,UAAU,GAAG,QAAQ,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,cAAA,EAAgB,CAChB,KAAK,GAAA;;QACD,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE;YACrB,2CAA2C;YAC3C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;YAChC,IACI,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAC7B,KAAK,GAAG,CAAC,EACT,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;SAErD;QACD,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,KAAK,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAI,CAAC;IACvB,CAAC;IAED;;OAEG,CACI,KAAK,GAAA;;QACR,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,GAAI,CAAC;QACrB,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACpB,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,YAAY,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,CAAC,CAAC;QAC9B,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB,CAAC;IAED;;;;;OAKG,CACI,aAAa,CAAC,IAAY,EAAA;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;QACb,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;IAEO,QAAQ,CAAC,KAAa,EAAE,GAAW,EAAA;QACvC,MAAO,KAAK,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAE;YACxD,IAAI,CAAC,WAAW,EAAE,CAAC;SACtB;QAED,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAC7B,KAAK,GAAG,IAAI,CAAC,YAAY,EACzB,GAAG,GAAG,IAAI,CAAC,YAAY,CAC1B,CAAC;QAEF,MAAO,GAAG,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAE;YACrD,IAAI,CAAC,WAAW,EAAE,CAAC;YACnB,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;SAC9D;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,WAAW,GAAA;QACf,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QAC5C,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;IAED;;;;OAIG,CACI,KAAK,CAAC,KAAa,EAAA;;QACtB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;YACtD,OAAO;SACV;QAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;YACxB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;SACrB;IACL,CAAC;IAED;;;;OAIG,CACI,GAAG,CAAC,KAAc,EAAA;;QACrB,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,CAAA,KAAA,CAAA,KAAA,IAAI,CAAC,GAAG,EAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAA,IAAA,CAAA,IAAG,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC;YACpD,OAAO;SACV;QAED,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG,CACI,KAAK,GAAA;QACR,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG,CACI,MAAM,GAAA;QACT,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QAExB,MACI,IAAI,CAAC,SAAS,CAAC,OAAO,IACtB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CACvC;YACE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;SACzD;QAED,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;IACzC,CAAC;IAED;;;;;OAKG,CACI,UAAU,CAAC,KAAa,EAAA;QAC3B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACtB,CAAC;IACD;;;;;OAKG,CACI,IAAI,CAAC,KAAc,EAAA;QACtB,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IACpB,CAAC;CACJ", "debugId": null}}, {"offset": {"line": 4306, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/entities@4.5.0/node_modules/entities/lib/esm/generated/encode-html.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/entities/61afd4701eaa736978b13c7351cd3de9a96b04bc/src/", "sources": ["generated/encode-html.ts"], "sourcesContent": [], "names": [], "mappings": "AAAA,8CAA8C;;;;;AAM9C,SAAS,WAAW,CAChB,GAAM;IAEN,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE;QACjC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;KAClC;IACD,OAAO,GAAG,CAAC;AACf,CAAC;uCAGc,IAAI,GAAG,CAAwB,aAAA,EAAe,CAAA,WAAW,CAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,EAAE;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,EAAE;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,UAAU;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,OAAO;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,EAAE;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,EAAE;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,EAAE;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,EAAE;QAAC,QAAQ;KAAC;IAAC;QAAC,EAAE;QAAC,SAAS;KAAC;IAAC;QAAC,EAAE;QAAC,UAAU;KAAC;IAAC;QAAC,EAAE;QAAC,SAAS;KAAC;IAAC;QAAC,GAAG;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,EAAE;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,EAAE;QAAC,aAAa;KAAC;IAAC;QAAC,GAAG;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,EAAE;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe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aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,uBAAuB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,yBAAyB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,EAAE;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,eAAe;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,cAAc;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,EAAE;QAAC,QAAQ;KAAC;IAAC;QAAC,EAAE;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,EAAE;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,EAAE;QAAC,wBAAwB;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,EAAE;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,EAAE;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,gBAAgB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,UAAU;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,gBAAgB;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,gBAAgB;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,gBAAgB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,uBAAuB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,uBAAuB;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,wBAAwB;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,EAAE;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,QAAQ;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,wBAAwB;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,4BAA4B;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,WAAW;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,OAAO;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,QAAQ;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,WAAW;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,QAAQ;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,UAAU;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,aAAa;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,WAAW;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,OAAO;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,OAAO;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,aAAa;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,aAAa;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,IAAI,GAAG,CAAgB,aAAA,EAAe,CAAA,WAAW,CAAC;gBAAC;oBAAC,GAAG;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,IAAI;oBAAC,OAAO;iBAAC;aAAC,CAAC,CAAC;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,IAAI,GAAG,CAAgB,aAAA,EAAe,CAAA,WAAW,CAAC;gBAAC;oBAAC,GAAG;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,IAAI;oBAAC,OAAO;iBAAC;aAAC,CAAC,CAAC;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,gBAAgB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,oBAAoB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,aAAa;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,eAAe;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,gBAAgB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,gBAAgB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,mBAAmB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,qBAAqB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,UAAU;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,UAAU;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,gBAAgB;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,qBAAqB;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,WAAW;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,sBAAsB;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,WAAW;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,OAAO;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,MAAM;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,OAAO;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,yBAAyB;KAAC;IAAC;QAAC,CAAC;QAAC,yBAAyB;KAAC;IAAC;QAAC,CAAC;QAAC,wBAAwB;KAAC;IAAC;QAAC,CAAC;QAAC,0BAA0B;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,yBAAyB;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,WAAW;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,YAAY;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,UAAU;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,EAAE;QAAC,WAAW;KAAC;IAAC;QAAC,EAAE;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,EAAE;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,EAAE;QAAC,SAAS;KAAC;IAAC;QAAC,GAAG;QAAC,YAAY;KAAC;IAAC;QAAC,EAAE;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,EAAE;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,EAAE;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,EAAE;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,wBAAwB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,sBAAsB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,EAAE;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,EAAE;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,EAAE;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,GAAG;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,EAAE;QAAC,QAAQ;KAAC;IAAC;QAAC,EAAE;QAAC,qBAAqB;KAAC;IAAC;QAAC,EAAE;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,EAAE;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,EAAE;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,sBAAsB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,sBAAsB;KAAC;IAAC;QAAC,CAAC;QAAC,uBAAuB;KAAC;IAAC;QAAC,CAAC;QAAC,wBAAwB;KAAC;IAAC;QAAC,CAAC;QAAC,4BAA4B;KAAC;IAAC;QAAC,CAAC;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,GAAG;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,cAAc;KAAC;IAAC;QAAC,CAAC;QAAC,gBAAgB;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,UAAU;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,uBAAuB;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,sBAAsB;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,sBAAsB;KAAC;IAAC;QAAC,CAAC;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,iBAAiB;KAAC;IAAC;QAAC,CAAC;QAAC,kBAAkB;KAAC;IAAC;QAAC,CAAC;QAAC,oBAAoB;KAAC;IAAC;QAAC,CAAC;QAAC,sBAAsB;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,sBAAsB;KAAC;IAAC;QAAC,CAAC;QAAC,mBAAmB;KAAC;IAAC;QAAC,CAAC;QAAC,qBAAqB;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,gBAAgB;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,mBAAmB;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,sBAAsB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,oBAAoB;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,uBAAuB;QAAA,CAAC;KAAC;IAAC;QAAC,EAAE;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,gBAAgB;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,aAAa;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,EAAE;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,WAAW;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,YAAY;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,QAAQ;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,YAAY;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,aAAa;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,YAAY;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,aAAa;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,YAAY;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,qBAAqB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,kBAAkB;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,2BAA2B;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,QAAQ;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,QAAQ;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,iBAAiB;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,oBAAoB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,OAAO;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,oBAAoB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,YAAY;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,eAAe;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,MAAM;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,QAAQ;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,QAAQ;YAAC,CAAC,EAAC,GAAG;YAAC,CAAC,EAAC,SAAS;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,iBAAiB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,KAAK;YAAC,CAAC,EAAC,iBAAiB;QAAA,CAAC;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,WAAW;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,OAAO;KAAC;IAAC;QAAC,CAAC;QAAC,QAAQ;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC;YAAC,CAAC,EAAC,SAAS;YAAC,CAAC,EAAC,IAAI;YAAC,CAAC,EAAC,UAAU;QAAA,CAAC;KAAC;IAAC;QAAC,KAAK;QAAC;YAAC,CAAC,EAAC,IAAI,GAAG,CAAgB,aAAA,EAAe,CAAA,WAAW,CAAC;gBAAC;oBAAC,KAAK;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,EAAE;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,OAAO;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;gBAAC;oBAAC,CAAC;oBAAC,QAAQ;iBAAC;aAAC,CAAC,CAAC;QAAA,CAAC;KAAC;IAAC;QAAC,IAAI;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,SAAS;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;IAAC;QAAC,CAAC;QAAC,UAAU;KAAC;CAAC,CAAC,CAAC,CAAC", "debugId": null}}, {"offset": {"line": 10386, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/entities@4.5.0/node_modules/entities/lib/esm/escape.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/entities/61afd4701eaa736978b13c7351cd3de9a96b04bc/src/", "sources": ["escape.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAO,MAAM,WAAW,GAAG,sBAAsB,CAAC;AAElD,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC;IACvB;QAAC,EAAE;QAAE,QAAQ;KAAC;IACd;QAAC,EAAE;QAAE,OAAO;KAAC;IACb;QAAC,EAAE;QAAE,QAAQ;KAAC;IACd;QAAC,EAAE;QAAE,MAAM;KAAC;IACZ;QAAC,EAAE;QAAE,MAAM;KAAC;CACf,CAAC,CAAC;AAGI,MAAM,YAAY,GACrB,uEAAuE;AACvE,MAAM,CAAC,SAAS,CAAC,WAAW,IAAI,IAAI,GAC9B,CAAC,GAAW,EAAE,KAAa,EAAU,CAAG,CAAD,EAAI,CAAC,WAAW,CAAC,KAAK,CAAE,GAE/D,CAAC,CAAS,EAAE,KAAa,EAAU,CAC/B,CADiC,AAChC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,MAAM,GACnC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,GACtC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,GACvB,MAAM,GACN,OAAO,GACP,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AASlC,SAAU,SAAS,CAAC,GAAW;IACjC,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,KAAK,CAAC;IAEV,MAAO,CAAC,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAE;QAC7C,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;QACtB,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/B,MAAM,IAAI,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,IAAI,KAAK,SAAS,EAAE;YACpB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;YACxC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;SACnB,MAAM;YACH,GAAG,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA,GAAA,EAAM,YAAY,CACjD,GAAG,EACH,CAAC,CACJ,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA,CAAA,CAAG,CAAC;YAClB,4CAA4C;YAC5C,OAAO,GAAG,WAAW,CAAC,SAAS,IAAI,MAAM,CACrC,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,MAAM,CAC7B,CAAC;SACL;KACJ;IAED,OAAO,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,CAAC;AAWM,MAAM,MAAM,GAAG,SAAS,CAAC;AAEhC;;;;;;;;;GASG,CACH,SAAS,UAAU,CACf,KAAa,EACb,GAAwB;IAExB,OAAO,SAAS,MAAM,CAAC,IAAY;QAC/B,IAAI,KAAK,CAAC;QACV,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,IAAI,MAAM,GAAG,EAAE,CAAC;QAEhB,MAAQ,CAAD,IAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,AAAE;YAC/B,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,EAAE;gBACzB,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;aAClD;YAED,kDAAkD;YAClD,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAE,CAAC;YAE3C,kCAAkC;YAClC,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;SAC7B;QAED,OAAO,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAC5C,CAAC,CAAC;AACN,CAAC;AASM,MAAM,UAAU,GAAG,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAQtD,MAAM,eAAe,GAAG,UAAU,CACrC,aAAa,EACb,IAAI,GAAG,CAAC;IACJ;QAAC,EAAE;QAAE,QAAQ;KAAC;IACd;QAAC,EAAE;QAAE,OAAO;KAAC;IACb;QAAC,GAAG;QAAE,QAAQ;KAAC;CAClB,CAAC,CACL,CAAC;AAQK,MAAM,UAAU,GAAG,UAAU,CAChC,cAAc,EACd,IAAI,GAAG,CAAC;IACJ;QAAC,EAAE;QAAE,OAAO;KAAC;IACb;QAAC,EAAE;QAAE,MAAM;KAAC;IACZ;QAAC,EAAE;QAAE,MAAM;KAAC;IACZ;QAAC,GAAG;QAAE,QAAQ;KAAC;CAClB,CAAC,CACL,CAAC", "debugId": null}}, {"offset": {"line": 10510, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/entities@4.5.0/node_modules/entities/lib/esm/encode.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/entities/61afd4701eaa736978b13c7351cd3de9a96b04bc/src/", "sources": ["encode.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;AAAA,OAAO,QAAQ,MAAM,4BAA4B,CAAC;AAClD,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;;;AAExD,MAAM,YAAY,GAAG,qCAAqC,CAAC;AAarD,SAAU,UAAU,CAAC,IAAY;IACnC,OAAO,gBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAChD,CAAC;AASK,SAAU,kBAAkB,CAAC,IAAY;IAC3C,OAAO,gBAAgB,CAAC,oNAAW,EAAE,IAAI,CAAC,CAAC;AAC/C,CAAC;AAED,SAAS,gBAAgB,CAAC,MAAc,EAAE,GAAW;IACjD,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAI,KAAK,CAAC;IAEV,MAAO,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAE;QACxC,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;QACtB,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACjC,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,IAAI,GAAG,qOAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAE9B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC1B,kDAAkD;YAClD,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE;gBACpB,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACvC,MAAM,KAAK,GACP,OAAO,IAAI,CAAC,CAAC,KAAK,QAAQ,GACpB,IAAI,CAAC,CAAC,KAAK,QAAQ,GACf,IAAI,CAAC,CAAC,GACN,SAAS,GACb,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAE/B,IAAI,KAAK,KAAK,SAAS,EAAE;oBACrB,GAAG,IAAI,KAAK,CAAC;oBACb,OAAO,GAAG,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC;oBAChC,SAAS;iBACZ;aACJ;YAED,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC;SACjB;QAED,4EAA4E;QAC5E,IAAI,IAAI,KAAK,SAAS,EAAE;YACpB,GAAG,IAAI,IAAI,CAAC;YACZ,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;SACnB,MAAM;YACH,MAAM,EAAE,OAAG,qNAAY,EAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAChC,GAAG,IAAI,CAAA,GAAA,EAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAA,CAAA,CAAG,CAAC;YAChC,4CAA4C;YAC5C,OAAO,GAAG,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC;SACrD;KACJ;IAED,OAAO,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACrC,CAAC", "debugId": null}}, {"offset": {"line": 10566, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/entities@4.5.0/node_modules/entities/lib/esm/index.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/entities/61afd4701eaa736978b13c7351cd3de9a96b04bc/src/", "sources": ["index.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;AAAA,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,aAAa,CAAC;AAClE,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AAC7D,OAAO,EACH,SAAS,EACT,UAAU,EACV,eAAe,EACf,UAAU,GACb,MAAM,aAAa,CAAC;;;;AAGrB,IAAY,WAKX;AALD,CAAA,SAAY,WAAW;IACnB,+BAAA,EAAiC,CACjC,WAAA,CAAA,WAAA,CAAA,MAAA,GAAA,EAAA,GAAA,KAAO,CAAA;IACP,iEAAA,EAAmE,CACnE,WAAA,CAAA,WAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAQ,CAAA;AACZ,CAAC,EALW,WAAW,IAAA,CAAX,WAAW,GAAA,CAAA,CAAA,GAKtB;AAED,IAAY,YA2BX;AA3BD,CAAA,SAAY,YAAY;IACpB;;;OAGG,CACH,YAAA,CAAA,YAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAI,CAAA;IACJ;;;;OAIG,CACH,YAAA,CAAA,YAAA,CAAA,QAAA,GAAA,EAAA,GAAA,OAAK,CAAA;IACL;;;OAGG,CACH,YAAA,CAAA,YAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAS,CAAA;IACT;;;OAGG,CACH,YAAA,CAAA,YAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAS,CAAA;IACT;;;OAGG,CACH,YAAA,CAAA,YAAA,CAAA,OAAA,GAAA,EAAA,GAAA,MAAI,CAAA;AACR,CAAC,EA3BW,YAAY,IAAA,CAAZ,YAAY,GAAA,CAAA,CAAA,GA2BvB;AA4BK,SAAU,MAAM,CAClB,IAAY,EACZ,UAAyC,WAAW,CAAC,GAAG;IAExD,MAAM,KAAK,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;IAEpE,IAAI,KAAK,KAAK,WAAW,CAAC,IAAI,EAAE;QAC5B,MAAM,IAAI,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;QACpE,WAAO,mOAAU,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACjC;IAED,WAAO,kOAAS,EAAC,IAAI,CAAC,CAAC;AAC3B,CAAC;AASK,SAAU,YAAY,CACxB,IAAY,EACZ,UAAyC,WAAW,CAAC,GAAG;;IAExD,MAAM,IAAI,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC;QAAE,KAAK,EAAE,OAAO;IAAA,CAAE,CAAC,CAAC,CAAC,OAAO,CAAC;IACxE,CAAA,KAAA,IAAI,CAAC,IAAI,MAAA,QAAA,OAAA,KAAA,IAAA,KAAT,IAAI,CAAC,IAAI,GAAK,qOAAY,CAAC,MAAM,EAAC;IAElC,OAAO,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC9B,CAAC;AAwBK,SAAU,MAAM,CAClB,IAAY,EACZ,UAAyC,WAAW,CAAC,GAAG;IAExD,MAAM,IAAI,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC;QAAE,KAAK,EAAE,OAAO;IAAA,CAAE,CAAC,CAAC,CAAC,OAAO,CAAC;IAExE,wCAAwC;IACxC,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,EAAE,WAAO,mNAAU,EAAC,IAAI,CAAC,CAAC;IAC7D,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,SAAS,EAAE,WAAO,wNAAe,EAAC,IAAI,CAAC,CAAC;IACvE,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,EAAE,WAAO,mNAAU,EAAC,IAAI,CAAC,CAAC;IAE7D,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,CAAC,IAAI,EAAE;QACjC,IAAI,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,EAAE;YAClC,WAAO,2NAAkB,EAAC,IAAI,CAAC,CAAC;SACnC;QAED,WAAO,mNAAU,EAAC,IAAI,CAAC,CAAC;KAC3B;IAED,qCAAqC;IACrC,WAAO,kNAAS,EAAC,IAAI,CAAC,CAAC;AAC3B,CAAC", "debugId": null}}, {"offset": {"line": 10654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/dom-serializer%402.0.0/node_modules/dom-serializer/lib/esm/foreignNames.js"], "sourcesContent": ["export const elementNames = new Map([\n    \"altGlyph\",\n    \"altGlyphDef\",\n    \"altGlyphItem\",\n    \"animateColor\",\n    \"animateMotion\",\n    \"animateTransform\",\n    \"clipPath\",\n    \"feBlend\",\n    \"feColorMatrix\",\n    \"feComponentTransfer\",\n    \"feComposite\",\n    \"feConvolveMatrix\",\n    \"feDiffuseLighting\",\n    \"feDisplacementMap\",\n    \"feDistantLight\",\n    \"feDropShadow\",\n    \"feFlood\",\n    \"feFuncA\",\n    \"feFuncB\",\n    \"feFuncG\",\n    \"feFuncR\",\n    \"feGaussianBlur\",\n    \"feImage\",\n    \"feMerge\",\n    \"feMergeNode\",\n    \"feMorphology\",\n    \"feOffset\",\n    \"fePointLight\",\n    \"feSpecularLighting\",\n    \"feSpotLight\",\n    \"feTile\",\n    \"feTurbulence\",\n    \"foreignObject\",\n    \"glyphRef\",\n    \"linearGradient\",\n    \"radialGradient\",\n    \"textPath\",\n].map((val) => [val.toLowerCase(), val]));\nexport const attributeNames = new Map([\n    \"definitionURL\",\n    \"attributeName\",\n    \"attributeType\",\n    \"baseFrequency\",\n    \"baseProfile\",\n    \"calcMode\",\n    \"clipPathUnits\",\n    \"diffuseConstant\",\n    \"edgeMode\",\n    \"filterUnits\",\n    \"glyphRef\",\n    \"gradientTransform\",\n    \"gradientUnits\",\n    \"kernelMatrix\",\n    \"kernelUnitLength\",\n    \"keyPoints\",\n    \"keySplines\",\n    \"keyTimes\",\n    \"lengthAdjust\",\n    \"limitingConeAngle\",\n    \"markerHeight\",\n    \"markerUnits\",\n    \"markerWidth\",\n    \"maskContentUnits\",\n    \"maskUnits\",\n    \"numOctaves\",\n    \"pathLength\",\n    \"patternContentUnits\",\n    \"patternTransform\",\n    \"patternUnits\",\n    \"pointsAtX\",\n    \"pointsAtY\",\n    \"pointsAtZ\",\n    \"preserveAlpha\",\n    \"preserveAspectRatio\",\n    \"primitiveUnits\",\n    \"refX\",\n    \"refY\",\n    \"repeatCount\",\n    \"repeatDur\",\n    \"requiredExtensions\",\n    \"requiredFeatures\",\n    \"specularConstant\",\n    \"specularExponent\",\n    \"spreadMethod\",\n    \"startOffset\",\n    \"stdDeviation\",\n    \"stitchTiles\",\n    \"surfaceScale\",\n    \"systemLanguage\",\n    \"tableValues\",\n    \"targetX\",\n    \"targetY\",\n    \"textLength\",\n    \"viewBox\",\n    \"viewTarget\",\n    \"xChannelSelector\",\n    \"yChannelSelector\",\n    \"zoomAndPan\",\n].map((val) => [val.toLowerCase(), val]));\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,eAAe,IAAI,IAAI;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH,CAAC,GAAG,CAAC,CAAC,MAAQ;QAAC,IAAI,WAAW;QAAI;KAAI;AAChC,MAAM,iBAAiB,IAAI,IAAI;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH,CAAC,GAAG,CAAC,CAAC,MAAQ;QAAC,IAAI,WAAW;QAAI;KAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/dom-serializer%402.0.0/node_modules/dom-serializer/lib/esm/index.js"], "sourcesContent": ["/*\n * Module dependencies\n */\nimport * as ElementType from \"domelementtype\";\nimport { encodeXML, escapeAttribute, escapeText } from \"entities\";\n/**\n * Mixed-case SVG and MathML tags & attributes\n * recognized by the HTML parser.\n *\n * @see https://html.spec.whatwg.org/multipage/parsing.html#parsing-main-inforeign\n */\nimport { elementNames, attributeNames } from \"./foreignNames.js\";\nconst unencodedElements = new Set([\n    \"style\",\n    \"script\",\n    \"xmp\",\n    \"iframe\",\n    \"noembed\",\n    \"noframes\",\n    \"plaintext\",\n    \"noscript\",\n]);\nfunction replaceQuotes(value) {\n    return value.replace(/\"/g, \"&quot;\");\n}\n/**\n * Format attributes\n */\nfunction formatAttributes(attributes, opts) {\n    var _a;\n    if (!attributes)\n        return;\n    const encode = ((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) === false\n        ? replaceQuotes\n        : opts.xmlMode || opts.encodeEntities !== \"utf8\"\n            ? encodeXML\n            : escapeAttribute;\n    return Object.keys(attributes)\n        .map((key) => {\n        var _a, _b;\n        const value = (_a = attributes[key]) !== null && _a !== void 0 ? _a : \"\";\n        if (opts.xmlMode === \"foreign\") {\n            /* Fix up mixed-case attribute names */\n            key = (_b = attributeNames.get(key)) !== null && _b !== void 0 ? _b : key;\n        }\n        if (!opts.emptyAttrs && !opts.xmlMode && value === \"\") {\n            return key;\n        }\n        return `${key}=\"${encode(value)}\"`;\n    })\n        .join(\" \");\n}\n/**\n * Self-enclosing tags\n */\nconst singleTag = new Set([\n    \"area\",\n    \"base\",\n    \"basefont\",\n    \"br\",\n    \"col\",\n    \"command\",\n    \"embed\",\n    \"frame\",\n    \"hr\",\n    \"img\",\n    \"input\",\n    \"isindex\",\n    \"keygen\",\n    \"link\",\n    \"meta\",\n    \"param\",\n    \"source\",\n    \"track\",\n    \"wbr\",\n]);\n/**\n * Renders a DOM node or an array of DOM nodes to a string.\n *\n * Can be thought of as the equivalent of the `outerHTML` of the passed node(s).\n *\n * @param node Node to be rendered.\n * @param options Changes serialization behavior\n */\nexport function render(node, options = {}) {\n    const nodes = \"length\" in node ? node : [node];\n    let output = \"\";\n    for (let i = 0; i < nodes.length; i++) {\n        output += renderNode(nodes[i], options);\n    }\n    return output;\n}\nexport default render;\nfunction renderNode(node, options) {\n    switch (node.type) {\n        case ElementType.Root:\n            return render(node.children, options);\n        // @ts-expect-error We don't use `Doctype` yet\n        case ElementType.Doctype:\n        case ElementType.Directive:\n            return renderDirective(node);\n        case ElementType.Comment:\n            return renderComment(node);\n        case ElementType.CDATA:\n            return renderCdata(node);\n        case ElementType.Script:\n        case ElementType.Style:\n        case ElementType.Tag:\n            return renderTag(node, options);\n        case ElementType.Text:\n            return renderText(node, options);\n    }\n}\nconst foreignModeIntegrationPoints = new Set([\n    \"mi\",\n    \"mo\",\n    \"mn\",\n    \"ms\",\n    \"mtext\",\n    \"annotation-xml\",\n    \"foreignObject\",\n    \"desc\",\n    \"title\",\n]);\nconst foreignElements = new Set([\"svg\", \"math\"]);\nfunction renderTag(elem, opts) {\n    var _a;\n    // Handle SVG / MathML in HTML\n    if (opts.xmlMode === \"foreign\") {\n        /* Fix up mixed-case element names */\n        elem.name = (_a = elementNames.get(elem.name)) !== null && _a !== void 0 ? _a : elem.name;\n        /* Exit foreign mode at integration points */\n        if (elem.parent &&\n            foreignModeIntegrationPoints.has(elem.parent.name)) {\n            opts = { ...opts, xmlMode: false };\n        }\n    }\n    if (!opts.xmlMode && foreignElements.has(elem.name)) {\n        opts = { ...opts, xmlMode: \"foreign\" };\n    }\n    let tag = `<${elem.name}`;\n    const attribs = formatAttributes(elem.attribs, opts);\n    if (attribs) {\n        tag += ` ${attribs}`;\n    }\n    if (elem.children.length === 0 &&\n        (opts.xmlMode\n            ? // In XML mode or foreign mode, and user hasn't explicitly turned off self-closing tags\n                opts.selfClosingTags !== false\n            : // User explicitly asked for self-closing tags, even in HTML mode\n                opts.selfClosingTags && singleTag.has(elem.name))) {\n        if (!opts.xmlMode)\n            tag += \" \";\n        tag += \"/>\";\n    }\n    else {\n        tag += \">\";\n        if (elem.children.length > 0) {\n            tag += render(elem.children, opts);\n        }\n        if (opts.xmlMode || !singleTag.has(elem.name)) {\n            tag += `</${elem.name}>`;\n        }\n    }\n    return tag;\n}\nfunction renderDirective(elem) {\n    return `<${elem.data}>`;\n}\nfunction renderText(elem, opts) {\n    var _a;\n    let data = elem.data || \"\";\n    // If entities weren't decoded, no need to encode them back\n    if (((_a = opts.encodeEntities) !== null && _a !== void 0 ? _a : opts.decodeEntities) !== false &&\n        !(!opts.xmlMode &&\n            elem.parent &&\n            unencodedElements.has(elem.parent.name))) {\n        data =\n            opts.xmlMode || opts.encodeEntities !== \"utf8\"\n                ? encodeXML(data)\n                : escapeText(data);\n    }\n    return data;\n}\nfunction renderCdata(elem) {\n    return `<![CDATA[${elem.children[0].data}]]>`;\n}\nfunction renderComment(elem) {\n    return `<!--${elem.data}-->`;\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AACD;AACA;AAAA;AACA;;;;;CAKC,GACD;;;;AACA,MAAM,oBAAoB,IAAI,IAAI;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,SAAS,cAAc,KAAK;IACxB,OAAO,MAAM,OAAO,CAAC,MAAM;AAC/B;AACA;;CAEC,GACD,SAAS,iBAAiB,UAAU,EAAE,IAAI;IACtC,IAAI;IACJ,IAAI,CAAC,YACD;IACJ,MAAM,SAAS,CAAC,CAAC,KAAK,KAAK,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,cAAc,MAAM,QAC/F,gBACA,KAAK,OAAO,IAAI,KAAK,cAAc,KAAK,SACpC,kNAAS,GACT,wNAAe;IACzB,OAAO,OAAO,IAAI,CAAC,YACd,GAAG,CAAC,CAAC;QACN,IAAI,IAAI;QACR,MAAM,QAAQ,CAAC,KAAK,UAAU,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACtE,IAAI,KAAK,OAAO,KAAK,WAAW;YAC5B,qCAAqC,GACrC,MAAM,CAAC,KAAK,+OAAc,CAAC,GAAG,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QAC1E;QACA,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC,KAAK,OAAO,IAAI,UAAU,IAAI;YACnD,OAAO;QACX;QACA,OAAO,GAAG,IAAI,EAAE,EAAE,OAAO,OAAO,CAAC,CAAC;IACtC,GACK,IAAI,CAAC;AACd;AACA;;CAEC,GACD,MAAM,YAAY,IAAI,IAAI;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AASM,SAAS,OAAO,IAAI,EAAE,UAAU,CAAC,CAAC;IACrC,MAAM,QAAQ,YAAY,OAAO,OAAO;QAAC;KAAK;IAC9C,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,UAAU,WAAW,KAAK,CAAC,EAAE,EAAE;IACnC;IACA,OAAO;AACX;uCACe;AACf,SAAS,WAAW,IAAI,EAAE,OAAO;IAC7B,OAAQ,KAAK,IAAI;QACb,KAAK,wNAAgB;YACjB,OAAO,OAAO,KAAK,QAAQ,EAAE;QACjC,8CAA8C;QAC9C,KAAK,2NAAmB;QACxB,KAAK,6NAAqB;YACtB,OAAO,gBAAgB;QAC3B,KAAK,2NAAmB;YACpB,OAAO,cAAc;QACzB,KAAK,yNAAiB;YAClB,OAAO,YAAY;QACvB,KAAK,0NAAkB;QACvB,KAAK,yNAAiB;QACtB,KAAK,uNAAe;YAChB,OAAO,UAAU,MAAM;QAC3B,KAAK,wNAAgB;YACjB,OAAO,WAAW,MAAM;IAChC;AACJ;AACA,MAAM,+BAA+B,IAAI,IAAI;IACzC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,MAAM,kBAAkB,IAAI,IAAI;IAAC;IAAO;CAAO;AAC/C,SAAS,UAAU,IAAI,EAAE,IAAI;IACzB,IAAI;IACJ,8BAA8B;IAC9B,IAAI,KAAK,OAAO,KAAK,WAAW;QAC5B,mCAAmC,GACnC,KAAK,IAAI,GAAG,CAAC,KAAK,6OAAY,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,IAAI;QACzF,2CAA2C,GAC3C,IAAI,KAAK,MAAM,IACX,6BAA6B,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,GAAG;YACpD,OAAO;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAM;QACrC;IACJ;IACA,IAAI,CAAC,KAAK,OAAO,IAAI,gBAAgB,GAAG,CAAC,KAAK,IAAI,GAAG;QACjD,OAAO;YAAE,GAAG,IAAI;YAAE,SAAS;QAAU;IACzC;IACA,IAAI,MAAM,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE;IACzB,MAAM,UAAU,iBAAiB,KAAK,OAAO,EAAE;IAC/C,IAAI,SAAS;QACT,OAAO,CAAC,CAAC,EAAE,SAAS;IACxB;IACA,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,KACzB,CAAC,KAAK,OAAO,GAEL,KAAK,eAAe,KAAK,QAEzB,KAAK,eAAe,IAAI,UAAU,GAAG,CAAC,KAAK,IAAI,CAAC,GAAG;QAC3D,IAAI,CAAC,KAAK,OAAO,EACb,OAAO;QACX,OAAO;IACX,OACK;QACD,OAAO;QACP,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC1B,OAAO,OAAO,KAAK,QAAQ,EAAE;QACjC;QACA,IAAI,KAAK,OAAO,IAAI,CAAC,UAAU,GAAG,CAAC,KAAK,IAAI,GAAG;YAC3C,OAAO,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;QAC5B;IACJ;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,IAAI;IACzB,OAAO,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AAC3B;AACA,SAAS,WAAW,IAAI,EAAE,IAAI;IAC1B,IAAI;IACJ,IAAI,OAAO,KAAK,IAAI,IAAI;IACxB,2DAA2D;IAC3D,IAAI,CAAC,CAAC,KAAK,KAAK,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,cAAc,MAAM,SACtF,CAAC,CAAC,CAAC,KAAK,OAAO,IACX,KAAK,MAAM,IACX,kBAAkB,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG;QAC9C,OACI,KAAK,OAAO,IAAI,KAAK,cAAc,KAAK,SAClC,IAAA,kNAAS,EAAC,QACV,IAAA,mNAAU,EAAC;IACzB;IACA,OAAO;AACX;AACA,SAAS,YAAY,IAAI;IACrB,OAAO,CAAC,SAAS,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;AACjD;AACA,SAAS,cAAc,IAAI;IACvB,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10949, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/domutils@3.2.2/node_modules/domutils/lib/esm/stringify.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["stringify.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,OAAO,EACH,KAAK,EACL,OAAO,EACP,MAAM,EACN,WAAW,EAEX,SAAS,GACZ,MAAM,YAAY,CAAC;AACpB,OAAO,UAAoC,MAAM,gBAAgB,CAAC;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;;;;AASvC,SAAU,YAAY,CACxB,IAAkC,EAClC,OAA8B;IAE9B,WAAO,iOAAU,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACrC,CAAC;AASK,SAAU,YAAY,CACxB,IAAa,EACb,OAA8B;IAE9B,WAAO,sNAAW,EAAC,IAAI,CAAC,GAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAG,CAAD,WAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GACjE,EAAE,CAAC;AACb,CAAC;AAUK,SAAU,OAAO,CAAC,IAAyB;IAC7C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3D,QAAI,gNAAK,EAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC3E,QAAI,kNAAO,EAAC,IAAI,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACjD,QAAI,iNAAM,EAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnC,OAAO,EAAE,CAAC;AACd,CAAC;AAUK,SAAU,WAAW,CAAC,IAAyB;IACjD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC/D,QAAI,sNAAW,EAAC,IAAI,CAAC,IAAI,KAAC,oNAAS,EAAC,IAAI,CAAC,EAAE,CAAC;QACxC,OAAO,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IACD,QAAI,iNAAM,EAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnC,OAAO,EAAE,CAAC;AACd,CAAC;AAUK,SAAU,SAAS,CAAC,IAAyB;IAC/C,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7D,QAAI,sNAAW,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,+NAAW,CAAC,GAAG,QAAI,kNAAO,EAAC,IAAI,CAAC,CAAC,EAAE,CAAC;QACxE,OAAO,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IACD,QAAI,iNAAM,EAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnC,OAAO,EAAE,CAAC;AACd,CAAC", "debugId": null}}, {"offset": {"line": 11001, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/domutils@3.2.2/node_modules/domutils/lib/esm/traversal.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["traversal.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,OAAO,EACH,KAAK,EAKL,WAAW,GACd,MAAM,YAAY,CAAC;;AASd,SAAU,WAAW,CAAC,IAAa;IACrC,WAAO,sNAAW,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;AAClD,CAAC;AAUK,SAAU,SAAS,CAAC,IAAa;IACnC,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;AAC/B,CAAC;AAaK,SAAU,WAAW,CAAC,IAAa;IACrC,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;IAC/B,IAAI,MAAM,IAAI,IAAI,EAAE,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC;IAE/C,MAAM,QAAQ,GAAG;QAAC,IAAI;KAAC,CAAC;IACxB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IAC1B,MAAO,IAAI,IAAI,IAAI,CAAE,CAAC;QAClB,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IACtB,CAAC;IACD,MAAO,IAAI,IAAI,IAAI,CAAE,CAAC;QAClB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IACtB,CAAC;IACD,OAAO,QAAQ,CAAC;AACpB,CAAC;AAUK,SAAU,iBAAiB,CAC7B,IAAa,EACb,IAAY;;IAEZ,OAAO,CAAA,KAAA,IAAI,CAAC,OAAO,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,EAAA,CAAG,IAAI,CAAC,CAAC;AAChC,CAAC;AAUK,SAAU,SAAS,CAAC,IAAa,EAAE,IAAY;IACjD,OAAO,AACH,IAAI,CAAC,OAAO,IAAI,IAAI,IACpB,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IACxD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAC7B,CAAC;AACN,CAAC;AASK,SAAU,OAAO,CAAC,IAAa;IACjC,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,CAAC;AAUK,SAAU,kBAAkB,CAAC,IAAa;IAC5C,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IACpB,MAAO,IAAI,KAAK,IAAI,IAAI,KAAC,gNAAK,EAAC,IAAI,CAAC,CAAE,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IACxD,OAAO,IAAI,CAAC;AAChB,CAAC;AAUK,SAAU,kBAAkB,CAAC,IAAa;IAC5C,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;IACpB,MAAO,IAAI,KAAK,IAAI,IAAI,KAAC,gNAAK,EAAC,IAAI,CAAC,CAAE,CAAC,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IACxD,OAAO,IAAI,CAAC;AAChB,CAAC", "debugId": null}}, {"offset": {"line": 11069, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/domutils@3.2.2/node_modules/domutils/lib/esm/manipulation.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["manipulation.ts"], "sourcesContent": [], "names": [], "mappings": "AAEA;;;;;GAKG;;;;;;;;;;;;;;AACG,SAAU,aAAa,CAAC,IAAe;IACzC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IAC1C,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IAE1C,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;QACpC,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC;YACnB,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,CAAC;AASK,SAAU,cAAc,CAAC,IAAe,EAAE,WAAsB;IAClE,MAAM,IAAI,GAAG,AAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAI,IAAI,EAAE,CAAC;QACP,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;IAC5B,CAAC;IAED,MAAM,IAAI,GAAG,AAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5C,IAAI,IAAI,EAAE,CAAC;QACP,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC;IAC5B,CAAC;IAED,MAAM,MAAM,GAAG,AAAC,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAClD,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC;QAC/B,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,WAAW,CAAC;QAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACvB,CAAC;AACL,CAAC;AASK,SAAU,WAAW,CAAC,MAAkB,EAAE,KAAgB;IAC5D,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IAClB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IAEtB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC;QAClC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5D,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;QACrB,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;IACzB,CAAC,MAAM,CAAC;QACJ,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IACtB,CAAC;AACL,CAAC;AASK,SAAU,MAAM,CAAC,IAAe,EAAE,IAAe;IACnD,aAAa,CAAC,IAAI,CAAC,CAAC;IAEpB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;IAE3B,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;IACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IAErB,IAAI,QAAQ,EAAE,CAAC;QACX,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;QACrB,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC;YAC/B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QACzD,CAAC;IACL,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC;QAChB,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;AACL,CAAC;AASK,SAAU,YAAY,CAAC,MAAkB,EAAE,KAAgB;IAC7D,aAAa,CAAC,KAAK,CAAC,CAAC;IAErB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IACtB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IAElB,IAAI,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;QACvC,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACnC,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC;QACrB,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC;IACzB,CAAC,MAAM,CAAC;QACJ,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;IACtB,CAAC;AACL,CAAC;AASK,SAAU,OAAO,CAAC,IAAe,EAAE,IAAe;IACpD,aAAa,CAAC,IAAI,CAAC,CAAC;IAEpB,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IACxB,IAAI,MAAM,EAAE,CAAC;QACT,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC;QAC/B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACtB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,CAAC", "debugId": null}}, {"offset": {"line": 11179, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/domutils@3.2.2/node_modules/domutils/lib/esm/querying.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["querying.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,OAAO,EAAE,KAAK,EAAE,WAAW,EAAgC,MAAM,YAAY,CAAC;;AAYxE,SAAU,MAAM,CAClB,IAAgC,EAChC,IAAyB,EACzB,OAAO,GAAG,IAAI,EACd,QAAgB,QAAQ;IAExB,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAAC,IAAI;KAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3E,CAAC;AAYK,SAAU,IAAI,CAChB,IAAgC,EAChC,KAA6B,EAC7B,OAAgB,EAChB,KAAa;IAEb,MAAM,MAAM,GAAc,EAAE,CAAC;IAC7B,2CAAA,EAA6C,CAC7C,MAAM,SAAS,GAAgB;QAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YAAC,KAAK;SAAC;KAAC,CAAC;IACxE,4CAAA,EAA8C,CAC9C,MAAM,UAAU,GAAG;QAAC,CAAC;KAAC,CAAC;IAEvB,OAAS,CAAC;QACN,sEAAsE;QACtE,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YACvC,qDAAqD;YACrD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC1B,OAAO,MAAM,CAAC;YAClB,CAAC;YAED,sDAAsD;YACtD,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YAGnB,SAAS;QACb,CAAC;QAED,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACb,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,OAAO,MAAM,CAAC;QACpC,CAAC;QAED,IAAI,OAAO,QAAI,sNAAW,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3D;;;eAGG,CACH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;AACL,CAAC;AAWK,SAAU,YAAY,CACxB,IAA0B,EAC1B,KAAU;IAEV,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAWK,SAAU,OAAO,CACnB,IAAgC,EAChC,KAA6B,EAC7B,OAAO,GAAG,IAAI;IAEd,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAAC,KAAK;KAAC,CAAC;IAC7D,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,CAAE,CAAC;QAC5C,MAAM,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;QAC9B,QAAI,gNAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QAChB,CAAC;QACD,IAAI,OAAO,QAAI,sNAAW,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3D,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACjD,IAAI,KAAK,EAAE,OAAO,KAAK,CAAC;QAC5B,CAAC;IACL,CAAC;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAUK,SAAU,SAAS,CACrB,IAAgC,EAChC,KAA6B;IAE7B,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QAAC,KAAK;KAAC,CAAC,CAAC,IAAI,CAChD,CAAC,IAAI,EAAE,CACH,CADK,GACJ,gNAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,OAC1B,sNAAW,EAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAC5D,CAAC;AACN,CAAC;AAYK,SAAU,OAAO,CACnB,IAAgC,EAChC,KAA6B;IAE7B,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,SAAS,GAAG;QAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YAAC,KAAK;SAAC;KAAC,CAAC;IAC3D,MAAM,UAAU,GAAG;QAAC,CAAC;KAAC,CAAC;IAEvB,OAAS,CAAC;QACN,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;YACvC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,MAAM,CAAC;YAClB,CAAC;YAED,sDAAsD;YACtD,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YAGnB,SAAS;QACb,CAAC;QAED,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,QAAI,gNAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEjD,QAAI,sNAAW,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChD,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrC,CAAC;IACL,CAAC;AACL,CAAC", "debugId": null}}, {"offset": {"line": 11293, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/domutils@3.2.2/node_modules/domutils/lib/esm/legacy.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["legacy.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,EAAoB,MAAM,YAAY,CAAC;AAE7D,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;;;AAqBhD;;GAEG,CACH,MAAM,MAAM,GAGR;IACA,QAAQ,EAAC,IAAI;QACT,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAa,EAAE,EAAE,GAAC,gNAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D,CAAC,MAAM,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;YACtB,OAAO,gNAAK,CAAC;QACjB,CAAC;QACD,OAAO,CAAC,IAAa,EAAE,EAAE,GAAC,gNAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IAChE,CAAC;IACD,QAAQ,EAAC,IAAI;QACT,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAa,EAAE,CAAG,CAAD,GAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,CAAC,IAAa,EAAE,CAAG,CAAD,GAAK,CAAC,IAAI,KAAK,IAAI,CAAC;IACjD,CAAC;IACD,YAAY,EAAC,IAAI;QACb,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;YAC7B,OAAO,CAAC,IAAa,EAAE,EAAE,GAAC,iNAAM,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,CAAC,IAAa,EAAE,EAAE,GAAC,iNAAM,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IACjE,CAAC;CACJ,CAAC;AAEF;;;;;;;;GAQG,CACH,SAAS,cAAc,CACnB,MAAc,EACd,KAAwD;IAExD,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;QAC9B,OAAO,CAAC,IAAa,EAAE,EAAE,GAAC,gNAAK,EAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;IACzE,CAAC;IACD,OAAO,CAAC,IAAa,EAAE,EAAE,GAAC,gNAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC;AAC5E,CAAC;AAED;;;;;;;;GAQG,CACH,SAAS,YAAY,CAAC,CAAW,EAAE,CAAW;IAC1C,OAAO,CAAC,IAAa,EAAE,CAAG,CAAD,AAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACjD,CAAC;AAED;;;;;;;GAOG,CACH,SAAS,WAAW,CAAC,OAAwB;IACzC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;QAC3C,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3B,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAClD,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,GAClB,cAAc,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AAClE,CAAC;AAUK,SAAU,WAAW,CAAC,OAAwB,EAAE,IAAa;IAC/D,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACpC,CAAC;AAYK,SAAU,WAAW,CACvB,OAAwB,EACxB,KAA0B,EAC1B,OAAgB,EAChB,QAAgB,QAAQ;IAExB,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,CAAC,KAAC,iNAAM,EAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3D,CAAC;AAWK,SAAU,cAAc,CAC1B,EAAsC,EACtC,KAA0B,EAC1B,OAAO,GAAG,IAAI;IAEd,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,GAAG;QAAC,KAAK;KAAC,CAAC;IAC3C,WAAO,kNAAO,EAAC,cAAc,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC7D,CAAC;AAYK,SAAU,oBAAoB,CAChC,OAA6C,EAC7C,KAA0B,EAC1B,OAAO,GAAG,IAAI,EACd,QAAgB,QAAQ;IAExB,WAAO,iNAAM,EACT,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAC3B,KAAK,EACL,OAAO,EACP,KAAK,CACK,CAAC;AACnB,CAAC;AAYK,SAAU,sBAAsB,CAClC,SAA+C,EAC/C,KAA0B,EAC1B,OAAO,GAAG,IAAI,EACd,QAAgB,QAAQ;IAExB,WAAO,iNAAM,EACT,cAAc,CAAC,OAAO,EAAE,SAAS,CAAC,EAClC,KAAK,EACL,OAAO,EACP,KAAK,CACK,CAAC;AACnB,CAAC;AAYK,SAAU,oBAAoB,CAChC,IAAoD,EACpD,KAA0B,EAC1B,OAAO,GAAG,IAAI,EACd,QAAgB,QAAQ;IAExB,WAAO,iNAAM,EAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAc,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC7E,CAAC", "debugId": null}}, {"offset": {"line": 11402, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/domutils@3.2.2/node_modules/domutils/lib/esm/helpers.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["helpers.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAE,WAAW,EAAuB,MAAM,YAAY,CAAC;;AAUxD,SAAU,aAAa,CAAC,KAAgB;IAC1C,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;IAEvB;;;OAGG,CACH,MAAO,EAAE,GAAG,IAAI,CAAC,CAAE,CAAC;QAChB,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QAExB;;;;WAIG,CACH,IAAI,GAAG,GAAG,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;YACnD,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACrB,SAAS;QACb,CAAC;QAED,IAAK,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAE,CAAC;YACpE,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3B,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBACrB,MAAM;YACV,CAAC;QACL,CAAC;IACL,CAAC;IAED,OAAO,KAAK,CAAC;AACjB,CAAC;AAKD,IAAkB,gBAMjB;AAND,CAAA,SAAkB,gBAAgB;IAC9B,gBAAA,CAAA,gBAAA,CAAA,eAAA,GAAA,EAAA,GAAA,cAAgB,CAAA;IAChB,gBAAA,CAAA,gBAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAa,CAAA;IACb,gBAAA,CAAA,gBAAA,CAAA,YAAA,GAAA,EAAA,GAAA,WAAa,CAAA;IACb,gBAAA,CAAA,gBAAA,CAAA,WAAA,GAAA,EAAA,GAAA,UAAY,CAAA;IACZ,gBAAA,CAAA,gBAAA,CAAA,eAAA,GAAA,GAAA,GAAA,cAAiB,CAAA;AACrB,CAAC,EANiB,gBAAgB,IAAA,CAAhB,gBAAgB,GAAA,CAAA,CAAA,GAMjC;AA4BK,SAAU,uBAAuB,CACnC,KAAc,EACd,KAAc;IAEd,MAAM,QAAQ,GAAiB,EAAE,CAAC;IAClC,MAAM,QAAQ,GAAiB,EAAE,CAAC;IAElC,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;QAClB,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAI,OAAO,OAAG,sNAAW,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;IACxD,MAAO,OAAO,CAAE,CAAC;QACb,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;IAC7B,CAAC;IACD,OAAO,OAAG,sNAAW,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;IACpD,MAAO,OAAO,CAAE,CAAC;QACb,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;IAC7B,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC1D,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,MAAO,GAAG,GAAG,MAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAE,CAAC;QACrD,GAAG,EAAE,CAAC;IACV,CAAC;IAED,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;QACZ,OAAO,gBAAgB,CAAC,YAAY,CAAC;IACzC,CAAC;IAED,MAAM,YAAY,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IACvC,MAAM,QAAQ,GAAc,YAAY,CAAC,QAAQ,CAAC;IAClD,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC/B,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;IAE/B,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC1D,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;YACzB,OAAO,gBAAgB,CAAC,SAAS,GAAG,gBAAgB,CAAC,YAAY,CAAC;QACtE,CAAC;QACD,OAAO,gBAAgB,CAAC,SAAS,CAAC;IACtC,CAAC;IACD,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;QACzB,OAAO,gBAAgB,CAAC,SAAS,GAAG,gBAAgB,CAAC,QAAQ,CAAC;IAClE,CAAC;IACD,OAAO,gBAAgB,CAAC,SAAS,CAAC;AACtC,CAAC;AAWK,SAAU,UAAU,CAAoB,KAAU;IACpD,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE,CAAG,CAAD,AAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEnE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAChB,MAAM,QAAQ,GAAG,uBAAuB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/C,IAAI,QAAQ,GAAG,gBAAgB,CAAC,SAAS,EAAE,CAAC;YACxC,OAAO,CAAC,CAAC,CAAC;QACd,CAAC,MAAM,IAAI,QAAQ,GAAG,gBAAgB,CAAC,SAAS,EAAE,CAAC;YAC/C,OAAO,CAAC,CAAC;QACb,CAAC;QACD,OAAO,CAAC,CAAC;IACb,CAAC,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACjB,CAAC", "debugId": null}}, {"offset": {"line": 11503, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/domutils@3.2.2/node_modules/domutils/lib/esm/feeds.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["feeds.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;AACA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAC7C,OAAO,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAC;;;AAgF7C,SAAU,OAAO,CAAC,GAAc;IAClC,MAAM,QAAQ,GAAG,aAAa,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IAEjD,OAAO,CAAC,QAAQ,GACV,IAAI,GACJ,QAAQ,CAAC,IAAI,KAAK,MAAM,GACtB,WAAW,CAAC,QAAQ,CAAC,GACrB,UAAU,CAAC,QAAQ,CAAC,CAAC;AACjC,CAAC;AAED;;;;;GAKG,CACH,SAAS,WAAW,CAAC,QAAiB;;IAClC,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC;IAEjC,MAAM,IAAI,GAAS;QACf,IAAI,EAAE,MAAM;QACZ,KAAK,MAAE,6NAAoB,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;;YACtD,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;YAC1B,MAAM,KAAK,GAAa;gBAAE,KAAK,EAAE,gBAAgB,CAAC,QAAQ,CAAC;YAAA,CAAE,CAAC;YAE9D,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9C,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YAEpD,MAAM,IAAI,GAAG,CAAA,KAAA,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,OAAO,CAAC,MAAM,CAAC,CAAC;YAC9D,IAAI,IAAI,EAAE,CAAC;gBACP,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;YACtB,CAAC;YAED,MAAM,WAAW,GACb,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC7D,IAAI,WAAW,EAAE,CAAC;gBACd,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;YACpC,CAAC;YAED,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC3C,IAAI,OAAO,EAAE,CAAC;gBACV,KAAK,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YACtC,CAAC;YAED,OAAO,KAAK,CAAC;QACjB,CAAC,CAAC;KACL,CAAC;IAEF,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;IAC3C,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACjD,MAAM,IAAI,GAAG,CAAA,KAAA,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,OAAO,CAAC,MAAM,CAAC,CAAC;IAC5D,IAAI,IAAI,EAAE,CAAC;QACP,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACrB,CAAC;IACD,gBAAgB,CAAC,IAAI,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAE1D,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;IACzC,IAAI,OAAO,EAAE,CAAC;QACV,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAExD,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;;GAKG,CACH,SAAS,UAAU,CAAC,QAAiB;;IACjC,MAAM,MAAM,GAAG,CAAA,KAAA,CAAA,KAAA,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,MAAA,QAAA,OAAA,KAAA,IAAA,KAAA,IAAA,GAAE,QAAQ,MAAA,QAAA,OAAA,KAAA,IAAA,KAAI,EAAE,CAAC;IAE3E,MAAM,IAAI,GAAS;QACf,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QAChC,EAAE,EAAE,EAAE;QACN,KAAK,MAAE,6NAAoB,EAAC,MAAM,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,GAAG,CACtD,CAAC,IAAa,EAAE,EAAE;YACd,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;YAC1B,MAAM,KAAK,GAAa;gBAAE,KAAK,EAAE,gBAAgB,CAAC,QAAQ,CAAC;YAAA,CAAE,CAAC;YAC9D,gBAAgB,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YAChD,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;YACpD,gBAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YAClD,gBAAgB,CAAC,KAAK,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAChE,MAAM,OAAO,GACT,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC7D,IAAI,OAAO,EAAE,KAAK,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YAE/C,OAAO,KAAK,CAAC;QACjB,CAAC,CACJ;KACJ,CAAC;IAEF,gBAAgB,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;IACjD,gBAAgB,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAC/C,gBAAgB,CAAC,IAAI,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;IAE7D,MAAM,OAAO,GAAG,KAAK,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IAC/C,IAAI,OAAO,EAAE,CAAC;QACV,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAEjE,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,MAAM,iBAAiB,GAAG;IAAC,KAAK;IAAE,MAAM;IAAE,MAAM;CAAU,CAAC;AAC3D,MAAM,cAAc,GAAG;IACnB,UAAU;IACV,SAAS;IACT,WAAW;IACX,cAAc;IACd,UAAU;IACV,UAAU;IACV,QAAQ;IACR,OAAO;CACD,CAAC;AAEX;;;;;GAKG,CACH,SAAS,gBAAgB,CAAC,KAAgB;IACtC,WAAO,6NAAoB,EAAC,eAAe,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QAC7D,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAEzB,MAAM,KAAK,GAAkB;YACzB,MAAM,EAAE,OAAO,CAAC,QAAQ,CAET;YACf,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC;SACpC,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,iBAAiB,CAAE,CAAC;YACrC,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClB,KAAK,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YACpC,CAAC;QACL,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,cAAc,CAAE,CAAC;YAClC,IAAI,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClB,KAAK,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAClD,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACxB,KAAK,CAAC,UAAU,GAAG,OAAO,CACtB,YAAY,CACuB,CAAC;QAC5C,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;;;GAMG,CACH,SAAS,aAAa,CAClB,OAA6C,EAC7C,IAAe;IAEf,WAAO,6NAAoB,EAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,CAAC;AAED;;;;;;;GAOG,CACH,SAAS,KAAK,CACV,OAAe,EACf,KAA0B,EAC1B,OAAO,GAAG,KAAK;IAEf,WAAO,uNAAW,MAAC,6NAAoB,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAChF,CAAC;AAED;;;;;;;;GAQG,CACH,SAAS,gBAAgB,CACrB,GAAM,EACN,IAAa,EACb,OAAe,EACf,KAAgB,EAChB,OAAO,GAAG,KAAK;IAEf,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;IAC3C,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,GAA4B,CAAC;AACtD,CAAC;AAED;;;;;GAKG,CACH,SAAS,WAAW,CAAC,KAAa;IAC9B,OAAO,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,SAAS,CAAC;AACtE,CAAC", "debugId": null}}, {"offset": {"line": 11683, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/domutils@3.2.2/node_modules/domutils/lib/esm/index.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/0ab8bcf1ecfc70dfc93291a4cb2496578ac25e9c/src/", "sources": ["index.ts"], "sourcesContent": [], "names": [], "mappings": ";AAAA,cAAc,gBAAgB,CAAC;AAC/B,cAAc,gBAAgB,CAAC;AAC/B,cAAc,mBAAmB,CAAC;AAClC,cAAc,eAAe,CAAC;AAC9B,cAAc,aAAa,CAAC;AAC5B,cAAc,cAAc,CAAC;AAC7B,cAAc,YAAY,CAAC;AAC3B,8DAAA,EAAgE,CAChE,OAAO,EACH,KAAK,EACL,OAAO,EACP,MAAM,EACN,SAAS,EACT,UAAU,EACV,WAAW,GACd,MAAM,YAAY,CAAC", "debugId": null}}, {"offset": {"line": 11704, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/.pnpm/htmlparser2@8.0.2/node_modules/htmlparser2/lib/esm/index.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/htmlparser2/c123610e003a1eaebc61febed01cabb6e41eb658/src/", "sources": ["index.ts"], "sourcesContent": [], "names": [], "mappings": ";;;;;;;;;;AAAA,OAAO,EAAE,MAAM,EAAiB,MAAM,aAAa,CAAC;AAGpD,OAAO,EACH,UAAU,GAKb,MAAM,YAAY,CAAC;AAqDpB,OAAO,EACH,OAAO,IAAI,SAAS,GAEvB,MAAM,gBAAgB,CAAC;AAExB;;;GAGG,CACH,OAAO,KAAK,WAAW,MAAM,gBAAgB,CAAC;AAE9C,OAAO,EAAE,OAAO,EAAQ,MAAM,UAAU,CAAC;;;;;;AA7CnC,SAAU,aAAa,CAAC,IAAY,EAAE,OAAiB;IACzD,MAAM,OAAO,GAAG,IAAI,sOAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACnD,IAAI,qNAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACvC,OAAO,OAAO,CAAC,IAAI,CAAC;AACxB,CAAC;AAWK,SAAU,QAAQ,CAAC,IAAY,EAAE,OAAiB;IACpD,OAAO,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC;AACjD,CAAC;AAQK,SAAU,eAAe,CAC3B,QAAyD,EACzD,OAAiB,EACjB,eAA4C;IAE5C,MAAM,OAAO,GAAG,IAAI,sOAAU,CAAC,QAAQ,EAAE,OAAO,EAAE,eAAe,CAAC,CAAC;IACnE,OAAO,IAAI,qNAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC;;;;;AAiBD,MAAM,uBAAuB,GAAG;IAAE,OAAO,EAAE,IAAI;AAAA,CAAE,CAAC;AAQ5C,SAAU,SAAS,CACrB,IAAY,EACZ,UAAmB,uBAAuB;IAE1C,WAAO,+MAAO,EAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;AAC5C,CAAC", "debugId": null}}, {"offset": {"line": 11755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/deepmerge%404.3.1/node_modules/deepmerge/dist/cjs.js"], "sourcesContent": ["'use strict';\n\nvar isMergeableObject = function isMergeableObject(value) {\n\treturn isNonNullObject(value)\n\t\t&& !isSpecial(value)\n};\n\nfunction isNonNullObject(value) {\n\treturn !!value && typeof value === 'object'\n}\n\nfunction isSpecial(value) {\n\tvar stringValue = Object.prototype.toString.call(value);\n\n\treturn stringValue === '[object RegExp]'\n\t\t|| stringValue === '[object Date]'\n\t\t|| isReactElement(value)\n}\n\n// see https://github.com/facebook/react/blob/b5ac963fb791d1298e7f396236383bc955f916c1/src/isomorphic/classic/element/ReactElement.js#L21-L25\nvar canUseSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = canUseSymbol ? Symbol.for('react.element') : 0xeac7;\n\nfunction isReactElement(value) {\n\treturn value.$$typeof === REACT_ELEMENT_TYPE\n}\n\nfunction emptyTarget(val) {\n\treturn Array.isArray(val) ? [] : {}\n}\n\nfunction cloneUnlessOtherwiseSpecified(value, options) {\n\treturn (options.clone !== false && options.isMergeableObject(value))\n\t\t? deepmerge(emptyTarget(value), value, options)\n\t\t: value\n}\n\nfunction defaultArrayMerge(target, source, options) {\n\treturn target.concat(source).map(function(element) {\n\t\treturn cloneUnlessOtherwiseSpecified(element, options)\n\t})\n}\n\nfunction getMergeFunction(key, options) {\n\tif (!options.customMerge) {\n\t\treturn deepmerge\n\t}\n\tvar customMerge = options.customMerge(key);\n\treturn typeof customMerge === 'function' ? customMerge : deepmerge\n}\n\nfunction getEnumerableOwnPropertySymbols(target) {\n\treturn Object.getOwnPropertySymbols\n\t\t? Object.getOwnPropertySymbols(target).filter(function(symbol) {\n\t\t\treturn Object.propertyIsEnumerable.call(target, symbol)\n\t\t})\n\t\t: []\n}\n\nfunction getKeys(target) {\n\treturn Object.keys(target).concat(getEnumerableOwnPropertySymbols(target))\n}\n\nfunction propertyIsOnObject(object, property) {\n\ttry {\n\t\treturn property in object\n\t} catch(_) {\n\t\treturn false\n\t}\n}\n\n// Protects from prototype poisoning and unexpected merging up the prototype chain.\nfunction propertyIsUnsafe(target, key) {\n\treturn propertyIsOnObject(target, key) // Properties are safe to merge if they don't exist in the target yet,\n\t\t&& !(Object.hasOwnProperty.call(target, key) // unsafe if they exist up the prototype chain,\n\t\t\t&& Object.propertyIsEnumerable.call(target, key)) // and also unsafe if they're nonenumerable.\n}\n\nfunction mergeObject(target, source, options) {\n\tvar destination = {};\n\tif (options.isMergeableObject(target)) {\n\t\tgetKeys(target).forEach(function(key) {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(target[key], options);\n\t\t});\n\t}\n\tgetKeys(source).forEach(function(key) {\n\t\tif (propertyIsUnsafe(target, key)) {\n\t\t\treturn\n\t\t}\n\n\t\tif (propertyIsOnObject(target, key) && options.isMergeableObject(source[key])) {\n\t\t\tdestination[key] = getMergeFunction(key, options)(target[key], source[key], options);\n\t\t} else {\n\t\t\tdestination[key] = cloneUnlessOtherwiseSpecified(source[key], options);\n\t\t}\n\t});\n\treturn destination\n}\n\nfunction deepmerge(target, source, options) {\n\toptions = options || {};\n\toptions.arrayMerge = options.arrayMerge || defaultArrayMerge;\n\toptions.isMergeableObject = options.isMergeableObject || isMergeableObject;\n\t// cloneUnlessOtherwiseSpecified is added to `options` so that custom arrayMerge()\n\t// implementations can use it. The caller may not replace it.\n\toptions.cloneUnlessOtherwiseSpecified = cloneUnlessOtherwiseSpecified;\n\n\tvar sourceIsArray = Array.isArray(source);\n\tvar targetIsArray = Array.isArray(target);\n\tvar sourceAndTargetTypesMatch = sourceIsArray === targetIsArray;\n\n\tif (!sourceAndTargetTypesMatch) {\n\t\treturn cloneUnlessOtherwiseSpecified(source, options)\n\t} else if (sourceIsArray) {\n\t\treturn options.arrayMerge(target, source, options)\n\t} else {\n\t\treturn mergeObject(target, source, options)\n\t}\n}\n\ndeepmerge.all = function deepmergeAll(array, options) {\n\tif (!Array.isArray(array)) {\n\t\tthrow new Error('first argument should be an array')\n\t}\n\n\treturn array.reduce(function(prev, next) {\n\t\treturn deepmerge(prev, next, options)\n\t}, {})\n};\n\nvar deepmerge_1 = deepmerge;\n\nmodule.exports = deepmerge_1;\n"], "names": [], "mappings": "AAEA,IAAI,oBAAoB,SAAS,kBAAkB,KAAK;IACvD,OAAO,gBAAgB,UACnB,CAAC,UAAU;AAChB;AAEA,SAAS,gBAAgB,KAAK;IAC7B,OAAO,CAAC,CAAC,SAAS,OAAO,UAAU;AACpC;AAEA,SAAS,UAAU,KAAK;IACvB,IAAI,cAAc,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;IAEjD,OAAO,gBAAgB,qBACnB,gBAAgB,mBAChB,eAAe;AACpB;AAEA,6IAA6I;AAC7I,IAAI,eAAe,OAAO,WAAW,cAAc,OAAO,GAAG;AAC7D,IAAI,qBAAqB,eAAe,OAAO,GAAG,CAAC,mBAAmB;AAEtE,SAAS,eAAe,KAAK;IAC5B,OAAO,MAAM,QAAQ,KAAK;AAC3B;AAEA,SAAS,YAAY,GAAG;IACvB,OAAO,MAAM,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;AACnC;AAEA,SAAS,8BAA8B,KAAK,EAAE,OAAO;IACpD,OAAO,AAAC,QAAQ,KAAK,KAAK,SAAS,QAAQ,iBAAiB,CAAC,SAC1D,UAAU,YAAY,QAAQ,OAAO,WACrC;AACJ;AAEA,SAAS,kBAAkB,MAAM,EAAE,MAAM,EAAE,OAAO;IACjD,OAAO,OAAO,MAAM,CAAC,QAAQ,GAAG,CAAC,SAAS,OAAO;QAChD,OAAO,8BAA8B,SAAS;IAC/C;AACD;AAEA,SAAS,iBAAiB,GAAG,EAAE,OAAO;IACrC,IAAI,CAAC,QAAQ,WAAW,EAAE;QACzB,OAAO;IACR;IACA,IAAI,cAAc,QAAQ,WAAW,CAAC;IACtC,OAAO,OAAO,gBAAgB,aAAa,cAAc;AAC1D;AAEA,SAAS,gCAAgC,MAAM;IAC9C,OAAO,OAAO,qBAAqB,GAChC,OAAO,qBAAqB,CAAC,QAAQ,MAAM,CAAC,SAAS,MAAM;QAC5D,OAAO,OAAO,oBAAoB,CAAC,IAAI,CAAC,QAAQ;IACjD,KACE,EAAE;AACN;AAEA,SAAS,QAAQ,MAAM;IACtB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,gCAAgC;AACnE;AAEA,SAAS,mBAAmB,MAAM,EAAE,QAAQ;IAC3C,IAAI;QACH,OAAO,YAAY;IACpB,EAAE,OAAM,GAAG;QACV,OAAO;IACR;AACD;AAEA,mFAAmF;AACnF,SAAS,iBAAiB,MAAM,EAAE,GAAG;IACpC,OAAO,mBAAmB,QAAQ,KAAK,sEAAsE;QACzG,CAAC,CAAC,OAAO,cAAc,CAAC,IAAI,CAAC,QAAQ,KAAK,+CAA+C;QACxF,OAAO,oBAAoB,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,4CAA4C;;AACjG;AAEA,SAAS,YAAY,MAAM,EAAE,MAAM,EAAE,OAAO;IAC3C,IAAI,cAAc,CAAC;IACnB,IAAI,QAAQ,iBAAiB,CAAC,SAAS;QACtC,QAAQ,QAAQ,OAAO,CAAC,SAAS,GAAG;YACnC,WAAW,CAAC,IAAI,GAAG,8BAA8B,MAAM,CAAC,IAAI,EAAE;QAC/D;IACD;IACA,QAAQ,QAAQ,OAAO,CAAC,SAAS,GAAG;QACnC,IAAI,iBAAiB,QAAQ,MAAM;YAClC;QACD;QAEA,IAAI,mBAAmB,QAAQ,QAAQ,QAAQ,iBAAiB,CAAC,MAAM,CAAC,IAAI,GAAG;YAC9E,WAAW,CAAC,IAAI,GAAG,iBAAiB,KAAK,SAAS,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE;QAC7E,OAAO;YACN,WAAW,CAAC,IAAI,GAAG,8BAA8B,MAAM,CAAC,IAAI,EAAE;QAC/D;IACD;IACA,OAAO;AACR;AAEA,SAAS,UAAU,MAAM,EAAE,MAAM,EAAE,OAAO;IACzC,UAAU,WAAW,CAAC;IACtB,QAAQ,UAAU,GAAG,QAAQ,UAAU,IAAI;IAC3C,QAAQ,iBAAiB,GAAG,QAAQ,iBAAiB,IAAI;IACzD,kFAAkF;IAClF,6DAA6D;IAC7D,QAAQ,6BAA6B,GAAG;IAExC,IAAI,gBAAgB,MAAM,OAAO,CAAC;IAClC,IAAI,gBAAgB,MAAM,OAAO,CAAC;IAClC,IAAI,4BAA4B,kBAAkB;IAElD,IAAI,CAAC,2BAA2B;QAC/B,OAAO,8BAA8B,QAAQ;IAC9C,OAAO,IAAI,eAAe;QACzB,OAAO,QAAQ,UAAU,CAAC,QAAQ,QAAQ;IAC3C,OAAO;QACN,OAAO,YAAY,QAAQ,QAAQ;IACpC;AACD;AAEA,UAAU,GAAG,GAAG,SAAS,aAAa,KAAK,EAAE,OAAO;IACnD,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;QAC1B,MAAM,IAAI,MAAM;IACjB;IAEA,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI,EAAE,IAAI;QACtC,OAAO,UAAU,MAAM,MAAM;IAC9B,GAAG,CAAC;AACL;AAEA,IAAI,cAAc;AAElB,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 11862, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/html-to-text%409.0.5/node_modules/html-to-text/lib/html-to-text.mjs"], "sourcesContent": ["import { hp2Builder } from '@selderee/plugin-htmlparser2';\nimport { parseDocument } from 'htmlparser2';\nimport { DecisionTree } from 'selderee';\nimport merge from 'deepmerge';\nimport { render } from 'dom-serializer';\n\n/**\n * Make a recursive function that will only run to a given depth\n * and switches to an alternative function at that depth. \\\n * No limitation if `n` is `undefined` (Just wraps `f` in that case).\n *\n * @param   { number | undefined } n   Allowed depth of recursion. `undefined` for no limitation.\n * @param   { Function }           f   Function that accepts recursive callback as the first argument.\n * @param   { Function }           [g] Function to run instead, when maximum depth was reached. Do nothing by default.\n * @returns { Function }\n */\nfunction limitedDepthRecursive (n, f, g = () => undefined) {\n  if (n === undefined) {\n    const f1 = function (...args) { return f(f1, ...args); };\n    return f1;\n  }\n  if (n >= 0) {\n    return function (...args) { return f(limitedDepthRecursive(n - 1, f, g), ...args); };\n  }\n  return g;\n}\n\n/**\n * Return the same string or a substring with\n * the given character occurrences removed from each side.\n *\n * @param   { string } str  A string to trim.\n * @param   { string } char A character to be trimmed.\n * @returns { string }\n */\nfunction trimCharacter (str, char) {\n  let start = 0;\n  let end = str.length;\n  while (start < end && str[start] === char) { ++start; }\n  while (end > start && str[end - 1] === char) { --end; }\n  return (start > 0 || end < str.length)\n    ? str.substring(start, end)\n    : str;\n}\n\n/**\n * Return the same string or a substring with\n * the given character occurrences removed from the end only.\n *\n * @param   { string } str  A string to trim.\n * @param   { string } char A character to be trimmed.\n * @returns { string }\n */\nfunction trimCharacterEnd (str, char) {\n  let end = str.length;\n  while (end > 0 && str[end - 1] === char) { --end; }\n  return (end < str.length)\n    ? str.substring(0, end)\n    : str;\n}\n\n/**\n * Return a new string will all characters replaced with unicode escape sequences.\n * This extreme kind of escaping can used to be safely compose regular expressions.\n *\n * @param { string } str A string to escape.\n * @returns { string } A string of unicode escape sequences.\n */\nfunction unicodeEscape (str) {\n  return str.replace(/[\\s\\S]/g, c => '\\\\u' + c.charCodeAt().toString(16).padStart(4, '0'));\n}\n\n/**\n * Deduplicate an array by a given key callback.\n * Item properties are merged recursively and with the preference for last defined values.\n * Of items with the same key, merged item takes the place of the last item,\n * others are omitted.\n *\n * @param { any[] } items An array to deduplicate.\n * @param { (x: any) => string } getKey Callback to get a value that distinguishes unique items.\n * @returns { any[] }\n */\nfunction mergeDuplicatesPreferLast (items, getKey) {\n  const map = new Map();\n  for (let i = items.length; i-- > 0;) {\n    const item = items[i];\n    const key = getKey(item);\n    map.set(\n      key,\n      (map.has(key))\n        ? merge(item, map.get(key), { arrayMerge: overwriteMerge$1 })\n        : item\n    );\n  }\n  return [...map.values()].reverse();\n}\n\nconst overwriteMerge$1 = (acc, src, options) => [...src];\n\n/**\n * Get a nested property from an object.\n *\n * @param   { object }   obj  The object to query for the value.\n * @param   { string[] } path The path to the property.\n * @returns { any }\n */\nfunction get (obj, path) {\n  for (const key of path) {\n    if (!obj) { return undefined; }\n    obj = obj[key];\n  }\n  return obj;\n}\n\n/**\n * Convert a number into alphabetic sequence representation (Sequence without zeroes).\n *\n * For example: `a, ..., z, aa, ..., zz, aaa, ...`.\n *\n * @param   { number } num              Number to convert. Must be >= 1.\n * @param   { string } [baseChar = 'a'] Character for 1 in the sequence.\n * @param   { number } [base = 26]      Number of characters in the sequence.\n * @returns { string }\n */\nfunction numberToLetterSequence (num, baseChar = 'a', base = 26) {\n  const digits = [];\n  do {\n    num -= 1;\n    digits.push(num % base);\n    num = (num / base) >> 0; // quick `floor`\n  } while (num > 0);\n  const baseCode = baseChar.charCodeAt(0);\n  return digits\n    .reverse()\n    .map(n => String.fromCharCode(baseCode + n))\n    .join('');\n}\n\nconst I = ['I', 'X', 'C', 'M'];\nconst V = ['V', 'L', 'D'];\n\n/**\n * Convert a number to it's Roman representation. No large numbers extension.\n *\n * @param   { number } num Number to convert. `0 < num <= 3999`.\n * @returns { string }\n */\nfunction numberToRoman (num) {\n  return [...(num) + '']\n    .map(n => +n)\n    .reverse()\n    .map((v, i) => ((v % 5 < 4)\n      ? (v < 5 ? '' : V[i]) + I[i].repeat(v % 5)\n      : I[i] + (v < 5 ? V[i] : I[i + 1])))\n    .reverse()\n    .join('');\n}\n\n/**\n * Helps to build text from words.\n */\nclass InlineTextBuilder {\n  /**\n   * Creates an instance of InlineTextBuilder.\n   *\n   * If `maxLineLength` is not provided then it is either `options.wordwrap` or unlimited.\n   *\n   * @param { Options } options           HtmlToText options.\n   * @param { number }  [ maxLineLength ] This builder will try to wrap text to fit this line length.\n   */\n  constructor (options, maxLineLength = undefined) {\n    /** @type { string[][] } */\n    this.lines = [];\n    /** @type { string[] }   */\n    this.nextLineWords = [];\n    this.maxLineLength = maxLineLength || options.wordwrap || Number.MAX_VALUE;\n    this.nextLineAvailableChars = this.maxLineLength;\n    this.wrapCharacters = get(options, ['longWordSplit', 'wrapCharacters']) || [];\n    this.forceWrapOnLimit = get(options, ['longWordSplit', 'forceWrapOnLimit']) || false;\n\n    this.stashedSpace = false;\n    this.wordBreakOpportunity = false;\n  }\n\n  /**\n   * Add a new word.\n   *\n   * @param { string } word A word to add.\n   * @param { boolean } [noWrap] Don't wrap text even if the line is too long.\n   */\n  pushWord (word, noWrap = false) {\n    if (this.nextLineAvailableChars <= 0 && !noWrap) {\n      this.startNewLine();\n    }\n    const isLineStart = this.nextLineWords.length === 0;\n    const cost = word.length + (isLineStart ? 0 : 1);\n    if ((cost <= this.nextLineAvailableChars) || noWrap) { // Fits into available budget\n\n      this.nextLineWords.push(word);\n      this.nextLineAvailableChars -= cost;\n\n    } else { // Does not fit - try to split the word\n\n      // The word is moved to a new line - prefer to wrap between words.\n      const [first, ...rest] = this.splitLongWord(word);\n      if (!isLineStart) { this.startNewLine(); }\n      this.nextLineWords.push(first);\n      this.nextLineAvailableChars -= first.length;\n      for (const part of rest) {\n        this.startNewLine();\n        this.nextLineWords.push(part);\n        this.nextLineAvailableChars -= part.length;\n      }\n\n    }\n  }\n\n  /**\n   * Pop a word from the currently built line.\n   * This doesn't affect completed lines.\n   *\n   * @returns { string }\n   */\n  popWord () {\n    const lastWord = this.nextLineWords.pop();\n    if (lastWord !== undefined) {\n      const isLineStart = this.nextLineWords.length === 0;\n      const cost = lastWord.length + (isLineStart ? 0 : 1);\n      this.nextLineAvailableChars += cost;\n    }\n    return lastWord;\n  }\n\n  /**\n   * Concat a word to the last word already in the builder.\n   * Adds a new word in case there are no words yet in the last line.\n   *\n   * @param { string } word A word to be concatenated.\n   * @param { boolean } [noWrap] Don't wrap text even if the line is too long.\n   */\n  concatWord (word, noWrap = false) {\n    if (this.wordBreakOpportunity && word.length > this.nextLineAvailableChars) {\n      this.pushWord(word, noWrap);\n      this.wordBreakOpportunity = false;\n    } else {\n      const lastWord = this.popWord();\n      this.pushWord((lastWord) ? lastWord.concat(word) : word, noWrap);\n    }\n  }\n\n  /**\n   * Add current line (and more empty lines if provided argument > 1) to the list of complete lines and start a new one.\n   *\n   * @param { number } n Number of line breaks that will be added to the resulting string.\n   */\n  startNewLine (n = 1) {\n    this.lines.push(this.nextLineWords);\n    if (n > 1) {\n      this.lines.push(...Array.from({ length: n - 1 }, () => []));\n    }\n    this.nextLineWords = [];\n    this.nextLineAvailableChars = this.maxLineLength;\n  }\n\n  /**\n   * No words in this builder.\n   *\n   * @returns { boolean }\n   */\n  isEmpty () {\n    return this.lines.length === 0\n        && this.nextLineWords.length === 0;\n  }\n\n  clear () {\n    this.lines.length = 0;\n    this.nextLineWords.length = 0;\n    this.nextLineAvailableChars = this.maxLineLength;\n  }\n\n  /**\n   * Join all lines of words inside the InlineTextBuilder into a complete string.\n   *\n   * @returns { string }\n   */\n  toString () {\n    return [...this.lines, this.nextLineWords]\n      .map(words => words.join(' '))\n      .join('\\n');\n  }\n\n  /**\n   * Split a long word up to fit within the word wrap limit.\n   * Use either a character to split looking back from the word wrap limit,\n   * or truncate to the word wrap limit.\n   *\n   * @param   { string }   word Input word.\n   * @returns { string[] }      Parts of the word.\n   */\n  splitLongWord (word) {\n    const parts = [];\n    let idx = 0;\n    while (word.length > this.maxLineLength) {\n\n      const firstLine = word.substring(0, this.maxLineLength);\n      const remainingChars = word.substring(this.maxLineLength);\n\n      const splitIndex = firstLine.lastIndexOf(this.wrapCharacters[idx]);\n\n      if (splitIndex > -1) { // Found a character to split on\n\n        word = firstLine.substring(splitIndex + 1) + remainingChars;\n        parts.push(firstLine.substring(0, splitIndex + 1));\n\n      } else { // Not found a character to split on\n\n        idx++;\n        if (idx < this.wrapCharacters.length) { // There is next character to try\n\n          word = firstLine + remainingChars;\n\n        } else { // No more characters to try\n\n          if (this.forceWrapOnLimit) {\n            parts.push(firstLine);\n            word = remainingChars;\n            if (word.length > this.maxLineLength) {\n              continue;\n            }\n          } else {\n            word = firstLine + remainingChars;\n          }\n          break;\n\n        }\n\n      }\n\n    }\n    parts.push(word); // Add remaining part to array\n    return parts;\n  }\n}\n\n/* eslint-disable max-classes-per-file */\n\n\nclass StackItem {\n  constructor (next = null) { this.next = next; }\n\n  getRoot () { return (this.next) ? this.next : this; }\n}\n\nclass BlockStackItem extends StackItem {\n  constructor (options, next = null, leadingLineBreaks = 1, maxLineLength = undefined) {\n    super(next);\n    this.leadingLineBreaks = leadingLineBreaks;\n    this.inlineTextBuilder = new InlineTextBuilder(options, maxLineLength);\n    this.rawText = '';\n    this.stashedLineBreaks = 0;\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass ListStackItem extends BlockStackItem {\n  constructor (\n    options,\n    next = null,\n    {\n      interRowLineBreaks = 1,\n      leadingLineBreaks = 2,\n      maxLineLength = undefined,\n      maxPrefixLength = 0,\n      prefixAlign = 'left',\n    } = {}\n  ) {\n    super(options, next, leadingLineBreaks, maxLineLength);\n    this.maxPrefixLength = maxPrefixLength;\n    this.prefixAlign = prefixAlign;\n    this.interRowLineBreaks = interRowLineBreaks;\n  }\n}\n\nclass ListItemStackItem extends BlockStackItem {\n  constructor (\n    options,\n    next = null,\n    {\n      leadingLineBreaks = 1,\n      maxLineLength = undefined,\n      prefix = '',\n    } = {}\n  ) {\n    super(options, next, leadingLineBreaks, maxLineLength);\n    this.prefix = prefix;\n  }\n}\n\nclass TableStackItem extends StackItem {\n  constructor (next = null) {\n    super(next);\n    this.rows = [];\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TableRowStackItem extends StackItem {\n  constructor (next = null) {\n    super(next);\n    this.cells = [];\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TableCellStackItem extends StackItem {\n  constructor (options, next = null, maxColumnWidth = undefined) {\n    super(next);\n    this.inlineTextBuilder = new InlineTextBuilder(options, maxColumnWidth);\n    this.rawText = '';\n    this.stashedLineBreaks = 0;\n    this.isPre = next && next.isPre;\n    this.isNoWrap = next && next.isNoWrap;\n  }\n}\n\nclass TransformerStackItem extends StackItem {\n  constructor (next = null, transform) {\n    super(next);\n    this.transform = transform;\n  }\n}\n\nfunction charactersToCodes (str) {\n  return [...str]\n    .map(c => '\\\\u' + c.charCodeAt(0).toString(16).padStart(4, '0'))\n    .join('');\n}\n\n/**\n * Helps to handle HTML whitespaces.\n *\n * @class WhitespaceProcessor\n */\nclass WhitespaceProcessor {\n\n  /**\n   * Creates an instance of WhitespaceProcessor.\n   *\n   * @param { Options } options    HtmlToText options.\n   * @memberof WhitespaceProcessor\n   */\n  constructor (options) {\n    this.whitespaceChars = (options.preserveNewlines)\n      ? options.whitespaceCharacters.replace(/\\n/g, '')\n      : options.whitespaceCharacters;\n    const whitespaceCodes = charactersToCodes(this.whitespaceChars);\n    this.leadingWhitespaceRe = new RegExp(`^[${whitespaceCodes}]`);\n    this.trailingWhitespaceRe = new RegExp(`[${whitespaceCodes}]$`);\n    this.allWhitespaceOrEmptyRe = new RegExp(`^[${whitespaceCodes}]*$`);\n    this.newlineOrNonWhitespaceRe = new RegExp(`(\\\\n|[^\\\\n${whitespaceCodes}])`, 'g');\n    this.newlineOrNonNewlineStringRe = new RegExp(`(\\\\n|[^\\\\n]+)`, 'g');\n\n    if (options.preserveNewlines) {\n\n      const wordOrNewlineRe = new RegExp(`\\\\n|[^\\\\n${whitespaceCodes}]+`, 'gm');\n\n      /**\n       * Shrink whitespaces and wrap text, add to the builder.\n       *\n       * @param { string }                  text              Input text.\n       * @param { InlineTextBuilder }       inlineTextBuilder A builder to receive processed text.\n       * @param { (str: string) => string } [ transform ]     A transform to be applied to words.\n       * @param { boolean }                 [noWrap] Don't wrap text even if the line is too long.\n       */\n      this.shrinkWrapAdd = function (text, inlineTextBuilder, transform = (str => str), noWrap = false) {\n        if (!text) { return; }\n        const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n        let anyMatch = false;\n        let m = wordOrNewlineRe.exec(text);\n        if (m) {\n          anyMatch = true;\n          if (m[0] === '\\n') {\n            inlineTextBuilder.startNewLine();\n          } else if (previouslyStashedSpace || this.testLeadingWhitespace(text)) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          } else {\n            inlineTextBuilder.concatWord(transform(m[0]), noWrap);\n          }\n          while ((m = wordOrNewlineRe.exec(text)) !== null) {\n            if (m[0] === '\\n') {\n              inlineTextBuilder.startNewLine();\n            } else {\n              inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n            }\n          }\n        }\n        inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch) || (this.testTrailingWhitespace(text));\n        // No need to stash a space in case last added item was a new line,\n        // but that won't affect anything later anyway.\n      };\n\n    } else {\n\n      const wordRe = new RegExp(`[^${whitespaceCodes}]+`, 'g');\n\n      this.shrinkWrapAdd = function (text, inlineTextBuilder, transform = (str => str), noWrap = false) {\n        if (!text) { return; }\n        const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n        let anyMatch = false;\n        let m = wordRe.exec(text);\n        if (m) {\n          anyMatch = true;\n          if (previouslyStashedSpace || this.testLeadingWhitespace(text)) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          } else {\n            inlineTextBuilder.concatWord(transform(m[0]), noWrap);\n          }\n          while ((m = wordRe.exec(text)) !== null) {\n            inlineTextBuilder.pushWord(transform(m[0]), noWrap);\n          }\n        }\n        inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch) || this.testTrailingWhitespace(text);\n      };\n\n    }\n  }\n\n  /**\n   * Add text with only minimal processing.\n   * Everything between newlines considered a single word.\n   * No whitespace is trimmed.\n   * Not affected by preserveNewlines option - `\\n` always starts a new line.\n   *\n   * `noWrap` argument is `true` by default - this won't start a new line\n   * even if there is not enough space left in the current line.\n   *\n   * @param { string }            text              Input text.\n   * @param { InlineTextBuilder } inlineTextBuilder A builder to receive processed text.\n   * @param { boolean }           [noWrap] Don't wrap text even if the line is too long.\n   */\n  addLiteral (text, inlineTextBuilder, noWrap = true) {\n    if (!text) { return; }\n    const previouslyStashedSpace = inlineTextBuilder.stashedSpace;\n    let anyMatch = false;\n    let m = this.newlineOrNonNewlineStringRe.exec(text);\n    if (m) {\n      anyMatch = true;\n      if (m[0] === '\\n') {\n        inlineTextBuilder.startNewLine();\n      } else if (previouslyStashedSpace) {\n        inlineTextBuilder.pushWord(m[0], noWrap);\n      } else {\n        inlineTextBuilder.concatWord(m[0], noWrap);\n      }\n      while ((m = this.newlineOrNonNewlineStringRe.exec(text)) !== null) {\n        if (m[0] === '\\n') {\n          inlineTextBuilder.startNewLine();\n        } else {\n          inlineTextBuilder.pushWord(m[0], noWrap);\n        }\n      }\n    }\n    inlineTextBuilder.stashedSpace = (previouslyStashedSpace && !anyMatch);\n  }\n\n  /**\n   * Test whether the given text starts with HTML whitespace character.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testLeadingWhitespace (text) {\n    return this.leadingWhitespaceRe.test(text);\n  }\n\n  /**\n   * Test whether the given text ends with HTML whitespace character.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testTrailingWhitespace (text) {\n    return this.trailingWhitespaceRe.test(text);\n  }\n\n  /**\n   * Test whether the given text contains any non-whitespace characters.\n   *\n   * @param   { string }  text  The string to test.\n   * @returns { boolean }\n   */\n  testContainsWords (text) {\n    return !this.allWhitespaceOrEmptyRe.test(text);\n  }\n\n  /**\n   * Return the number of newlines if there are no words.\n   *\n   * If any word is found then return zero regardless of the actual number of newlines.\n   *\n   * @param   { string }  text  Input string.\n   * @returns { number }\n   */\n  countNewlinesNoWords (text) {\n    this.newlineOrNonWhitespaceRe.lastIndex = 0;\n    let counter = 0;\n    let match;\n    while ((match = this.newlineOrNonWhitespaceRe.exec(text)) !== null) {\n      if (match[0] === '\\n') {\n        counter++;\n      } else {\n        return 0;\n      }\n    }\n    return counter;\n  }\n\n}\n\n/**\n * Helps to build text from inline and block elements.\n *\n * @class BlockTextBuilder\n */\nclass BlockTextBuilder {\n\n  /**\n   * Creates an instance of BlockTextBuilder.\n   *\n   * @param { Options } options HtmlToText options.\n   * @param { import('selderee').Picker<DomNode, TagDefinition> } picker Selectors decision tree picker.\n   * @param { any} [metadata] Optional metadata for HTML document, for use in formatters.\n   */\n  constructor (options, picker, metadata = undefined) {\n    this.options = options;\n    this.picker = picker;\n    this.metadata = metadata;\n    this.whitespaceProcessor = new WhitespaceProcessor(options);\n    /** @type { StackItem } */\n    this._stackItem = new BlockStackItem(options);\n    /** @type { TransformerStackItem } */\n    this._wordTransformer = undefined;\n  }\n\n  /**\n   * Put a word-by-word transform function onto the transformations stack.\n   *\n   * Mainly used for uppercasing. Can be bypassed to add unformatted text such as URLs.\n   *\n   * Word transformations applied before wrapping.\n   *\n   * @param { (str: string) => string } wordTransform Word transformation function.\n   */\n  pushWordTransform (wordTransform) {\n    this._wordTransformer = new TransformerStackItem(this._wordTransformer, wordTransform);\n  }\n\n  /**\n   * Remove a function from the word transformations stack.\n   *\n   * @returns { (str: string) => string } A function that was removed.\n   */\n  popWordTransform () {\n    if (!this._wordTransformer) { return undefined; }\n    const transform = this._wordTransformer.transform;\n    this._wordTransformer = this._wordTransformer.next;\n    return transform;\n  }\n\n  /**\n   * Ignore wordwrap option in followup inline additions and disable automatic wrapping.\n   */\n  startNoWrap () {\n    this._stackItem.isNoWrap = true;\n  }\n\n  /**\n   * Return automatic wrapping to behavior defined by options.\n   */\n  stopNoWrap () {\n    this._stackItem.isNoWrap = false;\n  }\n\n  /** @returns { (str: string) => string } */\n  _getCombinedWordTransformer () {\n    const wt = (this._wordTransformer)\n      ? ((str) => applyTransformer(str, this._wordTransformer))\n      : undefined;\n    const ce = this.options.encodeCharacters;\n    return (wt)\n      ? ((ce) ? (str) => ce(wt(str)) : wt)\n      : ce;\n  }\n\n  _popStackItem () {\n    const item = this._stackItem;\n    this._stackItem = item.next;\n    return item;\n  }\n\n  /**\n   * Add a line break into currently built block.\n   */\n  addLineBreak () {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += '\\n';\n    } else {\n      this._stackItem.inlineTextBuilder.startNewLine();\n    }\n  }\n\n  /**\n   * Allow to break line in case directly following text will not fit.\n   */\n  addWordBreakOpportunity () {\n    if (\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    ) {\n      this._stackItem.inlineTextBuilder.wordBreakOpportunity = true;\n    }\n  }\n\n  /**\n   * Add a node inline into the currently built block.\n   *\n   * @param { string } str\n   * Text content of a node to add.\n   *\n   * @param { object } [param1]\n   * Object holding the parameters of the operation.\n   *\n   * @param { boolean } [param1.noWordTransform]\n   * Ignore word transformers if there are any.\n   * Don't encode characters as well.\n   * (Use this for things like URL addresses).\n   */\n  addInline (str, { noWordTransform = false } = {}) {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += str;\n      return;\n    }\n\n    if (\n      str.length === 0 || // empty string\n      (\n        this._stackItem.stashedLineBreaks && // stashed linebreaks make whitespace irrelevant\n        !this.whitespaceProcessor.testContainsWords(str) // no words to add\n      )\n    ) { return; }\n\n    if (this.options.preserveNewlines) {\n      const newlinesNumber = this.whitespaceProcessor.countNewlinesNoWords(str);\n      if (newlinesNumber > 0) {\n        this._stackItem.inlineTextBuilder.startNewLine(newlinesNumber);\n        // keep stashedLineBreaks unchanged\n        return;\n      }\n    }\n\n    if (this._stackItem.stashedLineBreaks) {\n      this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks);\n    }\n    this.whitespaceProcessor.shrinkWrapAdd(\n      str,\n      this._stackItem.inlineTextBuilder,\n      (noWordTransform) ? undefined : this._getCombinedWordTransformer(),\n      this._stackItem.isNoWrap\n    );\n    this._stackItem.stashedLineBreaks = 0; // inline text doesn't introduce line breaks\n  }\n\n  /**\n   * Add a string inline into the currently built block.\n   *\n   * Use this for markup elements that don't have to adhere\n   * to text layout rules.\n   *\n   * @param { string } str Text to add.\n   */\n  addLiteral (str) {\n    if (!(\n      this._stackItem instanceof BlockStackItem\n      || this._stackItem instanceof ListItemStackItem\n      || this._stackItem instanceof TableCellStackItem\n    )) { return; }\n\n    if (str.length === 0) { return; }\n\n    if (this._stackItem.isPre) {\n      this._stackItem.rawText += str;\n      return;\n    }\n\n    if (this._stackItem.stashedLineBreaks) {\n      this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks);\n    }\n    this.whitespaceProcessor.addLiteral(\n      str,\n      this._stackItem.inlineTextBuilder,\n      this._stackItem.isNoWrap\n    );\n    this._stackItem.stashedLineBreaks = 0;\n  }\n\n  /**\n   * Start building a new block.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the block.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This block should have at least this number of line breaks to separate it from any preceding block.\n   *\n   * @param { number }  [param0.reservedLineLength]\n   * Reserve this number of characters on each line for block markup.\n   *\n   * @param { boolean } [param0.isPre]\n   * Should HTML whitespace be preserved inside this block.\n   */\n  openBlock ({ leadingLineBreaks = 1, reservedLineLength = 0, isPre = false } = {}) {\n    const maxLineLength = Math.max(20, this._stackItem.inlineTextBuilder.maxLineLength - reservedLineLength);\n    this._stackItem = new BlockStackItem(\n      this.options,\n      this._stackItem,\n      leadingLineBreaks,\n      maxLineLength\n    );\n    if (isPre) { this._stackItem.isPre = true; }\n  }\n\n  /**\n   * Finalize currently built block, add it's content to the parent block.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the block.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This block should have at least this number of line breaks to separate it from any following block.\n   *\n   * @param { (str: string) => string } [param0.blockTransform]\n   * A function to transform the block text before adding to the parent block.\n   * This happens after word wrap and should be used in combination with reserved line length\n   * in order to keep line lengths correct.\n   * Used for whole block markup.\n   */\n  closeBlock ({ trailingLineBreaks = 1, blockTransform = undefined } = {}) {\n    const block = this._popStackItem();\n    const blockText = (blockTransform) ? blockTransform(getText(block)) : getText(block);\n    addText(this._stackItem, blockText, block.leadingLineBreaks, Math.max(block.stashedLineBreaks, trailingLineBreaks));\n  }\n\n  /**\n   * Start building a new list.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the list.\n   *\n   * @param { number } [param0.maxPrefixLength]\n   * Length of the longest list item prefix.\n   * If not supplied or too small then list items won't be aligned properly.\n   *\n   * @param { 'left' | 'right' } [param0.prefixAlign]\n   * Specify how prefixes of different lengths have to be aligned\n   * within a column.\n   *\n   * @param { number } [param0.interRowLineBreaks]\n   * Minimum number of line breaks between list items.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This list should have at least this number of line breaks to separate it from any preceding block.\n   */\n  openList ({ maxPrefixLength = 0, prefixAlign = 'left', interRowLineBreaks = 1, leadingLineBreaks = 2 } = {}) {\n    this._stackItem = new ListStackItem(this.options, this._stackItem, {\n      interRowLineBreaks: interRowLineBreaks,\n      leadingLineBreaks: leadingLineBreaks,\n      maxLineLength: this._stackItem.inlineTextBuilder.maxLineLength,\n      maxPrefixLength: maxPrefixLength,\n      prefixAlign: prefixAlign\n    });\n  }\n\n  /**\n   * Start building a new list item.\n   *\n   * @param {object} param0\n   * Object holding the parameters of the list item.\n   *\n   * @param { string } [param0.prefix]\n   * Prefix for this list item (item number, bullet point, etc).\n   */\n  openListItem ({ prefix = '' } = {}) {\n    if (!(this._stackItem instanceof ListStackItem)) {\n      throw new Error('Can\\'t add a list item to something that is not a list! Check the formatter.');\n    }\n    const list = this._stackItem;\n    const prefixLength = Math.max(prefix.length, list.maxPrefixLength);\n    const maxLineLength = Math.max(20, list.inlineTextBuilder.maxLineLength - prefixLength);\n    this._stackItem = new ListItemStackItem(this.options, list, {\n      prefix: prefix,\n      maxLineLength: maxLineLength,\n      leadingLineBreaks: list.interRowLineBreaks\n    });\n  }\n\n  /**\n   * Finalize currently built list item, add it's content to the parent list.\n   */\n  closeListItem () {\n    const listItem = this._popStackItem();\n    const list = listItem.next;\n\n    const prefixLength = Math.max(listItem.prefix.length, list.maxPrefixLength);\n    const spacing = '\\n' + ' '.repeat(prefixLength);\n    const prefix = (list.prefixAlign === 'right')\n      ? listItem.prefix.padStart(prefixLength)\n      : listItem.prefix.padEnd(prefixLength);\n    const text = prefix + getText(listItem).replace(/\\n/g, spacing);\n\n    addText(\n      list,\n      text,\n      listItem.leadingLineBreaks,\n      Math.max(listItem.stashedLineBreaks, list.interRowLineBreaks)\n    );\n  }\n\n  /**\n   * Finalize currently built list, add it's content to the parent block.\n   *\n   * @param { object } param0\n   * Object holding the parameters of the list.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This list should have at least this number of line breaks to separate it from any following block.\n   */\n  closeList ({ trailingLineBreaks = 2 } = {}) {\n    const list = this._popStackItem();\n    const text = getText(list);\n    if (text) {\n      addText(this._stackItem, text, list.leadingLineBreaks, trailingLineBreaks);\n    }\n  }\n\n  /**\n   * Start building a table.\n   */\n  openTable () {\n    this._stackItem = new TableStackItem(this._stackItem);\n  }\n\n  /**\n   * Start building a table row.\n   */\n  openTableRow () {\n    if (!(this._stackItem instanceof TableStackItem)) {\n      throw new Error('Can\\'t add a table row to something that is not a table! Check the formatter.');\n    }\n    this._stackItem = new TableRowStackItem(this._stackItem);\n  }\n\n  /**\n   * Start building a table cell.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the cell.\n   *\n   * @param { number } [param0.maxColumnWidth]\n   * Wrap cell content to this width. Fall back to global wordwrap value if undefined.\n   */\n  openTableCell ({ maxColumnWidth = undefined } = {}) {\n    if (!(this._stackItem instanceof TableRowStackItem)) {\n      throw new Error('Can\\'t add a table cell to something that is not a table row! Check the formatter.');\n    }\n    this._stackItem = new TableCellStackItem(this.options, this._stackItem, maxColumnWidth);\n  }\n\n  /**\n   * Finalize currently built table cell and add it to parent table row's cells.\n   *\n   * @param { object } [param0]\n   * Object holding the parameters of the cell.\n   *\n   * @param { number } [param0.colspan] How many columns this cell should occupy.\n   * @param { number } [param0.rowspan] How many rows this cell should occupy.\n   */\n  closeTableCell ({ colspan = 1, rowspan = 1 } = {}) {\n    const cell = this._popStackItem();\n    const text = trimCharacter(getText(cell), '\\n');\n    cell.next.cells.push({ colspan: colspan, rowspan: rowspan, text: text });\n  }\n\n  /**\n   * Finalize currently built table row and add it to parent table's rows.\n   */\n  closeTableRow () {\n    const row = this._popStackItem();\n    row.next.rows.push(row.cells);\n  }\n\n  /**\n   * Finalize currently built table and add the rendered text to the parent block.\n   *\n   * @param { object } param0\n   * Object holding the parameters of the table.\n   *\n   * @param { TablePrinter } param0.tableToString\n   * A function to convert a table of stringified cells into a complete table.\n   *\n   * @param { number } [param0.leadingLineBreaks]\n   * This table should have at least this number of line breaks to separate if from any preceding block.\n   *\n   * @param { number } [param0.trailingLineBreaks]\n   * This table should have at least this number of line breaks to separate it from any following block.\n   */\n  closeTable ({ tableToString, leadingLineBreaks = 2, trailingLineBreaks = 2 }) {\n    const table = this._popStackItem();\n    const output = tableToString(table.rows);\n    if (output) {\n      addText(this._stackItem, output, leadingLineBreaks, trailingLineBreaks);\n    }\n  }\n\n  /**\n   * Return the rendered text content of this builder.\n   *\n   * @returns { string }\n   */\n  toString () {\n    return getText(this._stackItem.getRoot());\n    // There should only be the root item if everything is closed properly.\n  }\n\n}\n\nfunction getText (stackItem) {\n  if (!(\n    stackItem instanceof BlockStackItem\n    || stackItem instanceof ListItemStackItem\n    || stackItem instanceof TableCellStackItem\n  )) {\n    throw new Error('Only blocks, list items and table cells can be requested for text contents.');\n  }\n  return (stackItem.inlineTextBuilder.isEmpty())\n    ? stackItem.rawText\n    : stackItem.rawText + stackItem.inlineTextBuilder.toString();\n}\n\nfunction addText (stackItem, text, leadingLineBreaks, trailingLineBreaks) {\n  if (!(\n    stackItem instanceof BlockStackItem\n    || stackItem instanceof ListItemStackItem\n    || stackItem instanceof TableCellStackItem\n  )) {\n    throw new Error('Only blocks, list items and table cells can contain text.');\n  }\n  const parentText = getText(stackItem);\n  const lineBreaks = Math.max(stackItem.stashedLineBreaks, leadingLineBreaks);\n  stackItem.inlineTextBuilder.clear();\n  if (parentText) {\n    stackItem.rawText = parentText + '\\n'.repeat(lineBreaks) + text;\n  } else {\n    stackItem.rawText = text;\n    stackItem.leadingLineBreaks = lineBreaks;\n  }\n  stackItem.stashedLineBreaks = trailingLineBreaks;\n}\n\n/**\n * @param { string } str A string to transform.\n * @param { TransformerStackItem } transformer A transformer item (with possible continuation).\n * @returns { string }\n */\nfunction applyTransformer (str, transformer) {\n  return ((transformer) ? applyTransformer(transformer.transform(str), transformer.next) : str);\n}\n\n/**\n * Compile selectors into a decision tree,\n * return a function intended for batch processing.\n *\n * @param   { Options } [options = {}]   HtmlToText options (defaults, formatters, user options merged, deduplicated).\n * @returns { (html: string, metadata?: any) => string } Pre-configured converter function.\n * @static\n */\nfunction compile$1 (options = {}) {\n  const selectorsWithoutFormat = options.selectors.filter(s => !s.format);\n  if (selectorsWithoutFormat.length) {\n    throw new Error(\n      'Following selectors have no specified format: ' +\n      selectorsWithoutFormat.map(s => `\\`${s.selector}\\``).join(', ')\n    );\n  }\n  const picker = new DecisionTree(\n    options.selectors.map(s => [s.selector, s])\n  ).build(hp2Builder);\n\n  if (typeof options.encodeCharacters !== 'function') {\n    options.encodeCharacters = makeReplacerFromDict(options.encodeCharacters);\n  }\n\n  const baseSelectorsPicker = new DecisionTree(\n    options.baseElements.selectors.map((s, i) => [s, i + 1])\n  ).build(hp2Builder);\n  function findBaseElements (dom) {\n    return findBases(dom, options, baseSelectorsPicker);\n  }\n\n  const limitedWalk = limitedDepthRecursive(\n    options.limits.maxDepth,\n    recursiveWalk,\n    function (dom, builder) {\n      builder.addInline(options.limits.ellipsis || '');\n    }\n  );\n\n  return function (html, metadata = undefined) {\n    return process(html, metadata, options, picker, findBaseElements, limitedWalk);\n  };\n}\n\n\n/**\n * Convert given HTML according to preprocessed options.\n *\n * @param { string } html HTML content to convert.\n * @param { any } metadata Optional metadata for HTML document, for use in formatters.\n * @param { Options } options HtmlToText options (preprocessed).\n * @param { import('selderee').Picker<DomNode, TagDefinition> } picker\n * Tag definition picker for DOM nodes processing.\n * @param { (dom: DomNode[]) => DomNode[] } findBaseElements\n * Function to extract elements from HTML DOM\n * that will only be present in the output text.\n * @param { RecursiveCallback } walk Recursive callback.\n * @returns { string }\n */\nfunction process (html, metadata, options, picker, findBaseElements, walk) {\n  const maxInputLength = options.limits.maxInputLength;\n  if (maxInputLength && html && html.length > maxInputLength) {\n    console.warn(\n      `Input length ${html.length} is above allowed limit of ${maxInputLength}. Truncating without ellipsis.`\n    );\n    html = html.substring(0, maxInputLength);\n  }\n\n  const document = parseDocument(html, { decodeEntities: options.decodeEntities });\n  const bases = findBaseElements(document.children);\n  const builder = new BlockTextBuilder(options, picker, metadata);\n  walk(bases, builder);\n  return builder.toString();\n}\n\n\nfunction findBases (dom, options, baseSelectorsPicker) {\n  const results = [];\n\n  function recursiveWalk (walk, /** @type { DomNode[] } */ dom) {\n    dom = dom.slice(0, options.limits.maxChildNodes);\n    for (const elem of dom) {\n      if (elem.type !== 'tag') {\n        continue;\n      }\n      const pickedSelectorIndex = baseSelectorsPicker.pick1(elem);\n      if (pickedSelectorIndex > 0) {\n        results.push({ selectorIndex: pickedSelectorIndex, element: elem });\n      } else if (elem.children) {\n        walk(elem.children);\n      }\n      if (results.length >= options.limits.maxBaseElements) {\n        return;\n      }\n    }\n  }\n\n  const limitedWalk = limitedDepthRecursive(\n    options.limits.maxDepth,\n    recursiveWalk\n  );\n  limitedWalk(dom);\n\n  if (options.baseElements.orderBy !== 'occurrence') { // 'selectors'\n    results.sort((a, b) => a.selectorIndex - b.selectorIndex);\n  }\n  return (options.baseElements.returnDomByDefault && results.length === 0)\n    ? dom\n    : results.map(x => x.element);\n}\n\n/**\n * Function to walk through DOM nodes and accumulate their string representations.\n *\n * @param   { RecursiveCallback } walk    Recursive callback.\n * @param   { DomNode[] }         [dom]   Nodes array to process.\n * @param   { BlockTextBuilder }  builder Passed around to accumulate output text.\n * @private\n */\nfunction recursiveWalk (walk, dom, builder) {\n  if (!dom) { return; }\n\n  const options = builder.options;\n\n  const tooManyChildNodes = dom.length > options.limits.maxChildNodes;\n  if (tooManyChildNodes) {\n    dom = dom.slice(0, options.limits.maxChildNodes);\n    dom.push({\n      data: options.limits.ellipsis,\n      type: 'text'\n    });\n  }\n\n  for (const elem of dom) {\n    switch (elem.type) {\n      case 'text': {\n        builder.addInline(elem.data);\n        break;\n      }\n      case 'tag': {\n        const tagDefinition = builder.picker.pick1(elem);\n        const format = options.formatters[tagDefinition.format];\n        format(elem, walk, builder, tagDefinition.options || {});\n        break;\n      }\n    }\n  }\n\n  return;\n}\n\n/**\n * @param { Object<string,string | false> } dict\n * A dictionary where keys are characters to replace\n * and values are replacement strings.\n *\n * First code point from dict keys is used.\n * Compound emojis with ZWJ are not supported (not until Node 16).\n *\n * @returns { ((str: string) => string) | undefined }\n */\nfunction makeReplacerFromDict (dict) {\n  if (!dict || Object.keys(dict).length === 0) {\n    return undefined;\n  }\n  /** @type { [string, string][] } */\n  const entries = Object.entries(dict).filter(([, v]) => v !== false);\n  const regex = new RegExp(\n    entries\n      .map(([c]) => `(${unicodeEscape([...c][0])})`)\n      .join('|'),\n    'g'\n  );\n  const values = entries.map(([, v]) => v);\n  const replacer = (m, ...cgs) => values[cgs.findIndex(cg => cg)];\n  return (str) => str.replace(regex, replacer);\n}\n\n/**\n * Dummy formatter that discards the input and does nothing.\n *\n * @type { FormatCallback }\n */\nfunction formatSkip (elem, walk, builder, formatOptions) {\n  /* do nothing */\n}\n\n/**\n * Insert the given string literal inline instead of a tag.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineString (elem, walk, builder, formatOptions) {\n  builder.addLiteral(formatOptions.string || '');\n}\n\n/**\n * Insert a block with the given string literal instead of a tag.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockString (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.addLiteral(formatOptions.string || '');\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process an inline-level element.\n *\n * @type { FormatCallback }\n */\nfunction formatInline (elem, walk, builder, formatOptions) {\n  walk(elem.children, builder);\n}\n\n/**\n * Process a block-level container.\n *\n * @type { FormatCallback }\n */\nfunction formatBlock$1 (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\nfunction renderOpenTag (elem) {\n  const attrs = (elem.attribs && elem.attribs.length)\n    ? ' ' + Object.entries(elem.attribs)\n      .map(([k, v]) => ((v === '') ? k : `${k}=${v.replace(/\"/g, '&quot;')}`))\n      .join(' ')\n    : '';\n  return `<${elem.name}${attrs}>`;\n}\n\nfunction renderCloseTag (elem) {\n  return `</${elem.name}>`;\n}\n\n/**\n * Render an element as inline HTML tag, walk through it's children.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineTag (elem, walk, builder, formatOptions) {\n  builder.startNoWrap();\n  builder.addLiteral(renderOpenTag(elem));\n  builder.stopNoWrap();\n  walk(elem.children, builder);\n  builder.startNoWrap();\n  builder.addLiteral(renderCloseTag(elem));\n  builder.stopNoWrap();\n}\n\n/**\n * Render an element as HTML block bag, walk through it's children.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockTag (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.startNoWrap();\n  builder.addLiteral(renderOpenTag(elem));\n  builder.stopNoWrap();\n  walk(elem.children, builder);\n  builder.startNoWrap();\n  builder.addLiteral(renderCloseTag(elem));\n  builder.stopNoWrap();\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Render an element with all it's children as inline HTML.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineHtml (elem, walk, builder, formatOptions) {\n  builder.startNoWrap();\n  builder.addLiteral(\n    render(elem, { decodeEntities: builder.options.decodeEntities })\n  );\n  builder.stopNoWrap();\n}\n\n/**\n * Render an element with all it's children as HTML block.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockHtml (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.startNoWrap();\n  builder.addLiteral(\n    render(elem, { decodeEntities: builder.options.decodeEntities })\n  );\n  builder.stopNoWrap();\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Render inline element wrapped with given strings.\n *\n * @type { FormatCallback }\n */\nfunction formatInlineSurround (elem, walk, builder, formatOptions) {\n  builder.addLiteral(formatOptions.prefix || '');\n  walk(elem.children, builder);\n  builder.addLiteral(formatOptions.suffix || '');\n}\n\nvar genericFormatters = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  block: formatBlock$1,\n  blockHtml: formatBlockHtml,\n  blockString: formatBlockString,\n  blockTag: formatBlockTag,\n  inline: formatInline,\n  inlineHtml: formatInlineHtml,\n  inlineString: formatInlineString,\n  inlineSurround: formatInlineSurround,\n  inlineTag: formatInlineTag,\n  skip: formatSkip\n});\n\nfunction getRow (matrix, j) {\n  if (!matrix[j]) { matrix[j] = []; }\n  return matrix[j];\n}\n\nfunction findFirstVacantIndex (row, x = 0) {\n  while (row[x]) { x++; }\n  return x;\n}\n\nfunction transposeInPlace (matrix, maxSize) {\n  for (let i = 0; i < maxSize; i++) {\n    const rowI = getRow(matrix, i);\n    for (let j = 0; j < i; j++) {\n      const rowJ = getRow(matrix, j);\n      if (rowI[j] || rowJ[i]) {\n        const temp = rowI[j];\n        rowI[j] = rowJ[i];\n        rowJ[i] = temp;\n      }\n    }\n  }\n}\n\nfunction putCellIntoLayout (cell, layout, baseRow, baseCol) {\n  for (let r = 0; r < cell.rowspan; r++) {\n    const layoutRow = getRow(layout, baseRow + r);\n    for (let c = 0; c < cell.colspan; c++) {\n      layoutRow[baseCol + c] = cell;\n    }\n  }\n}\n\nfunction getOrInitOffset (offsets, index) {\n  if (offsets[index] === undefined) {\n    offsets[index] = (index === 0) ? 0 : 1 + getOrInitOffset(offsets, index - 1);\n  }\n  return offsets[index];\n}\n\nfunction updateOffset (offsets, base, span, value) {\n  offsets[base + span] = Math.max(\n    getOrInitOffset(offsets, base + span),\n    getOrInitOffset(offsets, base) + value\n  );\n}\n\n/**\n * Render a table into a string.\n * Cells can contain multiline text and span across multiple rows and columns.\n *\n * Modifies cells to add lines array.\n *\n * @param { TablePrinterCell[][] } tableRows Table to render.\n * @param { number } rowSpacing Number of spaces between columns.\n * @param { number } colSpacing Number of empty lines between rows.\n * @returns { string }\n */\nfunction tableToString (tableRows, rowSpacing, colSpacing) {\n  const layout = [];\n  let colNumber = 0;\n  const rowNumber = tableRows.length;\n  const rowOffsets = [0];\n  // Fill the layout table and row offsets row-by-row.\n  for (let j = 0; j < rowNumber; j++) {\n    const layoutRow = getRow(layout, j);\n    const cells = tableRows[j];\n    let x = 0;\n    for (let i = 0; i < cells.length; i++) {\n      const cell = cells[i];\n      x = findFirstVacantIndex(layoutRow, x);\n      putCellIntoLayout(cell, layout, j, x);\n      x += cell.colspan;\n      cell.lines = cell.text.split('\\n');\n      const cellHeight = cell.lines.length;\n      updateOffset(rowOffsets, j, cell.rowspan, cellHeight + rowSpacing);\n    }\n    colNumber = (layoutRow.length > colNumber) ? layoutRow.length : colNumber;\n  }\n\n  transposeInPlace(layout, (rowNumber > colNumber) ? rowNumber : colNumber);\n\n  const outputLines = [];\n  const colOffsets = [0];\n  // Fill column offsets and output lines column-by-column.\n  for (let x = 0; x < colNumber; x++) {\n    let y = 0;\n    let cell;\n    const rowsInThisColumn = Math.min(rowNumber, layout[x].length);\n    while (y < rowsInThisColumn) {\n      cell = layout[x][y];\n      if (cell) {\n        if (!cell.rendered) {\n          let cellWidth = 0;\n          for (let j = 0; j < cell.lines.length; j++) {\n            const line = cell.lines[j];\n            const lineOffset = rowOffsets[y] + j;\n            outputLines[lineOffset] = (outputLines[lineOffset] || '').padEnd(colOffsets[x]) + line;\n            cellWidth = (line.length > cellWidth) ? line.length : cellWidth;\n          }\n          updateOffset(colOffsets, x, cell.colspan, cellWidth + colSpacing);\n          cell.rendered = true;\n        }\n        y += cell.rowspan;\n      } else {\n        const lineOffset = rowOffsets[y];\n        outputLines[lineOffset] = (outputLines[lineOffset] || '');\n        y++;\n      }\n    }\n  }\n\n  return outputLines.join('\\n');\n}\n\n/**\n * Process a line-break.\n *\n * @type { FormatCallback }\n */\nfunction formatLineBreak (elem, walk, builder, formatOptions) {\n  builder.addLineBreak();\n}\n\n/**\n * Process a `wbr` tag (word break opportunity).\n *\n * @type { FormatCallback }\n */\nfunction formatWbr (elem, walk, builder, formatOptions) {\n  builder.addWordBreakOpportunity();\n}\n\n/**\n * Process a horizontal line.\n *\n * @type { FormatCallback }\n */\nfunction formatHorizontalLine (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  builder.addInline('-'.repeat(formatOptions.length || builder.options.wordwrap || 40));\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a paragraph.\n *\n * @type { FormatCallback }\n */\nfunction formatParagraph (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a preformatted content.\n *\n * @type { FormatCallback }\n */\nfunction formatPre (elem, walk, builder, formatOptions) {\n  builder.openBlock({\n    isPre: true,\n    leadingLineBreaks: formatOptions.leadingLineBreaks || 2\n  });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a heading.\n *\n * @type { FormatCallback }\n */\nfunction formatHeading (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks || 2 });\n  if (formatOptions.uppercase !== false) {\n    builder.pushWordTransform(str => str.toUpperCase());\n    walk(elem.children, builder);\n    builder.popWordTransform();\n  } else {\n    walk(elem.children, builder);\n  }\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks || 2 });\n}\n\n/**\n * Process a blockquote.\n *\n * @type { FormatCallback }\n */\nfunction formatBlockquote (elem, walk, builder, formatOptions) {\n  builder.openBlock({\n    leadingLineBreaks: formatOptions.leadingLineBreaks || 2,\n    reservedLineLength: 2\n  });\n  walk(elem.children, builder);\n  builder.closeBlock({\n    trailingLineBreaks: formatOptions.trailingLineBreaks || 2,\n    blockTransform: str => ((formatOptions.trimEmptyLines !== false) ? trimCharacter(str, '\\n') : str)\n      .split('\\n')\n      .map(line => '> ' + line)\n      .join('\\n')\n  });\n}\n\nfunction withBrackets (str, brackets) {\n  if (!brackets) { return str; }\n\n  const lbr = (typeof brackets[0] === 'string')\n    ? brackets[0]\n    : '[';\n  const rbr = (typeof brackets[1] === 'string')\n    ? brackets[1]\n    : ']';\n  return lbr + str + rbr;\n}\n\nfunction pathRewrite (path, rewriter, baseUrl, metadata, elem) {\n  const modifiedPath = (typeof rewriter === 'function')\n    ? rewriter(path, metadata, elem)\n    : path;\n  return (modifiedPath[0] === '/' && baseUrl)\n    ? trimCharacterEnd(baseUrl, '/') + modifiedPath\n    : modifiedPath;\n}\n\n/**\n * Process an image.\n *\n * @type { FormatCallback }\n */\nfunction formatImage (elem, walk, builder, formatOptions) {\n  const attribs = elem.attribs || {};\n  const alt = (attribs.alt)\n    ? attribs.alt\n    : '';\n  const src = (!attribs.src)\n    ? ''\n    : pathRewrite(attribs.src, formatOptions.pathRewrite, formatOptions.baseUrl, builder.metadata, elem);\n  const text = (!src)\n    ? alt\n    : (!alt)\n      ? withBrackets(src, formatOptions.linkBrackets)\n      : alt + ' ' + withBrackets(src, formatOptions.linkBrackets);\n\n  builder.addInline(text, { noWordTransform: true });\n}\n\n// a img baseUrl\n// a img pathRewrite\n// a img linkBrackets\n\n// a     ignoreHref: false\n//            ignoreText ?\n// a     noAnchorUrl: true\n//            can be replaced with selector\n// a     hideLinkHrefIfSameAsText: false\n//            how to compare, what to show (text, href, normalized) ?\n// a     mailto protocol removed without options\n\n// a     protocols: mailto, tel, ...\n//            can be matched with selector?\n\n// anchors, protocols - only if no pathRewrite fn is provided\n\n// normalize-url ?\n\n// a\n// a[href^=\"#\"] - format:skip by default\n// a[href^=\"mailto:\"] - ?\n\n/**\n * Process an anchor.\n *\n * @type { FormatCallback }\n */\nfunction formatAnchor (elem, walk, builder, formatOptions) {\n  function getHref () {\n    if (formatOptions.ignoreHref) { return ''; }\n    if (!elem.attribs || !elem.attribs.href) { return ''; }\n    let href = elem.attribs.href.replace(/^mailto:/, '');\n    if (formatOptions.noAnchorUrl && href[0] === '#') { return ''; }\n    href = pathRewrite(href, formatOptions.pathRewrite, formatOptions.baseUrl, builder.metadata, elem);\n    return href;\n  }\n  const href = getHref();\n  if (!href) {\n    walk(elem.children, builder);\n  } else {\n    let text = '';\n    builder.pushWordTransform(\n      str => {\n        if (str) { text += str; }\n        return str;\n      }\n    );\n    walk(elem.children, builder);\n    builder.popWordTransform();\n\n    const hideSameLink = formatOptions.hideLinkHrefIfSameAsText && href === text;\n    if (!hideSameLink) {\n      builder.addInline(\n        (!text)\n          ? href\n          : ' ' + withBrackets(href, formatOptions.linkBrackets),\n        { noWordTransform: true }\n      );\n    }\n  }\n}\n\n/**\n * @param { DomNode }           elem               List items with their prefixes.\n * @param { RecursiveCallback } walk               Recursive callback to process child nodes.\n * @param { BlockTextBuilder }  builder            Passed around to accumulate output text.\n * @param { FormatOptions }     formatOptions      Options specific to a formatter.\n * @param { () => string }      nextPrefixCallback Function that returns increasing index each time it is called.\n */\nfunction formatList (elem, walk, builder, formatOptions, nextPrefixCallback) {\n  const isNestedList = get(elem, ['parent', 'name']) === 'li';\n\n  // With Roman numbers, index length is not as straightforward as with Arabic numbers or letters,\n  // so the dumb length comparison is the most robust way to get the correct value.\n  let maxPrefixLength = 0;\n  const listItems = (elem.children || [])\n    // it might be more accurate to check only for html spaces here, but no significant benefit\n    .filter(child => child.type !== 'text' || !/^\\s*$/.test(child.data))\n    .map(function (child) {\n      if (child.name !== 'li') {\n        return { node: child, prefix: '' };\n      }\n      const prefix = (isNestedList)\n        ? nextPrefixCallback().trimStart()\n        : nextPrefixCallback();\n      if (prefix.length > maxPrefixLength) { maxPrefixLength = prefix.length; }\n      return { node: child, prefix: prefix };\n    });\n  if (!listItems.length) { return; }\n\n  builder.openList({\n    interRowLineBreaks: 1,\n    leadingLineBreaks: isNestedList ? 1 : (formatOptions.leadingLineBreaks || 2),\n    maxPrefixLength: maxPrefixLength,\n    prefixAlign: 'left'\n  });\n\n  for (const { node, prefix } of listItems) {\n    builder.openListItem({ prefix: prefix });\n    walk([node], builder);\n    builder.closeListItem();\n  }\n\n  builder.closeList({ trailingLineBreaks: isNestedList ? 1 : (formatOptions.trailingLineBreaks || 2) });\n}\n\n/**\n * Process an unordered list.\n *\n * @type { FormatCallback }\n */\nfunction formatUnorderedList (elem, walk, builder, formatOptions) {\n  const prefix = formatOptions.itemPrefix || ' * ';\n  return formatList(elem, walk, builder, formatOptions, () => prefix);\n}\n\n/**\n * Process an ordered list.\n *\n * @type { FormatCallback }\n */\nfunction formatOrderedList (elem, walk, builder, formatOptions) {\n  let nextIndex = Number(elem.attribs.start || '1');\n  const indexFunction = getOrderedListIndexFunction(elem.attribs.type);\n  const nextPrefixCallback = () => ' ' + indexFunction(nextIndex++) + '. ';\n  return formatList(elem, walk, builder, formatOptions, nextPrefixCallback);\n}\n\n/**\n * Return a function that can be used to generate index markers of a specified format.\n *\n * @param   { string } [olType='1'] Marker type.\n * @returns { (i: number) => string }\n */\nfunction getOrderedListIndexFunction (olType = '1') {\n  switch (olType) {\n    case 'a': return (i) => numberToLetterSequence(i, 'a');\n    case 'A': return (i) => numberToLetterSequence(i, 'A');\n    case 'i': return (i) => numberToRoman(i).toLowerCase();\n    case 'I': return (i) => numberToRoman(i);\n    case '1':\n    default: return (i) => (i).toString();\n  }\n}\n\n/**\n * Given a list of class and ID selectors (prefixed with '.' and '#'),\n * return them as separate lists of names without prefixes.\n *\n * @param { string[] } selectors Class and ID selectors (`[\".class\", \"#id\"]` etc).\n * @returns { { classes: string[], ids: string[] } }\n */\nfunction splitClassesAndIds (selectors) {\n  const classes = [];\n  const ids = [];\n  for (const selector of selectors) {\n    if (selector.startsWith('.')) {\n      classes.push(selector.substring(1));\n    } else if (selector.startsWith('#')) {\n      ids.push(selector.substring(1));\n    }\n  }\n  return { classes: classes, ids: ids };\n}\n\nfunction isDataTable (attr, tables) {\n  if (tables === true) { return true; }\n  if (!attr) { return false; }\n\n  const { classes, ids } = splitClassesAndIds(tables);\n  const attrClasses = (attr['class'] || '').split(' ');\n  const attrIds = (attr['id'] || '').split(' ');\n\n  return attrClasses.some(x => classes.includes(x)) || attrIds.some(x => ids.includes(x));\n}\n\n/**\n * Process a table (either as a container or as a data table, depending on options).\n *\n * @type { FormatCallback }\n */\nfunction formatTable (elem, walk, builder, formatOptions) {\n  return isDataTable(elem.attribs, builder.options.tables)\n    ? formatDataTable(elem, walk, builder, formatOptions)\n    : formatBlock(elem, walk, builder, formatOptions);\n}\n\nfunction formatBlock (elem, walk, builder, formatOptions) {\n  builder.openBlock({ leadingLineBreaks: formatOptions.leadingLineBreaks });\n  walk(elem.children, builder);\n  builder.closeBlock({ trailingLineBreaks: formatOptions.trailingLineBreaks });\n}\n\n/**\n * Process a data table.\n *\n * @type { FormatCallback }\n */\nfunction formatDataTable (elem, walk, builder, formatOptions) {\n  builder.openTable();\n  elem.children.forEach(walkTable);\n  builder.closeTable({\n    tableToString: (rows) => tableToString(rows, formatOptions.rowSpacing ?? 0, formatOptions.colSpacing ?? 3),\n    leadingLineBreaks: formatOptions.leadingLineBreaks,\n    trailingLineBreaks: formatOptions.trailingLineBreaks\n  });\n\n  function formatCell (cellNode) {\n    const colspan = +get(cellNode, ['attribs', 'colspan']) || 1;\n    const rowspan = +get(cellNode, ['attribs', 'rowspan']) || 1;\n    builder.openTableCell({ maxColumnWidth: formatOptions.maxColumnWidth });\n    walk(cellNode.children, builder);\n    builder.closeTableCell({ colspan: colspan, rowspan: rowspan });\n  }\n\n  function walkTable (elem) {\n    if (elem.type !== 'tag') { return; }\n\n    const formatHeaderCell = (formatOptions.uppercaseHeaderCells !== false)\n      ? (cellNode) => {\n        builder.pushWordTransform(str => str.toUpperCase());\n        formatCell(cellNode);\n        builder.popWordTransform();\n      }\n      : formatCell;\n\n    switch (elem.name) {\n      case 'thead':\n      case 'tbody':\n      case 'tfoot':\n      case 'center':\n        elem.children.forEach(walkTable);\n        return;\n\n      case 'tr': {\n        builder.openTableRow();\n        for (const childOfTr of elem.children) {\n          if (childOfTr.type !== 'tag') { continue; }\n          switch (childOfTr.name) {\n            case 'th': {\n              formatHeaderCell(childOfTr);\n              break;\n            }\n            case 'td': {\n              formatCell(childOfTr);\n              break;\n            }\n              // do nothing\n          }\n        }\n        builder.closeTableRow();\n        break;\n      }\n        // do nothing\n    }\n  }\n}\n\nvar textFormatters = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  anchor: formatAnchor,\n  blockquote: formatBlockquote,\n  dataTable: formatDataTable,\n  heading: formatHeading,\n  horizontalLine: formatHorizontalLine,\n  image: formatImage,\n  lineBreak: formatLineBreak,\n  orderedList: formatOrderedList,\n  paragraph: formatParagraph,\n  pre: formatPre,\n  table: formatTable,\n  unorderedList: formatUnorderedList,\n  wbr: formatWbr\n});\n\n/**\n * Default options.\n *\n * @constant\n * @type { Options }\n * @default\n * @private\n */\nconst DEFAULT_OPTIONS = {\n  baseElements: {\n    selectors: [ 'body' ],\n    orderBy: 'selectors', // 'selectors' | 'occurrence'\n    returnDomByDefault: true\n  },\n  decodeEntities: true,\n  encodeCharacters: {},\n  formatters: {},\n  limits: {\n    ellipsis: '...',\n    maxBaseElements: undefined,\n    maxChildNodes: undefined,\n    maxDepth: undefined,\n    maxInputLength: (1 << 24) // 16_777_216\n  },\n  longWordSplit: {\n    forceWrapOnLimit: false,\n    wrapCharacters: []\n  },\n  preserveNewlines: false,\n  selectors: [\n    { selector: '*', format: 'inline' },\n    {\n      selector: 'a',\n      format: 'anchor',\n      options: {\n        baseUrl: null,\n        hideLinkHrefIfSameAsText: false,\n        ignoreHref: false,\n        linkBrackets: ['[', ']'],\n        noAnchorUrl: true\n      }\n    },\n    { selector: 'article', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'aside', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'blockquote',\n      format: 'blockquote',\n      options: { leadingLineBreaks: 2, trailingLineBreaks: 2, trimEmptyLines: true }\n    },\n    { selector: 'br', format: 'lineBreak' },\n    { selector: 'div', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'footer', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'form', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'h1', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h2', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h3', format: 'heading', options: { leadingLineBreaks: 3, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h4', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h5', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'h6', format: 'heading', options: { leadingLineBreaks: 2, trailingLineBreaks: 2, uppercase: true } },\n    { selector: 'header', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'hr',\n      format: 'horizontalLine',\n      options: { leadingLineBreaks: 2, length: undefined, trailingLineBreaks: 2 }\n    },\n    {\n      selector: 'img',\n      format: 'image',\n      options: { baseUrl: null, linkBrackets: ['[', ']'] }\n    },\n    { selector: 'main', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    { selector: 'nav', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'ol',\n      format: 'orderedList',\n      options: { leadingLineBreaks: 2, trailingLineBreaks: 2 }\n    },\n    { selector: 'p', format: 'paragraph', options: { leadingLineBreaks: 2, trailingLineBreaks: 2 } },\n    { selector: 'pre', format: 'pre', options: { leadingLineBreaks: 2, trailingLineBreaks: 2 } },\n    { selector: 'section', format: 'block', options: { leadingLineBreaks: 1, trailingLineBreaks: 1 } },\n    {\n      selector: 'table',\n      format: 'table',\n      options: {\n        colSpacing: 3,\n        leadingLineBreaks: 2,\n        maxColumnWidth: 60,\n        rowSpacing: 0,\n        trailingLineBreaks: 2,\n        uppercaseHeaderCells: true\n      }\n    },\n    {\n      selector: 'ul',\n      format: 'unorderedList',\n      options: { itemPrefix: ' * ', leadingLineBreaks: 2, trailingLineBreaks: 2 }\n    },\n    { selector: 'wbr', format: 'wbr' },\n  ],\n  tables: [], // deprecated\n  whitespaceCharacters: ' \\t\\r\\n\\f\\u200b',\n  wordwrap: 80\n};\n\nconst concatMerge = (acc, src, options) => [...acc, ...src];\nconst overwriteMerge = (acc, src, options) => [...src];\nconst selectorsMerge = (acc, src, options) => (\n  (acc.some(s => typeof s === 'object'))\n    ? concatMerge(acc, src) // selectors\n    : overwriteMerge(acc, src) // baseElements.selectors\n);\n\n/**\n * Preprocess options, compile selectors into a decision tree,\n * return a function intended for batch processing.\n *\n * @param   { Options } [options = {}]   HtmlToText options.\n * @returns { (html: string, metadata?: any) => string } Pre-configured converter function.\n * @static\n */\nfunction compile (options = {}) {\n  options = merge(\n    DEFAULT_OPTIONS,\n    options,\n    {\n      arrayMerge: overwriteMerge,\n      customMerge: (key) => ((key === 'selectors') ? selectorsMerge : undefined)\n    }\n  );\n  options.formatters = Object.assign({}, genericFormatters, textFormatters, options.formatters);\n  options.selectors = mergeDuplicatesPreferLast(options.selectors, (s => s.selector));\n\n  handleDeprecatedOptions(options);\n\n  return compile$1(options);\n}\n\n/**\n * Convert given HTML content to plain text string.\n *\n * @param   { string }  html           HTML content to convert.\n * @param   { Options } [options = {}] HtmlToText options.\n * @param   { any }     [metadata]     Optional metadata for HTML document, for use in formatters.\n * @returns { string }                 Plain text string.\n * @static\n *\n * @example\n * const { convert } = require('html-to-text');\n * const text = convert('<h1>Hello World</h1>', {\n *   wordwrap: 130\n * });\n * console.log(text); // HELLO WORLD\n */\nfunction convert (html, options = {}, metadata = undefined) {\n  return compile(options)(html, metadata);\n}\n\n/**\n * Map previously existing and now deprecated options to the new options layout.\n * This is a subject for cleanup in major releases.\n *\n * @param { Options } options HtmlToText options.\n */\nfunction handleDeprecatedOptions (options) {\n  if (options.tags) {\n    const tagDefinitions = Object.entries(options.tags).map(\n      ([selector, definition]) => ({ ...definition, selector: selector || '*' })\n    );\n    options.selectors.push(...tagDefinitions);\n    options.selectors = mergeDuplicatesPreferLast(options.selectors, (s => s.selector));\n  }\n\n  function set (obj, path, value) {\n    const valueKey = path.pop();\n    for (const key of path) {\n      let nested = obj[key];\n      if (!nested) {\n        nested = {};\n        obj[key] = nested;\n      }\n      obj = nested;\n    }\n    obj[valueKey] = value;\n  }\n\n  if (options['baseElement']) {\n    const baseElement = options['baseElement'];\n    set(\n      options,\n      ['baseElements', 'selectors'],\n      (Array.isArray(baseElement) ? baseElement : [baseElement])\n    );\n  }\n  if (options['returnDomByDefault'] !== undefined) {\n    set(options, ['baseElements', 'returnDomByDefault'], options['returnDomByDefault']);\n  }\n\n  for (const definition of options.selectors) {\n    if (definition.format === 'anchor' && get(definition, ['options', 'noLinkBrackets'])) {\n      set(definition, ['options', 'linkBrackets'], false);\n    }\n  }\n}\n\nexport { compile, convert, convert as htmlToText };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA;;;;;;;;;CASC,GACD,SAAS,sBAAuB,CAAC,EAAE,CAAC,EAAE,IAAI,IAAM,SAAS;IACvD,IAAI,MAAM,WAAW;QACnB,MAAM,KAAK,SAAU,GAAG,IAAI;YAAI,OAAO,EAAE,OAAO;QAAO;QACvD,OAAO;IACT;IACA,IAAI,KAAK,GAAG;QACV,OAAO,SAAU,GAAG,IAAI;YAAI,OAAO,EAAE,sBAAsB,IAAI,GAAG,GAAG,OAAO;QAAO;IACrF;IACA,OAAO;AACT;AAEA;;;;;;;CAOC,GACD,SAAS,cAAe,GAAG,EAAE,IAAI;IAC/B,IAAI,QAAQ;IACZ,IAAI,MAAM,IAAI,MAAM;IACpB,MAAO,QAAQ,OAAO,GAAG,CAAC,MAAM,KAAK,KAAM;QAAE,EAAE;IAAO;IACtD,MAAO,MAAM,SAAS,GAAG,CAAC,MAAM,EAAE,KAAK,KAAM;QAAE,EAAE;IAAK;IACtD,OAAO,AAAC,QAAQ,KAAK,MAAM,IAAI,MAAM,GACjC,IAAI,SAAS,CAAC,OAAO,OACrB;AACN;AAEA;;;;;;;CAOC,GACD,SAAS,iBAAkB,GAAG,EAAE,IAAI;IAClC,IAAI,MAAM,IAAI,MAAM;IACpB,MAAO,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE,KAAK,KAAM;QAAE,EAAE;IAAK;IAClD,OAAO,AAAC,MAAM,IAAI,MAAM,GACpB,IAAI,SAAS,CAAC,GAAG,OACjB;AACN;AAEA;;;;;;CAMC,GACD,SAAS,cAAe,GAAG;IACzB,OAAO,IAAI,OAAO,CAAC,WAAW,CAAA,IAAK,QAAQ,EAAE,UAAU,GAAG,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AACrF;AAEA;;;;;;;;;CASC,GACD,SAAS,0BAA2B,KAAK,EAAE,MAAM;IAC/C,MAAM,MAAM,IAAI;IAChB,IAAK,IAAI,IAAI,MAAM,MAAM,EAAE,MAAM,GAAI;QACnC,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,MAAM,MAAM,OAAO;QACnB,IAAI,GAAG,CACL,KACA,AAAC,IAAI,GAAG,CAAC,OACL,IAAA,yMAAK,EAAC,MAAM,IAAI,GAAG,CAAC,MAAM;YAAE,YAAY;QAAiB,KACzD;IAER;IACA,OAAO;WAAI,IAAI,MAAM;KAAG,CAAC,OAAO;AAClC;AAEA,MAAM,mBAAmB,CAAC,KAAK,KAAK,UAAY;WAAI;KAAI;AAExD;;;;;;CAMC,GACD,SAAS,IAAK,GAAG,EAAE,IAAI;IACrB,KAAK,MAAM,OAAO,KAAM;QACtB,IAAI,CAAC,KAAK;YAAE,OAAO;QAAW;QAC9B,MAAM,GAAG,CAAC,IAAI;IAChB;IACA,OAAO;AACT;AAEA;;;;;;;;;CASC,GACD,SAAS,uBAAwB,GAAG,EAAE,WAAW,GAAG,EAAE,OAAO,EAAE;IAC7D,MAAM,SAAS,EAAE;IACjB,GAAG;QACD,OAAO;QACP,OAAO,IAAI,CAAC,MAAM;QAClB,MAAM,AAAC,MAAM,QAAS,GAAG,gBAAgB;IAC3C,QAAS,MAAM,EAAG;IAClB,MAAM,WAAW,SAAS,UAAU,CAAC;IACrC,OAAO,OACJ,OAAO,GACP,GAAG,CAAC,CAAA,IAAK,OAAO,YAAY,CAAC,WAAW,IACxC,IAAI,CAAC;AACV;AAEA,MAAM,IAAI;IAAC;IAAK;IAAK;IAAK;CAAI;AAC9B,MAAM,IAAI;IAAC;IAAK;IAAK;CAAI;AAEzB;;;;;CAKC,GACD,SAAS,cAAe,GAAG;IACzB,OAAO;WAAI,AAAC,MAAO;KAAG,CACnB,GAAG,CAAC,CAAA,IAAK,CAAC,GACV,OAAO,GACP,GAAG,CAAC,CAAC,GAAG,IAAO,AAAC,IAAI,IAAI,IACrB,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,KACtC,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,GAClC,OAAO,GACP,IAAI,CAAC;AACV;AAEA;;CAEC,GACD,MAAM;IACJ;;;;;;;GAOC,GACD,YAAa,OAAO,EAAE,gBAAgB,SAAS,CAAE;QAC/C,yBAAyB,GACzB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,yBAAyB,GACzB,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC,aAAa,GAAG,iBAAiB,QAAQ,QAAQ,IAAI,OAAO,SAAS;QAC1E,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa;QAChD,IAAI,CAAC,cAAc,GAAG,IAAI,SAAS;YAAC;YAAiB;SAAiB,KAAK,EAAE;QAC7E,IAAI,CAAC,gBAAgB,GAAG,IAAI,SAAS;YAAC;YAAiB;SAAmB,KAAK;QAE/E,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,oBAAoB,GAAG;IAC9B;IAEA;;;;;GAKC,GACD,SAAU,IAAI,EAAE,SAAS,KAAK,EAAE;QAC9B,IAAI,IAAI,CAAC,sBAAsB,IAAI,KAAK,CAAC,QAAQ;YAC/C,IAAI,CAAC,YAAY;QACnB;QACA,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK;QAClD,MAAM,OAAO,KAAK,MAAM,GAAG,CAAC,cAAc,IAAI,CAAC;QAC/C,IAAI,AAAC,QAAQ,IAAI,CAAC,sBAAsB,IAAK,QAAQ;YAEnD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,sBAAsB,IAAI;QAEjC,OAAO;YAEL,kEAAkE;YAClE,MAAM,CAAC,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC;YAC5C,IAAI,CAAC,aAAa;gBAAE,IAAI,CAAC,YAAY;YAAI;YACzC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,sBAAsB,IAAI,MAAM,MAAM;YAC3C,KAAK,MAAM,QAAQ,KAAM;gBACvB,IAAI,CAAC,YAAY;gBACjB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBACxB,IAAI,CAAC,sBAAsB,IAAI,KAAK,MAAM;YAC5C;QAEF;IACF;IAEA;;;;;GAKC,GACD,UAAW;QACT,MAAM,WAAW,IAAI,CAAC,aAAa,CAAC,GAAG;QACvC,IAAI,aAAa,WAAW;YAC1B,MAAM,cAAc,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK;YAClD,MAAM,OAAO,SAAS,MAAM,GAAG,CAAC,cAAc,IAAI,CAAC;YACnD,IAAI,CAAC,sBAAsB,IAAI;QACjC;QACA,OAAO;IACT;IAEA;;;;;;GAMC,GACD,WAAY,IAAI,EAAE,SAAS,KAAK,EAAE;QAChC,IAAI,IAAI,CAAC,oBAAoB,IAAI,KAAK,MAAM,GAAG,IAAI,CAAC,sBAAsB,EAAE;YAC1E,IAAI,CAAC,QAAQ,CAAC,MAAM;YACpB,IAAI,CAAC,oBAAoB,GAAG;QAC9B,OAAO;YACL,MAAM,WAAW,IAAI,CAAC,OAAO;YAC7B,IAAI,CAAC,QAAQ,CAAC,AAAC,WAAY,SAAS,MAAM,CAAC,QAAQ,MAAM;QAC3D;IACF;IAEA;;;;GAIC,GACD,aAAc,IAAI,CAAC,EAAE;QACnB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa;QAClC,IAAI,IAAI,GAAG;YACT,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,IAAI,CAAC;gBAAE,QAAQ,IAAI;YAAE,GAAG,IAAM,EAAE;QAC3D;QACA,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa;IAClD;IAEA;;;;GAIC,GACD,UAAW;QACT,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,KACtB,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK;IACvC;IAEA,QAAS;QACP,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;QAC5B,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,aAAa;IAClD;IAEA;;;;GAIC,GACD,WAAY;QACV,OAAO;eAAI,IAAI,CAAC,KAAK;YAAE,IAAI,CAAC,aAAa;SAAC,CACvC,GAAG,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,MACxB,IAAI,CAAC;IACV;IAEA;;;;;;;GAOC,GACD,cAAe,IAAI,EAAE;QACnB,MAAM,QAAQ,EAAE;QAChB,IAAI,MAAM;QACV,MAAO,KAAK,MAAM,GAAG,IAAI,CAAC,aAAa,CAAE;YAEvC,MAAM,YAAY,KAAK,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa;YACtD,MAAM,iBAAiB,KAAK,SAAS,CAAC,IAAI,CAAC,aAAa;YAExD,MAAM,aAAa,UAAU,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI;YAEjE,IAAI,aAAa,CAAC,GAAG;gBAEnB,OAAO,UAAU,SAAS,CAAC,aAAa,KAAK;gBAC7C,MAAM,IAAI,CAAC,UAAU,SAAS,CAAC,GAAG,aAAa;YAEjD,OAAO;gBAEL;gBACA,IAAI,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;oBAEpC,OAAO,YAAY;gBAErB,OAAO;oBAEL,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACzB,MAAM,IAAI,CAAC;wBACX,OAAO;wBACP,IAAI,KAAK,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE;4BACpC;wBACF;oBACF,OAAO;wBACL,OAAO,YAAY;oBACrB;oBACA;gBAEF;YAEF;QAEF;QACA,MAAM,IAAI,CAAC,OAAO,8BAA8B;QAChD,OAAO;IACT;AACF;AAEA,uCAAuC,GAGvC,MAAM;IACJ,YAAa,OAAO,IAAI,CAAE;QAAE,IAAI,CAAC,IAAI,GAAG;IAAM;IAE9C,UAAW;QAAE,OAAO,AAAC,IAAI,CAAC,IAAI,GAAI,IAAI,CAAC,IAAI,GAAG,IAAI;IAAE;AACtD;AAEA,MAAM,uBAAuB;IAC3B,YAAa,OAAO,EAAE,OAAO,IAAI,EAAE,oBAAoB,CAAC,EAAE,gBAAgB,SAAS,CAAE;QACnF,KAAK,CAAC;QACN,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,iBAAiB,GAAG,IAAI,kBAAkB,SAAS;QACxD,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK;QAC/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,KAAK,QAAQ;IACvC;AACF;AAEA,MAAM,sBAAsB;IAC1B,YACE,OAAO,EACP,OAAO,IAAI,EACX,EACE,qBAAqB,CAAC,EACtB,oBAAoB,CAAC,EACrB,gBAAgB,SAAS,EACzB,kBAAkB,CAAC,EACnB,cAAc,MAAM,EACrB,GAAG,CAAC,CAAC,CACN;QACA,KAAK,CAAC,SAAS,MAAM,mBAAmB;QACxC,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,kBAAkB,GAAG;IAC5B;AACF;AAEA,MAAM,0BAA0B;IAC9B,YACE,OAAO,EACP,OAAO,IAAI,EACX,EACE,oBAAoB,CAAC,EACrB,gBAAgB,SAAS,EACzB,SAAS,EAAE,EACZ,GAAG,CAAC,CAAC,CACN;QACA,KAAK,CAAC,SAAS,MAAM,mBAAmB;QACxC,IAAI,CAAC,MAAM,GAAG;IAChB;AACF;AAEA,MAAM,uBAAuB;IAC3B,YAAa,OAAO,IAAI,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK;QAC/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,KAAK,QAAQ;IACvC;AACF;AAEA,MAAM,0BAA0B;IAC9B,YAAa,OAAO,IAAI,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK;QAC/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,KAAK,QAAQ;IACvC;AACF;AAEA,MAAM,2BAA2B;IAC/B,YAAa,OAAO,EAAE,OAAO,IAAI,EAAE,iBAAiB,SAAS,CAAE;QAC7D,KAAK,CAAC;QACN,IAAI,CAAC,iBAAiB,GAAG,IAAI,kBAAkB,SAAS;QACxD,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,KAAK;QAC/B,IAAI,CAAC,QAAQ,GAAG,QAAQ,KAAK,QAAQ;IACvC;AACF;AAEA,MAAM,6BAA6B;IACjC,YAAa,OAAO,IAAI,EAAE,SAAS,CAAE;QACnC,KAAK,CAAC;QACN,IAAI,CAAC,SAAS,GAAG;IACnB;AACF;AAEA,SAAS,kBAAmB,GAAG;IAC7B,OAAO;WAAI;KAAI,CACZ,GAAG,CAAC,CAAA,IAAK,QAAQ,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAC1D,IAAI,CAAC;AACV;AAEA;;;;CAIC,GACD,MAAM;IAEJ;;;;;GAKC,GACD,YAAa,OAAO,CAAE;QACpB,IAAI,CAAC,eAAe,GAAG,AAAC,QAAQ,gBAAgB,GAC5C,QAAQ,oBAAoB,CAAC,OAAO,CAAC,OAAO,MAC5C,QAAQ,oBAAoB;QAChC,MAAM,kBAAkB,kBAAkB,IAAI,CAAC,eAAe;QAC9D,IAAI,CAAC,mBAAmB,GAAG,IAAI,OAAO,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAC7D,IAAI,CAAC,oBAAoB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,gBAAgB,EAAE,CAAC;QAC9D,IAAI,CAAC,sBAAsB,GAAG,IAAI,OAAO,CAAC,EAAE,EAAE,gBAAgB,GAAG,CAAC;QAClE,IAAI,CAAC,wBAAwB,GAAG,IAAI,OAAO,CAAC,UAAU,EAAE,gBAAgB,EAAE,CAAC,EAAE;QAC7E,IAAI,CAAC,2BAA2B,GAAG,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE;QAE/D,IAAI,QAAQ,gBAAgB,EAAE;YAE5B,MAAM,kBAAkB,IAAI,OAAO,CAAC,SAAS,EAAE,gBAAgB,EAAE,CAAC,EAAE;YAEpE;;;;;;;OAOC,GACD,IAAI,CAAC,aAAa,GAAG,SAAU,IAAI,EAAE,iBAAiB,EAAE,YAAa,CAAA,MAAO,GAAI,EAAE,SAAS,KAAK;gBAC9F,IAAI,CAAC,MAAM;oBAAE;gBAAQ;gBACrB,MAAM,yBAAyB,kBAAkB,YAAY;gBAC7D,IAAI,WAAW;gBACf,IAAI,IAAI,gBAAgB,IAAI,CAAC;gBAC7B,IAAI,GAAG;oBACL,WAAW;oBACX,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM;wBACjB,kBAAkB,YAAY;oBAChC,OAAO,IAAI,0BAA0B,IAAI,CAAC,qBAAqB,CAAC,OAAO;wBACrE,kBAAkB,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG;oBAC9C,OAAO;wBACL,kBAAkB,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG;oBAChD;oBACA,MAAO,CAAC,IAAI,gBAAgB,IAAI,CAAC,KAAK,MAAM,KAAM;wBAChD,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM;4BACjB,kBAAkB,YAAY;wBAChC,OAAO;4BACL,kBAAkB,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG;wBAC9C;oBACF;gBACF;gBACA,kBAAkB,YAAY,GAAG,AAAC,0BAA0B,CAAC,YAAc,IAAI,CAAC,sBAAsB,CAAC;YACvG,mEAAmE;YACnE,+CAA+C;YACjD;QAEF,OAAO;YAEL,MAAM,SAAS,IAAI,OAAO,CAAC,EAAE,EAAE,gBAAgB,EAAE,CAAC,EAAE;YAEpD,IAAI,CAAC,aAAa,GAAG,SAAU,IAAI,EAAE,iBAAiB,EAAE,YAAa,CAAA,MAAO,GAAI,EAAE,SAAS,KAAK;gBAC9F,IAAI,CAAC,MAAM;oBAAE;gBAAQ;gBACrB,MAAM,yBAAyB,kBAAkB,YAAY;gBAC7D,IAAI,WAAW;gBACf,IAAI,IAAI,OAAO,IAAI,CAAC;gBACpB,IAAI,GAAG;oBACL,WAAW;oBACX,IAAI,0BAA0B,IAAI,CAAC,qBAAqB,CAAC,OAAO;wBAC9D,kBAAkB,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG;oBAC9C,OAAO;wBACL,kBAAkB,UAAU,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG;oBAChD;oBACA,MAAO,CAAC,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,KAAM;wBACvC,kBAAkB,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG;oBAC9C;gBACF;gBACA,kBAAkB,YAAY,GAAG,AAAC,0BAA0B,CAAC,YAAa,IAAI,CAAC,sBAAsB,CAAC;YACxG;QAEF;IACF;IAEA;;;;;;;;;;;;GAYC,GACD,WAAY,IAAI,EAAE,iBAAiB,EAAE,SAAS,IAAI,EAAE;QAClD,IAAI,CAAC,MAAM;YAAE;QAAQ;QACrB,MAAM,yBAAyB,kBAAkB,YAAY;QAC7D,IAAI,WAAW;QACf,IAAI,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC;QAC9C,IAAI,GAAG;YACL,WAAW;YACX,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM;gBACjB,kBAAkB,YAAY;YAChC,OAAO,IAAI,wBAAwB;gBACjC,kBAAkB,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE;YACnC,OAAO;gBACL,kBAAkB,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE;YACrC;YACA,MAAO,CAAC,IAAI,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,KAAK,MAAM,KAAM;gBACjE,IAAI,CAAC,CAAC,EAAE,KAAK,MAAM;oBACjB,kBAAkB,YAAY;gBAChC,OAAO;oBACL,kBAAkB,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE;gBACnC;YACF;QACF;QACA,kBAAkB,YAAY,GAAI,0BAA0B,CAAC;IAC/D;IAEA;;;;;GAKC,GACD,sBAAuB,IAAI,EAAE;QAC3B,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;IACvC;IAEA;;;;;GAKC,GACD,uBAAwB,IAAI,EAAE;QAC5B,OAAO,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;IACxC;IAEA;;;;;GAKC,GACD,kBAAmB,IAAI,EAAE;QACvB,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;IAC3C;IAEA;;;;;;;GAOC,GACD,qBAAsB,IAAI,EAAE;QAC1B,IAAI,CAAC,wBAAwB,CAAC,SAAS,GAAG;QAC1C,IAAI,UAAU;QACd,IAAI;QACJ,MAAO,CAAC,QAAQ,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,MAAM,KAAM;YAClE,IAAI,KAAK,CAAC,EAAE,KAAK,MAAM;gBACrB;YACF,OAAO;gBACL,OAAO;YACT;QACF;QACA,OAAO;IACT;AAEF;AAEA;;;;CAIC,GACD,MAAM;IAEJ;;;;;;GAMC,GACD,YAAa,OAAO,EAAE,MAAM,EAAE,WAAW,SAAS,CAAE;QAClD,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,mBAAmB,GAAG,IAAI,oBAAoB;QACnD,wBAAwB,GACxB,IAAI,CAAC,UAAU,GAAG,IAAI,eAAe;QACrC,mCAAmC,GACnC,IAAI,CAAC,gBAAgB,GAAG;IAC1B;IAEA;;;;;;;;GAQC,GACD,kBAAmB,aAAa,EAAE;QAChC,IAAI,CAAC,gBAAgB,GAAG,IAAI,qBAAqB,IAAI,CAAC,gBAAgB,EAAE;IAC1E;IAEA;;;;GAIC,GACD,mBAAoB;QAClB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YAAE,OAAO;QAAW;QAChD,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC,SAAS;QACjD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI;QAClD,OAAO;IACT;IAEA;;GAEC,GACD,cAAe;QACb,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG;IAC7B;IAEA;;GAEC,GACD,aAAc;QACZ,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG;IAC7B;IAEA,yCAAyC,GACzC,8BAA+B;QAC7B,MAAM,KAAK,AAAC,IAAI,CAAC,gBAAgB,GAC5B,CAAC,MAAQ,iBAAiB,KAAK,IAAI,CAAC,gBAAgB,IACrD;QACJ,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,gBAAgB;QACxC,OAAO,AAAC,KACH,AAAC,KAAM,CAAC,MAAQ,GAAG,GAAG,QAAQ,KAC/B;IACN;IAEA,gBAAiB;QACf,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,IAAI,CAAC,UAAU,GAAG,KAAK,IAAI;QAC3B,OAAO;IACT;IAEA;;GAEC,GACD,eAAgB;QACd,IAAI,CAAC,CACH,IAAI,CAAC,UAAU,YAAY,kBACxB,IAAI,CAAC,UAAU,YAAY,qBAC3B,IAAI,CAAC,UAAU,YAAY,kBAChC,GAAG;YAAE;QAAQ;QACb,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;YACzB,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI;QAC7B,OAAO;YACL,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY;QAChD;IACF;IAEA;;GAEC,GACD,0BAA2B;QACzB,IACE,IAAI,CAAC,UAAU,YAAY,kBACxB,IAAI,CAAC,UAAU,YAAY,qBAC3B,IAAI,CAAC,UAAU,YAAY,oBAC9B;YACA,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,oBAAoB,GAAG;QAC3D;IACF;IAEA;;;;;;;;;;;;;GAaC,GACD,UAAW,GAAG,EAAE,EAAE,kBAAkB,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QAChD,IAAI,CAAC,CACH,IAAI,CAAC,UAAU,YAAY,kBACxB,IAAI,CAAC,UAAU,YAAY,qBAC3B,IAAI,CAAC,UAAU,YAAY,kBAChC,GAAG;YAAE;QAAQ;QAEb,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;YACzB,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI;YAC3B;QACF;QAEA,IACE,IAAI,MAAM,KAAK,KAEb,IAAI,CAAC,UAAU,CAAC,iBAAiB,IAAI,gDAAgD;QACrF,CAAC,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,KAAK,kBAAkB;UAErE;YAAE;QAAQ;QAEZ,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;YACjC,MAAM,iBAAiB,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,CAAC;YACrE,IAAI,iBAAiB,GAAG;gBACtB,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC;gBAC/C,mCAAmC;gBACnC;YACF;QACF;QAEA,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE;YACrC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB;QAClF;QACA,IAAI,CAAC,mBAAmB,CAAC,aAAa,CACpC,KACA,IAAI,CAAC,UAAU,CAAC,iBAAiB,EACjC,AAAC,kBAAmB,YAAY,IAAI,CAAC,2BAA2B,IAChE,IAAI,CAAC,UAAU,CAAC,QAAQ;QAE1B,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG,GAAG,4CAA4C;IACrF;IAEA;;;;;;;GAOC,GACD,WAAY,GAAG,EAAE;QACf,IAAI,CAAC,CACH,IAAI,CAAC,UAAU,YAAY,kBACxB,IAAI,CAAC,UAAU,YAAY,qBAC3B,IAAI,CAAC,UAAU,YAAY,kBAChC,GAAG;YAAE;QAAQ;QAEb,IAAI,IAAI,MAAM,KAAK,GAAG;YAAE;QAAQ;QAEhC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;YACzB,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI;YAC3B;QACF;QAEA,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE;YACrC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB;QAClF;QACA,IAAI,CAAC,mBAAmB,CAAC,UAAU,CACjC,KACA,IAAI,CAAC,UAAU,CAAC,iBAAiB,EACjC,IAAI,CAAC,UAAU,CAAC,QAAQ;QAE1B,IAAI,CAAC,UAAU,CAAC,iBAAiB,GAAG;IACtC;IAEA;;;;;;;;;;;;;;GAcC,GACD,UAAW,EAAE,oBAAoB,CAAC,EAAE,qBAAqB,CAAC,EAAE,QAAQ,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE;QAChF,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa,GAAG;QACrF,IAAI,CAAC,UAAU,GAAG,IAAI,eACpB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,UAAU,EACf,mBACA;QAEF,IAAI,OAAO;YAAE,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG;QAAM;IAC7C;IAEA;;;;;;;;;;;;;;GAcC,GACD,WAAY,EAAE,qBAAqB,CAAC,EAAE,iBAAiB,SAAS,EAAE,GAAG,CAAC,CAAC,EAAE;QACvE,MAAM,QAAQ,IAAI,CAAC,aAAa;QAChC,MAAM,YAAY,AAAC,iBAAkB,eAAe,QAAQ,UAAU,QAAQ;QAC9E,QAAQ,IAAI,CAAC,UAAU,EAAE,WAAW,MAAM,iBAAiB,EAAE,KAAK,GAAG,CAAC,MAAM,iBAAiB,EAAE;IACjG;IAEA;;;;;;;;;;;;;;;;;;;GAmBC,GACD,SAAU,EAAE,kBAAkB,CAAC,EAAE,cAAc,MAAM,EAAE,qBAAqB,CAAC,EAAE,oBAAoB,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAC3G,IAAI,CAAC,UAAU,GAAG,IAAI,cAAc,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YACjE,oBAAoB;YACpB,mBAAmB;YACnB,eAAe,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,aAAa;YAC9D,iBAAiB;YACjB,aAAa;QACf;IACF;IAEA;;;;;;;;GAQC,GACD,aAAc,EAAE,SAAS,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE;QAClC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,aAAa,GAAG;YAC/C,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,MAAM,eAAe,KAAK,GAAG,CAAC,OAAO,MAAM,EAAE,KAAK,eAAe;QACjE,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAI,KAAK,iBAAiB,CAAC,aAAa,GAAG;QAC1E,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAkB,IAAI,CAAC,OAAO,EAAE,MAAM;YAC1D,QAAQ;YACR,eAAe;YACf,mBAAmB,KAAK,kBAAkB;QAC5C;IACF;IAEA;;GAEC,GACD,gBAAiB;QACf,MAAM,WAAW,IAAI,CAAC,aAAa;QACnC,MAAM,OAAO,SAAS,IAAI;QAE1B,MAAM,eAAe,KAAK,GAAG,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,KAAK,eAAe;QAC1E,MAAM,UAAU,OAAO,IAAI,MAAM,CAAC;QAClC,MAAM,SAAS,AAAC,KAAK,WAAW,KAAK,UACjC,SAAS,MAAM,CAAC,QAAQ,CAAC,gBACzB,SAAS,MAAM,CAAC,MAAM,CAAC;QAC3B,MAAM,OAAO,SAAS,QAAQ,UAAU,OAAO,CAAC,OAAO;QAEvD,QACE,MACA,MACA,SAAS,iBAAiB,EAC1B,KAAK,GAAG,CAAC,SAAS,iBAAiB,EAAE,KAAK,kBAAkB;IAEhE;IAEA;;;;;;;;GAQC,GACD,UAAW,EAAE,qBAAqB,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAC1C,MAAM,OAAO,IAAI,CAAC,aAAa;QAC/B,MAAM,OAAO,QAAQ;QACrB,IAAI,MAAM;YACR,QAAQ,IAAI,CAAC,UAAU,EAAE,MAAM,KAAK,iBAAiB,EAAE;QACzD;IACF;IAEA;;GAEC,GACD,YAAa;QACX,IAAI,CAAC,UAAU,GAAG,IAAI,eAAe,IAAI,CAAC,UAAU;IACtD;IAEA;;GAEC,GACD,eAAgB;QACd,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,cAAc,GAAG;YAChD,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAkB,IAAI,CAAC,UAAU;IACzD;IAEA;;;;;;;;GAQC,GACD,cAAe,EAAE,iBAAiB,SAAS,EAAE,GAAG,CAAC,CAAC,EAAE;QAClD,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,YAAY,iBAAiB,GAAG;YACnD,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,CAAC,UAAU,GAAG,IAAI,mBAAmB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;IAC1E;IAEA;;;;;;;;GAQC,GACD,eAAgB,EAAE,UAAU,CAAC,EAAE,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACjD,MAAM,OAAO,IAAI,CAAC,aAAa;QAC/B,MAAM,OAAO,cAAc,QAAQ,OAAO;QAC1C,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAAE,SAAS;YAAS,SAAS;YAAS,MAAM;QAAK;IACxE;IAEA;;GAEC,GACD,gBAAiB;QACf,MAAM,MAAM,IAAI,CAAC,aAAa;QAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK;IAC9B;IAEA;;;;;;;;;;;;;;GAcC,GACD,WAAY,EAAE,aAAa,EAAE,oBAAoB,CAAC,EAAE,qBAAqB,CAAC,EAAE,EAAE;QAC5E,MAAM,QAAQ,IAAI,CAAC,aAAa;QAChC,MAAM,SAAS,cAAc,MAAM,IAAI;QACvC,IAAI,QAAQ;YACV,QAAQ,IAAI,CAAC,UAAU,EAAE,QAAQ,mBAAmB;QACtD;IACF;IAEA;;;;GAIC,GACD,WAAY;QACV,OAAO,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO;IACtC,uEAAuE;IACzE;AAEF;AAEA,SAAS,QAAS,SAAS;IACzB,IAAI,CAAC,CACH,qBAAqB,kBAClB,qBAAqB,qBACrB,qBAAqB,kBAC1B,GAAG;QACD,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,AAAC,UAAU,iBAAiB,CAAC,OAAO,KACvC,UAAU,OAAO,GACjB,UAAU,OAAO,GAAG,UAAU,iBAAiB,CAAC,QAAQ;AAC9D;AAEA,SAAS,QAAS,SAAS,EAAE,IAAI,EAAE,iBAAiB,EAAE,kBAAkB;IACtE,IAAI,CAAC,CACH,qBAAqB,kBAClB,qBAAqB,qBACrB,qBAAqB,kBAC1B,GAAG;QACD,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,aAAa,QAAQ;IAC3B,MAAM,aAAa,KAAK,GAAG,CAAC,UAAU,iBAAiB,EAAE;IACzD,UAAU,iBAAiB,CAAC,KAAK;IACjC,IAAI,YAAY;QACd,UAAU,OAAO,GAAG,aAAa,KAAK,MAAM,CAAC,cAAc;IAC7D,OAAO;QACL,UAAU,OAAO,GAAG;QACpB,UAAU,iBAAiB,GAAG;IAChC;IACA,UAAU,iBAAiB,GAAG;AAChC;AAEA;;;;CAIC,GACD,SAAS,iBAAkB,GAAG,EAAE,WAAW;IACzC,OAAQ,AAAC,cAAe,iBAAiB,YAAY,SAAS,CAAC,MAAM,YAAY,IAAI,IAAI;AAC3F;AAEA;;;;;;;CAOC,GACD,SAAS,UAAW,UAAU,CAAC,CAAC;IAC9B,MAAM,yBAAyB,QAAQ,SAAS,CAAC,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM;IACtE,IAAI,uBAAuB,MAAM,EAAE;QACjC,MAAM,IAAI,MACR,mDACA,uBAAuB,GAAG,CAAC,CAAA,IAAK,CAAC,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IAE9D;IACA,MAAM,SAAS,IAAI,kNAAY,CAC7B,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAA,IAAK;YAAC,EAAE,QAAQ;YAAE;SAAE,GAC1C,KAAK,CAAC,4QAAU;IAElB,IAAI,OAAO,QAAQ,gBAAgB,KAAK,YAAY;QAClD,QAAQ,gBAAgB,GAAG,qBAAqB,QAAQ,gBAAgB;IAC1E;IAEA,MAAM,sBAAsB,IAAI,kNAAY,CAC1C,QAAQ,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,IAAM;YAAC;YAAG,IAAI;SAAE,GACvD,KAAK,CAAC,4QAAU;IAClB,SAAS,iBAAkB,GAAG;QAC5B,OAAO,UAAU,KAAK,SAAS;IACjC;IAEA,MAAM,cAAc,sBAClB,QAAQ,MAAM,CAAC,QAAQ,EACvB,eACA,SAAU,GAAG,EAAE,OAAO;QACpB,QAAQ,SAAS,CAAC,QAAQ,MAAM,CAAC,QAAQ,IAAI;IAC/C;IAGF,OAAO,SAAU,IAAI,EAAE,WAAW,SAAS;QACzC,OAAO,QAAQ,MAAM,UAAU,SAAS,QAAQ,kBAAkB;IACpE;AACF;AAGA;;;;;;;;;;;;;CAaC,GACD,SAAS,QAAS,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,IAAI;IACvE,MAAM,iBAAiB,QAAQ,MAAM,CAAC,cAAc;IACpD,IAAI,kBAAkB,QAAQ,KAAK,MAAM,GAAG,gBAAgB;QAC1D,QAAQ,IAAI,CACV,CAAC,aAAa,EAAE,KAAK,MAAM,CAAC,2BAA2B,EAAE,eAAe,8BAA8B,CAAC;QAEzG,OAAO,KAAK,SAAS,CAAC,GAAG;IAC3B;IAEA,MAAM,WAAW,IAAA,2OAAa,EAAC,MAAM;QAAE,gBAAgB,QAAQ,cAAc;IAAC;IAC9E,MAAM,QAAQ,iBAAiB,SAAS,QAAQ;IAChD,MAAM,UAAU,IAAI,iBAAiB,SAAS,QAAQ;IACtD,KAAK,OAAO;IACZ,OAAO,QAAQ,QAAQ;AACzB;AAGA,SAAS,UAAW,GAAG,EAAE,OAAO,EAAE,mBAAmB;IACnD,MAAM,UAAU,EAAE;IAElB,SAAS,cAAe,IAAI,EAAE,wBAAwB,GAAG,GAAG;QAC1D,MAAM,IAAI,KAAK,CAAC,GAAG,QAAQ,MAAM,CAAC,aAAa;QAC/C,KAAK,MAAM,QAAQ,IAAK;YACtB,IAAI,KAAK,IAAI,KAAK,OAAO;gBACvB;YACF;YACA,MAAM,sBAAsB,oBAAoB,KAAK,CAAC;YACtD,IAAI,sBAAsB,GAAG;gBAC3B,QAAQ,IAAI,CAAC;oBAAE,eAAe;oBAAqB,SAAS;gBAAK;YACnE,OAAO,IAAI,KAAK,QAAQ,EAAE;gBACxB,KAAK,KAAK,QAAQ;YACpB;YACA,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,eAAe,EAAE;gBACpD;YACF;QACF;IACF;IAEA,MAAM,cAAc,sBAClB,QAAQ,MAAM,CAAC,QAAQ,EACvB;IAEF,YAAY;IAEZ,IAAI,QAAQ,YAAY,CAAC,OAAO,KAAK,cAAc;QACjD,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,aAAa,GAAG,EAAE,aAAa;IAC1D;IACA,OAAO,AAAC,QAAQ,YAAY,CAAC,kBAAkB,IAAI,QAAQ,MAAM,KAAK,IAClE,MACA,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;AAChC;AAEA;;;;;;;CAOC,GACD,SAAS,cAAe,IAAI,EAAE,GAAG,EAAE,OAAO;IACxC,IAAI,CAAC,KAAK;QAAE;IAAQ;IAEpB,MAAM,UAAU,QAAQ,OAAO;IAE/B,MAAM,oBAAoB,IAAI,MAAM,GAAG,QAAQ,MAAM,CAAC,aAAa;IACnE,IAAI,mBAAmB;QACrB,MAAM,IAAI,KAAK,CAAC,GAAG,QAAQ,MAAM,CAAC,aAAa;QAC/C,IAAI,IAAI,CAAC;YACP,MAAM,QAAQ,MAAM,CAAC,QAAQ;YAC7B,MAAM;QACR;IACF;IAEA,KAAK,MAAM,QAAQ,IAAK;QACtB,OAAQ,KAAK,IAAI;YACf,KAAK;gBAAQ;oBACX,QAAQ,SAAS,CAAC,KAAK,IAAI;oBAC3B;gBACF;YACA,KAAK;gBAAO;oBACV,MAAM,gBAAgB,QAAQ,MAAM,CAAC,KAAK,CAAC;oBAC3C,MAAM,SAAS,QAAQ,UAAU,CAAC,cAAc,MAAM,CAAC;oBACvD,OAAO,MAAM,MAAM,SAAS,cAAc,OAAO,IAAI,CAAC;oBACtD;gBACF;QACF;IACF;IAEA;AACF;AAEA;;;;;;;;;CASC,GACD,SAAS,qBAAsB,IAAI;IACjC,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,MAAM,MAAM,KAAK,GAAG;QAC3C,OAAO;IACT;IACA,iCAAiC,GACjC,MAAM,UAAU,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC,GAAG,EAAE,GAAK,MAAM;IAC7D,MAAM,QAAQ,IAAI,OAChB,QACG,GAAG,CAAC,CAAC,CAAC,EAAE,GAAK,CAAC,CAAC,EAAE,cAAc;eAAI;SAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAC5C,IAAI,CAAC,MACR;IAEF,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAC,GAAG,EAAE,GAAK;IACtC,MAAM,WAAW,CAAC,GAAG,GAAG,MAAQ,MAAM,CAAC,IAAI,SAAS,CAAC,CAAA,KAAM,IAAI;IAC/D,OAAO,CAAC,MAAQ,IAAI,OAAO,CAAC,OAAO;AACrC;AAEA;;;;CAIC,GACD,SAAS,WAAY,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;AACrD,cAAc,GAChB;AAEA;;;;CAIC,GACD,SAAS,mBAAoB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC7D,QAAQ,UAAU,CAAC,cAAc,MAAM,IAAI;AAC7C;AAEA;;;;CAIC,GACD,SAAS,kBAAmB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC5D,QAAQ,SAAS,CAAC;QAAE,mBAAmB,cAAc,iBAAiB,IAAI;IAAE;IAC5E,QAAQ,UAAU,CAAC,cAAc,MAAM,IAAI;IAC3C,QAAQ,UAAU,CAAC;QAAE,oBAAoB,cAAc,kBAAkB,IAAI;IAAE;AACjF;AAEA;;;;CAIC,GACD,SAAS,aAAc,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IACvD,KAAK,KAAK,QAAQ,EAAE;AACtB;AAEA;;;;CAIC,GACD,SAAS,cAAe,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IACxD,QAAQ,SAAS,CAAC;QAAE,mBAAmB,cAAc,iBAAiB,IAAI;IAAE;IAC5E,KAAK,KAAK,QAAQ,EAAE;IACpB,QAAQ,UAAU,CAAC;QAAE,oBAAoB,cAAc,kBAAkB,IAAI;IAAE;AACjF;AAEA,SAAS,cAAe,IAAI;IAC1B,MAAM,QAAQ,AAAC,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAC9C,MAAM,OAAO,OAAO,CAAC,KAAK,OAAO,EAChC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAM,AAAC,MAAM,KAAM,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,WAAW,EACrE,IAAI,CAAC,OACN;IACJ,OAAO,CAAC,CAAC,EAAE,KAAK,IAAI,GAAG,MAAM,CAAC,CAAC;AACjC;AAEA,SAAS,eAAgB,IAAI;IAC3B,OAAO,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;AAC1B;AAEA;;;;CAIC,GACD,SAAS,gBAAiB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC1D,QAAQ,WAAW;IACnB,QAAQ,UAAU,CAAC,cAAc;IACjC,QAAQ,UAAU;IAClB,KAAK,KAAK,QAAQ,EAAE;IACpB,QAAQ,WAAW;IACnB,QAAQ,UAAU,CAAC,eAAe;IAClC,QAAQ,UAAU;AACpB;AAEA;;;;CAIC,GACD,SAAS,eAAgB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IACzD,QAAQ,SAAS,CAAC;QAAE,mBAAmB,cAAc,iBAAiB,IAAI;IAAE;IAC5E,QAAQ,WAAW;IACnB,QAAQ,UAAU,CAAC,cAAc;IACjC,QAAQ,UAAU;IAClB,KAAK,KAAK,QAAQ,EAAE;IACpB,QAAQ,WAAW;IACnB,QAAQ,UAAU,CAAC,eAAe;IAClC,QAAQ,UAAU;IAClB,QAAQ,UAAU,CAAC;QAAE,oBAAoB,cAAc,kBAAkB,IAAI;IAAE;AACjF;AAEA;;;;CAIC,GACD,SAAS,iBAAkB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC3D,QAAQ,WAAW;IACnB,QAAQ,UAAU,CAChB,IAAA,gOAAM,EAAC,MAAM;QAAE,gBAAgB,QAAQ,OAAO,CAAC,cAAc;IAAC;IAEhE,QAAQ,UAAU;AACpB;AAEA;;;;CAIC,GACD,SAAS,gBAAiB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC1D,QAAQ,SAAS,CAAC;QAAE,mBAAmB,cAAc,iBAAiB,IAAI;IAAE;IAC5E,QAAQ,WAAW;IACnB,QAAQ,UAAU,CAChB,IAAA,gOAAM,EAAC,MAAM;QAAE,gBAAgB,QAAQ,OAAO,CAAC,cAAc;IAAC;IAEhE,QAAQ,UAAU;IAClB,QAAQ,UAAU,CAAC;QAAE,oBAAoB,cAAc,kBAAkB,IAAI;IAAE;AACjF;AAEA;;;;CAIC,GACD,SAAS,qBAAsB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC/D,QAAQ,UAAU,CAAC,cAAc,MAAM,IAAI;IAC3C,KAAK,KAAK,QAAQ,EAAE;IACpB,QAAQ,UAAU,CAAC,cAAc,MAAM,IAAI;AAC7C;AAEA,IAAI,oBAAoB,WAAW,GAAE,OAAO,MAAM,CAAC;IACjD,WAAW;IACX,OAAO;IACP,WAAW;IACX,aAAa;IACb,UAAU;IACV,QAAQ;IACR,YAAY;IACZ,cAAc;IACd,gBAAgB;IAChB,WAAW;IACX,MAAM;AACR;AAEA,SAAS,OAAQ,MAAM,EAAE,CAAC;IACxB,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;QAAE,MAAM,CAAC,EAAE,GAAG,EAAE;IAAE;IAClC,OAAO,MAAM,CAAC,EAAE;AAClB;AAEA,SAAS,qBAAsB,GAAG,EAAE,IAAI,CAAC;IACvC,MAAO,GAAG,CAAC,EAAE,CAAE;QAAE;IAAK;IACtB,OAAO;AACT;AAEA,SAAS,iBAAkB,MAAM,EAAE,OAAO;IACxC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;QAChC,MAAM,OAAO,OAAO,QAAQ;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,MAAM,OAAO,OAAO,QAAQ;YAC5B,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE;gBACtB,MAAM,OAAO,IAAI,CAAC,EAAE;gBACpB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;gBACjB,IAAI,CAAC,EAAE,GAAG;YACZ;QACF;IACF;AACF;AAEA,SAAS,kBAAmB,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;IACxD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,OAAO,EAAE,IAAK;QACrC,MAAM,YAAY,OAAO,QAAQ,UAAU;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,OAAO,EAAE,IAAK;YACrC,SAAS,CAAC,UAAU,EAAE,GAAG;QAC3B;IACF;AACF;AAEA,SAAS,gBAAiB,OAAO,EAAE,KAAK;IACtC,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW;QAChC,OAAO,CAAC,MAAM,GAAG,AAAC,UAAU,IAAK,IAAI,IAAI,gBAAgB,SAAS,QAAQ;IAC5E;IACA,OAAO,OAAO,CAAC,MAAM;AACvB;AAEA,SAAS,aAAc,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK;IAC/C,OAAO,CAAC,OAAO,KAAK,GAAG,KAAK,GAAG,CAC7B,gBAAgB,SAAS,OAAO,OAChC,gBAAgB,SAAS,QAAQ;AAErC;AAEA;;;;;;;;;;CAUC,GACD,SAAS,cAAe,SAAS,EAAE,UAAU,EAAE,UAAU;IACvD,MAAM,SAAS,EAAE;IACjB,IAAI,YAAY;IAChB,MAAM,YAAY,UAAU,MAAM;IAClC,MAAM,aAAa;QAAC;KAAE;IACtB,oDAAoD;IACpD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAClC,MAAM,YAAY,OAAO,QAAQ;QACjC,MAAM,QAAQ,SAAS,CAAC,EAAE;QAC1B,IAAI,IAAI;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,IAAI,qBAAqB,WAAW;YACpC,kBAAkB,MAAM,QAAQ,GAAG;YACnC,KAAK,KAAK,OAAO;YACjB,KAAK,KAAK,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;YAC7B,MAAM,aAAa,KAAK,KAAK,CAAC,MAAM;YACpC,aAAa,YAAY,GAAG,KAAK,OAAO,EAAE,aAAa;QACzD;QACA,YAAY,AAAC,UAAU,MAAM,GAAG,YAAa,UAAU,MAAM,GAAG;IAClE;IAEA,iBAAiB,QAAQ,AAAC,YAAY,YAAa,YAAY;IAE/D,MAAM,cAAc,EAAE;IACtB,MAAM,aAAa;QAAC;KAAE;IACtB,yDAAyD;IACzD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAClC,IAAI,IAAI;QACR,IAAI;QACJ,MAAM,mBAAmB,KAAK,GAAG,CAAC,WAAW,MAAM,CAAC,EAAE,CAAC,MAAM;QAC7D,MAAO,IAAI,iBAAkB;YAC3B,OAAO,MAAM,CAAC,EAAE,CAAC,EAAE;YACnB,IAAI,MAAM;gBACR,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAClB,IAAI,YAAY;oBAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,IAAK;wBAC1C,MAAM,OAAO,KAAK,KAAK,CAAC,EAAE;wBAC1B,MAAM,aAAa,UAAU,CAAC,EAAE,GAAG;wBACnC,WAAW,CAAC,WAAW,GAAG,CAAC,WAAW,CAAC,WAAW,IAAI,EAAE,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE,IAAI;wBAClF,YAAY,AAAC,KAAK,MAAM,GAAG,YAAa,KAAK,MAAM,GAAG;oBACxD;oBACA,aAAa,YAAY,GAAG,KAAK,OAAO,EAAE,YAAY;oBACtD,KAAK,QAAQ,GAAG;gBAClB;gBACA,KAAK,KAAK,OAAO;YACnB,OAAO;gBACL,MAAM,aAAa,UAAU,CAAC,EAAE;gBAChC,WAAW,CAAC,WAAW,GAAI,WAAW,CAAC,WAAW,IAAI;gBACtD;YACF;QACF;IACF;IAEA,OAAO,YAAY,IAAI,CAAC;AAC1B;AAEA;;;;CAIC,GACD,SAAS,gBAAiB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC1D,QAAQ,YAAY;AACtB;AAEA;;;;CAIC,GACD,SAAS,UAAW,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IACpD,QAAQ,uBAAuB;AACjC;AAEA;;;;CAIC,GACD,SAAS,qBAAsB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC/D,QAAQ,SAAS,CAAC;QAAE,mBAAmB,cAAc,iBAAiB,IAAI;IAAE;IAC5E,QAAQ,SAAS,CAAC,IAAI,MAAM,CAAC,cAAc,MAAM,IAAI,QAAQ,OAAO,CAAC,QAAQ,IAAI;IACjF,QAAQ,UAAU,CAAC;QAAE,oBAAoB,cAAc,kBAAkB,IAAI;IAAE;AACjF;AAEA;;;;CAIC,GACD,SAAS,gBAAiB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC1D,QAAQ,SAAS,CAAC;QAAE,mBAAmB,cAAc,iBAAiB,IAAI;IAAE;IAC5E,KAAK,KAAK,QAAQ,EAAE;IACpB,QAAQ,UAAU,CAAC;QAAE,oBAAoB,cAAc,kBAAkB,IAAI;IAAE;AACjF;AAEA;;;;CAIC,GACD,SAAS,UAAW,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IACpD,QAAQ,SAAS,CAAC;QAChB,OAAO;QACP,mBAAmB,cAAc,iBAAiB,IAAI;IACxD;IACA,KAAK,KAAK,QAAQ,EAAE;IACpB,QAAQ,UAAU,CAAC;QAAE,oBAAoB,cAAc,kBAAkB,IAAI;IAAE;AACjF;AAEA;;;;CAIC,GACD,SAAS,cAAe,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IACxD,QAAQ,SAAS,CAAC;QAAE,mBAAmB,cAAc,iBAAiB,IAAI;IAAE;IAC5E,IAAI,cAAc,SAAS,KAAK,OAAO;QACrC,QAAQ,iBAAiB,CAAC,CAAA,MAAO,IAAI,WAAW;QAChD,KAAK,KAAK,QAAQ,EAAE;QACpB,QAAQ,gBAAgB;IAC1B,OAAO;QACL,KAAK,KAAK,QAAQ,EAAE;IACtB;IACA,QAAQ,UAAU,CAAC;QAAE,oBAAoB,cAAc,kBAAkB,IAAI;IAAE;AACjF;AAEA;;;;CAIC,GACD,SAAS,iBAAkB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC3D,QAAQ,SAAS,CAAC;QAChB,mBAAmB,cAAc,iBAAiB,IAAI;QACtD,oBAAoB;IACtB;IACA,KAAK,KAAK,QAAQ,EAAE;IACpB,QAAQ,UAAU,CAAC;QACjB,oBAAoB,cAAc,kBAAkB,IAAI;QACxD,gBAAgB,CAAA,MAAO,CAAC,AAAC,cAAc,cAAc,KAAK,QAAS,cAAc,KAAK,QAAQ,GAAG,EAC9F,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,OAAQ,OAAO,MACnB,IAAI,CAAC;IACV;AACF;AAEA,SAAS,aAAc,GAAG,EAAE,QAAQ;IAClC,IAAI,CAAC,UAAU;QAAE,OAAO;IAAK;IAE7B,MAAM,MAAM,AAAC,OAAO,QAAQ,CAAC,EAAE,KAAK,WAChC,QAAQ,CAAC,EAAE,GACX;IACJ,MAAM,MAAM,AAAC,OAAO,QAAQ,CAAC,EAAE,KAAK,WAChC,QAAQ,CAAC,EAAE,GACX;IACJ,OAAO,MAAM,MAAM;AACrB;AAEA,SAAS,YAAa,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI;IAC3D,MAAM,eAAe,AAAC,OAAO,aAAa,aACtC,SAAS,MAAM,UAAU,QACzB;IACJ,OAAO,AAAC,YAAY,CAAC,EAAE,KAAK,OAAO,UAC/B,iBAAiB,SAAS,OAAO,eACjC;AACN;AAEA;;;;CAIC,GACD,SAAS,YAAa,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IACtD,MAAM,UAAU,KAAK,OAAO,IAAI,CAAC;IACjC,MAAM,MAAM,AAAC,QAAQ,GAAG,GACpB,QAAQ,GAAG,GACX;IACJ,MAAM,MAAM,AAAC,CAAC,QAAQ,GAAG,GACrB,KACA,YAAY,QAAQ,GAAG,EAAE,cAAc,WAAW,EAAE,cAAc,OAAO,EAAE,QAAQ,QAAQ,EAAE;IACjG,MAAM,OAAO,AAAC,CAAC,MACX,MACA,AAAC,CAAC,MACA,aAAa,KAAK,cAAc,YAAY,IAC5C,MAAM,MAAM,aAAa,KAAK,cAAc,YAAY;IAE9D,QAAQ,SAAS,CAAC,MAAM;QAAE,iBAAiB;IAAK;AAClD;AAEA,gBAAgB;AAChB,oBAAoB;AACpB,qBAAqB;AAErB,0BAA0B;AAC1B,0BAA0B;AAC1B,0BAA0B;AAC1B,2CAA2C;AAC3C,wCAAwC;AACxC,qEAAqE;AACrE,gDAAgD;AAEhD,oCAAoC;AACpC,2CAA2C;AAE3C,6DAA6D;AAE7D,kBAAkB;AAElB,IAAI;AACJ,wCAAwC;AACxC,yBAAyB;AAEzB;;;;CAIC,GACD,SAAS,aAAc,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IACvD,SAAS;QACP,IAAI,cAAc,UAAU,EAAE;YAAE,OAAO;QAAI;QAC3C,IAAI,CAAC,KAAK,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,IAAI,EAAE;YAAE,OAAO;QAAI;QACtD,IAAI,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY;QACjD,IAAI,cAAc,WAAW,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK;YAAE,OAAO;QAAI;QAC/D,OAAO,YAAY,MAAM,cAAc,WAAW,EAAE,cAAc,OAAO,EAAE,QAAQ,QAAQ,EAAE;QAC7F,OAAO;IACT;IACA,MAAM,OAAO;IACb,IAAI,CAAC,MAAM;QACT,KAAK,KAAK,QAAQ,EAAE;IACtB,OAAO;QACL,IAAI,OAAO;QACX,QAAQ,iBAAiB,CACvB,CAAA;YACE,IAAI,KAAK;gBAAE,QAAQ;YAAK;YACxB,OAAO;QACT;QAEF,KAAK,KAAK,QAAQ,EAAE;QACpB,QAAQ,gBAAgB;QAExB,MAAM,eAAe,cAAc,wBAAwB,IAAI,SAAS;QACxE,IAAI,CAAC,cAAc;YACjB,QAAQ,SAAS,CACf,AAAC,CAAC,OACE,OACA,MAAM,aAAa,MAAM,cAAc,YAAY,GACvD;gBAAE,iBAAiB;YAAK;QAE5B;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAAS,WAAY,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,kBAAkB;IACzE,MAAM,eAAe,IAAI,MAAM;QAAC;QAAU;KAAO,MAAM;IAEvD,gGAAgG;IAChG,iFAAiF;IACjF,IAAI,kBAAkB;IACtB,MAAM,YAAY,CAAC,KAAK,QAAQ,IAAI,EAAE,CACpC,2FAA2F;KAC1F,MAAM,CAAC,CAAA,QAAS,MAAM,IAAI,KAAK,UAAU,CAAC,QAAQ,IAAI,CAAC,MAAM,IAAI,GACjE,GAAG,CAAC,SAAU,KAAK;QAClB,IAAI,MAAM,IAAI,KAAK,MAAM;YACvB,OAAO;gBAAE,MAAM;gBAAO,QAAQ;YAAG;QACnC;QACA,MAAM,SAAS,AAAC,eACZ,qBAAqB,SAAS,KAC9B;QACJ,IAAI,OAAO,MAAM,GAAG,iBAAiB;YAAE,kBAAkB,OAAO,MAAM;QAAE;QACxE,OAAO;YAAE,MAAM;YAAO,QAAQ;QAAO;IACvC;IACF,IAAI,CAAC,UAAU,MAAM,EAAE;QAAE;IAAQ;IAEjC,QAAQ,QAAQ,CAAC;QACf,oBAAoB;QACpB,mBAAmB,eAAe,IAAK,cAAc,iBAAiB,IAAI;QAC1E,iBAAiB;QACjB,aAAa;IACf;IAEA,KAAK,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,UAAW;QACxC,QAAQ,YAAY,CAAC;YAAE,QAAQ;QAAO;QACtC,KAAK;YAAC;SAAK,EAAE;QACb,QAAQ,aAAa;IACvB;IAEA,QAAQ,SAAS,CAAC;QAAE,oBAAoB,eAAe,IAAK,cAAc,kBAAkB,IAAI;IAAG;AACrG;AAEA;;;;CAIC,GACD,SAAS,oBAAqB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC9D,MAAM,SAAS,cAAc,UAAU,IAAI;IAC3C,OAAO,WAAW,MAAM,MAAM,SAAS,eAAe,IAAM;AAC9D;AAEA;;;;CAIC,GACD,SAAS,kBAAmB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC5D,IAAI,YAAY,OAAO,KAAK,OAAO,CAAC,KAAK,IAAI;IAC7C,MAAM,gBAAgB,4BAA4B,KAAK,OAAO,CAAC,IAAI;IACnE,MAAM,qBAAqB,IAAM,MAAM,cAAc,eAAe;IACpE,OAAO,WAAW,MAAM,MAAM,SAAS,eAAe;AACxD;AAEA;;;;;CAKC,GACD,SAAS,4BAA6B,SAAS,GAAG;IAChD,OAAQ;QACN,KAAK;YAAK,OAAO,CAAC,IAAM,uBAAuB,GAAG;QAClD,KAAK;YAAK,OAAO,CAAC,IAAM,uBAAuB,GAAG;QAClD,KAAK;YAAK,OAAO,CAAC,IAAM,cAAc,GAAG,WAAW;QACpD,KAAK;YAAK,OAAO,CAAC,IAAM,cAAc;QACtC,KAAK;QACL;YAAS,OAAO,CAAC,IAAM,AAAC,EAAG,QAAQ;IACrC;AACF;AAEA;;;;;;CAMC,GACD,SAAS,mBAAoB,SAAS;IACpC,MAAM,UAAU,EAAE;IAClB,MAAM,MAAM,EAAE;IACd,KAAK,MAAM,YAAY,UAAW;QAChC,IAAI,SAAS,UAAU,CAAC,MAAM;YAC5B,QAAQ,IAAI,CAAC,SAAS,SAAS,CAAC;QAClC,OAAO,IAAI,SAAS,UAAU,CAAC,MAAM;YACnC,IAAI,IAAI,CAAC,SAAS,SAAS,CAAC;QAC9B;IACF;IACA,OAAO;QAAE,SAAS;QAAS,KAAK;IAAI;AACtC;AAEA,SAAS,YAAa,IAAI,EAAE,MAAM;IAChC,IAAI,WAAW,MAAM;QAAE,OAAO;IAAM;IACpC,IAAI,CAAC,MAAM;QAAE,OAAO;IAAO;IAE3B,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,mBAAmB;IAC5C,MAAM,cAAc,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,EAAE,KAAK,CAAC;IAChD,MAAM,UAAU,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,KAAK,CAAC;IAEzC,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,QAAQ,QAAQ,CAAC,OAAO,QAAQ,IAAI,CAAC,CAAA,IAAK,IAAI,QAAQ,CAAC;AACtF;AAEA;;;;CAIC,GACD,SAAS,YAAa,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IACtD,OAAO,YAAY,KAAK,OAAO,EAAE,QAAQ,OAAO,CAAC,MAAM,IACnD,gBAAgB,MAAM,MAAM,SAAS,iBACrC,YAAY,MAAM,MAAM,SAAS;AACvC;AAEA,SAAS,YAAa,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IACtD,QAAQ,SAAS,CAAC;QAAE,mBAAmB,cAAc,iBAAiB;IAAC;IACvE,KAAK,KAAK,QAAQ,EAAE;IACpB,QAAQ,UAAU,CAAC;QAAE,oBAAoB,cAAc,kBAAkB;IAAC;AAC5E;AAEA;;;;CAIC,GACD,SAAS,gBAAiB,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa;IAC1D,QAAQ,SAAS;IACjB,KAAK,QAAQ,CAAC,OAAO,CAAC;IACtB,QAAQ,UAAU,CAAC;QACjB,eAAe,CAAC,OAAS,cAAc,MAAM,cAAc,UAAU,IAAI,GAAG,cAAc,UAAU,IAAI;QACxG,mBAAmB,cAAc,iBAAiB;QAClD,oBAAoB,cAAc,kBAAkB;IACtD;IAEA,SAAS,WAAY,QAAQ;QAC3B,MAAM,UAAU,CAAC,IAAI,UAAU;YAAC;YAAW;SAAU,KAAK;QAC1D,MAAM,UAAU,CAAC,IAAI,UAAU;YAAC;YAAW;SAAU,KAAK;QAC1D,QAAQ,aAAa,CAAC;YAAE,gBAAgB,cAAc,cAAc;QAAC;QACrE,KAAK,SAAS,QAAQ,EAAE;QACxB,QAAQ,cAAc,CAAC;YAAE,SAAS;YAAS,SAAS;QAAQ;IAC9D;IAEA,SAAS,UAAW,IAAI;QACtB,IAAI,KAAK,IAAI,KAAK,OAAO;YAAE;QAAQ;QAEnC,MAAM,mBAAmB,AAAC,cAAc,oBAAoB,KAAK,QAC7D,CAAC;YACD,QAAQ,iBAAiB,CAAC,CAAA,MAAO,IAAI,WAAW;YAChD,WAAW;YACX,QAAQ,gBAAgB;QAC1B,IACE;QAEJ,OAAQ,KAAK,IAAI;YACf,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,KAAK,QAAQ,CAAC,OAAO,CAAC;gBACtB;YAEF,KAAK;gBAAM;oBACT,QAAQ,YAAY;oBACpB,KAAK,MAAM,aAAa,KAAK,QAAQ,CAAE;wBACrC,IAAI,UAAU,IAAI,KAAK,OAAO;4BAAE;wBAAU;wBAC1C,OAAQ,UAAU,IAAI;4BACpB,KAAK;gCAAM;oCACT,iBAAiB;oCACjB;gCACF;4BACA,KAAK;gCAAM;oCACT,WAAW;oCACX;gCACF;wBAEF;oBACF;oBACA,QAAQ,aAAa;oBACrB;gBACF;QAEF;IACF;AACF;AAEA,IAAI,iBAAiB,WAAW,GAAE,OAAO,MAAM,CAAC;IAC9C,WAAW;IACX,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,SAAS;IACT,gBAAgB;IAChB,OAAO;IACP,WAAW;IACX,aAAa;IACb,WAAW;IACX,KAAK;IACL,OAAO;IACP,eAAe;IACf,KAAK;AACP;AAEA;;;;;;;CAOC,GACD,MAAM,kBAAkB;IACtB,cAAc;QACZ,WAAW;YAAE;SAAQ;QACrB,SAAS;QACT,oBAAoB;IACtB;IACA,gBAAgB;IAChB,kBAAkB,CAAC;IACnB,YAAY,CAAC;IACb,QAAQ;QACN,UAAU;QACV,iBAAiB;QACjB,eAAe;QACf,UAAU;QACV,gBAAiB,KAAK;IACxB;IACA,eAAe;QACb,kBAAkB;QAClB,gBAAgB,EAAE;IACpB;IACA,kBAAkB;IAClB,WAAW;QACT;YAAE,UAAU;YAAK,QAAQ;QAAS;QAClC;YACE,UAAU;YACV,QAAQ;YACR,SAAS;gBACP,SAAS;gBACT,0BAA0B;gBAC1B,YAAY;gBACZ,cAAc;oBAAC;oBAAK;iBAAI;gBACxB,aAAa;YACf;QACF;QACA;YAAE,UAAU;YAAW,QAAQ;YAAS,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QACjG;YAAE,UAAU;YAAS,QAAQ;YAAS,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QAC/F;YACE,UAAU;YACV,QAAQ;YACR,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;gBAAG,gBAAgB;YAAK;QAC/E;QACA;YAAE,UAAU;YAAM,QAAQ;QAAY;QACtC;YAAE,UAAU;YAAO,QAAQ;YAAS,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QAC7F;YAAE,UAAU;YAAU,QAAQ;YAAS,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QAChG;YAAE,UAAU;YAAQ,QAAQ;YAAS,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QAC9F;YAAE,UAAU;YAAM,QAAQ;YAAW,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;gBAAG,WAAW;YAAK;QAAE;QAC/G;YAAE,UAAU;YAAM,QAAQ;YAAW,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;gBAAG,WAAW;YAAK;QAAE;QAC/G;YAAE,UAAU;YAAM,QAAQ;YAAW,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;gBAAG,WAAW;YAAK;QAAE;QAC/G;YAAE,UAAU;YAAM,QAAQ;YAAW,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;gBAAG,WAAW;YAAK;QAAE;QAC/G;YAAE,UAAU;YAAM,QAAQ;YAAW,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;gBAAG,WAAW;YAAK;QAAE;QAC/G;YAAE,UAAU;YAAM,QAAQ;YAAW,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;gBAAG,WAAW;YAAK;QAAE;QAC/G;YAAE,UAAU;YAAU,QAAQ;YAAS,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QAChG;YACE,UAAU;YACV,QAAQ;YACR,SAAS;gBAAE,mBAAmB;gBAAG,QAAQ;gBAAW,oBAAoB;YAAE;QAC5E;QACA;YACE,UAAU;YACV,QAAQ;YACR,SAAS;gBAAE,SAAS;gBAAM,cAAc;oBAAC;oBAAK;iBAAI;YAAC;QACrD;QACA;YAAE,UAAU;YAAQ,QAAQ;YAAS,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QAC9F;YAAE,UAAU;YAAO,QAAQ;YAAS,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QAC7F;YACE,UAAU;YACV,QAAQ;YACR,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QACzD;QACA;YAAE,UAAU;YAAK,QAAQ;YAAa,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QAC/F;YAAE,UAAU;YAAO,QAAQ;YAAO,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QAC3F;YAAE,UAAU;YAAW,QAAQ;YAAS,SAAS;gBAAE,mBAAmB;gBAAG,oBAAoB;YAAE;QAAE;QACjG;YACE,UAAU;YACV,QAAQ;YACR,SAAS;gBACP,YAAY;gBACZ,mBAAmB;gBACnB,gBAAgB;gBAChB,YAAY;gBACZ,oBAAoB;gBACpB,sBAAsB;YACxB;QACF;QACA;YACE,UAAU;YACV,QAAQ;YACR,SAAS;gBAAE,YAAY;gBAAO,mBAAmB;gBAAG,oBAAoB;YAAE;QAC5E;QACA;YAAE,UAAU;YAAO,QAAQ;QAAM;KAClC;IACD,QAAQ,EAAE;IACV,sBAAsB;IACtB,UAAU;AACZ;AAEA,MAAM,cAAc,CAAC,KAAK,KAAK,UAAY;WAAI;WAAQ;KAAI;AAC3D,MAAM,iBAAiB,CAAC,KAAK,KAAK,UAAY;WAAI;KAAI;AACtD,MAAM,iBAAiB,CAAC,KAAK,KAAK,UAChC,AAAC,IAAI,IAAI,CAAC,CAAA,IAAK,OAAO,MAAM,YACxB,YAAY,KAAK,KAAK,YAAY;OAClC,eAAe,KAAK,KAAK,yBAAyB;;AAGxD;;;;;;;CAOC,GACD,SAAS,QAAS,UAAU,CAAC,CAAC;IAC5B,UAAU,IAAA,yMAAK,EACb,iBACA,SACA;QACE,YAAY;QACZ,aAAa,CAAC,MAAS,AAAC,QAAQ,cAAe,iBAAiB;IAClE;IAEF,QAAQ,UAAU,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,mBAAmB,gBAAgB,QAAQ,UAAU;IAC5F,QAAQ,SAAS,GAAG,0BAA0B,QAAQ,SAAS,EAAG,CAAA,IAAK,EAAE,QAAQ;IAEjF,wBAAwB;IAExB,OAAO,UAAU;AACnB;AAEA;;;;;;;;;;;;;;;CAeC,GACD,SAAS,QAAS,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,WAAW,SAAS;IACxD,OAAO,QAAQ,SAAS,MAAM;AAChC;AAEA;;;;;CAKC,GACD,SAAS,wBAAyB,OAAO;IACvC,IAAI,QAAQ,IAAI,EAAE;QAChB,MAAM,iBAAiB,OAAO,OAAO,CAAC,QAAQ,IAAI,EAAE,GAAG,CACrD,CAAC,CAAC,UAAU,WAAW,GAAK,CAAC;gBAAE,GAAG,UAAU;gBAAE,UAAU,YAAY;YAAI,CAAC;QAE3E,QAAQ,SAAS,CAAC,IAAI,IAAI;QAC1B,QAAQ,SAAS,GAAG,0BAA0B,QAAQ,SAAS,EAAG,CAAA,IAAK,EAAE,QAAQ;IACnF;IAEA,SAAS,IAAK,GAAG,EAAE,IAAI,EAAE,KAAK;QAC5B,MAAM,WAAW,KAAK,GAAG;QACzB,KAAK,MAAM,OAAO,KAAM;YACtB,IAAI,SAAS,GAAG,CAAC,IAAI;YACrB,IAAI,CAAC,QAAQ;gBACX,SAAS,CAAC;gBACV,GAAG,CAAC,IAAI,GAAG;YACb;YACA,MAAM;QACR;QACA,GAAG,CAAC,SAAS,GAAG;IAClB;IAEA,IAAI,OAAO,CAAC,cAAc,EAAE;QAC1B,MAAM,cAAc,OAAO,CAAC,cAAc;QAC1C,IACE,SACA;YAAC;YAAgB;SAAY,EAC5B,MAAM,OAAO,CAAC,eAAe,cAAc;YAAC;SAAY;IAE7D;IACA,IAAI,OAAO,CAAC,qBAAqB,KAAK,WAAW;QAC/C,IAAI,SAAS;YAAC;YAAgB;SAAqB,EAAE,OAAO,CAAC,qBAAqB;IACpF;IAEA,KAAK,MAAM,cAAc,QAAQ,SAAS,CAAE;QAC1C,IAAI,WAAW,MAAM,KAAK,YAAY,IAAI,YAAY;YAAC;YAAW;SAAiB,GAAG;YACpF,IAAI,YAAY;gBAAC;gBAAW;aAAe,EAAE;QAC/C;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 13940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/better/node_modules/.pnpm/%40react-email%2Brender%401.2.1_r_c5a25c3dc1e6309fdf7858c89bda5b7e/node_modules/%40react-email/render/dist/node/index.mjs"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// src/node/render.tsx\nimport { Suspense } from \"react\";\n\n// src/shared/utils/pretty.ts\nimport * as html from \"prettier/plugins/html\";\nimport { format } from \"prettier/standalone\";\nfunction recursivelyMapDoc(doc, callback) {\n  if (Array.isArray(doc)) {\n    return doc.map((innerDoc) => recursivelyMapDoc(innerDoc, callback));\n  }\n  if (typeof doc === \"object\") {\n    if (doc.type === \"group\") {\n      return __spreadProps(__spreadValues({}, doc), {\n        contents: recursivelyMapDoc(doc.contents, callback),\n        expandedStates: recursivelyMapDoc(\n          doc.expandedStates,\n          callback\n        )\n      });\n    }\n    if (\"contents\" in doc) {\n      return __spreadProps(__spreadValues({}, doc), {\n        contents: recursivelyMapDoc(doc.contents, callback)\n      });\n    }\n    if (\"parts\" in doc) {\n      return __spreadProps(__spreadValues({}, doc), {\n        parts: recursivelyMapDoc(doc.parts, callback)\n      });\n    }\n    if (doc.type === \"if-break\") {\n      return __spreadProps(__spreadValues({}, doc), {\n        breakContents: recursivelyMapDoc(doc.breakContents, callback),\n        flatContents: recursivelyMapDoc(doc.flatContents, callback)\n      });\n    }\n  }\n  return callback(doc);\n}\nvar modifiedHtml = __spreadValues({}, html);\nif (modifiedHtml.printers) {\n  const previousPrint = modifiedHtml.printers.html.print;\n  modifiedHtml.printers.html.print = (path, options, print, args) => {\n    const node = path.getNode();\n    const rawPrintingResult = previousPrint(path, options, print, args);\n    if (node.type === \"ieConditionalComment\") {\n      const printingResult = recursivelyMapDoc(rawPrintingResult, (doc) => {\n        if (typeof doc === \"object\" && doc.type === \"line\") {\n          return doc.soft ? \"\" : \" \";\n        }\n        return doc;\n      });\n      return printingResult;\n    }\n    return rawPrintingResult;\n  };\n}\nvar defaults = {\n  endOfLine: \"lf\",\n  tabWidth: 2,\n  plugins: [modifiedHtml],\n  bracketSameLine: true,\n  parser: \"html\"\n};\nvar pretty = (str, options = {}) => {\n  return format(str.replaceAll(\"\\0\", \"\"), __spreadValues(__spreadValues({}, defaults), options));\n};\n\n// src/shared/utils/to-plain-text.ts\nimport {\n  convert\n} from \"html-to-text\";\nvar plainTextSelectors = [\n  { selector: \"img\", format: \"skip\" },\n  { selector: \"[data-skip-in-text=true]\", format: \"skip\" },\n  {\n    selector: \"a\",\n    options: { linkBrackets: false }\n  }\n];\nfunction toPlainText(html2, options) {\n  return convert(html2, __spreadValues({\n    selectors: plainTextSelectors\n  }, options));\n}\n\n// src/node/read-stream.ts\nimport { Writable } from \"node:stream\";\nvar decoder = new TextDecoder(\"utf-8\");\nvar readStream = (stream) => __async(void 0, null, function* () {\n  let result = \"\";\n  if (\"pipeTo\" in stream) {\n    const writableStream = new WritableStream({\n      write(chunk) {\n        result += decoder.decode(chunk);\n      }\n    });\n    yield stream.pipeTo(writableStream);\n  } else {\n    const writable = new Writable({\n      write(chunk, _encoding, callback) {\n        result += decoder.decode(chunk);\n        callback();\n      }\n    });\n    stream.pipe(writable);\n    yield new Promise((resolve, reject) => {\n      writable.on(\"error\", reject);\n      writable.on(\"close\", () => {\n        resolve();\n      });\n    });\n  }\n  return result;\n});\n\n// src/node/render.tsx\nimport { jsx } from \"react/jsx-runtime\";\nvar render = (node, options) => __async(void 0, null, function* () {\n  const suspendedElement = /* @__PURE__ */ jsx(Suspense, { children: node });\n  const reactDOMServer = yield import(\"react-dom/server\").then(\n    // This is beacuse react-dom/server is CJS\n    (m) => m.default\n  );\n  let html2;\n  if (Object.hasOwn(reactDOMServer, \"renderToReadableStream\")) {\n    html2 = yield readStream(\n      yield reactDOMServer.renderToReadableStream(suspendedElement, {\n        progressiveChunkSize: Number.POSITIVE_INFINITY\n      })\n    );\n  } else {\n    yield new Promise((resolve, reject) => {\n      const stream = reactDOMServer.renderToPipeableStream(suspendedElement, {\n        onAllReady() {\n          return __async(this, null, function* () {\n            html2 = yield readStream(stream);\n            resolve();\n          });\n        },\n        onError(error) {\n          reject(error);\n        },\n        progressiveChunkSize: Number.POSITIVE_INFINITY\n      });\n    });\n  }\n  if (options == null ? void 0 : options.plainText) {\n    return toPlainText(html2, options.htmlToTextOptions);\n  }\n  const doctype = '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">';\n  const document = `${doctype}${html2.replace(/<!DOCTYPE.*?>/, \"\")}`;\n  if (options == null ? void 0 : options.pretty) {\n    return pretty(document);\n  }\n  return document;\n});\n\n// src/node/index.ts\nvar renderAsync = (element, options) => {\n  return render(element, options);\n};\nexport {\n  plainTextSelectors,\n  pretty,\n  render,\n  renderAsync,\n  toPlainText\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAwCA,sBAAsB;AACtB;AAEA,6BAA6B;AAC7B;AACA;AA+DA,oCAAoC;AACpC;AAiBA,0BAA0B;AAC1B;AA6BA,sBAAsB;AACtB;;;;;;AA7JA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,aAAa,OAAO,gBAAgB;AACxC,IAAI,oBAAoB,OAAO,yBAAyB;AACxD,IAAI,sBAAsB,OAAO,qBAAqB;AACtD,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,eAAe,OAAO,SAAS,CAAC,oBAAoB;AACxD,IAAI,kBAAkB,CAAC,KAAK,KAAK,QAAU,OAAO,MAAM,UAAU,KAAK,KAAK;QAAE,YAAY;QAAM,cAAc;QAAM,UAAU;QAAM;IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;AAC1J,IAAI,iBAAiB,CAAC,GAAG;IACvB,IAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,EAC3B,IAAI,aAAa,IAAI,CAAC,GAAG,OACvB,gBAAgB,GAAG,MAAM,CAAC,CAAC,KAAK;IACpC,IAAI,qBACF,KAAK,IAAI,QAAQ,oBAAoB,GAAI;QACvC,IAAI,aAAa,IAAI,CAAC,GAAG,OACvB,gBAAgB,GAAG,MAAM,CAAC,CAAC,KAAK;IACpC;IACF,OAAO;AACT;AACA,IAAI,gBAAgB,CAAC,GAAG,IAAM,WAAW,GAAG,kBAAkB;AAC9D,IAAI,UAAU,CAAC,QAAQ,aAAa;IAClC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI,YAAY,CAAC;YACf,IAAI;gBACF,KAAK,UAAU,IAAI,CAAC;YACtB,EAAE,OAAO,GAAG;gBACV,OAAO;YACT;QACF;QACA,IAAI,WAAW,CAAC;YACd,IAAI;gBACF,KAAK,UAAU,KAAK,CAAC;YACvB,EAAE,OAAO,GAAG;gBACV,OAAO;YACT;QACF;QACA,IAAI,OAAO,CAAC,IAAM,EAAE,IAAI,GAAG,QAAQ,EAAE,KAAK,IAAI,QAAQ,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW;QACvF,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,QAAQ,YAAY,EAAE,IAAI;IAC9D;AACF;;;;AAQA,SAAS,kBAAkB,GAAG,EAAE,QAAQ;IACtC,IAAI,MAAM,OAAO,CAAC,MAAM;QACtB,OAAO,IAAI,GAAG,CAAC,CAAC,WAAa,kBAAkB,UAAU;IAC3D;IACA,IAAI,OAAO,QAAQ,UAAU;QAC3B,IAAI,IAAI,IAAI,KAAK,SAAS;YACxB,OAAO,cAAc,eAAe,CAAC,GAAG,MAAM;gBAC5C,UAAU,kBAAkB,IAAI,QAAQ,EAAE;gBAC1C,gBAAgB,kBACd,IAAI,cAAc,EAClB;YAEJ;QACF;QACA,IAAI,cAAc,KAAK;YACrB,OAAO,cAAc,eAAe,CAAC,GAAG,MAAM;gBAC5C,UAAU,kBAAkB,IAAI,QAAQ,EAAE;YAC5C;QACF;QACA,IAAI,WAAW,KAAK;YAClB,OAAO,cAAc,eAAe,CAAC,GAAG,MAAM;gBAC5C,OAAO,kBAAkB,IAAI,KAAK,EAAE;YACtC;QACF;QACA,IAAI,IAAI,IAAI,KAAK,YAAY;YAC3B,OAAO,cAAc,eAAe,CAAC,GAAG,MAAM;gBAC5C,eAAe,kBAAkB,IAAI,aAAa,EAAE;gBACpD,cAAc,kBAAkB,IAAI,YAAY,EAAE;YACpD;QACF;IACF;IACA,OAAO,SAAS;AAClB;AACA,IAAI,eAAe,eAAe,CAAC,GAAG;AACtC,IAAI,aAAa,QAAQ,EAAE;IACzB,MAAM,gBAAgB,aAAa,QAAQ,CAAC,IAAI,CAAC,KAAK;IACtD,aAAa,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,SAAS,OAAO;QACxD,MAAM,OAAO,KAAK,OAAO;QACzB,MAAM,oBAAoB,cAAc,MAAM,SAAS,OAAO;QAC9D,IAAI,KAAK,IAAI,KAAK,wBAAwB;YACxC,MAAM,iBAAiB,kBAAkB,mBAAmB,CAAC;gBAC3D,IAAI,OAAO,QAAQ,YAAY,IAAI,IAAI,KAAK,QAAQ;oBAClD,OAAO,IAAI,IAAI,GAAG,KAAK;gBACzB;gBACA,OAAO;YACT;YACA,OAAO;QACT;QACA,OAAO;IACT;AACF;AACA,IAAI,WAAW;IACb,WAAW;IACX,UAAU;IACV,SAAS;QAAC;KAAa;IACvB,iBAAiB;IACjB,QAAQ;AACV;AACA,IAAI,SAAS,CAAC,KAAK,UAAU,CAAC,CAAC;IAC7B,OAAO,IAAA,sJAAM,EAAC,IAAI,UAAU,CAAC,MAAM,KAAK,eAAe,eAAe,CAAC,GAAG,WAAW;AACvF;;AAMA,IAAI,qBAAqB;IACvB;QAAE,UAAU;QAAO,QAAQ;IAAO;IAClC;QAAE,UAAU;QAA4B,QAAQ;IAAO;IACvD;QACE,UAAU;QACV,SAAS;YAAE,cAAc;QAAM;IACjC;CACD;AACD,SAAS,YAAY,KAAK,EAAE,OAAO;IACjC,OAAO,IAAA,0OAAO,EAAC,OAAO,eAAe;QACnC,WAAW;IACb,GAAG;AACL;;AAIA,IAAI,UAAU,IAAI,YAAY;AAC9B,IAAI,aAAa,CAAC,SAAW,QAAQ,KAAK,GAAG,MAAM;QACjD,IAAI,SAAS;QACb,IAAI,YAAY,QAAQ;YACtB,MAAM,iBAAiB,IAAI,eAAe;gBACxC,OAAM,KAAK;oBACT,UAAU,QAAQ,MAAM,CAAC;gBAC3B;YACF;YACA,MAAM,OAAO,MAAM,CAAC;QACtB,OAAO;YACL,MAAM,WAAW,IAAI,iIAAQ,CAAC;gBAC5B,OAAM,KAAK,EAAE,SAAS,EAAE,QAAQ;oBAC9B,UAAU,QAAQ,MAAM,CAAC;oBACzB;gBACF;YACF;YACA,OAAO,IAAI,CAAC;YACZ,MAAM,IAAI,QAAQ,CAAC,SAAS;gBAC1B,SAAS,EAAE,CAAC,SAAS;gBACrB,SAAS,EAAE,CAAC,SAAS;oBACnB;gBACF;YACF;QACF;QACA,OAAO;IACT;;AAIA,IAAI,SAAS,CAAC,MAAM,UAAY,QAAQ,KAAK,GAAG,MAAM;QACpD,MAAM,mBAAmB,aAAa,GAAG,IAAA,6VAAG,EAAC,gVAAQ,EAAE;YAAE,UAAU;QAAK;QACxE,MAAM,iBAAiB,MAAM,8MAA2B,IAAI,CAC1D,0CAA0C;QAC1C,CAAC,IAAM,EAAE,OAAO;QAElB,IAAI;QACJ,IAAI,OAAO,MAAM,CAAC,gBAAgB,2BAA2B;YAC3D,QAAQ,MAAM,WACZ,CAAA,MAAM,eAAe,sBAAsB,CAAC,kBAAkB;gBAC5D,sBAAsB,OAAO,iBAAiB;YAChD,EAAC;QAEL,OAAO;YACL,MAAM,IAAI,QAAQ,CAAC,SAAS;gBAC1B,MAAM,SAAS,eAAe,sBAAsB,CAAC,kBAAkB;oBACrE;wBACE,OAAO,QAAQ,IAAI,EAAE,MAAM;4BACzB,QAAQ,MAAM,WAAW;4BACzB;wBACF;oBACF;oBACA,SAAQ,KAAK;wBACX,OAAO;oBACT;oBACA,sBAAsB,OAAO,iBAAiB;gBAChD;YACF;QACF;QACA,IAAI,WAAW,OAAO,KAAK,IAAI,QAAQ,SAAS,EAAE;YAChD,OAAO,YAAY,OAAO,QAAQ,iBAAiB;QACrD;QACA,MAAM,UAAU;QAChB,MAAM,WAAW,GAAG,UAAU,MAAM,OAAO,CAAC,iBAAiB,KAAK;QAClE,IAAI,WAAW,OAAO,KAAK,IAAI,QAAQ,MAAM,EAAE;YAC7C,OAAO,OAAO;QAChB;QACA,OAAO;IACT;AAEA,oBAAoB;AACpB,IAAI,cAAc,CAAC,SAAS;IAC1B,OAAO,OAAO,SAAS;AACzB", "ignoreList": [0], "debugId": null}}]}