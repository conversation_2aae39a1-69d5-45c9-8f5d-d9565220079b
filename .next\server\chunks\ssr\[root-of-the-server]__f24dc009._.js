module.exports=[75202,a=>a.a(async(b,c)=>{try{let b=await a.y("prettier/plugins/html");a.n(b),c()}catch(a){c(a)}},!0),35167,a=>a.a(async(b,c)=>{try{let b=await a.y("prettier/standalone");a.n(b),c()}catch(a){c(a)}},!0),71236,(a,b,c)=>{"use strict";var d=function(a){var b,c,d;return!!(b=a)&&"object"==typeof b&&(c=a,"[object RegExp]"!==(d=Object.prototype.toString.call(c))&&"[object Date]"!==d&&c.$$typeof!==e)},e="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function f(a,b){return!1!==b.clone&&b.isMergeableObject(a)?j(Array.isArray(a)?[]:{},a,b):a}function g(a,b,c){return a.concat(b).map(function(a){return f(a,c)})}function h(a){return Object.keys(a).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(a).filter(function(b){return Object.propertyIsEnumerable.call(a,b)}):[])}function i(a,b){try{return b in a}catch(a){return!1}}function j(a,b,c){(c=c||{}).arrayMerge=c.arrayMerge||g,c.isMergeableObject=c.isMergeableObject||d,c.cloneUnlessOtherwiseSpecified=f;var e,k,l=Array.isArray(b);return l!==Array.isArray(a)?f(b,c):l?c.arrayMerge(a,b,c):(k={},(e=c).isMergeableObject(a)&&h(a).forEach(function(b){k[b]=f(a[b],e)}),h(b).forEach(function(c){i(a,c)&&!(Object.hasOwnProperty.call(a,c)&&Object.propertyIsEnumerable.call(a,c))||(i(a,c)&&e.isMergeableObject(b[c])?k[c]=(function(a,b){if(!b.customMerge)return j;var c=b.customMerge(a);return"function"==typeof c?c:j})(c,e)(a[c],b[c],e):k[c]=f(b[c],e))}),k)}j.all=function(a,b){if(!Array.isArray(a))throw Error("first argument should be an array");return a.reduce(function(a,c){return j(a,c,b)},{})},b.exports=j},77570,a=>{"use strict";a.s(["convert",()=>cj],77570),function(a){a.Root="root",a.Text="text",a.Directive="directive",a.Comment="comment",a.Script="script",a.Style="style",a.Tag="tag",a.CDATA="cdata",a.Doctype="doctype"}(bx||(bx={}));let b=bx.Root,c=bx.Text,d=bx.Directive,e=bx.Comment,f=bx.Script,g=bx.Style,h=bx.Tag,i=bx.CDATA,j=bx.Doctype;class k{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(a){this.parent=a}get previousSibling(){return this.prev}set previousSibling(a){this.prev=a}get nextSibling(){return this.next}set nextSibling(a){this.next=a}cloneNode(a=!1){return u(this,a)}}class l extends k{constructor(a){super(),this.data=a}get nodeValue(){return this.data}set nodeValue(a){this.data=a}}class m extends l{constructor(){super(...arguments),this.type=bx.Text}get nodeType(){return 3}}class n extends l{constructor(){super(...arguments),this.type=bx.Comment}get nodeType(){return 8}}class o extends l{constructor(a,b){super(b),this.name=a,this.type=bx.Directive}get nodeType(){return 1}}class p extends k{constructor(a){super(),this.children=a}get firstChild(){var a;return null!=(a=this.children[0])?a:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(a){this.children=a}}class q extends p{constructor(){super(...arguments),this.type=bx.CDATA}get nodeType(){return 4}}class r extends p{constructor(){super(...arguments),this.type=bx.Root}get nodeType(){return 9}}class s extends p{constructor(a,b,c=[],d="script"===a?bx.Script:"style"===a?bx.Style:bx.Tag){super(c),this.name=a,this.attribs=b,this.type=d}get nodeType(){return 1}get tagName(){return this.name}set tagName(a){this.name=a}get attributes(){return Object.keys(this.attribs).map(a=>{var b,c;return{name:a,value:this.attribs[a],namespace:null==(b=this["x-attribsNamespace"])?void 0:b[a],prefix:null==(c=this["x-attribsPrefix"])?void 0:c[a]}})}}function t(a){return a.type===bx.Tag||a.type===bx.Script||a.type===bx.Style}function u(a,b=!1){let c;if(a.type===bx.Text)c=new m(a.data);else if(a.type===bx.Comment)c=new n(a.data);else if(t(a)){let d=b?v(a.children):[],e=new s(a.name,{...a.attribs},d);d.forEach(a=>a.parent=e),null!=a.namespace&&(e.namespace=a.namespace),a["x-attribsNamespace"]&&(e["x-attribsNamespace"]={...a["x-attribsNamespace"]}),a["x-attribsPrefix"]&&(e["x-attribsPrefix"]={...a["x-attribsPrefix"]}),c=e}else if(a.type===bx.CDATA){let d=b?v(a.children):[],e=new q(d);d.forEach(a=>a.parent=e),c=e}else if(a.type===bx.Root){let d=b?v(a.children):[],e=new r(d);d.forEach(a=>a.parent=e),a["x-mode"]&&(e["x-mode"]=a["x-mode"]),c=e}else if(a.type===bx.Directive){let b=new o(a.name,a.data);null!=a["x-name"]&&(b["x-name"]=a["x-name"],b["x-publicId"]=a["x-publicId"],b["x-systemId"]=a["x-systemId"]),c=b}else throw Error(`Not implemented yet: ${a.type}`);return c.startIndex=a.startIndex,c.endIndex=a.endIndex,null!=a.sourceCodeLocation&&(c.sourceCodeLocation=a.sourceCodeLocation),c}function v(a){let b=a.map(a=>u(a,!0));for(let a=1;a<b.length;a++)b[a].prev=b[a-1],b[a-1].next=b[a];return b}let w={withStartIndices:!1,withEndIndices:!1,xmlMode:!1};class x{constructor(a,b,c){this.dom=[],this.root=new r(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof b&&(c=b,b=w),"object"==typeof a&&(b=a,a=void 0),this.callback=null!=a?a:null,this.options=null!=b?b:w,this.elementCB=null!=c?c:null}onparserinit(a){this.parser=a}onreset(){this.dom=[],this.root=new r(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(a){this.handleCallback(a)}onclosetag(){this.lastNode=null;let a=this.tagStack.pop();this.options.withEndIndices&&(a.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(a)}onopentag(a,b){let c=new s(a,b,void 0,this.options.xmlMode?bx.Tag:void 0);this.addNode(c),this.tagStack.push(c)}ontext(a){let{lastNode:b}=this;if(b&&b.type===bx.Text)b.data+=a,this.options.withEndIndices&&(b.endIndex=this.parser.endIndex);else{let b=new m(a);this.addNode(b),this.lastNode=b}}oncomment(a){if(this.lastNode&&this.lastNode.type===bx.Comment){this.lastNode.data+=a;return}let b=new n(a);this.addNode(b),this.lastNode=b}oncommentend(){this.lastNode=null}oncdatastart(){let a=new m(""),b=new q([a]);this.addNode(b),a.parent=b,this.lastNode=a}oncdataend(){this.lastNode=null}onprocessinginstruction(a,b){let c=new o(a,b);this.addNode(c)}handleCallback(a){if("function"==typeof this.callback)this.callback(a,this.dom);else if(a)throw a}addNode(a){let b=this.tagStack[this.tagStack.length-1],c=b.children[b.children.length-1];this.options.withStartIndices&&(a.startIndex=this.parser.startIndex),this.options.withEndIndices&&(a.endIndex=this.parser.endIndex),b.children.push(a),c&&(a.prev=c,c.next=a),a.parent=b,this.lastNode=null}}let y=/\n/g;function z(a,b="",c={}){let d="string"==typeof b?b:"",e=a.map(A),f=!!("string"!=typeof b?b:c).lineNumbers;return function(a,b=0){let c=f?function(a){let b=[...a.matchAll(y)].map(a=>a.index||0);b.unshift(-1);let c=function a(b,c,d){if(d-c==1)return{offset:b[c],index:c+1};let e=Math.ceil((c+d)/2),f=a(b,c,e),g=a(b,e,d);return{offset:f.offset,low:f,high:g}}(b,0,b.length);return a=>(function a(b,c){return Object.prototype.hasOwnProperty.call(b,"index")?{line:b.index,column:c-b.offset}:a(b.high.offset<c?b.high:b.low,c)})(c,a)}(a):()=>({line:0,column:0}),g=b,h=[];a:for(;g<a.length;){let b=!1;for(let f of e){f.regex.lastIndex=g;let e=f.regex.exec(a);if(e&&e[0].length>0){if(!f.discard){let a=c(g),b="string"==typeof f.replace?e[0].replace(new RegExp(f.regex.source,f.regex.flags),f.replace):e[0];h.push({state:d,name:f.name,text:b,offset:g,len:e[0].length,line:a.line,column:a.column})}if(g=f.regex.lastIndex,b=!0,f.push){let b=f.push(a,g);h.push(...b.tokens),g=b.offset}if(f.pop)break a;break}}if(!b)break}return{tokens:h,offset:g,complete:a.length<=g}}}function A(a,b){return{...a,regex:function(a,b){if(0===a.name.length)throw Error(`Rule #${b} has empty name, which is not allowed.`);if(Object.prototype.hasOwnProperty.call(a,"regex")){var c=a.regex;if(c.global)throw Error(`Regular expression /${c.source}/${c.flags} contains the global flag, which is not allowed.`);return c.sticky?c:RegExp(c.source,c.flags+"y")}if(Object.prototype.hasOwnProperty.call(a,"str")){if(0===a.str.length)throw Error(`Rule #${b} ("${a.name}") has empty "str" property, which is not allowed.`);return RegExp(B(a.str),"y")}return RegExp(B(a.name),"y")}(a,b)}}function B(a){return a.replace(/[-[\]{}()*+!<=:?./\\^$|#\s,]/g,"\\$&")}function C(a,b){return(c,d)=>{let e,f=d;return d<c.tokens.length?void 0!==(e=a(c.tokens[d],c,d))&&f++:b?.(c,d),void 0===e?{matched:!1}:{matched:!0,position:f,value:e}}}function D(a,b){return a.matched?{matched:!0,position:a.position,value:b(a.value,a.position)}:a}function E(a,b){return a.matched?b(a):a}function F(a,b){return(c,d)=>D(a(c,d),(a,e)=>b(a,c,d,e))}function G(a,b){return(c,d)=>{let e=a(c,d);return e.matched?e:{matched:!0,position:d,value:b}}}function H(...a){return(b,c)=>{for(let d of a){let a=d(b,c);if(a.matched)return a}return{matched:!1}}}function I(a,b){return(c,d)=>{let e=a(c,d);return e.matched?e:b(c,d)}}function J(a){var b;return b=()=>!0,(c,d)=>{let e=[],f=!0;do{let g=a(c,d);g.matched&&b(g.value,e.length+1,c,d,g.position)?(e.push(g.value),d=g.position):f=!1}while(f)return{matched:!0,position:d,value:e}}}function K(a,b,c){return(d,e)=>E(a(d,e),a=>D(b(d,a.position),(b,f)=>c(a.value,b,d,e,f)))}function L(a,b){return K(a,b,(a,b)=>b)}function M(a,b,c,d){return(e,f)=>E(a(e,f),a=>E(b(e,a.position),b=>D(c(e,b.position),(c,g)=>d(a.value,b.value,c,e,f,g))))}function N(a,b,c){return M(a,b,c,(a,b)=>b)}function O(a,b,c){var d,e;return d=a,e=a=>{var d,e,f;return d=K(b,c,(a,b)=>[a,b]),e=(a,[b,c])=>b(a,c),f=a=>F(d,(b,c,d,f)=>e(a,b,c,d,f)),(b,c)=>{let d=!0,e=a,g=c;do{let a=f(e,b,g)(b,g);a.matched?(e=a.value,g=a.position):d=!1}while(d)return{matched:!0,position:g,value:e}}},(a,b)=>E(d(a,b),c=>e(c.value,a,b,c.position)(a,c.position))}let P=`(?:\\n|\\r\\n|\\r|\\f)`,Q=`[^\\x00-\\x7F]`,R=`(?:\\\\[0-9a-f]{1,6}(?:\\r\\n|[ \\n\\r\\t\\f])?)`,S=`(?:\\\\[^\\n\\r\\f0-9a-f])`,T=`(?:[_a-z]|${Q}|${R}|${S})`,U=`(?:[_a-z0-9-]|${Q}|${R}|${S})`,V=`(?:${U}+)`,W=`(?:[-]?${T}${U}*)`,X=`'([^\\n\\r\\f\\\\']|\\\\${P}|${Q}|${R}|${S})*'`,Y=`"([^\\n\\r\\f\\\\"]|\\\\${P}|${Q}|${R}|${S})*"`,Z=z([{name:"ws",regex:RegExp(`(?:[ \\t\\r\\n\\f]*)`)},{name:"hash",regex:RegExp(`#${V}`,"i")},{name:"ident",regex:RegExp(W,"i")},{name:"str1",regex:RegExp(X,"i")},{name:"str2",regex:RegExp(Y,"i")},{name:"*"},{name:"."},{name:","},{name:"["},{name:"]"},{name:"="},{name:">"},{name:"|"},{name:"+"},{name:"~"},{name:"^"},{name:"$"}]),$=z([{name:"unicode",regex:RegExp(R,"i")},{name:"escape",regex:RegExp(S,"i")},{name:"any",regex:RegExp("[\\s\\S]","i")}]);function _([a,b,c],[d,e,f]){return[a+d,b+e,c+f]}let aa=C(a=>"unicode"===a.name?String.fromCodePoint(parseInt(a.text.slice(1),16)):void 0),ab=F(J(H(aa,C(a=>"escape"===a.name?a.text.slice(1):void 0),C(a=>"any"===a.name?a.text:void 0))),a=>a.join(""));function ac(a){return ab({tokens:$(a).tokens,options:void 0},0).value}function ad(a){return C(b=>b.name===a||void 0)}let ae=C(a=>"ws"===a.name?null:void 0),af=G(ae,null);function ag(a){return N(af,a,af)}let ah=C(a=>"ident"===a.name?ac(a.text):void 0),ai=C(a=>"hash"===a.name?ac(a.text.slice(1)):void 0),aj=C(a=>a.name.startsWith("str")?ac(a.text.slice(1,-1)):void 0),ak=K(G(ah,""),ad("|"),a=>a),al=I(K(ak,ah,(a,b)=>({name:b,namespace:a})),F(ah,a=>({name:a,namespace:null}))),am=I(K(ak,ad("*"),a=>({type:"universal",namespace:a,specificity:[0,0,0]})),F(ad("*"),()=>({type:"universal",namespace:null,specificity:[0,0,0]}))),an=F(al,({name:a,namespace:b})=>({type:"tag",name:a,namespace:b,specificity:[0,0,1]})),ao=K(ad("."),ah,(a,b)=>({type:"class",name:b,specificity:[0,1,0]})),ap=F(ai,a=>({type:"id",name:a,specificity:[1,0,0]})),aq=C(a=>{if("ident"===a.name){if("i"===a.text||"I"===a.text)return"i";if("s"===a.text||"S"===a.text)return"s"}}),ar=I(K(aj,G(L(af,aq),null),(a,b)=>({value:a,modifier:b})),K(ah,G(L(ae,aq),null),(a,b)=>({value:a,modifier:b}))),as=H(F(ad("="),()=>"="),K(ad("~"),ad("="),()=>"~="),K(ad("|"),ad("="),()=>"|="),K(ad("^"),ad("="),()=>"^="),K(ad("$"),ad("="),()=>"$="),K(ad("*"),ad("="),()=>"*=")),at=I(M(ad("["),ag(al),ad("]"),(a,{name:b,namespace:c})=>({type:"attrPresence",name:b,namespace:c,specificity:[0,1,0]})),N(ad("["),M(ag(al),as,ag(ar),({name:a,namespace:b},c,{value:d,modifier:e})=>({type:"attrValue",name:a,namespace:b,matcher:c,value:d,modifier:e,specificity:[0,1,0]})),ad("]"))),au=I(am,an),av=H(ap,ao,at),aw=F(I(function(...a){return F(function(...a){return(b,c)=>{let d=[],e=c;for(let c of a){let a=c(b,e);if(!a.matched)return{matched:!1};d.push(a.value),e=a.position}return{matched:!0,position:e,value:d}}}(...a),a=>a.flatMap(a=>a))}(au,J(av)),function(a){return K(a,J(a),(a,b)=>[a,...b])}(av)),a=>({type:"compound",list:a,specificity:a.map(a=>a.specificity).reduce(_,[0,0,0])})),ax=I(ag(H(F(ad(">"),()=>">"),F(ad("+"),()=>"+"),F(ad("~"),()=>"~"),K(ad("|"),ad("|"),()=>"||"))),F(ae,()=>" ")),ay=O(aw,F(ax,a=>(b,c)=>({type:"compound",list:[...c.list,{type:"combinator",combinator:a,left:b,specificity:b.specificity}],specificity:_(b.specificity,c.specificity)})),aw);function az(a,b,c=1){return`${a.replace(/(\t)|(\r)|(\n)/g,(a,b,c)=>b?"␉":c?"␍":"␊")}
${"".padEnd(b)}${"^".repeat(c)}`}function aA(a){if(!a.type)throw Error("This is not an AST node.");switch(a.type){case"universal":return aB(a.namespace)+"*";case"tag":return aB(a.namespace)+aD(a.name);case"class":return"."+aD(a.name);case"id":return"#"+aD(a.name);case"attrPresence":return`[${aB(a.namespace)}${aD(a.name)}]`;case"attrValue":return`[${aB(a.namespace)}${aD(a.name)}${a.matcher}"${a.value.replace(/(")|(\\)|(\x00)|([\x01-\x1f]|\x7f)/g,(a,b,c,d,e)=>b?'\\"':c?"\\\\":d?"�":aC(e))}"${a.modifier?a.modifier:""}]`;case"combinator":return aA(a.left)+a.combinator;case"compound":return a.list.reduce((a,b)=>"combinator"===b.type?aA(b)+a:a+aA(b),"");case"list":return a.list.map(aA).join(",")}}function aB(a){return a||""===a?aD(a)+"|":""}function aC(a){return`\\${a.codePointAt(0).toString(16)} `}function aD(a){return a.replace(/(^[0-9])|(^-[0-9])|(^-$)|([-0-9a-zA-Z_]|[^\x00-\x7F])|(\x00)|([\x01-\x1f]|\x7f)|([\s\S])/g,(a,b,c,d,e,f,g,h)=>b?aC(b):c?"-"+aC(c.slice(1)):d?"\\-":e||(f?"�":g?aC(g):"\\"+h))}function aE(a){switch(a.type){case"universal":case"tag":return[1];case"id":return[2];case"class":return[3,a.name];case"attrPresence":return[4,aA(a)];case"attrValue":return[5,aA(a)];case"combinator":return[15,aA(a)]}}O(F(ay,a=>({type:"list",list:[a]})),F(ag(ad(",")),()=>(a,b)=>({type:"list",list:[...a.list,b]})),ay);function aF(a,b){if(!Array.isArray(a)||!Array.isArray(b))throw Error("Arguments must be arrays.");let c=a.length<b.length?a.length:b.length;for(let d=0;d<c;d++)if(a[d]!==b[d])return a[d]<b[d]?-1:1;return a.length-b.length}class aG{constructor(a){this.branches=aH(function(a){let b=a.length,c=Array(b);for(let e=0;e<b;e++){var d;let[b,f]=a[e],g=(function a(b){let c=[];b.list.forEach(b=>{switch(b.type){case"class":c.push({matcher:"~=",modifier:null,name:"class",namespace:null,specificity:b.specificity,type:"attrValue",value:b.name});break;case"id":c.push({matcher:"=",modifier:null,name:"id",namespace:null,specificity:b.specificity,type:"attrValue",value:b.name});break;case"combinator":a(b.left),c.push(b);break;case"universal":break;default:c.push(b)}}),b.list=c}(d=function(a){if(!("string"==typeof a||a instanceof String))throw Error("Expected a selector string. Actual input is not a string!");let b=Z(a);if(!b.complete)throw Error(`The input "${a}" was only partially tokenized, stopped at offset ${b.offset}!
`+az(a,b.offset));let c=ag(ay)({tokens:b.tokens,options:void 0},0);if(!c.matched)throw Error(`No match for "${a}" input!`);if(c.position<b.tokens.length){let d=b.tokens[c.position];throw Error(`The input "${a}" was only partially parsed, stopped at offset ${d.offset}!
`+az(a,d.offset,d.len))}return c.value}(b)),!function a(b){if(!b.type)throw Error("This is not an AST node.");switch(b.type){case"compound":b.list.forEach(a),b.list.sort((a,b)=>aF(aE(a),aE(b)));break;case"combinator":a(b.left);break;case"list":b.list.forEach(a),b.list.sort((a,b)=>aA(a)<aA(b)?-1:1)}return b}(d),d);c[e]={ast:g,terminal:{type:"terminal",valueContainer:{index:e,value:f,specificity:g.specificity}}}}return c}(a))}build(a){return a(this.branches)}}function aH(a){let b=[];for(;a.length;){let c=aM(a,a=>!0,aI),{matches:d,nonmatches:e,empty:f}=function(a,b){let c=[],d=[],e=[];for(let f of a){let a=f.ast.list;a.length?(a.some(a=>aI(a)===b)?c:d).push(f):e.push(f)}return{matches:c,nonmatches:d,empty:e}}(a,c);a=e,d.length&&b.push(function(a,b){if("tag"===a)return{type:"tagName",variants:Object.entries(aK(b,a=>"tag"===a.type,a=>a.name)).map(([a,b])=>({type:"variant",value:a,cont:aH(b.items)}))};if(a.startsWith("attrValue "))return function(a,b){let c=aK(b,b=>"attrValue"===b.type&&b.name===a,a=>`${a.matcher} ${a.modifier||""} ${a.value}`),d=[];for(let a of Object.values(c)){let b=a.oneSimpleSelector,c=function(a){if("i"===a.modifier){let b=a.value.toLowerCase();switch(a.matcher){case"=":return a=>b===a.toLowerCase();case"~=":return a=>a.toLowerCase().split(/[ \t]+/).includes(b);case"^=":return a=>a.toLowerCase().startsWith(b);case"$=":return a=>a.toLowerCase().endsWith(b);case"*=":return a=>a.toLowerCase().includes(b);case"|=":return a=>{let c=a.toLowerCase();return b===c||c.startsWith(b)&&"-"===c[b.length]}}}else{let b=a.value;switch(a.matcher){case"=":return a=>b===a;case"~=":return a=>a.split(/[ \t]+/).includes(b);case"^=":return a=>a.startsWith(b);case"$=":return a=>a.endsWith(b);case"*=":return a=>a.includes(b);case"|=":return a=>b===a||a.startsWith(b)&&"-"===a[b.length]}}}(b),e=aH(a.items);d.push({type:"matcher",matcher:b.matcher,modifier:b.modifier,value:b.value,predicate:c,cont:e})}return{type:"attrValue",name:a,matchers:d}}(a.substring(10),b);if(a.startsWith("attrPresence "))return function(a,b){for(let c of b)aL(c,b=>"attrPresence"===b.type&&b.name===a);return{type:"attrPresence",name:a,cont:aH(b)}}(a.substring(13),b);if("combinator >"===a)return aJ(">",b);if("combinator +"===a)return aJ("+",b);throw Error(`Unsupported selector kind: ${a}`)}(c,d)),f.length&&b.push(...function(a){let b=[];for(let c of a){let a=c.terminal;if("terminal"===a.type)b.push(a);else{let{matches:c,rest:d}=function(a,b){let c=[],d=[];for(let e of a)b(e)?c.push(e):d.push(e);return{matches:c,rest:d}}(a.cont,a=>"terminal"===a.type);c.forEach(a=>b.push(a)),d.length&&(a.cont=d,b.push(a))}}return b}(f))}return b}function aI(a){switch(a.type){case"attrPresence":return`attrPresence ${a.name}`;case"attrValue":return`attrValue ${a.name}`;case"combinator":return`combinator ${a.combinator}`;default:return a.type}}function aJ(a,b){let c=aK(b,b=>"combinator"===b.type&&b.combinator===a,a=>aA(a.left)),d=[];for(let a of Object.values(c)){let b=aH(a.items),c=a.oneSimpleSelector.left;d.push({ast:c,terminal:{type:"popElement",cont:b}})}return{type:"pushElement",combinator:a,cont:aH(d)}}function aK(a,b,c){let d={};for(;a.length;){let e=aM(a,b,c),f=a=>b(a)&&c(a)===e,{matches:g,rest:h}=function(a,b){let c=[],d=[];for(let e of a)b(e)?c.push(e):d.push(e);return{matches:c,rest:d}}(a,a=>a.ast.list.some(f)),i=null;for(let a of g){let b=aL(a,f);i||(i=b)}if(null==i)throw Error("No simple selector is found.");d[e]={oneSimpleSelector:i,items:g},a=h}return d}function aL(a,b){let c=a.ast.list,d=Array(c.length),e=-1;for(let a=c.length;a-- >0;)b(c[a])&&(d[a]=!0,e=a);if(-1==e)throw Error("Couldn't find the required simple selector.");let f=c[e];return a.ast.list=c.filter((a,b)=>!d[b]),f}function aM(a,b,c){let d={};for(let e of a){let a={};for(let d of e.ast.list.filter(b))a[c(d)]=!0;for(let b of Object.keys(a))d[b]?d[b]++:d[b]=1}let e="",f=0;for(let a of Object.entries(d))a[1]>f&&(e=a[0],f=a[1]);return e}class aN{constructor(a){this.f=a}pickAll(a){return this.f(a)}pick1(a,b=!1){let c=this.f(a),d=c.length;if(0===d)return null;if(1===d)return c[0].value;let e=b?aO:aP,f=c[0];for(let a=1;a<d;a++){let b=c[a];e(f,b)&&(f=b)}return f.value}}function aO(a,b){let c=aF(b.specificity,a.specificity);return c>0||0===c&&b.index<a.index}function aP(a,b){let c=aF(b.specificity,a.specificity);return c>0||0===c&&b.index>a.index}function aQ(a){return new aN(aR(a))}function aR(a){let b=a.map(aS);return(a,...c)=>b.flatMap(b=>b(a,...c))}function aS(a){switch(a.type){case"terminal":{let b=[a.valueContainer];return(a,...c)=>b}case"tagName":var b=a;let c={};for(let a of b.variants)c[a.value]=aR(a.cont);return(a,...b)=>{let d=c[a.name];return d?d(a,...b):[]};case"attrValue":var d=a;let e=[];for(let a of d.matchers){let b=a.predicate,c=aR(a.cont);e.push((a,d,...e)=>b(a)?c(d,...e):[])}let f=d.name;return(a,...b)=>{let c=a.attribs[f];return c||""===c?e.flatMap(d=>d(c,a,...b)):[]};case"attrPresence":var g=a;let h=g.name,i=aR(g.cont);return(a,...b)=>Object.prototype.hasOwnProperty.call(a.attribs,h)?i(a,...b):[];case"pushElement":var j=a;let k=aR(j.cont),l="+"===j.combinator?aT:aU;return(a,...b)=>{let c=l(a);return null===c?[]:k(c,a,...b)};case"popElement":var m=a;let n=aR(m.cont);return(a,b,...c)=>n(b,...c)}}let aT=a=>{let b=a.prev;return null===b?null:t(b)?b:aT(b)},aU=a=>{let b=a.parent;return b&&t(b)?b:null},aV=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀\ud835\udd04rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀\ud835\udd38plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀\ud835\udc9cign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀\ud835\udd05pf;쀀\ud835\udd39eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀\ud835\udc9epĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀\ud835\udd07Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀\ud835\udd3bƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀\ud835\udc9frok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀\ud835\udd08rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀\ud835\udd3csilon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀\ud835\udd09lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀\ud835\udd3dAll;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀\ud835\udd0a;拙pf;쀀\ud835\udd3eeater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀\ud835\udca2;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀\ud835\udd40a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀\ud835\udd0dpf;쀀\ud835\udd41ǣ߇\0ߌr;쀀\ud835\udca5rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀\ud835\udd0epf;쀀\ud835\udd42cr;쀀\ud835\udca6րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀\ud835\udd0fĀ;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀\ud835\udd43erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀\ud835\udd10nusPlus;戓pf;쀀\ud835\udd44cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀\ud835\udd11ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀\ud835\udca9ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀\ud835\udd12rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀\ud835\udd46enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀\ud835\udcaaash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀\ud835\udd13i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀\ud835\udcab;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀\ud835\udd14pf;愚cr;쀀\ud835\udcac؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀\ud835\udd16ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀\ud835\udd4aɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀\ud835\udcaear;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀\ud835\udd17Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀\ud835\udd4bipleDot;惛Āctዖዛr;쀀\ud835\udcafrok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀\ud835\udd18rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀\ud835\udd4cЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀\ud835\udcb0ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀\ud835\udd19pf;쀀\ud835\udd4dcr;쀀\ud835\udcb1dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀\ud835\udd1apf;쀀\ud835\udd4ecr;쀀\ud835\udcb2Ȁfiosᓋᓐᓒᓘr;쀀\ud835\udd1b;䎞pf;쀀\ud835\udd4fcr;쀀\ud835\udcb3ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀\ud835\udd1cpf;쀀\ud835\udd50cr;쀀\ud835\udcb4ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀\ud835\udcb5௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀\ud835\udd1erave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀\ud835\udd52΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀\ud835\udcb6;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀\ud835\udd1fg΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀\ud835\udd53Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀\ud835\udcb7mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀\ud835\udd20ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀\ud835\udd54oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀\ud835\udcb8Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀\ud835\udd21arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀\ud835\udd55ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀\ud835\udcb9;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀\ud835\udd22ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀\ud835\udd56ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀\ud835\udd23lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀\ud835\udd57ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀\ud835\udcbbࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀\ud835\udd24Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀\ud835\udd58Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀\ud835\udd25sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀\ud835\udd59bar;怕ƀclt≯≴≸r;쀀\ud835\udcbdasè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀\ud835\udd26rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀\ud835\udd5aa;䎹uest耻¿䂿Āci⎊⎏r;쀀\ud835\udcbenʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀\ud835\udd27ath;䈷pf;쀀\ud835\udd5bǣ⏬\0⏱r;쀀\ud835\udcbfrcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀\ud835\udd28reen;䄸cy;䑅cy;䑜pf;쀀\ud835\udd5ccr;쀀\ud835\udcc0஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀\ud835\udd29Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀\ud835\udd5dus;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀\ud835\udcc1mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀\ud835\udd2ao;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀\ud835\udd5eĀct⣸⣽r;쀀\ud835\udcc2pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀\ud835\udd2bȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀\ud835\udd5f膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀\ud835\udcc3ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀\ud835\udd2cͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀\ud835\udd60ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀\ud835\udd2dƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀\ud835\udd61nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀\ud835\udcc5;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀\ud835\udd2epf;쀀\ud835\udd62rime;恗cr;쀀\ud835\udcc6ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀\ud835\udd2fĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀\ud835\udd63us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀\ud835\udcc7Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀\ud835\udd30Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀\ud835\udd64aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀\ud835\udcc8tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀\ud835\udd31Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀\ud835\udd65rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀\ud835\udcc9;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀\ud835\udd32rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀\ud835\udd66̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀\ud835\udccaƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀\ud835\udd33tré㦮suĀbp㧯㧱»ജ»൙pf;쀀\ud835\udd67roð໻tré㦴Ācu㨆㨋r;쀀\ud835\udccbĀbp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀\ud835\udd34pf;쀀\ud835\udd68Ā;eᑹ㩦atèᑹcr;쀀\ud835\udcccૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀\ud835\udd35ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀\ud835\udd69imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀\ud835\udccdĀpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀\ud835\udd36cy;䑗pf;쀀\ud835\udd6acr;쀀\ud835\udcceĀcm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀\ud835\udd37cy;䐶grarr;懝pf;쀀\ud835\udd6bcr;쀀\ud835\udccfĀjn㮅㮇;怍j;怌'.split("").map(a=>a.charCodeAt(0))),aW=new Uint16Array("Ȁaglq	\x15\x18\x1bɭ\x0f\0\0\x12p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map(a=>a.charCodeAt(0))),aX=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),aY=null!=(by=String.fromCodePoint)?by:function(a){let b="";return a>65535&&(a-=65536,b+=String.fromCharCode(a>>>10&1023|55296),a=56320|1023&a),b+=String.fromCharCode(a)};function aZ(a){var b;return a>=55296&&a<=57343||a>1114111?65533:null!=(b=aX.get(a))?b:a}function a$(a){return a>=bz.ZERO&&a<=bz.NINE}!function(a){a[a.NUM=35]="NUM",a[a.SEMI=59]="SEMI",a[a.EQUALS=61]="EQUALS",a[a.ZERO=48]="ZERO",a[a.NINE=57]="NINE",a[a.LOWER_A=97]="LOWER_A",a[a.LOWER_F=102]="LOWER_F",a[a.LOWER_X=120]="LOWER_X",a[a.LOWER_Z=122]="LOWER_Z",a[a.UPPER_A=65]="UPPER_A",a[a.UPPER_F=70]="UPPER_F",a[a.UPPER_Z=90]="UPPER_Z"}(bz||(bz={})),!function(a){a[a.VALUE_LENGTH=49152]="VALUE_LENGTH",a[a.BRANCH_LENGTH=16256]="BRANCH_LENGTH",a[a.JUMP_TABLE=127]="JUMP_TABLE"}(bA||(bA={})),!function(a){a[a.EntityStart=0]="EntityStart",a[a.NumericStart=1]="NumericStart",a[a.NumericDecimal=2]="NumericDecimal",a[a.NumericHex=3]="NumericHex",a[a.NamedEntity=4]="NamedEntity"}(bB||(bB={})),function(a){a[a.Legacy=0]="Legacy",a[a.Strict=1]="Strict",a[a.Attribute=2]="Attribute"}(bC||(bC={}));class a_{constructor(a,b,c){this.decodeTree=a,this.emitCodePoint=b,this.errors=c,this.state=bB.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=bC.Strict}startEntity(a){this.decodeMode=a,this.state=bB.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(a,b){switch(this.state){case bB.EntityStart:if(a.charCodeAt(b)===bz.NUM)return this.state=bB.NumericStart,this.consumed+=1,this.stateNumericStart(a,b+1);return this.state=bB.NamedEntity,this.stateNamedEntity(a,b);case bB.NumericStart:return this.stateNumericStart(a,b);case bB.NumericDecimal:return this.stateNumericDecimal(a,b);case bB.NumericHex:return this.stateNumericHex(a,b);case bB.NamedEntity:return this.stateNamedEntity(a,b)}}stateNumericStart(a,b){return b>=a.length?-1:(32|a.charCodeAt(b))===bz.LOWER_X?(this.state=bB.NumericHex,this.consumed+=1,this.stateNumericHex(a,b+1)):(this.state=bB.NumericDecimal,this.stateNumericDecimal(a,b))}addToNumericResult(a,b,c,d){if(b!==c){let e=c-b;this.result=this.result*Math.pow(d,e)+parseInt(a.substr(b,e),d),this.consumed+=e}}stateNumericHex(a,b){let c=b;for(;b<a.length;){var d;let e=a.charCodeAt(b);if(!a$(e)&&(!((d=e)>=bz.UPPER_A)||!(d<=bz.UPPER_F))&&(!(d>=bz.LOWER_A)||!(d<=bz.LOWER_F)))return this.addToNumericResult(a,c,b,16),this.emitNumericEntity(e,3);b+=1}return this.addToNumericResult(a,c,b,16),-1}stateNumericDecimal(a,b){let c=b;for(;b<a.length;){let d=a.charCodeAt(b);if(!a$(d))return this.addToNumericResult(a,c,b,10),this.emitNumericEntity(d,2);b+=1}return this.addToNumericResult(a,c,b,10),-1}emitNumericEntity(a,b){var c;if(this.consumed<=b)return null==(c=this.errors)||c.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(a===bz.SEMI)this.consumed+=1;else if(this.decodeMode===bC.Strict)return 0;return this.emitCodePoint(aZ(this.result),this.consumed),this.errors&&(a!==bz.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(a,b){let{decodeTree:c}=this,d=c[this.treeIndex],e=(d&bA.VALUE_LENGTH)>>14;for(;b<a.length;b++,this.excess++){let f=a.charCodeAt(b);if(this.treeIndex=a1(c,d,this.treeIndex+Math.max(1,e),f),this.treeIndex<0)return 0===this.result||this.decodeMode===bC.Attribute&&(0===e||function(a){var b;return a===bz.EQUALS||(b=a)>=bz.UPPER_A&&b<=bz.UPPER_Z||b>=bz.LOWER_A&&b<=bz.LOWER_Z||a$(b)}(f))?0:this.emitNotTerminatedNamedEntity();if(0!=(e=((d=c[this.treeIndex])&bA.VALUE_LENGTH)>>14)){if(f===bz.SEMI)return this.emitNamedEntityData(this.treeIndex,e,this.consumed+this.excess);this.decodeMode!==bC.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return -1}emitNotTerminatedNamedEntity(){var a;let{result:b,decodeTree:c}=this,d=(c[b]&bA.VALUE_LENGTH)>>14;return this.emitNamedEntityData(b,d,this.consumed),null==(a=this.errors)||a.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(a,b,c){let{decodeTree:d}=this;return this.emitCodePoint(1===b?d[a]&~bA.VALUE_LENGTH:d[a+1],c),3===b&&this.emitCodePoint(d[a+2],c),c}end(){var a;switch(this.state){case bB.NamedEntity:return 0!==this.result&&(this.decodeMode!==bC.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case bB.NumericDecimal:return this.emitNumericEntity(0,2);case bB.NumericHex:return this.emitNumericEntity(0,3);case bB.NumericStart:return null==(a=this.errors)||a.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case bB.EntityStart:return 0}}}function a0(a){let b="",c=new a_(a,a=>b+=aY(a));return function(a,d){let e=0,f=0;for(;(f=a.indexOf("&",f))>=0;){b+=a.slice(e,f),c.startEntity(d);let g=c.write(a,f+1);if(g<0){e=f+c.end();break}e=f+g,f=0===g?e+1:e}let g=b+a.slice(e);return b="",g}}function a1(a,b,c,d){let e=(b&bA.BRANCH_LENGTH)>>7,f=b&bA.JUMP_TABLE;if(0===e)return 0!==f&&d===f?c:-1;if(f){let b=d-f;return b<0||b>=e?-1:a[c+b]-1}let g=c,h=g+e-1;for(;g<=h;){let b=g+h>>>1,c=a[b];if(c<d)g=b+1;else{if(!(c>d))return a[b+e];h=b-1}}return -1}function a2(a){return a===bD.Space||a===bD.NewLine||a===bD.Tab||a===bD.FormFeed||a===bD.CarriageReturn}function a3(a){return a===bD.Slash||a===bD.Gt||a2(a)}function a4(a){return a>=bD.Zero&&a<=bD.Nine}a0(aV),a0(aW),function(a){a[a.Tab=9]="Tab",a[a.NewLine=10]="NewLine",a[a.FormFeed=12]="FormFeed",a[a.CarriageReturn=13]="CarriageReturn",a[a.Space=32]="Space",a[a.ExclamationMark=33]="ExclamationMark",a[a.Number=35]="Number",a[a.Amp=38]="Amp",a[a.SingleQuote=39]="SingleQuote",a[a.DoubleQuote=34]="DoubleQuote",a[a.Dash=45]="Dash",a[a.Slash=47]="Slash",a[a.Zero=48]="Zero",a[a.Nine=57]="Nine",a[a.Semi=59]="Semi",a[a.Lt=60]="Lt",a[a.Eq=61]="Eq",a[a.Gt=62]="Gt",a[a.Questionmark=63]="Questionmark",a[a.UpperA=65]="UpperA",a[a.LowerA=97]="LowerA",a[a.UpperF=70]="UpperF",a[a.LowerF=102]="LowerF",a[a.UpperZ=90]="UpperZ",a[a.LowerZ=122]="LowerZ",a[a.LowerX=120]="LowerX",a[a.OpeningSquareBracket=91]="OpeningSquareBracket"}(bD||(bD={})),function(a){a[a.Text=1]="Text",a[a.BeforeTagName=2]="BeforeTagName",a[a.InTagName=3]="InTagName",a[a.InSelfClosingTag=4]="InSelfClosingTag",a[a.BeforeClosingTagName=5]="BeforeClosingTagName",a[a.InClosingTagName=6]="InClosingTagName",a[a.AfterClosingTagName=7]="AfterClosingTagName",a[a.BeforeAttributeName=8]="BeforeAttributeName",a[a.InAttributeName=9]="InAttributeName",a[a.AfterAttributeName=10]="AfterAttributeName",a[a.BeforeAttributeValue=11]="BeforeAttributeValue",a[a.InAttributeValueDq=12]="InAttributeValueDq",a[a.InAttributeValueSq=13]="InAttributeValueSq",a[a.InAttributeValueNq=14]="InAttributeValueNq",a[a.BeforeDeclaration=15]="BeforeDeclaration",a[a.InDeclaration=16]="InDeclaration",a[a.InProcessingInstruction=17]="InProcessingInstruction",a[a.BeforeComment=18]="BeforeComment",a[a.CDATASequence=19]="CDATASequence",a[a.InSpecialComment=20]="InSpecialComment",a[a.InCommentLike=21]="InCommentLike",a[a.BeforeSpecialS=22]="BeforeSpecialS",a[a.SpecialStartSequence=23]="SpecialStartSequence",a[a.InSpecialTag=24]="InSpecialTag",a[a.BeforeEntity=25]="BeforeEntity",a[a.BeforeNumericEntity=26]="BeforeNumericEntity",a[a.InNamedEntity=27]="InNamedEntity",a[a.InNumericEntity=28]="InNumericEntity",a[a.InHexEntity=29]="InHexEntity"}(bE||(bE={})),!function(a){a[a.NoValue=0]="NoValue",a[a.Unquoted=1]="Unquoted",a[a.Single=2]="Single",a[a.Double=3]="Double"}(bF||(bF={}));let a5={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101])};class a6{constructor({xmlMode:a=!1,decodeEntities:b=!0},c){this.cbs=c,this.state=bE.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=bE.Text,this.isSpecial=!1,this.running=!0,this.offset=0,this.currentSequence=void 0,this.sequenceIndex=0,this.trieIndex=0,this.trieCurrent=0,this.entityResult=0,this.entityExcess=0,this.xmlMode=a,this.decodeEntities=b,this.entityTrie=a?aW:aV}reset(){this.state=bE.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=bE.Text,this.currentSequence=void 0,this.running=!0,this.offset=0}write(a){this.offset+=this.buffer.length,this.buffer=a,this.parse()}end(){this.running&&this.finish()}pause(){this.running=!1}resume(){this.running=!0,this.index<this.buffer.length+this.offset&&this.parse()}getIndex(){return this.index}getSectionStart(){return this.sectionStart}stateText(a){a===bD.Lt||!this.decodeEntities&&this.fastForwardTo(bD.Lt)?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=bE.BeforeTagName,this.sectionStart=this.index):this.decodeEntities&&a===bD.Amp&&(this.state=bE.BeforeEntity)}stateSpecialStartSequence(a){let b=this.sequenceIndex===this.currentSequence.length;if(b?a3(a):(32|a)===this.currentSequence[this.sequenceIndex]){if(!b)return void this.sequenceIndex++}else this.isSpecial=!1;this.sequenceIndex=0,this.state=bE.InTagName,this.stateInTagName(a)}stateInSpecialTag(a){if(this.sequenceIndex===this.currentSequence.length){if(a===bD.Gt||a2(a)){let b=this.index-this.currentSequence.length;if(this.sectionStart<b){let a=this.index;this.index=b,this.cbs.ontext(this.sectionStart,b),this.index=a}this.isSpecial=!1,this.sectionStart=b+2,this.stateInClosingTagName(a);return}this.sequenceIndex=0}(32|a)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===a5.TitleEnd?this.decodeEntities&&a===bD.Amp&&(this.state=bE.BeforeEntity):this.fastForwardTo(bD.Lt)&&(this.sequenceIndex=1):this.sequenceIndex=Number(a===bD.Lt)}stateCDATASequence(a){a===a5.Cdata[this.sequenceIndex]?++this.sequenceIndex===a5.Cdata.length&&(this.state=bE.InCommentLike,this.currentSequence=a5.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=bE.InDeclaration,this.stateInDeclaration(a))}fastForwardTo(a){for(;++this.index<this.buffer.length+this.offset;)if(this.buffer.charCodeAt(this.index-this.offset)===a)return!0;return this.index=this.buffer.length+this.offset-1,!1}stateInCommentLike(a){a===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===a5.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index,2):this.cbs.oncomment(this.sectionStart,this.index,2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=bE.Text):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):a!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}isTagStartChar(a){return this.xmlMode?!a3(a):a>=bD.LowerA&&a<=bD.LowerZ||a>=bD.UpperA&&a<=bD.UpperZ}startSpecial(a,b){this.isSpecial=!0,this.currentSequence=a,this.sequenceIndex=b,this.state=bE.SpecialStartSequence}stateBeforeTagName(a){if(a===bD.ExclamationMark)this.state=bE.BeforeDeclaration,this.sectionStart=this.index+1;else if(a===bD.Questionmark)this.state=bE.InProcessingInstruction,this.sectionStart=this.index+1;else if(this.isTagStartChar(a)){let b=32|a;this.sectionStart=this.index,this.xmlMode||b!==a5.TitleEnd[2]?this.state=this.xmlMode||b!==a5.ScriptEnd[2]?bE.InTagName:bE.BeforeSpecialS:this.startSpecial(a5.TitleEnd,3)}else a===bD.Slash?this.state=bE.BeforeClosingTagName:(this.state=bE.Text,this.stateText(a))}stateInTagName(a){a3(a)&&(this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=bE.BeforeAttributeName,this.stateBeforeAttributeName(a))}stateBeforeClosingTagName(a){a2(a)||(a===bD.Gt?this.state=bE.Text:(this.state=this.isTagStartChar(a)?bE.InClosingTagName:bE.InSpecialComment,this.sectionStart=this.index))}stateInClosingTagName(a){(a===bD.Gt||a2(a))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=bE.AfterClosingTagName,this.stateAfterClosingTagName(a))}stateAfterClosingTagName(a){(a===bD.Gt||this.fastForwardTo(bD.Gt))&&(this.state=bE.Text,this.baseState=bE.Text,this.sectionStart=this.index+1)}stateBeforeAttributeName(a){a===bD.Gt?(this.cbs.onopentagend(this.index),this.isSpecial?(this.state=bE.InSpecialTag,this.sequenceIndex=0):this.state=bE.Text,this.baseState=this.state,this.sectionStart=this.index+1):a===bD.Slash?this.state=bE.InSelfClosingTag:a2(a)||(this.state=bE.InAttributeName,this.sectionStart=this.index)}stateInSelfClosingTag(a){a===bD.Gt?(this.cbs.onselfclosingtag(this.index),this.state=bE.Text,this.baseState=bE.Text,this.sectionStart=this.index+1,this.isSpecial=!1):a2(a)||(this.state=bE.BeforeAttributeName,this.stateBeforeAttributeName(a))}stateInAttributeName(a){(a===bD.Eq||a3(a))&&(this.cbs.onattribname(this.sectionStart,this.index),this.sectionStart=-1,this.state=bE.AfterAttributeName,this.stateAfterAttributeName(a))}stateAfterAttributeName(a){a===bD.Eq?this.state=bE.BeforeAttributeValue:a===bD.Slash||a===bD.Gt?(this.cbs.onattribend(bF.NoValue,this.index),this.state=bE.BeforeAttributeName,this.stateBeforeAttributeName(a)):a2(a)||(this.cbs.onattribend(bF.NoValue,this.index),this.state=bE.InAttributeName,this.sectionStart=this.index)}stateBeforeAttributeValue(a){a===bD.DoubleQuote?(this.state=bE.InAttributeValueDq,this.sectionStart=this.index+1):a===bD.SingleQuote?(this.state=bE.InAttributeValueSq,this.sectionStart=this.index+1):a2(a)||(this.sectionStart=this.index,this.state=bE.InAttributeValueNq,this.stateInAttributeValueNoQuotes(a))}handleInAttributeValue(a,b){a===b||!this.decodeEntities&&this.fastForwardTo(b)?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(b===bD.DoubleQuote?bF.Double:bF.Single,this.index),this.state=bE.BeforeAttributeName):this.decodeEntities&&a===bD.Amp&&(this.baseState=this.state,this.state=bE.BeforeEntity)}stateInAttributeValueDoubleQuotes(a){this.handleInAttributeValue(a,bD.DoubleQuote)}stateInAttributeValueSingleQuotes(a){this.handleInAttributeValue(a,bD.SingleQuote)}stateInAttributeValueNoQuotes(a){a2(a)||a===bD.Gt?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(bF.Unquoted,this.index),this.state=bE.BeforeAttributeName,this.stateBeforeAttributeName(a)):this.decodeEntities&&a===bD.Amp&&(this.baseState=this.state,this.state=bE.BeforeEntity)}stateBeforeDeclaration(a){a===bD.OpeningSquareBracket?(this.state=bE.CDATASequence,this.sequenceIndex=0):this.state=a===bD.Dash?bE.BeforeComment:bE.InDeclaration}stateInDeclaration(a){(a===bD.Gt||this.fastForwardTo(bD.Gt))&&(this.cbs.ondeclaration(this.sectionStart,this.index),this.state=bE.Text,this.sectionStart=this.index+1)}stateInProcessingInstruction(a){(a===bD.Gt||this.fastForwardTo(bD.Gt))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=bE.Text,this.sectionStart=this.index+1)}stateBeforeComment(a){a===bD.Dash?(this.state=bE.InCommentLike,this.currentSequence=a5.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=bE.InDeclaration}stateInSpecialComment(a){(a===bD.Gt||this.fastForwardTo(bD.Gt))&&(this.cbs.oncomment(this.sectionStart,this.index,0),this.state=bE.Text,this.sectionStart=this.index+1)}stateBeforeSpecialS(a){let b=32|a;b===a5.ScriptEnd[3]?this.startSpecial(a5.ScriptEnd,4):b===a5.StyleEnd[3]?this.startSpecial(a5.StyleEnd,4):(this.state=bE.InTagName,this.stateInTagName(a))}stateBeforeEntity(a){this.entityExcess=1,this.entityResult=0,a===bD.Number?this.state=bE.BeforeNumericEntity:a===bD.Amp||(this.trieIndex=0,this.trieCurrent=this.entityTrie[0],this.state=bE.InNamedEntity,this.stateInNamedEntity(a))}stateInNamedEntity(a){if(this.entityExcess+=1,this.trieIndex=a1(this.entityTrie,this.trieCurrent,this.trieIndex+1,a),this.trieIndex<0){this.emitNamedEntity(),this.index--;return}this.trieCurrent=this.entityTrie[this.trieIndex];let b=this.trieCurrent&bA.VALUE_LENGTH;if(b){let c=(b>>14)-1;if(this.allowLegacyEntity()||a===bD.Semi){let a=this.index-this.entityExcess+1;a>this.sectionStart&&this.emitPartial(this.sectionStart,a),this.entityResult=this.trieIndex,this.trieIndex+=c,this.entityExcess=0,this.sectionStart=this.index+1,0===c&&this.emitNamedEntity()}else this.trieIndex+=c}}emitNamedEntity(){if(this.state=this.baseState,0!==this.entityResult)switch((this.entityTrie[this.entityResult]&bA.VALUE_LENGTH)>>14){case 1:this.emitCodePoint(this.entityTrie[this.entityResult]&~bA.VALUE_LENGTH);break;case 2:this.emitCodePoint(this.entityTrie[this.entityResult+1]);break;case 3:this.emitCodePoint(this.entityTrie[this.entityResult+1]),this.emitCodePoint(this.entityTrie[this.entityResult+2])}}stateBeforeNumericEntity(a){(32|a)===bD.LowerX?(this.entityExcess++,this.state=bE.InHexEntity):(this.state=bE.InNumericEntity,this.stateInNumericEntity(a))}emitNumericEntity(a){let b=this.index-this.entityExcess-1;b+2+Number(this.state===bE.InHexEntity)!==this.index&&(b>this.sectionStart&&this.emitPartial(this.sectionStart,b),this.sectionStart=this.index+Number(a),this.emitCodePoint(aZ(this.entityResult))),this.state=this.baseState}stateInNumericEntity(a){a===bD.Semi?this.emitNumericEntity(!0):a4(a)?(this.entityResult=10*this.entityResult+(a-bD.Zero),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)}stateInHexEntity(a){if(a===bD.Semi)this.emitNumericEntity(!0);else if(a4(a))this.entityResult=16*this.entityResult+(a-bD.Zero),this.entityExcess++;else a>=bD.UpperA&&a<=bD.UpperF||a>=bD.LowerA&&a<=bD.LowerF?(this.entityResult=16*this.entityResult+((32|a)-bD.LowerA+10),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)}allowLegacyEntity(){return!this.xmlMode&&(this.baseState===bE.Text||this.baseState===bE.InSpecialTag)}cleanup(){this.running&&this.sectionStart!==this.index&&(this.state===bE.Text||this.state===bE.InSpecialTag&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):(this.state===bE.InAttributeValueDq||this.state===bE.InAttributeValueSq||this.state===bE.InAttributeValueNq)&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}shouldContinue(){return this.index<this.buffer.length+this.offset&&this.running}parse(){for(;this.shouldContinue();){let a=this.buffer.charCodeAt(this.index-this.offset);switch(this.state){case bE.Text:this.stateText(a);break;case bE.SpecialStartSequence:this.stateSpecialStartSequence(a);break;case bE.InSpecialTag:this.stateInSpecialTag(a);break;case bE.CDATASequence:this.stateCDATASequence(a);break;case bE.InAttributeValueDq:this.stateInAttributeValueDoubleQuotes(a);break;case bE.InAttributeName:this.stateInAttributeName(a);break;case bE.InCommentLike:this.stateInCommentLike(a);break;case bE.InSpecialComment:this.stateInSpecialComment(a);break;case bE.BeforeAttributeName:this.stateBeforeAttributeName(a);break;case bE.InTagName:this.stateInTagName(a);break;case bE.InClosingTagName:this.stateInClosingTagName(a);break;case bE.BeforeTagName:this.stateBeforeTagName(a);break;case bE.AfterAttributeName:this.stateAfterAttributeName(a);break;case bE.InAttributeValueSq:this.stateInAttributeValueSingleQuotes(a);break;case bE.BeforeAttributeValue:this.stateBeforeAttributeValue(a);break;case bE.BeforeClosingTagName:this.stateBeforeClosingTagName(a);break;case bE.AfterClosingTagName:this.stateAfterClosingTagName(a);break;case bE.BeforeSpecialS:this.stateBeforeSpecialS(a);break;case bE.InAttributeValueNq:this.stateInAttributeValueNoQuotes(a);break;case bE.InSelfClosingTag:this.stateInSelfClosingTag(a);break;case bE.InDeclaration:this.stateInDeclaration(a);break;case bE.BeforeDeclaration:this.stateBeforeDeclaration(a);break;case bE.BeforeComment:this.stateBeforeComment(a);break;case bE.InProcessingInstruction:this.stateInProcessingInstruction(a);break;case bE.InNamedEntity:this.stateInNamedEntity(a);break;case bE.BeforeEntity:this.stateBeforeEntity(a);break;case bE.InHexEntity:this.stateInHexEntity(a);break;case bE.InNumericEntity:this.stateInNumericEntity(a);break;default:this.stateBeforeNumericEntity(a)}this.index++}this.cleanup()}finish(){this.state===bE.InNamedEntity&&this.emitNamedEntity(),this.sectionStart<this.index&&this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){let a=this.buffer.length+this.offset;this.state===bE.InCommentLike?this.currentSequence===a5.CdataEnd?this.cbs.oncdata(this.sectionStart,a,0):this.cbs.oncomment(this.sectionStart,a,0):this.state===bE.InNumericEntity&&this.allowLegacyEntity()||this.state===bE.InHexEntity&&this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state===bE.InTagName||this.state===bE.BeforeAttributeName||this.state===bE.BeforeAttributeValue||this.state===bE.AfterAttributeName||this.state===bE.InAttributeName||this.state===bE.InAttributeValueSq||this.state===bE.InAttributeValueDq||this.state===bE.InAttributeValueNq||this.state===bE.InClosingTagName||this.cbs.ontext(this.sectionStart,a)}emitPartial(a,b){this.baseState!==bE.Text&&this.baseState!==bE.InSpecialTag?this.cbs.onattribdata(a,b):this.cbs.ontext(a,b)}emitCodePoint(a){this.baseState!==bE.Text&&this.baseState!==bE.InSpecialTag?this.cbs.onattribentity(a):this.cbs.ontextentity(a)}}let a7=new Set(["input","option","optgroup","select","button","datalist","textarea"]),a8=new Set(["p"]),a9=new Set(["thead","tbody"]),ba=new Set(["dd","dt"]),bb=new Set(["rt","rp"]),bc=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",a8],["h1",a8],["h2",a8],["h3",a8],["h4",a8],["h5",a8],["h6",a8],["select",a7],["input",a7],["output",a7],["button",a7],["datalist",a7],["textarea",a7],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",ba],["dt",ba],["address",a8],["article",a8],["aside",a8],["blockquote",a8],["details",a8],["div",a8],["dl",a8],["fieldset",a8],["figcaption",a8],["figure",a8],["footer",a8],["form",a8],["header",a8],["hr",a8],["main",a8],["nav",a8],["ol",a8],["pre",a8],["section",a8],["table",a8],["ul",a8],["rt",bb],["rp",bb],["tbody",a9],["tfoot",a9]]),bd=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),be=new Set(["math","svg"]),bf=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),bg=/\s|\//;class bh{constructor(a,b={}){var c,d,e,f,g;this.options=b,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.foreignContext=[],this.buffers=[],this.bufferOffset=0,this.writeIndex=0,this.ended=!1,this.cbs=null!=a?a:{},this.lowerCaseTagNames=null!=(c=b.lowerCaseTags)?c:!b.xmlMode,this.lowerCaseAttributeNames=null!=(d=b.lowerCaseAttributeNames)?d:!b.xmlMode,this.tokenizer=new(null!=(e=b.Tokenizer)?e:a6)(this.options,this),null==(g=(f=this.cbs).onparserinit)||g.call(f,this)}ontext(a,b){var c,d;let e=this.getSlice(a,b);this.endIndex=b-1,null==(d=(c=this.cbs).ontext)||d.call(c,e),this.startIndex=b}ontextentity(a){var b,c;let d=this.tokenizer.getSectionStart();this.endIndex=d-1,null==(c=(b=this.cbs).ontext)||c.call(b,aY(a)),this.startIndex=d}isVoidElement(a){return!this.options.xmlMode&&bd.has(a)}onopentagname(a,b){this.endIndex=b;let c=this.getSlice(a,b);this.lowerCaseTagNames&&(c=c.toLowerCase()),this.emitOpenTag(c)}emitOpenTag(a){var b,c,d,e;this.openTagStart=this.startIndex,this.tagname=a;let f=!this.options.xmlMode&&bc.get(a);if(f)for(;this.stack.length>0&&f.has(this.stack[this.stack.length-1]);){let a=this.stack.pop();null==(c=(b=this.cbs).onclosetag)||c.call(b,a,!0)}!this.isVoidElement(a)&&(this.stack.push(a),be.has(a)?this.foreignContext.push(!0):bf.has(a)&&this.foreignContext.push(!1)),null==(e=(d=this.cbs).onopentagname)||e.call(d,a),this.cbs.onopentag&&(this.attribs={})}endOpenTag(a){var b,c;this.startIndex=this.openTagStart,this.attribs&&(null==(c=(b=this.cbs).onopentag)||c.call(b,this.tagname,this.attribs,a),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""}onopentagend(a){this.endIndex=a,this.endOpenTag(!1),this.startIndex=a+1}onclosetag(a,b){var c,d,e,f,g,h;this.endIndex=b;let i=this.getSlice(a,b);if(this.lowerCaseTagNames&&(i=i.toLowerCase()),(be.has(i)||bf.has(i))&&this.foreignContext.pop(),this.isVoidElement(i))this.options.xmlMode||"br"!==i||(null==(d=(c=this.cbs).onopentagname)||d.call(c,"br"),null==(f=(e=this.cbs).onopentag)||f.call(e,"br",{},!0),null==(h=(g=this.cbs).onclosetag)||h.call(g,"br",!1));else{let a=this.stack.lastIndexOf(i);if(-1!==a)if(this.cbs.onclosetag){let b=this.stack.length-a;for(;b--;)this.cbs.onclosetag(this.stack.pop(),0!==b)}else this.stack.length=a;else this.options.xmlMode||"p"!==i||(this.emitOpenTag("p"),this.closeCurrentTag(!0))}this.startIndex=b+1}onselfclosingtag(a){this.endIndex=a,this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]?(this.closeCurrentTag(!1),this.startIndex=a+1):this.onopentagend(a)}closeCurrentTag(a){var b,c;let d=this.tagname;this.endOpenTag(a),this.stack[this.stack.length-1]===d&&(null==(c=(b=this.cbs).onclosetag)||c.call(b,d,!a),this.stack.pop())}onattribname(a,b){this.startIndex=a;let c=this.getSlice(a,b);this.attribname=this.lowerCaseAttributeNames?c.toLowerCase():c}onattribdata(a,b){this.attribvalue+=this.getSlice(a,b)}onattribentity(a){this.attribvalue+=aY(a)}onattribend(a,b){var c,d;this.endIndex=b,null==(d=(c=this.cbs).onattribute)||d.call(c,this.attribname,this.attribvalue,a===bF.Double?'"':a===bF.Single?"'":a===bF.NoValue?void 0:null),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribvalue=""}getInstructionName(a){let b=a.search(bg),c=b<0?a:a.substr(0,b);return this.lowerCaseTagNames&&(c=c.toLowerCase()),c}ondeclaration(a,b){this.endIndex=b;let c=this.getSlice(a,b);if(this.cbs.onprocessinginstruction){let a=this.getInstructionName(c);this.cbs.onprocessinginstruction(`!${a}`,`!${c}`)}this.startIndex=b+1}onprocessinginstruction(a,b){this.endIndex=b;let c=this.getSlice(a,b);if(this.cbs.onprocessinginstruction){let a=this.getInstructionName(c);this.cbs.onprocessinginstruction(`?${a}`,`?${c}`)}this.startIndex=b+1}oncomment(a,b,c){var d,e,f,g;this.endIndex=b,null==(e=(d=this.cbs).oncomment)||e.call(d,this.getSlice(a,b-c)),null==(g=(f=this.cbs).oncommentend)||g.call(f),this.startIndex=b+1}oncdata(a,b,c){var d,e,f,g,h,i,j,k,l,m;this.endIndex=b;let n=this.getSlice(a,b-c);this.options.xmlMode||this.options.recognizeCDATA?(null==(e=(d=this.cbs).oncdatastart)||e.call(d),null==(g=(f=this.cbs).ontext)||g.call(f,n),null==(i=(h=this.cbs).oncdataend)||i.call(h)):(null==(k=(j=this.cbs).oncomment)||k.call(j,`[CDATA[${n}]]`),null==(m=(l=this.cbs).oncommentend)||m.call(l)),this.startIndex=b+1}onend(){var a,b;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(let a=this.stack.length;a>0;this.cbs.onclosetag(this.stack[--a],!0));}null==(b=(a=this.cbs).onend)||b.call(a)}reset(){var a,b,c,d;null==(b=(a=this.cbs).onreset)||b.call(a),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack.length=0,this.startIndex=0,this.endIndex=0,null==(d=(c=this.cbs).onparserinit)||d.call(c,this),this.buffers.length=0,this.bufferOffset=0,this.writeIndex=0,this.ended=!1}parseComplete(a){this.reset(),this.end(a)}getSlice(a,b){for(;a-this.bufferOffset>=this.buffers[0].length;)this.shiftBuffer();let c=this.buffers[0].slice(a-this.bufferOffset,b-this.bufferOffset);for(;b-this.bufferOffset>this.buffers[0].length;)this.shiftBuffer(),c+=this.buffers[0].slice(0,b-this.bufferOffset);return c}shiftBuffer(){this.bufferOffset+=this.buffers[0].length,this.writeIndex--,this.buffers.shift()}write(a){var b,c;if(this.ended){null==(c=(b=this.cbs).onerror)||c.call(b,Error(".write() after done!"));return}this.buffers.push(a),this.tokenizer.running&&(this.tokenizer.write(a),this.writeIndex++)}end(a){var b,c;if(this.ended){null==(c=(b=this.cbs).onerror)||c.call(b,Error(".end() after done!"));return}a&&this.write(a),this.ended=!0,this.tokenizer.end()}pause(){this.tokenizer.pause()}resume(){for(this.tokenizer.resume();this.tokenizer.running&&this.writeIndex<this.buffers.length;)this.tokenizer.write(this.buffers[this.writeIndex++]);this.ended&&this.tokenizer.end()}parseChunk(a){this.write(a)}done(a){this.end(a)}}let bi=/["&'<>$\x80-\uFFFF]/g,bj=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]),bk=null!=String.prototype.codePointAt?(a,b)=>a.codePointAt(b):(a,b)=>(64512&a.charCodeAt(b))==55296?(a.charCodeAt(b)-55296)*1024+a.charCodeAt(b+1)-56320+65536:a.charCodeAt(b);function bl(a){let b,c="",d=0;for(;null!==(b=bi.exec(a));){let e=b.index,f=a.charCodeAt(e),g=bj.get(f);void 0!==g?(c+=a.substring(d,e)+g,d=e+1):(c+=`${a.substring(d,e)}&#x${bk(a,e).toString(16)};`,d=bi.lastIndex+=Number((64512&f)==55296))}return c+a.substr(d)}function bm(a,b){return function(c){let d,e=0,f="";for(;d=a.exec(c);)e!==d.index&&(f+=c.substring(e,d.index)),f+=b.get(d[0].charCodeAt(0)),e=d.index+1;return f+c.substring(e)}}bm(/[&<>'"]/g,bj);let bn=bm(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),bo=bm(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]]));!function(a){a[a.XML=0]="XML",a[a.HTML=1]="HTML"}(bG||(bG={})),function(a){a[a.UTF8=0]="UTF8",a[a.ASCII=1]="ASCII",a[a.Extensive=2]="Extensive",a[a.Attribute=3]="Attribute",a[a.Text=4]="Text"}(bH||(bH={}));let bp=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(a=>[a.toLowerCase(),a])),bq=new Map(["definitionURL","attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(a=>[a.toLowerCase(),a])),br=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function bs(a){return a.replace(/"/g,"&quot;")}let bt=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function bu(a,k={}){let l="length"in a?a:[a],m="";for(let a=0;a<l.length;a++)m+=function(a,k){var l,m,n;switch(a.type){case b:return bu(a.children,k);case j:case d:return l=a,`<${l.data}>`;case e:return m=a,`<!--${m.data}-->`;case i:return n=a,`<![CDATA[${n.children[0].data}]]>`;case f:case g:case h:return function(a,b){var c;"foreign"===b.xmlMode&&(a.name=null!=(c=bp.get(a.name))?c:a.name,a.parent&&bv.has(a.parent.name)&&(b={...b,xmlMode:!1})),!b.xmlMode&&bw.has(a.name)&&(b={...b,xmlMode:"foreign"});let d=`<${a.name}`,e=function(a,b){var c;if(!a)return;let d=(null!=(c=b.encodeEntities)?c:b.decodeEntities)===!1?bs:b.xmlMode||"utf8"!==b.encodeEntities?bl:bn;return Object.keys(a).map(c=>{var e,f;let g=null!=(e=a[c])?e:"";return("foreign"===b.xmlMode&&(c=null!=(f=bq.get(c))?f:c),b.emptyAttrs||b.xmlMode||""!==g)?`${c}="${d(g)}"`:c}).join(" ")}(a.attribs,b);return e&&(d+=` ${e}`),0===a.children.length&&(b.xmlMode?!1!==b.selfClosingTags:b.selfClosingTags&&bt.has(a.name))?(b.xmlMode||(d+=" "),d+="/>"):(d+=">",a.children.length>0&&(d+=bu(a.children,b)),(b.xmlMode||!bt.has(a.name))&&(d+=`</${a.name}>`)),d}(a,k);case c:return function(a,b){var c;let d=a.data||"";return(null!=(c=b.encodeEntities)?c:b.decodeEntities)===!1||!b.xmlMode&&a.parent&&br.has(a.parent.name)||(d=b.xmlMode||"utf8"!==b.encodeEntities?bl(d):bo(d)),d}(a,k)}}(l[a],k);return m}let bv=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),bw=new Set(["svg","math"]);!function(a){a[a.DISCONNECTED=1]="DISCONNECTED",a[a.PRECEDING=2]="PRECEDING",a[a.FOLLOWING=4]="FOLLOWING",a[a.CONTAINS=8]="CONTAINS",a[a.CONTAINED_BY=16]="CONTAINED_BY"}(bI||(bI={}));var bx,by,bz,bA,bB,bC,bD,bE,bF,bG,bH,bI,bJ=a.i(71236);function bK(a,b,c=()=>void 0){if(void 0===a){let a=function(...c){return b(a,...c)};return a}return a>=0?function(...d){return b(bK(a-1,b,c),...d)}:c}function bL(a,b){let c=0,d=a.length;for(;c<d&&a[c]===b;)++c;for(;d>c&&a[d-1]===b;)--d;return c>0||d<a.length?a.substring(c,d):a}function bM(a,b){let c=new Map;for(let d=a.length;d-- >0;){let e=a[d],f=b(e);c.set(f,c.has(f)?(0,bJ.default)(e,c.get(f),{arrayMerge:bN}):e)}return[...c.values()].reverse()}let bN=(a,b,c)=>[...b];function bO(a,b){for(let c of b){if(!a)return;a=a[c]}return a}function bP(a,b="a",c=26){let d=[];do d.push((a-=1)%c),a=a/c|0;while(a>0)let e=b.charCodeAt(0);return d.reverse().map(a=>String.fromCharCode(e+a)).join("")}let bQ=["I","X","C","M"],bR=["V","L","D"];function bS(a){return[...a+""].map(a=>+a).reverse().map((a,b)=>a%5<4?(a<5?"":bR[b])+bQ[b].repeat(a%5):bQ[b]+(a<5?bR[b]:bQ[b+1])).reverse().join("")}class bT{constructor(a,b){this.lines=[],this.nextLineWords=[],this.maxLineLength=b||a.wordwrap||Number.MAX_VALUE,this.nextLineAvailableChars=this.maxLineLength,this.wrapCharacters=bO(a,["longWordSplit","wrapCharacters"])||[],this.forceWrapOnLimit=bO(a,["longWordSplit","forceWrapOnLimit"])||!1,this.stashedSpace=!1,this.wordBreakOpportunity=!1}pushWord(a,b=!1){this.nextLineAvailableChars<=0&&!b&&this.startNewLine();let c=0===this.nextLineWords.length,d=a.length+ +!c;if(d<=this.nextLineAvailableChars||b)this.nextLineWords.push(a),this.nextLineAvailableChars-=d;else{let[b,...d]=this.splitLongWord(a);for(let a of(c||this.startNewLine(),this.nextLineWords.push(b),this.nextLineAvailableChars-=b.length,d))this.startNewLine(),this.nextLineWords.push(a),this.nextLineAvailableChars-=a.length}}popWord(){let a=this.nextLineWords.pop();if(void 0!==a){let b=0===this.nextLineWords.length,c=a.length+ +!b;this.nextLineAvailableChars+=c}return a}concatWord(a,b=!1){if(this.wordBreakOpportunity&&a.length>this.nextLineAvailableChars)this.pushWord(a,b),this.wordBreakOpportunity=!1;else{let c=this.popWord();this.pushWord(c?c.concat(a):a,b)}}startNewLine(a=1){this.lines.push(this.nextLineWords),a>1&&this.lines.push(...Array.from({length:a-1},()=>[])),this.nextLineWords=[],this.nextLineAvailableChars=this.maxLineLength}isEmpty(){return 0===this.lines.length&&0===this.nextLineWords.length}clear(){this.lines.length=0,this.nextLineWords.length=0,this.nextLineAvailableChars=this.maxLineLength}toString(){return[...this.lines,this.nextLineWords].map(a=>a.join(" ")).join("\n")}splitLongWord(a){let b=[],c=0;for(;a.length>this.maxLineLength;){let d=a.substring(0,this.maxLineLength),e=a.substring(this.maxLineLength),f=d.lastIndexOf(this.wrapCharacters[c]);if(f>-1)a=d.substring(f+1)+e,b.push(d.substring(0,f+1));else if(++c<this.wrapCharacters.length)a=d+e;else{if(this.forceWrapOnLimit){if(b.push(d),(a=e).length>this.maxLineLength)continue}else a=d+e;break}}return b.push(a),b}}class bU{constructor(a=null){this.next=a}getRoot(){return this.next?this.next:this}}class bV extends bU{constructor(a,b=null,c=1,d){super(b),this.leadingLineBreaks=c,this.inlineTextBuilder=new bT(a,d),this.rawText="",this.stashedLineBreaks=0,this.isPre=b&&b.isPre,this.isNoWrap=b&&b.isNoWrap}}class bW extends bV{constructor(a,b=null,{interRowLineBreaks:c=1,leadingLineBreaks:d=2,maxLineLength:e,maxPrefixLength:f=0,prefixAlign:g="left"}={}){super(a,b,d,e),this.maxPrefixLength=f,this.prefixAlign=g,this.interRowLineBreaks=c}}class bX extends bV{constructor(a,b=null,{leadingLineBreaks:c=1,maxLineLength:d,prefix:e=""}={}){super(a,b,c,d),this.prefix=e}}class bY extends bU{constructor(a=null){super(a),this.rows=[],this.isPre=a&&a.isPre,this.isNoWrap=a&&a.isNoWrap}}class bZ extends bU{constructor(a=null){super(a),this.cells=[],this.isPre=a&&a.isPre,this.isNoWrap=a&&a.isNoWrap}}class b$ extends bU{constructor(a,b=null,c){super(b),this.inlineTextBuilder=new bT(a,c),this.rawText="",this.stashedLineBreaks=0,this.isPre=b&&b.isPre,this.isNoWrap=b&&b.isNoWrap}}class b_ extends bU{constructor(a=null,b){super(a),this.transform=b}}class b0{constructor(a){this.whitespaceChars=a.preserveNewlines?a.whitespaceCharacters.replace(/\n/g,""):a.whitespaceCharacters;let b=[...this.whitespaceChars].map(a=>"\\u"+a.charCodeAt(0).toString(16).padStart(4,"0")).join("");if(this.leadingWhitespaceRe=RegExp(`^[${b}]`),this.trailingWhitespaceRe=RegExp(`[${b}]$`),this.allWhitespaceOrEmptyRe=RegExp(`^[${b}]*$`),this.newlineOrNonWhitespaceRe=RegExp(`(\\n|[^\\n${b}])`,"g"),this.newlineOrNonNewlineStringRe=RegExp(`(\\n|[^\\n]+)`,"g"),a.preserveNewlines){let a=RegExp(`\\n|[^\\n${b}]+`,"gm");this.shrinkWrapAdd=function(b,c,d=a=>a,e=!1){if(!b)return;let f=c.stashedSpace,g=!1,h=a.exec(b);if(h)for(g=!0,"\n"===h[0]?c.startNewLine():f||this.testLeadingWhitespace(b)?c.pushWord(d(h[0]),e):c.concatWord(d(h[0]),e);null!==(h=a.exec(b));)"\n"===h[0]?c.startNewLine():c.pushWord(d(h[0]),e);c.stashedSpace=f&&!g||this.testTrailingWhitespace(b)}}else{let a=RegExp(`[^${b}]+`,"g");this.shrinkWrapAdd=function(b,c,d=a=>a,e=!1){if(!b)return;let f=c.stashedSpace,g=!1,h=a.exec(b);if(h)for(g=!0,f||this.testLeadingWhitespace(b)?c.pushWord(d(h[0]),e):c.concatWord(d(h[0]),e);null!==(h=a.exec(b));)c.pushWord(d(h[0]),e);c.stashedSpace=f&&!g||this.testTrailingWhitespace(b)}}}addLiteral(a,b,c=!0){if(!a)return;let d=b.stashedSpace,e=!1,f=this.newlineOrNonNewlineStringRe.exec(a);if(f)for(e=!0,"\n"===f[0]?b.startNewLine():d?b.pushWord(f[0],c):b.concatWord(f[0],c);null!==(f=this.newlineOrNonNewlineStringRe.exec(a));)"\n"===f[0]?b.startNewLine():b.pushWord(f[0],c);b.stashedSpace=d&&!e}testLeadingWhitespace(a){return this.leadingWhitespaceRe.test(a)}testTrailingWhitespace(a){return this.trailingWhitespaceRe.test(a)}testContainsWords(a){return!this.allWhitespaceOrEmptyRe.test(a)}countNewlinesNoWords(a){let b;this.newlineOrNonWhitespaceRe.lastIndex=0;let c=0;for(;null!==(b=this.newlineOrNonWhitespaceRe.exec(a));)if("\n"!==b[0])return 0;else c++;return c}}class b1{constructor(a,b,c){this.options=a,this.picker=b,this.metadata=c,this.whitespaceProcessor=new b0(a),this._stackItem=new bV(a),this._wordTransformer=void 0}pushWordTransform(a){this._wordTransformer=new b_(this._wordTransformer,a)}popWordTransform(){if(!this._wordTransformer)return;let a=this._wordTransformer.transform;return this._wordTransformer=this._wordTransformer.next,a}startNoWrap(){this._stackItem.isNoWrap=!0}stopNoWrap(){this._stackItem.isNoWrap=!1}_getCombinedWordTransformer(){let a=this._wordTransformer?a=>(function a(b,c){return c?a(c.transform(b),c.next):b})(a,this._wordTransformer):void 0,b=this.options.encodeCharacters;return a?b?c=>b(a(c)):a:b}_popStackItem(){let a=this._stackItem;return this._stackItem=a.next,a}addLineBreak(){(this._stackItem instanceof bV||this._stackItem instanceof bX||this._stackItem instanceof b$)&&(this._stackItem.isPre?this._stackItem.rawText+="\n":this._stackItem.inlineTextBuilder.startNewLine())}addWordBreakOpportunity(){(this._stackItem instanceof bV||this._stackItem instanceof bX||this._stackItem instanceof b$)&&(this._stackItem.inlineTextBuilder.wordBreakOpportunity=!0)}addInline(a,{noWordTransform:b=!1}={}){if(this._stackItem instanceof bV||this._stackItem instanceof bX||this._stackItem instanceof b$){if(this._stackItem.isPre){this._stackItem.rawText+=a;return}if(0!==a.length&&(!this._stackItem.stashedLineBreaks||this.whitespaceProcessor.testContainsWords(a))){if(this.options.preserveNewlines){let b=this.whitespaceProcessor.countNewlinesNoWords(a);if(b>0)return void this._stackItem.inlineTextBuilder.startNewLine(b)}this._stackItem.stashedLineBreaks&&this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks),this.whitespaceProcessor.shrinkWrapAdd(a,this._stackItem.inlineTextBuilder,b?void 0:this._getCombinedWordTransformer(),this._stackItem.isNoWrap),this._stackItem.stashedLineBreaks=0}}}addLiteral(a){if((this._stackItem instanceof bV||this._stackItem instanceof bX||this._stackItem instanceof b$)&&0!==a.length){if(this._stackItem.isPre){this._stackItem.rawText+=a;return}this._stackItem.stashedLineBreaks&&this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks),this.whitespaceProcessor.addLiteral(a,this._stackItem.inlineTextBuilder,this._stackItem.isNoWrap),this._stackItem.stashedLineBreaks=0}}openBlock({leadingLineBreaks:a=1,reservedLineLength:b=0,isPre:c=!1}={}){let d=Math.max(20,this._stackItem.inlineTextBuilder.maxLineLength-b);this._stackItem=new bV(this.options,this._stackItem,a,d),c&&(this._stackItem.isPre=!0)}closeBlock({trailingLineBreaks:a=1,blockTransform:b}={}){let c=this._popStackItem(),d=b?b(b2(c)):b2(c);b3(this._stackItem,d,c.leadingLineBreaks,Math.max(c.stashedLineBreaks,a))}openList({maxPrefixLength:a=0,prefixAlign:b="left",interRowLineBreaks:c=1,leadingLineBreaks:d=2}={}){this._stackItem=new bW(this.options,this._stackItem,{interRowLineBreaks:c,leadingLineBreaks:d,maxLineLength:this._stackItem.inlineTextBuilder.maxLineLength,maxPrefixLength:a,prefixAlign:b})}openListItem({prefix:a=""}={}){if(!(this._stackItem instanceof bW))throw Error("Can't add a list item to something that is not a list! Check the formatter.");let b=this._stackItem,c=Math.max(a.length,b.maxPrefixLength),d=Math.max(20,b.inlineTextBuilder.maxLineLength-c);this._stackItem=new bX(this.options,b,{prefix:a,maxLineLength:d,leadingLineBreaks:b.interRowLineBreaks})}closeListItem(){let a=this._popStackItem(),b=a.next,c=Math.max(a.prefix.length,b.maxPrefixLength),d="\n"+" ".repeat(c),e=("right"===b.prefixAlign?a.prefix.padStart(c):a.prefix.padEnd(c))+b2(a).replace(/\n/g,d);b3(b,e,a.leadingLineBreaks,Math.max(a.stashedLineBreaks,b.interRowLineBreaks))}closeList({trailingLineBreaks:a=2}={}){let b=this._popStackItem(),c=b2(b);c&&b3(this._stackItem,c,b.leadingLineBreaks,a)}openTable(){this._stackItem=new bY(this._stackItem)}openTableRow(){if(!(this._stackItem instanceof bY))throw Error("Can't add a table row to something that is not a table! Check the formatter.");this._stackItem=new bZ(this._stackItem)}openTableCell({maxColumnWidth:a}={}){if(!(this._stackItem instanceof bZ))throw Error("Can't add a table cell to something that is not a table row! Check the formatter.");this._stackItem=new b$(this.options,this._stackItem,a)}closeTableCell({colspan:a=1,rowspan:b=1}={}){let c=this._popStackItem(),d=bL(b2(c),"\n");c.next.cells.push({colspan:a,rowspan:b,text:d})}closeTableRow(){let a=this._popStackItem();a.next.rows.push(a.cells)}closeTable({tableToString:a,leadingLineBreaks:b=2,trailingLineBreaks:c=2}){let d=a(this._popStackItem().rows);d&&b3(this._stackItem,d,b,c)}toString(){return b2(this._stackItem.getRoot())}}function b2(a){if(!(a instanceof bV||a instanceof bX||a instanceof b$))throw Error("Only blocks, list items and table cells can be requested for text contents.");return a.inlineTextBuilder.isEmpty()?a.rawText:a.rawText+a.inlineTextBuilder.toString()}function b3(a,b,c,d){if(!(a instanceof bV||a instanceof bX||a instanceof b$))throw Error("Only blocks, list items and table cells can contain text.");let e=b2(a),f=Math.max(a.stashedLineBreaks,c);a.inlineTextBuilder.clear(),e?a.rawText=e+"\n".repeat(f)+b:(a.rawText=b,a.leadingLineBreaks=f),a.stashedLineBreaks=d}function b4(a,b,c){if(!b)return;let d=c.options;for(let e of(b.length>d.limits.maxChildNodes&&(b=b.slice(0,d.limits.maxChildNodes)).push({data:d.limits.ellipsis,type:"text"}),b))switch(e.type){case"text":c.addInline(e.data);break;case"tag":{let b=c.picker.pick1(e);(0,d.formatters[b.format])(e,a,c,b.options||{})}}}function b5(a){let b=a.attribs&&a.attribs.length?" "+Object.entries(a.attribs).map(([a,b])=>""===b?a:`${a}=${b.replace(/"/g,"&quot;")}`).join(" "):"";return`<${a.name}${b}>`}function b6(a){return`</${a.name}>`}var b7=Object.freeze({__proto__:null,block:function(a,b,c,d){c.openBlock({leadingLineBreaks:d.leadingLineBreaks||2}),b(a.children,c),c.closeBlock({trailingLineBreaks:d.trailingLineBreaks||2})},blockHtml:function(a,b,c,d){c.openBlock({leadingLineBreaks:d.leadingLineBreaks||2}),c.startNoWrap(),c.addLiteral(bu(a,{decodeEntities:c.options.decodeEntities})),c.stopNoWrap(),c.closeBlock({trailingLineBreaks:d.trailingLineBreaks||2})},blockString:function(a,b,c,d){c.openBlock({leadingLineBreaks:d.leadingLineBreaks||2}),c.addLiteral(d.string||""),c.closeBlock({trailingLineBreaks:d.trailingLineBreaks||2})},blockTag:function(a,b,c,d){c.openBlock({leadingLineBreaks:d.leadingLineBreaks||2}),c.startNoWrap(),c.addLiteral(b5(a)),c.stopNoWrap(),b(a.children,c),c.startNoWrap(),c.addLiteral(b6(a)),c.stopNoWrap(),c.closeBlock({trailingLineBreaks:d.trailingLineBreaks||2})},inline:function(a,b,c,d){b(a.children,c)},inlineHtml:function(a,b,c,d){c.startNoWrap(),c.addLiteral(bu(a,{decodeEntities:c.options.decodeEntities})),c.stopNoWrap()},inlineString:function(a,b,c,d){c.addLiteral(d.string||"")},inlineSurround:function(a,b,c,d){c.addLiteral(d.prefix||""),b(a.children,c),c.addLiteral(d.suffix||"")},inlineTag:function(a,b,c,d){c.startNoWrap(),c.addLiteral(b5(a)),c.stopNoWrap(),b(a.children,c),c.startNoWrap(),c.addLiteral(b6(a)),c.stopNoWrap()},skip:function(a,b,c,d){}});function b8(a,b){return a[b]||(a[b]=[]),a[b]}function b9(a,b){return void 0===a[b]&&(a[b]=0===b?0:1+b9(a,b-1)),a[b]}function ca(a,b,c,d){a[b+c]=Math.max(b9(a,b+c),b9(a,b)+d)}function cb(a,b){return b?("string"==typeof b[0]?b[0]:"[")+a+("string"==typeof b[1]?b[1]:"]"):a}function cc(a,b,c,d,e){let f="function"==typeof b?b(a,d,e):a;return"/"===f[0]&&c?function(a,b){let c=a.length;for(;c>0&&"/"===a[c-1];)--c;return c<a.length?a.substring(0,c):a}(c,0)+f:f}function cd(a,b,c,d,e){let f="li"===bO(a,["parent","name"]),g=0,h=(a.children||[]).filter(a=>"text"!==a.type||!/^\s*$/.test(a.data)).map(function(a){if("li"!==a.name)return{node:a,prefix:""};let b=f?e().trimStart():e();return b.length>g&&(g=b.length),{node:a,prefix:b}});if(h.length){for(let{node:a,prefix:e}of(c.openList({interRowLineBreaks:1,leadingLineBreaks:f?1:d.leadingLineBreaks||2,maxPrefixLength:g,prefixAlign:"left"}),h))c.openListItem({prefix:e}),b([a],c),c.closeListItem();c.closeList({trailingLineBreaks:f?1:d.trailingLineBreaks||2})}}function ce(a,b,c,d){function e(a){let e=+bO(a,["attribs","colspan"])||1,f=+bO(a,["attribs","rowspan"])||1;c.openTableCell({maxColumnWidth:d.maxColumnWidth}),b(a.children,c),c.closeTableCell({colspan:e,rowspan:f})}c.openTable(),a.children.forEach(function a(b){if("tag"!==b.type)return;let f=!1!==d.uppercaseHeaderCells?a=>{c.pushWordTransform(a=>a.toUpperCase()),e(a),c.popWordTransform()}:e;switch(b.name){case"thead":case"tbody":case"tfoot":case"center":b.children.forEach(a);return;case"tr":for(let a of(c.openTableRow(),b.children))if("tag"===a.type)switch(a.name){case"th":f(a);break;case"td":e(a)}c.closeTableRow()}}),c.closeTable({tableToString:a=>(function(a,b,c){let d=[],e=0,f=a.length,g=[0];for(let c=0;c<f;c++){let f=b8(d,c),j=a[c],k=0;for(let a=0;a<j.length;a++){let e=j[a];k=function(a,b=0){for(;a[b];)b++;return b}(f,k);var h=c,i=k;for(let a=0;a<e.rowspan;a++){let b=b8(d,h+a);for(let a=0;a<e.colspan;a++)b[i+a]=e}k+=e.colspan,e.lines=e.text.split("\n");let l=e.lines.length;ca(g,c,e.rowspan,l+b)}e=f.length>e?f.length:e}!function(a,b){for(let c=0;c<b;c++){let b=b8(a,c);for(let d=0;d<c;d++){let e=b8(a,d);if(b[d]||e[c]){let a=b[d];b[d]=e[c],e[c]=a}}}}(d,f>e?f:e);let j=[],k=[0];for(let a=0;a<e;a++){let b,e=0,h=Math.min(f,d[a].length);for(;e<h;)if(b=d[a][e]){if(!b.rendered){let d=0;for(let c=0;c<b.lines.length;c++){let f=b.lines[c],h=g[e]+c;j[h]=(j[h]||"").padEnd(k[a])+f,d=f.length>d?f.length:d}ca(k,a,b.colspan,d+c),b.rendered=!0}e+=b.rowspan}else{let a=g[e];j[a]=j[a]||"",e++}}return j.join("\n")})(a,d.rowSpacing??0,d.colSpacing??3),leadingLineBreaks:d.leadingLineBreaks,trailingLineBreaks:d.trailingLineBreaks})}var cf=Object.freeze({__proto__:null,anchor:function(a,b,c,d){let e=function(){if(d.ignoreHref||!a.attribs||!a.attribs.href)return"";let b=a.attribs.href.replace(/^mailto:/,"");return d.noAnchorUrl&&"#"===b[0]?"":b=cc(b,d.pathRewrite,d.baseUrl,c.metadata,a)}();if(e){let f="";c.pushWordTransform(a=>(a&&(f+=a),a)),b(a.children,c),c.popWordTransform(),d.hideLinkHrefIfSameAsText&&e===f||c.addInline(f?" "+cb(e,d.linkBrackets):e,{noWordTransform:!0})}else b(a.children,c)},blockquote:function(a,b,c,d){c.openBlock({leadingLineBreaks:d.leadingLineBreaks||2,reservedLineLength:2}),b(a.children,c),c.closeBlock({trailingLineBreaks:d.trailingLineBreaks||2,blockTransform:a=>(!1!==d.trimEmptyLines?bL(a,"\n"):a).split("\n").map(a=>"> "+a).join("\n")})},dataTable:ce,heading:function(a,b,c,d){c.openBlock({leadingLineBreaks:d.leadingLineBreaks||2}),!1!==d.uppercase?(c.pushWordTransform(a=>a.toUpperCase()),b(a.children,c),c.popWordTransform()):b(a.children,c),c.closeBlock({trailingLineBreaks:d.trailingLineBreaks||2})},horizontalLine:function(a,b,c,d){c.openBlock({leadingLineBreaks:d.leadingLineBreaks||2}),c.addInline("-".repeat(d.length||c.options.wordwrap||40)),c.closeBlock({trailingLineBreaks:d.trailingLineBreaks||2})},image:function(a,b,c,d){let e=a.attribs||{},f=e.alt?e.alt:"",g=e.src?cc(e.src,d.pathRewrite,d.baseUrl,c.metadata,a):"",h=g?f?f+" "+cb(g,d.linkBrackets):cb(g,d.linkBrackets):f;c.addInline(h,{noWordTransform:!0})},lineBreak:function(a,b,c,d){c.addLineBreak()},orderedList:function(a,b,c,d){let e=Number(a.attribs.start||"1"),f=function(a="1"){switch(a){case"a":return a=>bP(a,"a");case"A":return a=>bP(a,"A");case"i":return a=>bS(a).toLowerCase();case"I":return a=>bS(a);default:return a=>a.toString()}}(a.attribs.type);return cd(a,b,c,d,()=>" "+f(e++)+". ")},paragraph:function(a,b,c,d){c.openBlock({leadingLineBreaks:d.leadingLineBreaks||2}),b(a.children,c),c.closeBlock({trailingLineBreaks:d.trailingLineBreaks||2})},pre:function(a,b,c,d){c.openBlock({isPre:!0,leadingLineBreaks:d.leadingLineBreaks||2}),b(a.children,c),c.closeBlock({trailingLineBreaks:d.trailingLineBreaks||2})},table:function(a,b,c,d){var e,f,g,h;return!function(a,b){if(!0===b)return!0;if(!a)return!1;let{classes:c,ids:d}=function(a){let b=[],c=[];for(let d of a)d.startsWith(".")?b.push(d.substring(1)):d.startsWith("#")&&c.push(d.substring(1));return{classes:b,ids:c}}(b),e=(a.class||"").split(" "),f=(a.id||"").split(" ");return e.some(a=>c.includes(a))||f.some(a=>d.includes(a))}(a.attribs,c.options.tables)?(e=a,f=b,g=c,h=d,void(g.openBlock({leadingLineBreaks:h.leadingLineBreaks}),f(e.children,g),g.closeBlock({trailingLineBreaks:h.trailingLineBreaks}))):ce(a,b,c,d)},unorderedList:function(a,b,c,d){let e=d.itemPrefix||" * ";return cd(a,b,c,d,()=>e)},wbr:function(a,b,c,d){c.addWordBreakOpportunity()}});let cg={baseElements:{selectors:["body"],orderBy:"selectors",returnDomByDefault:!0},decodeEntities:!0,encodeCharacters:{},formatters:{},limits:{ellipsis:"...",maxBaseElements:void 0,maxChildNodes:void 0,maxDepth:void 0,maxInputLength:0x1000000},longWordSplit:{forceWrapOnLimit:!1,wrapCharacters:[]},preserveNewlines:!1,selectors:[{selector:"*",format:"inline"},{selector:"a",format:"anchor",options:{baseUrl:null,hideLinkHrefIfSameAsText:!1,ignoreHref:!1,linkBrackets:["[","]"],noAnchorUrl:!0}},{selector:"article",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"aside",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"blockquote",format:"blockquote",options:{leadingLineBreaks:2,trailingLineBreaks:2,trimEmptyLines:!0}},{selector:"br",format:"lineBreak"},{selector:"div",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"footer",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"form",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"h1",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h2",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h3",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h4",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"h5",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"h6",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"header",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"hr",format:"horizontalLine",options:{leadingLineBreaks:2,length:void 0,trailingLineBreaks:2}},{selector:"img",format:"image",options:{baseUrl:null,linkBrackets:["[","]"]}},{selector:"main",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"nav",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"ol",format:"orderedList",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"p",format:"paragraph",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"pre",format:"pre",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"section",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"table",format:"table",options:{colSpacing:3,leadingLineBreaks:2,maxColumnWidth:60,rowSpacing:0,trailingLineBreaks:2,uppercaseHeaderCells:!0}},{selector:"ul",format:"unorderedList",options:{itemPrefix:" * ",leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"wbr",format:"wbr"}],tables:[],whitespaceCharacters:" 	\r\n\f​",wordwrap:80},ch=(a,b,c)=>[...b],ci=(a,b,c)=>a.some(a=>"object"==typeof a)?[...a,...b]:ch(a,b);function cj(a,b={},c){return(function(a={}){return(a=(0,bJ.default)(cg,a,{arrayMerge:ch,customMerge:a=>"selectors"===a?ci:void 0})).formatters=Object.assign({},b7,cf,a.formatters),a.selectors=bM(a.selectors,a=>a.selector),function(a){if(a.tags){let b=Object.entries(a.tags).map(([a,b])=>({...b,selector:a||"*"}));a.selectors.push(...b),a.selectors=bM(a.selectors,a=>a.selector)}function b(a,b,c){let d=b.pop();for(let c of b){let b=a[c];b||(b={},a[c]=b),a=b}a[d]=c}if(a.baseElement){let c=a.baseElement;b(a,["baseElements","selectors"],Array.isArray(c)?c:[c])}for(let c of(void 0!==a.returnDomByDefault&&b(a,["baseElements","returnDomByDefault"],a.returnDomByDefault),a.selectors))"anchor"===c.format&&bO(c,["options","noLinkBrackets"])&&b(c,["options","linkBrackets"],!1)}(a),function(a={}){let b=a.selectors.filter(a=>!a.format);if(b.length)throw Error("Following selectors have no specified format: "+b.map(a=>`\`${a.selector}\``).join(", "));let c=new aG(a.selectors.map(a=>[a.selector,a])).build(aQ);"function"!=typeof a.encodeCharacters&&(a.encodeCharacters=function(a){if(!a||0===Object.keys(a).length)return;let b=Object.entries(a).filter(([,a])=>!1!==a),c=RegExp(b.map(([a])=>`(${[...a][0].replace(/[\s\S]/g,a=>"\\u"+a.charCodeAt().toString(16).padStart(4,"0"))})`).join("|"),"g"),d=b.map(([,a])=>a),e=(a,...b)=>d[b.findIndex(a=>a)];return a=>a.replace(c,e)}(a.encodeCharacters));let d=new aG(a.baseElements.selectors.map((a,b)=>[a,b+1])).build(aQ);function e(b){var c=b,e=a,f=d;let g=[];return bK(e.limits.maxDepth,function(a,b){for(let c of b=b.slice(0,e.limits.maxChildNodes)){if("tag"!==c.type)continue;let b=f.pick1(c);if(b>0?g.push({selectorIndex:b,element:c}):c.children&&a(c.children),g.length>=e.limits.maxBaseElements)return}})(c),"occurrence"!==e.baseElements.orderBy&&g.sort((a,b)=>a.selectorIndex-b.selectorIndex),e.baseElements.returnDomByDefault&&0===g.length?c:g.map(a=>a.element)}let f=bK(a.limits.maxDepth,b4,function(b,c){c.addInline(a.limits.ellipsis||"")});return function(b,d){var g=b,h=d,i=a,j=c,k=e,l=f;let m=i.limits.maxInputLength;m&&g&&g.length>m&&(console.warn(`Input length ${g.length} is above allowed limit of ${m}. Truncating without ellipsis.`),g=g.substring(0,m));let n=k(function(a,b){let c=new x(void 0,b);return new bh(c,b).end(a),c.root}(g,{decodeEntities:i.decodeEntities}).children),o=new b1(i,j,h);return l(n,o),o.toString()}}(a)})(b)(a,c)}},81111,(a,b,c)=>{b.exports=a.x("node:stream",()=>require("node:stream"))},32709,a=>a.a(async(b,c)=>{try{a.s(["plainTextSelectors",()=>x,"pretty",()=>w,"render",()=>B,"renderAsync",()=>C,"toPlainText",()=>y]);var d=a.i(35399),e=a.i(75202),f=a.i(35167),g=a.i(77570),h=a.i(81111),i=a.i(38470),j=b([e,f]);[e,f]=j.then?(await j)():j;var k=Object.defineProperty,l=Object.defineProperties,m=Object.getOwnPropertyDescriptors,n=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable,q=(a,b,c)=>b in a?k(a,b,{enumerable:!0,configurable:!0,writable:!0,value:c}):a[b]=c,r=(a,b)=>{for(var c in b||(b={}))o.call(b,c)&&q(a,c,b[c]);if(n)for(var c of n(b))p.call(b,c)&&q(a,c,b[c]);return a},s=(a,b)=>l(a,m(b)),t=(a,b,c)=>new Promise((d,e)=>{var f=a=>{try{h(c.next(a))}catch(a){e(a)}},g=a=>{try{h(c.throw(a))}catch(a){e(a)}},h=a=>a.done?d(a.value):Promise.resolve(a.value).then(f,g);h((c=c.apply(a,b)).next())}),u=r({},e);if(u.printers){let a=u.printers.html.print;u.printers.html.print=(b,c,d,e)=>{let f=b.getNode(),g=a(b,c,d,e);return"ieConditionalComment"===f.type?function a(b,c){if(Array.isArray(b))return b.map(b=>a(b,c));if("object"==typeof b){if("group"===b.type)return s(r({},b),{contents:a(b.contents,c),expandedStates:a(b.expandedStates,c)});if("contents"in b)return s(r({},b),{contents:a(b.contents,c)});if("parts"in b)return s(r({},b),{parts:a(b.parts,c)});if("if-break"===b.type)return s(r({},b),{breakContents:a(b.breakContents,c),flatContents:a(b.flatContents,c)})}return c(b)}(g,a=>"object"==typeof a&&"line"===a.type?a.soft?"":" ":a):g}}var v={endOfLine:"lf",tabWidth:2,plugins:[u],bracketSameLine:!0,parser:"html"},w=(a,b={})=>(0,f.format)(a.replaceAll("\0",""),r(r({},v),b)),x=[{selector:"img",format:"skip"},{selector:"[data-skip-in-text=true]",format:"skip"},{selector:"a",options:{linkBrackets:!1}}];function y(a,b){return(0,g.convert)(a,r({selectors:x},b))}var z=new TextDecoder("utf-8"),A=a=>t(void 0,null,function*(){let b="";if("pipeTo"in a){let c=new WritableStream({write(a){b+=z.decode(a)}});yield a.pipeTo(c)}else{let c=new h.Writable({write(a,c,d){b+=z.decode(a),d()}});a.pipe(c),yield new Promise((a,b)=>{c.on("error",b),c.on("close",()=>{a()})})}return b}),B=(b,c)=>t(void 0,null,function*(){let e,f=(0,i.jsx)(d.Suspense,{children:b}),g=yield a.A(35075).then(a=>a.default);if(Object.hasOwn(g,"renderToReadableStream")?e=yield A((yield g.renderToReadableStream(f,{progressiveChunkSize:1/0}))):yield new Promise((a,b)=>{let c=g.renderToPipeableStream(f,{onAllReady(){return t(this,null,function*(){e=yield A(c),a()})},onError(a){b(a)},progressiveChunkSize:1/0})}),null==c?void 0:c.plainText)return y(e,c.htmlToTextOptions);let h=`<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">${e.replace(/<!DOCTYPE.*?>/,"")}`;return(null==c?void 0:c.pretty)?w(h):h}),C=(a,b)=>B(a,b);c()}catch(a){c(a)}},!1)];

//# sourceMappingURL=%5Broot-of-the-server%5D__f24dc009._.js.map