/* [project]/app/aeonikbold_b98b5370.module.css [app-client] (css) */
@font-face {
  font-family: aeonikBold;
  src: url("../media/fonnts_com_Aeonik_Bold-s.p.895166fa.ttf") format("truetype");
  font-display: swap;
}

@font-face {
  font-family: aeonikBold Fallback;
  src: local(Arial);
  ascent-override: 93.63%;
  descent-override: 18.73%;
  line-gap-override: 0.0%;
  size-adjust: 106.8%;
}

.aeonikbold_b98b5370-module__HbNZGa__className {
  font-family: aeonikBold, aeonikBold Fallback;
}

.aeonikbold_b98b5370-module__HbNZGa__variable {
  --font-aeonik-bold: "aeonikBold", "aeonikBold Fallback";
}

/*# sourceMappingURL=app_aeonikbold_b98b5370_module_css_bad6b30c._.single.css.map*/