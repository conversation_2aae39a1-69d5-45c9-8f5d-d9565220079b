/** biome-ignore-all lint/performance/noImgElement: <explanation> */
export default function FeaturesHome() {
  return (
    <section className="mb-40">
      <div className="mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl">
        <div className="flex flex-col items-center gap-[80px] font-aeonik">
          {/* Desktop */}
          <div className="flex w-full flex-col items-start gap-[60px]">
            <div className="flex w-full flex-col items-start gap-5">
              <div className="font-medium text-brand-500 text-xl">
                Desktop Intelligence
              </div>
              <h2 className="max-w-[320px] font-aeonik font-medium text-3xl text-white/80 leading-none tracking-[-1px] sm:max-w-2xl md:max-w-none md:text-[42px]">
                <PERSON><PERSON> understands what you see and hear.
              </h2>
            </div>
            <div className="relative flex aspect-square w-full items-center justify-center overflow-hidden rounded-[20px] bg-[#303030] md:aspect-auto">
              <video
                aria-label="Demo video 1"
                autoPlay
                className="h-full w-full scale-125 rounded-[20px] object-cover md:h-auto md:scale-110 md:object-contain"
                loop
                muted
                playsInline
                src="https://cdn.highlightai.com/media/landing/misc/hero_demo.webm"
              >
                <track kind="captions" label="English captions" srcLang="en" />
              </video>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
