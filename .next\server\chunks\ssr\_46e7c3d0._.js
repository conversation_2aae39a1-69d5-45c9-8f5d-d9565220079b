module.exports=[97895,39337,a=>{"use strict";function b(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}a.s(["cn",()=>aa],97895),a.s(["clsx",()=>b],39337);let c=(a,b)=>{if(0===a.length)return b.classGroupId;let d=a[0],e=b.nextPart.get(d),f=e?c(a.slice(1),e):void 0;if(f)return f;if(0===b.validators.length)return;let g=a.join("-");return b.validators.find(({validator:a})=>a(g))?.classGroupId},d=/^\[(.+)\]$/,e=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:f(b,a)).classGroupId=c;return}if("function"==typeof a)return g(a)?void e(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,g])=>{e(g,f(b,a),c,d)})})},f=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},g=a=>a.isThemeGetter,h=/\s+/;function i(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=j(a))&&(d&&(d+=" "),d+=b);return d}let j=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=j(a[d]))&&(c&&(c+=" "),c+=b);return c},k=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},l=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,m=/^\((?:(\w[\w-]*):)?(.+)\)$/i,n=/^\d+\/\d+$/,o=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,p=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,q=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,r=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,s=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,t=a=>n.test(a),u=a=>!!a&&!Number.isNaN(Number(a)),v=a=>!!a&&Number.isInteger(Number(a)),w=a=>a.endsWith("%")&&u(a.slice(0,-1)),x=a=>o.test(a),y=()=>!0,z=a=>p.test(a)&&!q.test(a),A=()=>!1,B=a=>r.test(a),C=a=>s.test(a),D=a=>!F(a)&&!L(a),E=a=>S(a,W,A),F=a=>l.test(a),G=a=>S(a,X,z),H=a=>S(a,Y,u),I=a=>S(a,U,A),J=a=>S(a,V,C),K=a=>S(a,$,B),L=a=>m.test(a),M=a=>T(a,X),N=a=>T(a,Z),O=a=>T(a,U),P=a=>T(a,W),Q=a=>T(a,V),R=a=>T(a,$,!0),S=(a,b,c)=>{let d=l.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},T=(a,b,c=!1)=>{let d=m.exec(a);return!!d&&(d[1]?b(d[1]):c)},U=a=>"position"===a||"percentage"===a,V=a=>"image"===a||"url"===a,W=a=>"length"===a||"size"===a||"bg-size"===a,X=a=>"length"===a,Y=a=>"number"===a,Z=a=>"family-name"===a,$=a=>"shadow"===a;Symbol.toStringTag;let _=function(a,...b){let f,g,j,k=function(h){let i;return g=(f={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(i),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(i),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)e(c[a],d,a,b);return d})(a),{conflictingClassGroups:f,conflictingClassGroupModifiers:g}=a;return{getClassGroupId:a=>{let e=a.split("-");return""===e[0]&&1!==e.length&&e.shift(),c(e,b)||(a=>{if(d.test(a)){let b=d.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let c=f[a]||[];return b&&g[a]?[...c,...g[a]]:c}}})(i)}).cache.get,j=f.cache.set,k=l,l(h)};function l(a){let b=g(a);if(b)return b;let c=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],i=a.trim().split(h),j="";for(let a=i.length-1;a>=0;a-=1){let b=i[a],{isExternal:h,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(h){j=b+(j.length>0?" "+j:j);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){j=b+(j.length>0?" "+j:j);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}j=b+(j.length>0?" "+j:j)}return j})(a,f);return j(a,c),c}return function(){return k(i.apply(null,arguments))}}(()=>{let a=k("color"),b=k("font"),c=k("text"),d=k("font-weight"),e=k("tracking"),f=k("leading"),g=k("breakpoint"),h=k("container"),i=k("spacing"),j=k("radius"),l=k("shadow"),m=k("inset-shadow"),n=k("text-shadow"),o=k("drop-shadow"),p=k("blur"),q=k("perspective"),r=k("aspect"),s=k("ease"),z=k("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...B(),L,F],S=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],U=()=>[L,F,i],V=()=>[t,"full","auto",...U()],W=()=>[v,"none","subgrid",L,F],X=()=>["auto",{span:["full",v,L,F]},v,L,F],Y=()=>[v,"auto",L,F],Z=()=>["auto","min","max","fr",L,F],$=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],aa=()=>["auto",...U()],ab=()=>[t,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...U()],ac=()=>[a,L,F],ad=()=>[...B(),O,I,{position:[L,F]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],af=()=>["auto","cover","contain",P,E,{size:[L,F]}],ag=()=>[w,M,G],ah=()=>["","none","full",j,L,F],ai=()=>["",u,M,G],aj=()=>["solid","dashed","dotted","double"],ak=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],al=()=>[u,w,O,I],am=()=>["","none",p,L,F],an=()=>["none",u,L,F],ao=()=>["none",u,L,F],ap=()=>[u,L,F],aq=()=>[t,"full",...U()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[x],breakpoint:[x],color:[y],container:[x],"drop-shadow":[x],ease:["in","out","in-out"],font:[D],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[x],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[x],shadow:[x],spacing:["px",u],text:[x],"text-shadow":[x],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",t,F,L,r]}],container:["container"],columns:[{columns:[u,F,L,h]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:V()}],"inset-x":[{"inset-x":V()}],"inset-y":[{"inset-y":V()}],start:[{start:V()}],end:[{end:V()}],top:[{top:V()}],right:[{right:V()}],bottom:[{bottom:V()}],left:[{left:V()}],visibility:["visible","invisible","collapse"],z:[{z:[v,"auto",L,F]}],basis:[{basis:[t,"full","auto",h,...U()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[u,t,"auto","initial","none",F]}],grow:[{grow:["",u,L,F]}],shrink:[{shrink:["",u,L,F]}],order:[{order:[v,"first","last","none",L,F]}],"grid-cols":[{"grid-cols":W()}],"col-start-end":[{col:X()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":W()}],"row-start-end":[{row:X()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:U()}],"gap-x":[{"gap-x":U()}],"gap-y":[{"gap-y":U()}],"justify-content":[{justify:[...$(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...$()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":$()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:U()}],px:[{px:U()}],py:[{py:U()}],ps:[{ps:U()}],pe:[{pe:U()}],pt:[{pt:U()}],pr:[{pr:U()}],pb:[{pb:U()}],pl:[{pl:U()}],m:[{m:aa()}],mx:[{mx:aa()}],my:[{my:aa()}],ms:[{ms:aa()}],me:[{me:aa()}],mt:[{mt:aa()}],mr:[{mr:aa()}],mb:[{mb:aa()}],ml:[{ml:aa()}],"space-x":[{"space-x":U()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":U()}],"space-y-reverse":["space-y-reverse"],size:[{size:ab()}],w:[{w:[h,"screen",...ab()]}],"min-w":[{"min-w":[h,"screen","none",...ab()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...ab()]}],h:[{h:["screen","lh",...ab()]}],"min-h":[{"min-h":["screen","lh","none",...ab()]}],"max-h":[{"max-h":["screen","lh",...ab()]}],"font-size":[{text:["base",c,M,G]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,L,H]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",w,F]}],"font-family":[{font:[N,F,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,L,F]}],"line-clamp":[{"line-clamp":[u,"none",L,H]}],leading:[{leading:[f,...U()]}],"list-image":[{"list-image":["none",L,F]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",L,F]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ac()}],"text-color":[{text:ac()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...aj(),"wavy"]}],"text-decoration-thickness":[{decoration:[u,"from-font","auto",L,G]}],"text-decoration-color":[{decoration:ac()}],"underline-offset":[{"underline-offset":[u,"auto",L,F]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:U()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L,F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L,F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ad()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:af()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},v,L,F],radial:["",L,F],conic:[v,L,F]},Q,J]}],"bg-color":[{bg:ac()}],"gradient-from-pos":[{from:ag()}],"gradient-via-pos":[{via:ag()}],"gradient-to-pos":[{to:ag()}],"gradient-from":[{from:ac()}],"gradient-via":[{via:ac()}],"gradient-to":[{to:ac()}],rounded:[{rounded:ah()}],"rounded-s":[{"rounded-s":ah()}],"rounded-e":[{"rounded-e":ah()}],"rounded-t":[{"rounded-t":ah()}],"rounded-r":[{"rounded-r":ah()}],"rounded-b":[{"rounded-b":ah()}],"rounded-l":[{"rounded-l":ah()}],"rounded-ss":[{"rounded-ss":ah()}],"rounded-se":[{"rounded-se":ah()}],"rounded-ee":[{"rounded-ee":ah()}],"rounded-es":[{"rounded-es":ah()}],"rounded-tl":[{"rounded-tl":ah()}],"rounded-tr":[{"rounded-tr":ah()}],"rounded-br":[{"rounded-br":ah()}],"rounded-bl":[{"rounded-bl":ah()}],"border-w":[{border:ai()}],"border-w-x":[{"border-x":ai()}],"border-w-y":[{"border-y":ai()}],"border-w-s":[{"border-s":ai()}],"border-w-e":[{"border-e":ai()}],"border-w-t":[{"border-t":ai()}],"border-w-r":[{"border-r":ai()}],"border-w-b":[{"border-b":ai()}],"border-w-l":[{"border-l":ai()}],"divide-x":[{"divide-x":ai()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ai()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...aj(),"hidden","none"]}],"divide-style":[{divide:[...aj(),"hidden","none"]}],"border-color":[{border:ac()}],"border-color-x":[{"border-x":ac()}],"border-color-y":[{"border-y":ac()}],"border-color-s":[{"border-s":ac()}],"border-color-e":[{"border-e":ac()}],"border-color-t":[{"border-t":ac()}],"border-color-r":[{"border-r":ac()}],"border-color-b":[{"border-b":ac()}],"border-color-l":[{"border-l":ac()}],"divide-color":[{divide:ac()}],"outline-style":[{outline:[...aj(),"none","hidden"]}],"outline-offset":[{"outline-offset":[u,L,F]}],"outline-w":[{outline:["",u,M,G]}],"outline-color":[{outline:ac()}],shadow:[{shadow:["","none",l,R,K]}],"shadow-color":[{shadow:ac()}],"inset-shadow":[{"inset-shadow":["none",m,R,K]}],"inset-shadow-color":[{"inset-shadow":ac()}],"ring-w":[{ring:ai()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ac()}],"ring-offset-w":[{"ring-offset":[u,G]}],"ring-offset-color":[{"ring-offset":ac()}],"inset-ring-w":[{"inset-ring":ai()}],"inset-ring-color":[{"inset-ring":ac()}],"text-shadow":[{"text-shadow":["none",n,R,K]}],"text-shadow-color":[{"text-shadow":ac()}],opacity:[{opacity:[u,L,F]}],"mix-blend":[{"mix-blend":[...ak(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ak()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[u]}],"mask-image-linear-from-pos":[{"mask-linear-from":al()}],"mask-image-linear-to-pos":[{"mask-linear-to":al()}],"mask-image-linear-from-color":[{"mask-linear-from":ac()}],"mask-image-linear-to-color":[{"mask-linear-to":ac()}],"mask-image-t-from-pos":[{"mask-t-from":al()}],"mask-image-t-to-pos":[{"mask-t-to":al()}],"mask-image-t-from-color":[{"mask-t-from":ac()}],"mask-image-t-to-color":[{"mask-t-to":ac()}],"mask-image-r-from-pos":[{"mask-r-from":al()}],"mask-image-r-to-pos":[{"mask-r-to":al()}],"mask-image-r-from-color":[{"mask-r-from":ac()}],"mask-image-r-to-color":[{"mask-r-to":ac()}],"mask-image-b-from-pos":[{"mask-b-from":al()}],"mask-image-b-to-pos":[{"mask-b-to":al()}],"mask-image-b-from-color":[{"mask-b-from":ac()}],"mask-image-b-to-color":[{"mask-b-to":ac()}],"mask-image-l-from-pos":[{"mask-l-from":al()}],"mask-image-l-to-pos":[{"mask-l-to":al()}],"mask-image-l-from-color":[{"mask-l-from":ac()}],"mask-image-l-to-color":[{"mask-l-to":ac()}],"mask-image-x-from-pos":[{"mask-x-from":al()}],"mask-image-x-to-pos":[{"mask-x-to":al()}],"mask-image-x-from-color":[{"mask-x-from":ac()}],"mask-image-x-to-color":[{"mask-x-to":ac()}],"mask-image-y-from-pos":[{"mask-y-from":al()}],"mask-image-y-to-pos":[{"mask-y-to":al()}],"mask-image-y-from-color":[{"mask-y-from":ac()}],"mask-image-y-to-color":[{"mask-y-to":ac()}],"mask-image-radial":[{"mask-radial":[L,F]}],"mask-image-radial-from-pos":[{"mask-radial-from":al()}],"mask-image-radial-to-pos":[{"mask-radial-to":al()}],"mask-image-radial-from-color":[{"mask-radial-from":ac()}],"mask-image-radial-to-color":[{"mask-radial-to":ac()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":B()}],"mask-image-conic-pos":[{"mask-conic":[u]}],"mask-image-conic-from-pos":[{"mask-conic-from":al()}],"mask-image-conic-to-pos":[{"mask-conic-to":al()}],"mask-image-conic-from-color":[{"mask-conic-from":ac()}],"mask-image-conic-to-color":[{"mask-conic-to":ac()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ad()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:af()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",L,F]}],filter:[{filter:["","none",L,F]}],blur:[{blur:am()}],brightness:[{brightness:[u,L,F]}],contrast:[{contrast:[u,L,F]}],"drop-shadow":[{"drop-shadow":["","none",o,R,K]}],"drop-shadow-color":[{"drop-shadow":ac()}],grayscale:[{grayscale:["",u,L,F]}],"hue-rotate":[{"hue-rotate":[u,L,F]}],invert:[{invert:["",u,L,F]}],saturate:[{saturate:[u,L,F]}],sepia:[{sepia:["",u,L,F]}],"backdrop-filter":[{"backdrop-filter":["","none",L,F]}],"backdrop-blur":[{"backdrop-blur":am()}],"backdrop-brightness":[{"backdrop-brightness":[u,L,F]}],"backdrop-contrast":[{"backdrop-contrast":[u,L,F]}],"backdrop-grayscale":[{"backdrop-grayscale":["",u,L,F]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u,L,F]}],"backdrop-invert":[{"backdrop-invert":["",u,L,F]}],"backdrop-opacity":[{"backdrop-opacity":[u,L,F]}],"backdrop-saturate":[{"backdrop-saturate":[u,L,F]}],"backdrop-sepia":[{"backdrop-sepia":["",u,L,F]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":U()}],"border-spacing-x":[{"border-spacing-x":U()}],"border-spacing-y":[{"border-spacing-y":U()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",L,F]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[u,"initial",L,F]}],ease:[{ease:["linear","initial",s,L,F]}],delay:[{delay:[u,L,F]}],animate:[{animate:["none",z,L,F]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[q,L,F]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:an()}],"rotate-x":[{"rotate-x":an()}],"rotate-y":[{"rotate-y":an()}],"rotate-z":[{"rotate-z":an()}],scale:[{scale:ao()}],"scale-x":[{"scale-x":ao()}],"scale-y":[{"scale-y":ao()}],"scale-z":[{"scale-z":ao()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[L,F,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:ac()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ac()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L,F]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":U()}],"scroll-mx":[{"scroll-mx":U()}],"scroll-my":[{"scroll-my":U()}],"scroll-ms":[{"scroll-ms":U()}],"scroll-me":[{"scroll-me":U()}],"scroll-mt":[{"scroll-mt":U()}],"scroll-mr":[{"scroll-mr":U()}],"scroll-mb":[{"scroll-mb":U()}],"scroll-ml":[{"scroll-ml":U()}],"scroll-p":[{"scroll-p":U()}],"scroll-px":[{"scroll-px":U()}],"scroll-py":[{"scroll-py":U()}],"scroll-ps":[{"scroll-ps":U()}],"scroll-pe":[{"scroll-pe":U()}],"scroll-pt":[{"scroll-pt":U()}],"scroll-pr":[{"scroll-pr":U()}],"scroll-pb":[{"scroll-pb":U()}],"scroll-pl":[{"scroll-pl":U()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L,F]}],fill:[{fill:["none",...ac()]}],"stroke-w":[{stroke:[u,M,G,H]}],stroke:[{stroke:["none",...ac()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function aa(...a){return _(b(a))}},51827,a=>{"use strict";a.s(["default",()=>g],51827);var b=a.i(29611);let c=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},d=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(0,b.forwardRef)(({color:a="currentColor",size:c=24,strokeWidth:f=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,b.createElement)("svg",{ref:l,...e,width:c,height:c,stroke:a,strokeWidth:g?24*Number(f)/Number(c):f,className:d("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,c])=>(0,b.createElement)(a,c)),...Array.isArray(i)?i:[i]])),g=(a,e)=>{let g=(0,b.forwardRef)(({className:g,...h},i)=>(0,b.createElement)(f,{ref:i,iconNode:e,className:d(`lucide-${c(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,g),...h}));return g.displayName=c(a),g}},40695,42261,a=>{"use strict";a.s(["Button",()=>j],40695);var b=a.i(69720),c=a.i(54152);a.s(["cva",()=>g],42261);var d=a.i(39337);let e=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,f=d.clsx,g=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return f(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:g,defaultVariants:h}=b,i=Object.keys(g).map(a=>{let b=null==c?void 0:c[a],d=null==h?void 0:h[a];if(null===b)return null;let f=e(b)||e(d);return g[a][f]}),j=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return f(a,i,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...h,...j}[b]):({...h,...j})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)};var h=a.i(97895);let i=g("inline-flex shrink-0 cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md font-medium text-sm outline-none transition-all focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:border-input dark:bg-input/30 dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 gap-1.5 rounded-md px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function j({className:a,variant:d,size:e,asChild:f=!1,...g}){let j=f?c.Slot:"button";return(0,b.jsx)(j,{className:(0,h.cn)(i({variant:d,size:e,className:a})),"data-slot":"button",...g})}},21380,a=>{"use strict";a.s(["createContextScope",()=>d]);var b=a.i(29611),c=a.i(69720);function d(a,e=[]){let f=[],g=()=>{let c=f.map(a=>b.createContext(a));return function(d){let e=d?.[a]||c;return b.useMemo(()=>({[`__scope${a}`]:{...d,[a]:e}}),[d,e])}};return g.scopeName=a,[function(d,e){let g=b.createContext(e),h=f.length;f=[...f,e];let i=d=>{let{scope:e,children:f,...i}=d,j=e?.[a]?.[h]||g,k=b.useMemo(()=>i,Object.values(i));return(0,c.jsx)(j.Provider,{value:k,children:f})};return i.displayName=d+"Provider",[i,function(c,f){let i=f?.[a]?.[h]||g,j=b.useContext(i);if(j)return j;if(void 0!==e)return e;throw Error(`\`${c}\` must be used within \`${d}\``)}]},function(...a){let c=a[0];if(1===a.length)return c;let d=()=>{let d=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=d.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return b.useMemo(()=>({[`__scope${c.scopeName}`]:e}),[e])}};return d.scopeName=c.scopeName,d}(g,...e)]}},59653,a=>{"use strict";function b(a,c,{checkForDefaultPrevented:d=!0}={}){return function(b){if(a?.(b),!1===d||!b.defaultPrevented)return c?.(b)}}a.s(["composeEventHandlers",()=>b])},71120,a=>{"use strict";a.s(["useLayoutEffect",()=>c]);var b=a.i(29611),c=globalThis?.document?b.useLayoutEffect:()=>{}},9806,a=>{"use strict";a.s(["useControllableState",()=>e],9806);var b=a.i(29611),c=a.i(71120);b[" useEffectEvent ".trim().toString()],b[" useInsertionEffect ".trim().toString()];var d=b[" useInsertionEffect ".trim().toString()]||c.useLayoutEffect;function e({prop:a,defaultProp:c,onChange:e=()=>{},caller:f}){let[g,h,i]=function({defaultProp:a,onChange:c}){let[e,f]=b.useState(a),g=b.useRef(e),h=b.useRef(c);return d(()=>{h.current=c},[c]),b.useEffect(()=>{g.current!==e&&(h.current?.(e),g.current=e)},[e,g]),[e,f,h]}({defaultProp:c,onChange:e}),j=void 0!==a,k=j?a:g;{let c=b.useRef(void 0!==a);b.useEffect(()=>{let a=c.current;if(a!==j){let b=j?"controlled":"uncontrolled";console.warn(`${f} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}c.current=j},[j,f])}return[k,b.useCallback(b=>{if(j){let c="function"==typeof b?b(a):b;c!==a&&i.current?.(c)}else h(b)},[j,a,h,i])]}Symbol("RADIX:SYNC_STATE")},38442,a=>{"use strict";a.s(["useDirection",()=>d]);var b=a.i(29611);a.i(69720);var c=b.createContext(void 0);function d(a){let d=b.useContext(c);return a||d||"ltr"}},91802,a=>{"use strict";a.s(["Presence",()=>e]);var b=a.i(29611),c=a.i(39003),d=a.i(71120),e=a=>{let{present:e,children:g}=a,h=function(a){var c,e;let[g,h]=b.useState(),i=b.useRef(null),j=b.useRef(a),k=b.useRef("none"),[l,m]=(c=a?"mounted":"unmounted",e={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},b.useReducer((a,b)=>e[a][b]??a,c));return b.useEffect(()=>{let a=f(i.current);k.current="mounted"===l?a:"none"},[l]),(0,d.useLayoutEffect)(()=>{let b=i.current,c=j.current;if(c!==a){let d=k.current,e=f(b);a?m("MOUNT"):"none"===e||b?.display==="none"?m("UNMOUNT"):c&&d!==e?m("ANIMATION_OUT"):m("UNMOUNT"),j.current=a}},[a,m]),(0,d.useLayoutEffect)(()=>{if(g){let a,b=g.ownerDocument.defaultView??window,c=c=>{let d=f(i.current).includes(CSS.escape(c.animationName));if(c.target===g&&d&&(m("ANIMATION_END"),!j.current)){let c=g.style.animationFillMode;g.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===g.style.animationFillMode&&(g.style.animationFillMode=c)})}},d=a=>{a.target===g&&(k.current=f(i.current))};return g.addEventListener("animationstart",d),g.addEventListener("animationcancel",c),g.addEventListener("animationend",c),()=>{b.clearTimeout(a),g.removeEventListener("animationstart",d),g.removeEventListener("animationcancel",c),g.removeEventListener("animationend",c)}}m("ANIMATION_END")},[g,m]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:b.useCallback(a=>{i.current=a?getComputedStyle(a):null,h(a)},[])}}(e),i="function"==typeof g?g({present:h.isPresent}):b.Children.only(g),j=(0,c.useComposedRefs)(h.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(i));return"function"==typeof g||h.isPresent?b.cloneElement(i,{ref:j}):null};function f(a){return a?.animationName||"none"}e.displayName="Presence"},29239,a=>{"use strict";a.s(["useId",()=>f]);var b=a.i(29611),c=a.i(71120),d=b[" useId ".trim().toString()]||(()=>void 0),e=0;function f(a){let[f,g]=b.useState(d());return(0,c.useLayoutEffect)(()=>{a||g(a=>a??String(e++))},[a]),a||(f?`radix-${f}`:"")}},97836,a=>{"use strict";a.s(["createCollection",()=>g]);var b=a.i(29611),c=a.i(21380),d=a.i(39003),e=a.i(54152),f=a.i(69720);function g(a){let g=a+"CollectionProvider",[h,i]=(0,c.createContextScope)(g),[j,k]=h(g,{collectionRef:{current:null},itemMap:new Map}),l=a=>{let{scope:c,children:d}=a,e=b.default.useRef(null),g=b.default.useRef(new Map).current;return(0,f.jsx)(j,{scope:c,itemMap:g,collectionRef:e,children:d})};l.displayName=g;let m=a+"CollectionSlot",n=(0,e.createSlot)(m),o=b.default.forwardRef((a,b)=>{let{scope:c,children:e}=a,g=k(m,c),h=(0,d.useComposedRefs)(b,g.collectionRef);return(0,f.jsx)(n,{ref:h,children:e})});o.displayName=m;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,e.createSlot)(p),s=b.default.forwardRef((a,c)=>{let{scope:e,children:g,...h}=a,i=b.default.useRef(null),j=(0,d.useComposedRefs)(c,i),l=k(p,e);return b.default.useEffect(()=>(l.itemMap.set(i,{ref:i,...h}),()=>void l.itemMap.delete(i))),(0,f.jsx)(r,{...{[q]:""},ref:j,children:g})});return s.displayName=p,[{Provider:l,Slot:o,ItemSlot:s},function(c){let d=k(a+"CollectionConsumer",c);return b.default.useCallback(()=>{let a=d.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(d.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[d.collectionRef,d.itemMap])},i]}var h=new WeakMap;function i(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=j(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function j(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],h.set(this,!0)}set(a,b){return h.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=j(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let k=this.size+ +!e;g<0&&h++;let l=[...this.#a],m=!1;for(let a=h;a<k;a++)if(h===a){let f=l[a];l[a]===b&&(f=l[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{m||l[a-1]!==b||(m=!0);let c=l[m?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=i(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=i(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return i(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}})},81533,a=>{"use strict";a.s(["ChevronDownIcon",()=>b],81533);let b=(0,a.i(51827).default)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},91300,a=>{"use strict";a.s(["features",()=>m,"featuresCompare",()=>n,"navItems",()=>l,"plans",()=>p,"reviews",()=>o],91300);var b=a.i(51827);let c=(0,b.default)("chart-no-axes-combined",[["path",{d:"M12 16v5",key:"zza2cw"}],["path",{d:"M16 14v7",key:"1g90b9"}],["path",{d:"M20 10v11",key:"1iqoj0"}],["path",{d:"m22 3-8.646 8.646a.5.5 0 0 1-.708 0L9.354 8.354a.5.5 0 0 0-.707 0L2 15",key:"1fw8x9"}],["path",{d:"M4 18v3",key:"1yp0dc"}],["path",{d:"M8 14v7",key:"n3cwzv"}]]),d=(0,b.default)("circle-check",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),e=(0,b.default)("database-zap",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 15 21.84",key:"14ibmq"}],["path",{d:"M21 5V8",key:"1marbg"}],["path",{d:"M21 12L18 17H22L19 22",key:"zafso"}],["path",{d:"M3 12A9 3 0 0 0 14.59 14.87",key:"1y4wr8"}]]),f=(0,b.default)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]),g=(0,b.default)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),h=(0,b.default)("message-circle",[["path",{d:"M2.992 16.342a2 2 0 0 1 .094 1.167l-1.065 3.29a1 1 0 0 0 1.236 1.168l3.413-.998a2 2 0 0 1 1.099.092 10 10 0 1 0-4.777-4.719",key:"1sd12s"}]]),i=(0,b.default)("notebook-pen",[["path",{d:"M13.4 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-7.4",key:"re6nr2"}],["path",{d:"M2 6h4",key:"aawbzj"}],["path",{d:"M2 10h4",key:"l0bgd4"}],["path",{d:"M2 14h4",key:"1gsvsf"}],["path",{d:"M2 18h4",key:"1bu2t1"}],["path",{d:"M21.378 5.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"pqwjuv"}]]),j=(0,b.default)("pen-line",[["path",{d:"M13 21h8",key:"1jsn5i"}],["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]),k=(0,b.default)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),l=[{href:"/pricing",label:"Pricing",icon:f},{href:"/about",label:"About",icon:i},{href:"/docs",label:"Docs",icon:k},{href:"/privacy",label:"Privacy",icon:g}],m=[{icon:h,title:"chat",description:"Chat with anyone in team."},{icon:j,title:"writing",description:"Notion like editor for writing."},{icon:d,title:"tasks",description:"Automated task tracking."},{icon:k,title:"teams",description:"Collaborate with your team."},{icon:e,title:"storage",description:"Unlimited storage for your files."},{icon:c,title:"analytics",description:"Easy to track your progress."}],n=[{feature:"Doesn't train on your data",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!0,Notion:!0},{feature:"Works across your entire computer",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!0,Notion:!1},{feature:"Always one click away",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!0,Notion:!1},{feature:"Custom actions and automations",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!0,Notion:!1},{feature:"Understands anything you're looking at",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!1,Notion:!1},{feature:"Integrated audio transcription",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!1,Notion:!1},{feature:"Built for both Mac & Windows",Highlight:!0,ChatGPT:!1,Claude:!1,Raycast:!1,Notion:!1}],o=[{name:"Eric Glyman",username:"Co-Founder at Ramp",body:"I've never seen anything like this before. It's amazing. I love it.",img:"https://avatar.vercel.sh/jack"},{name:"Eric James",username:"Co-Founder at Ramp",body:"I don't know what to say. I'm speechless. This is amazing.",img:"https://avatar.vercel.sh/jill"},{name:"Eric Kagabo",username:"Co-Founder at Ramp",body:"I'm at a loss for words. This is amazing. I love it.",img:"https://avatar.vercel.sh/john"},{name:"Eric Mugisha",username:"Co-Founder at Ramp",body:"I'm at a loss for words. This is amazing. I love it.",img:"https://avatar.vercel.sh/jane"},{name:"Eric David",username:"Co-Founder at Ramp",body:"I'm at a loss for words. This is amazing. I love it.",img:"https://avatar.vercel.sh/jenny"},{name:"Eric Tony",username:"Co-Founder at Ramp",body:"I'm at a loss for words. This is amazing. I love it.",img:"https://avatar.vercel.sh/james"}],p=[{id:"free",name:"Free",price:{monthly:0,yearly:0},description:"The perfect starting place for your web app or personal project.",features:["50 API calls / month","60 second checks","Single-user account","5 monitors","Basic email support"],cta:"Get started for free"},{id:"pro",name:"Pro",price:{monthly:90,yearly:75},description:"Everything you need to build and scale your business.",features:["Unlimited API calls","30 second checks","Multi-user account","10 monitors","Priority email support"],cta:"Subscribe to Pro",popular:!0},{id:"enterprise",name:"Enterprise",price:{monthly:"Get in touch for pricing",yearly:"Get in touch for pricing"},description:"Critical security, performance, observability and support.",features:["You can DDOS our API.","Nano-second checks.","Invite your extended family.","Unlimited monitors.","We'll sit on your desk."],cta:"Contact us"}]}];

//# sourceMappingURL=_46e7c3d0._.js.map