(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,90538,e=>{"use strict";e.s(["Accordion",()=>ei,"AccordionContent",()=>er,"AccordionItem",()=>en,"AccordionTrigger",()=>ea],90538);var t=e.i(65830),i=e.i(6943),n=e.i(46511),a=e.i(52683),r=e.i(68768),s=e.i(91967),o=e.i(77406),l=e.i(25666),d=e.i(43272),c=e.i(61702),u=e.i(24186),p="Collapsible",[h,f]=(0,n.createContextScope)(p),[m,v]=h(p),g=i.forwardRef((e,n)=>{let{__scopeCollapsible:a,open:r,defaultOpen:s,disabled:d,onOpenChange:c,...h}=e,[f,v]=(0,o.useControllableState)({prop:r,defaultProp:null!=s&&s,onChange:c,caller:p});return(0,t.jsx)(m,{scope:a,disabled:d,contentId:(0,u.useId)(),open:f,onOpenToggle:i.useCallback(()=>v(e=>!e),[v]),children:(0,t.jsx)(l.Primitive.div,{"data-state":j(f),"data-disabled":d?"":void 0,...h,ref:n})})});g.displayName=p;var b="CollapsibleTrigger",y=i.forwardRef((e,i)=>{let{__scopeCollapsible:n,...a}=e,r=v(b,n);return(0,t.jsx)(l.Primitive.button,{type:"button","aria-controls":r.contentId,"aria-expanded":r.open||!1,"data-state":j(r.open),"data-disabled":r.disabled?"":void 0,disabled:r.disabled,...a,ref:i,onClick:(0,s.composeEventHandlers)(e.onClick,r.onOpenToggle)})});y.displayName=b;var x="CollapsibleContent",w=i.forwardRef((e,i)=>{let{forceMount:n,...a}=e,r=v(x,e.__scopeCollapsible);return(0,t.jsx)(c.Presence,{present:n||r.open,children:e=>{let{present:n}=e;return(0,t.jsx)(_,{...a,ref:i,present:n})}})});w.displayName=x;var _=i.forwardRef((e,n)=>{let{__scopeCollapsible:a,present:s,children:o,...c}=e,u=v(x,a),[p,h]=i.useState(s),f=i.useRef(null),m=(0,r.useComposedRefs)(n,f),g=i.useRef(0),b=g.current,y=i.useRef(0),w=y.current,_=u.open||p,C=i.useRef(_),R=i.useRef(void 0);return i.useEffect(()=>{let e=requestAnimationFrame(()=>C.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,d.useLayoutEffect)(()=>{let e=f.current;if(e){R.current=R.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();g.current=t.height,y.current=t.width,C.current||(e.style.transitionDuration=R.current.transitionDuration,e.style.animationName=R.current.animationName),h(s)}},[u.open,s]),(0,t.jsx)(l.Primitive.div,{"data-state":j(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!_,...c,ref:m,style:{"--radix-collapsible-content-height":b?"".concat(b,"px"):void 0,"--radix-collapsible-content-width":w?"".concat(w,"px"):void 0,...e.style},children:_&&o})});function j(e){return e?"open":"closed"}var C=e.i(77590),R="Accordion",k=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[A,S,N]=(0,a.createCollection)(R),[E,I]=(0,n.createContextScope)(R,[N,f]),P=f(),T=i.default.forwardRef((e,i)=>{let{type:n,...a}=e;return(0,t.jsx)(A.Provider,{scope:e.__scopeAccordion,children:"multiple"===n?(0,t.jsx)(O,{...a,ref:i}):(0,t.jsx)(L,{...a,ref:i})})});T.displayName=R;var[U,D]=E(R),[F,M]=E(R,{collapsible:!1}),L=i.default.forwardRef((e,n)=>{let{value:a,defaultValue:r,onValueChange:s=()=>{},collapsible:l=!1,...d}=e,[c,u]=(0,o.useControllableState)({prop:a,defaultProp:null!=r?r:"",onChange:s,caller:R});return(0,t.jsx)(U,{scope:e.__scopeAccordion,value:i.default.useMemo(()=>c?[c]:[],[c]),onItemOpen:u,onItemClose:i.default.useCallback(()=>l&&u(""),[l,u]),children:(0,t.jsx)(F,{scope:e.__scopeAccordion,collapsible:l,children:(0,t.jsx)(V,{...d,ref:n})})})}),O=i.default.forwardRef((e,n)=>{let{value:a,defaultValue:r,onValueChange:s=()=>{},...l}=e,[d,c]=(0,o.useControllableState)({prop:a,defaultProp:null!=r?r:[],onChange:s,caller:R}),u=i.default.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[c]),p=i.default.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[c]);return(0,t.jsx)(U,{scope:e.__scopeAccordion,value:d,onItemOpen:u,onItemClose:p,children:(0,t.jsx)(F,{scope:e.__scopeAccordion,collapsible:!0,children:(0,t.jsx)(V,{...l,ref:n})})})}),[H,z]=E(R),V=i.default.forwardRef((e,n)=>{let{__scopeAccordion:a,disabled:o,dir:d,orientation:c="vertical",...u}=e,p=i.default.useRef(null),h=(0,r.useComposedRefs)(p,n),f=S(a),m="ltr"===(0,C.useDirection)(d),v=(0,s.composeEventHandlers)(e.onKeyDown,e=>{var t;if(!k.includes(e.key))return;let i=e.target,n=f().filter(e=>{var t;return!(null==(t=e.ref.current)?void 0:t.disabled)}),a=n.findIndex(e=>e.ref.current===i),r=n.length;if(-1===a)return;e.preventDefault();let s=a,o=r-1,l=()=>{(s=a+1)>o&&(s=0)},d=()=>{(s=a-1)<0&&(s=o)};switch(e.key){case"Home":s=0;break;case"End":s=o;break;case"ArrowRight":"horizontal"===c&&(m?l():d());break;case"ArrowDown":"vertical"===c&&l();break;case"ArrowLeft":"horizontal"===c&&(m?d():l());break;case"ArrowUp":"vertical"===c&&d()}null==(t=n[s%r].ref.current)||t.focus()});return(0,t.jsx)(H,{scope:a,disabled:o,direction:d,orientation:c,children:(0,t.jsx)(A.Slot,{scope:a,children:(0,t.jsx)(l.Primitive.div,{...u,"data-orientation":c,ref:h,onKeyDown:o?void 0:v})})})}),B="AccordionItem",[K,X]=E(B),W=i.default.forwardRef((e,i)=>{let{__scopeAccordion:n,value:a,...r}=e,s=z(B,n),o=D(B,n),l=P(n),d=(0,u.useId)(),c=a&&o.value.includes(a)||!1,p=s.disabled||e.disabled;return(0,t.jsx)(K,{scope:n,open:c,disabled:p,triggerId:d,children:(0,t.jsx)(g,{"data-orientation":s.orientation,"data-state":$(c),...l,...r,ref:i,disabled:p,open:c,onOpenChange:e=>{e?o.onItemOpen(a):o.onItemClose(a)}})})});W.displayName=B;var G="AccordionHeader",Y=i.default.forwardRef((e,i)=>{let{__scopeAccordion:n,...a}=e,r=z(R,n),s=X(G,n);return(0,t.jsx)(l.Primitive.h3,{"data-orientation":r.orientation,"data-state":$(s.open),"data-disabled":s.disabled?"":void 0,...a,ref:i})});Y.displayName=G;var J="AccordionTrigger",Z=i.default.forwardRef((e,i)=>{let{__scopeAccordion:n,...a}=e,r=z(R,n),s=X(J,n),o=M(J,n),l=P(n);return(0,t.jsx)(A.ItemSlot,{scope:n,children:(0,t.jsx)(y,{"aria-disabled":s.open&&!o.collapsible||void 0,"data-orientation":r.orientation,id:s.triggerId,...l,...a,ref:i})})});Z.displayName=J;var q="AccordionContent",Q=i.default.forwardRef((e,i)=>{let{__scopeAccordion:n,...a}=e,r=z(R,n),s=X(q,n),o=P(n);return(0,t.jsx)(w,{role:"region","aria-labelledby":s.triggerId,"data-orientation":r.orientation,...o,...a,ref:i,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function $(e){return e?"open":"closed"}Q.displayName=q;var ee=e.i(70287),et=e.i(47163);function ei(e){let{...i}=e;return(0,t.jsx)(T,{"data-slot":"accordion",...i})}function en(e){let{className:i,...n}=e;return(0,t.jsx)(W,{className:(0,et.cn)("border-b last:border-b-0",i),"data-slot":"accordion-item",...n})}function ea(e){let{className:i,children:n,...a}=e;return(0,t.jsx)(Y,{className:"flex",children:(0,t.jsxs)(Z,{className:(0,et.cn)("flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left font-medium text-sm outline-none transition-all hover:underline focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",i),"data-slot":"accordion-trigger",...a,children:[n,(0,t.jsx)(ee.ChevronDownIcon,{className:"pointer-events-none size-4 shrink-0 translate-y-0.5 text-muted-foreground transition-transform duration-200"})]})})}function er(e){let{className:i,children:n,...a}=e;return(0,t.jsx)(Q,{className:"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down","data-slot":"accordion-content",...a,children:(0,t.jsx)("div",{className:(0,et.cn)("pt-0 pb-4",i),children:n})})}},29601,e=>{"use strict";let t;e.s(["default",()=>eY],29601);var i,n=e.i(65830),a=e.i(6943);function r(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function s(){let e=r([":host{display:inline-block;direction:ltr;white-space:nowrap;isolation:isolate;line-height:"," !important}.number,.number__inner{display:inline-block;transform-origin:left top}:host([data-will-change]) :is(.number,.number__inner,.section,.digit,.digit__num,.symbol){will-change:transform}.number{--scale-x:calc(1 + var(",") / var(--width));transform:translateX(var(",")) scaleX(var(--scale-x));margin:0 calc(-1 * ",");position:relative;-webkit-mask-image:linear-gradient(to right,transparent 0,#000 ",",#000 calc(100% - ","),transparent ),linear-gradient(to bottom,transparent 0,#000 ",",#000 calc(100% - ","),transparent 100% ),radial-gradient(at bottom right,","),radial-gradient(at bottom left,","),radial-gradient(at top left,","),radial-gradient(at top right,",");-webkit-mask-size:100% calc(100% - "," * 2),calc(100% - "," * 2) 100%,"," ",","," ",","," ",","," ",";-webkit-mask-position:center,center,top left,top right,bottom right,bottom left;-webkit-mask-repeat:no-repeat}.number__inner{padding:"," ",";transform:scaleX(calc(1 / var(--scale-x))) translateX(calc(-1 * var(",void 0,")}.digit__num,.number .section::after{padding:"," 0}.digit__num{display:inline-block;--offset-raw:mod(var(--length) + var(--n) - mod(var(--c),var(--length)),var(--length));--offset:calc( var(--offset-raw) - var(--length) * round(down,var(--offset-raw) / (var(--length) / 2),1) );--y:clamp(-100%,var(--offset) * 100%,100%);transform:translateY(var(--y))}.digit__num[inert]{position:absolute;top:0;left:50%;transform:translateX(-50%) translateY(var(--y))}.digit:not(.is-spinning) .digit__num[inert]{display:none}.symbol__value{display:inline-block;mix-blend-mode:plus-lighter;white-space:pre}.section--justify-left .symbol > [inert]{left:0}.section--justify-right .symbol > [inert]{right:0}.animate-presence{opacity:calc(1 + var(","))}"],[":host{display:inline-block;direction:ltr;white-space:nowrap;isolation:isolate;line-height:"," !important}.number,.number__inner{display:inline-block;transform-origin:left top}:host([data-will-change]) :is(.number,.number__inner,.section,.digit,.digit__num,.symbol){will-change:transform}.number{--scale-x:calc(1 + var(",") / var(--width));transform:translateX(var(",")) scaleX(var(--scale-x));margin:0 calc(-1 * ",");position:relative;-webkit-mask-image:linear-gradient(to right,transparent 0,#000 ",",#000 calc(100% - ","),transparent ),linear-gradient(to bottom,transparent 0,#000 ",",#000 calc(100% - ","),transparent 100% ),radial-gradient(at bottom right,","),radial-gradient(at bottom left,","),radial-gradient(at top left,","),radial-gradient(at top right,",");-webkit-mask-size:100% calc(100% - "," * 2),calc(100% - "," * 2) 100%,"," ",","," ",","," ",","," ",";-webkit-mask-position:center,center,top left,top right,bottom right,bottom left;-webkit-mask-repeat:no-repeat}.number__inner{padding:"," ",";transform:scaleX(calc(1 / var(--scale-x))) translateX(calc(-1 * var(",")))}:host > :not(.number){z-index:5}.section,.symbol{display:inline-block;position:relative;isolation:isolate}.section::after{content:'\\200b';display:inline-block}.section--justify-left{transform-origin:center left}.section--justify-right{transform-origin:center right}.section > [inert],.symbol > [inert]{margin:0 !important;position:absolute !important;z-index:-1}.digit{display:inline-block;position:relative;--c:var(--current) + var(",")}.digit__num,.number .section::after{padding:"," 0}.digit__num{display:inline-block;--offset-raw:mod(var(--length) + var(--n) - mod(var(--c),var(--length)),var(--length));--offset:calc( var(--offset-raw) - var(--length) * round(down,var(--offset-raw) / (var(--length) / 2),1) );--y:clamp(-100%,var(--offset) * 100%,100%);transform:translateY(var(--y))}.digit__num[inert]{position:absolute;top:0;left:50%;transform:translateX(-50%) translateY(var(--y))}.digit:not(.is-spinning) .digit__num[inert]{display:none}.symbol__value{display:inline-block;mix-blend-mode:plus-lighter;white-space:pre}.section--justify-left .symbol > [inert]{left:0}.section--justify-right .symbol > [inert]{right:0}.animate-presence{opacity:calc(1 + var(","))}"]);return s=function(){return e},e}function o(){let e=r([":host{display:inline-block;direction:ltr;white-space:nowrap;line-height:"," !important}span{display:inline-block}:host([data-will-change]) span{will-change:transform}.number,.digit{padding:"," 0}.symbol{white-space:pre}"]);return o=function(){return e},e}let l=(e,t,i)=>{let n=document.createElement(e),[a,r]=Array.isArray(t)?[void 0,t]:[t,i];return a&&Object.assign(n,a),null==r||r.forEach(e=>n.appendChild(e)),n},d=(String.raw,String.raw),c=(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0})(),u="u">typeof CSS&&CSS.supports&&CSS.supports("line-height","mod(1,1)"),p="u">typeof matchMedia?matchMedia("(prefers-reduced-motion: reduce)"):null,h="--_number-flow-d-opacity",f="--_number-flow-d-width",m="--_number-flow-dx",v="--_number-flow-d",g=(()=>{try{return CSS.registerProperty({name:h,syntax:"<number>",inherits:!1,initialValue:"0"}),CSS.registerProperty({name:m,syntax:"<length>",inherits:!0,initialValue:"0px"}),CSS.registerProperty({name:f,syntax:"<number>",inherits:!1,initialValue:"0"}),CSS.registerProperty({name:v,syntax:"<number>",inherits:!0,initialValue:"0"}),!0}catch(e){return!1}})(),b="var(--number-flow-char-height, 1em)",y="var(--number-flow-mask-height, 0.25em)",x="calc(".concat(y," / 2)"),w="var(--number-flow-mask-width, 0.5em)",_="calc(".concat(w," / var(--scale-x))"),j="#000 0, transparent 71%",C=d(s(),b,f,m,w,_,_,y,y,j,j,j,j,y,_,_,y,_,y,_,y,_,y,x,w,m,v,x,h),R=HTMLElement,k=(d(o(),b,x),u&&c&&g);class A extends R{get animated(){return this._animated}set animated(e){var t;this.animated!==e&&(this._animated=e,null==(t=this.shadowRoot)||t.getAnimations().forEach(e=>e.finish()))}set data(e){var i;if(null==e)return;let{pre:n,integer:a,fraction:r,post:s,value:o}=e;if(this.created){let t=this._data;this._data=e,this.computedTrend="function"==typeof this.trend?this.trend(t.value,o):this.trend,this.computedAnimated=k&&this._animated&&(!this.respectMotionPreference||!(null!=p&&p.matches))&&this.offsetWidth>0&&this.offsetHeight>0,null==(i=this.plugins)||i.forEach(i=>{var n;return null==(n=i.onUpdate)?void 0:n.call(i,e,t,this)}),this.batched||this.willUpdate(),this._pre.update(n),this._num.update({integer:a,fraction:r}),this._post.update(s),this.batched||this.didUpdate()}else{this._data=e,this.attachShadow({mode:"open"});try{null!=this._internals||(this._internals=this.attachInternals()),this._internals.role="img"}catch(e){}if("u">typeof CSSStyleSheet&&this.shadowRoot.adoptedStyleSheets)t||(t=new CSSStyleSheet).replaceSync(C),this.shadowRoot.adoptedStyleSheets=[t];else{let e=document.createElement("style");e.textContent=C,this.shadowRoot.appendChild(e)}this._pre=new I(this,n,{justify:"right",part:"left"}),this.shadowRoot.appendChild(this._pre.el),this._num=new S(this,a,r),this.shadowRoot.appendChild(this._num.el),this._post=new I(this,s,{justify:"left",part:"right"}),this.shadowRoot.appendChild(this._post.el),this.created=!0}try{this._internals.ariaLabel=e.valueAsString}catch(e){}}willUpdate(){this._pre.willUpdate(),this._num.willUpdate(),this._post.willUpdate()}didUpdate(){if(!this.computedAnimated)return;this._abortAnimationsFinish?this._abortAnimationsFinish.abort():this.dispatchEvent(new Event("animationsstart")),this._pre.didUpdate(),this._num.didUpdate(),this._post.didUpdate();let e=new AbortController;Promise.all(this.shadowRoot.getAnimations().map(e=>e.finished)).then(()=>{e.signal.aborted||(this.dispatchEvent(new Event("animationsfinish")),this._abortAnimationsFinish=void 0)}),this._abortAnimationsFinish=e}constructor(){super(),this.created=!1,this.batched=!1;let{animated:e,...t}=this.constructor.defaultProps;this._animated=this.computedAnimated=e,Object.assign(this,t)}}A.defaultProps={transformTiming:{duration:900,easing:"linear(0,.005,.019,.039,.066,.096,.129,.165,.202,.24,.278,.316,.354,.39,.426,.461,.494,.526,.557,.586,.614,.64,.665,.689,.711,.731,.751,.769,.786,.802,.817,.831,.844,.856,.867,.877,.887,.896,.904,.912,.919,.925,.931,.937,.942,.947,.951,.955,.959,.962,.965,.968,.971,.973,.976,.978,.98,.981,.983,.984,.986,.987,.988,.989,.99,.991,.992,.992,.993,.994,.994,.995,.995,.996,.996,.9963,.9967,.9969,.9972,.9975,.9977,.9979,.9981,.9982,.9984,.9985,.9987,.9988,.9989,1)"},spinTiming:void 0,opacityTiming:{duration:450,easing:"ease-out"},animated:!0,trend:(e,t)=>Math.sign(t-e),respectMotionPreference:!0,plugins:void 0,digits:void 0};class S{willUpdate(){this._prevWidth=this.el.offsetWidth,this._prevLeft=this.el.getBoundingClientRect().left,this._integer.willUpdate(),this._fraction.willUpdate()}update(e){let{integer:t,fraction:i}=e;this._integer.update(t),this._fraction.update(i)}didUpdate(){let e=this.el.getBoundingClientRect();this._integer.didUpdate(),this._fraction.didUpdate();let t=this._prevLeft-e.left,i=this.el.offsetWidth,n=this._prevWidth-i;this.el.style.setProperty("--width",String(i)),this.el.animate({[m]:["".concat(t,"px"),"0px"],[f]:[n,0]},{...this.flow.transformTiming,composite:"accumulate"})}constructor(e,t,i,{className:n,...a}={}){this.flow=e,this._integer=new E(e,t,{justify:"right",part:"integer"}),this._fraction=new E(e,i,{justify:"left",part:"fraction"}),this._inner=l("span",{className:"number__inner"},[this._integer.el,this._fraction.el]),this.el=l("span",{...a,part:"number",className:"number ".concat(null!=n?n:"")},[this._inner])}}class N{addChar(e){let{startDigitsAtZero:t=!1,...i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="integer"===e.type||"fraction"===e.type?new U(this,e.type,t?0:e.value,e.pos,{...i,onRemove:this.onCharRemove(e.key)}):new D(this,e.type,e.value,{...i,onRemove:this.onCharRemove(e.key)});return this.children.set(e.key,n),n}unpop(e){e.el.removeAttribute("inert"),e.el.style.top="",e.el.style[this.justify]=""}pop(e){e.forEach(e=>{var t,i,n;e.el.style.top="".concat(e.el.offsetTop,"px"),e.el.style[this.justify]="".concat((t=e.el,"left"===this.justify?t.offsetLeft:(null!=(n=null==(i=t.offsetParent instanceof HTMLElement?t.offsetParent:null)?void 0:i.offsetWidth)?n:0)-t.offsetWidth-t.offsetLeft),"px")}),e.forEach(e=>{e.el.setAttribute("inert",""),e.present=!1})}addNewAndUpdateExisting(e){let t=new Map,i=new Map,n="left"===this.justify,a=n?"prepend":"append";if(function(e,t){let{reverse:i=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},n=e.length;for(let a=i?n-1:0;i?a>=0:a<n;i?a--:a++)t(e[a],a)}(e,e=>{let n;this.children.has(e.key)?(n=this.children.get(e.key),i.set(e,n),this.unpop(n),n.present=!0):(n=this.addChar(e,{startDigitsAtZero:!0,animateIn:!0}),t.set(e,n)),this.el[a](n.el)},{reverse:n}),this.flow.computedAnimated){let e=this.el.getBoundingClientRect();t.forEach(t=>{t.willUpdate(e)})}t.forEach((e,t)=>{e.update(t.value)}),i.forEach((e,t)=>{e.update(t.value)})}willUpdate(){let e=this.el.getBoundingClientRect();this._prevOffset=e[this.justify],this.children.forEach(t=>t.willUpdate(e))}didUpdate(){let e=this.el.getBoundingClientRect();this.children.forEach(t=>t.didUpdate(e));let t=e[this.justify],i=this._prevOffset-t;i&&this.children.size&&this.el.animate({transform:["translateX(".concat(i,"px)"),"none"]},{...this.flow.transformTiming,composite:"accumulate"})}constructor(e,t,{justify:i,className:n,...a},r){this.flow=e,this.children=new Map,this.onCharRemove=e=>()=>{this.children.delete(e)},this.justify=i;let s=t.map(e=>this.addChar(e).el);this.el=l("span",{...a,className:"section section--justify-".concat(i," ").concat(null!=n?n:"")},r?r(s):s)}}class E extends N{update(e){let t=new Map;this.children.forEach((i,n)=>{e.find(e=>e.key===n)||t.set(n,i),this.unpop(i)}),this.addNewAndUpdateExisting(e),t.forEach(e=>{e instanceof U&&e.update(0)}),this.pop(t)}}class I extends N{update(e){let t=new Map;this.children.forEach((i,n)=>{e.find(e=>e.key===n)||t.set(n,i)}),this.pop(t),this.addNewAndUpdateExisting(e)}}class P{get present(){return this._present}set present(e){if(this._present!==e){if(this._present=e,e?this.el.removeAttribute("inert"):this.el.setAttribute("inert",""),!this.flow.computedAnimated){e||this._remove();return}this.el.style.setProperty("--_number-flow-d-opacity",e?"0":"-.999"),this.el.animate({[h]:e?[-.9999,0]:[.999,0]},{...this.flow.opacityTiming,composite:"accumulate"}),e?this.flow.removeEventListener("animationsfinish",this._remove):this.flow.addEventListener("animationsfinish",this._remove,{once:!0})}}constructor(e,t,{onRemove:i,animateIn:n=!1}={}){this.flow=e,this.el=t,this._present=!0,this._remove=()=>{var e;this.el.remove(),null==(e=this._onRemove)||e.call(this)},this.el.classList.add("animate-presence"),this.flow.computedAnimated&&n&&this.el.animate({[h]:[-.9999,0]},{...this.flow.opacityTiming,composite:"accumulate"}),this._onRemove=i}}class T extends P{constructor(e,t,i,n){super(e.flow,i,n),this.section=e,this.value=t,this.el=i}}class U extends T{willUpdate(e){let t=this.el.getBoundingClientRect();this._prevValue=this.value;let i=t[this.section.justify]-e[this.section.justify],n=t.width/2;this._prevCenter="left"===this.section.justify?i+n:i-n}update(e){this.el.style.setProperty("--current",String(e)),this._numbers.forEach((t,i)=>i===e?t.removeAttribute("inert"):t.setAttribute("inert","")),this.value=e}didUpdate(e){var t;let i=this.el.getBoundingClientRect(),n=i[this.section.justify]-e[this.section.justify],a=i.width/2,r="left"===this.section.justify?n+a:n-a,s=this._prevCenter-r;s&&this.el.animate({transform:["translateX(".concat(s,"px)"),"none"]},{...this.flow.transformTiming,composite:"accumulate"});let o=this.getDelta();o&&(this.el.classList.add("is-spinning"),this.el.animate({[v]:[-o,0]},{...null!=(t=this.flow.spinTiming)?t:this.flow.transformTiming,composite:"accumulate"}),this.flow.addEventListener("animationsfinish",this._onAnimationsFinish,{once:!0}))}getDelta(){var e;if(this.flow.plugins)for(let t of this.flow.plugins){let i=null==(e=t.getDelta)?void 0:e.call(t,this.value,this._prevValue,this);if(null!=i)return i}let t=this.value-this._prevValue,i=this.flow.computedTrend||Math.sign(t);return i<0&&this.value>this._prevValue?this.value-this.length-this._prevValue:i>0&&this.value<this._prevValue?this.length-this._prevValue+this.value:t}constructor(e,t,i,n,a){var r,s,o;let d=(null!=(o=null==(s=null==(r=e.flow.digits)?void 0:r[n])?void 0:s.max)?o:9)+1,c=Array.from({length:d}).map((e,t)=>{let n=l("span",{className:"digit__num"},[document.createTextNode(String(t))]);return t!==i&&n.setAttribute("inert",""),n.style.setProperty("--n",String(t)),n}),u=l("span",{part:"digit ".concat(t,"-digit"),className:"digit"},c);u.style.setProperty("--current",String(i)),u.style.setProperty("--length",String(d)),super(e,i,u,a),this.pos=n,this._onAnimationsFinish=()=>{this.el.classList.remove("is-spinning")},this._numbers=c,this.length=d}}class D extends T{willUpdate(e){if("decimal"===this.type)return;let t=this.el.getBoundingClientRect();this._prevOffset=t[this.section.justify]-e[this.section.justify]}update(e){if(this.value!==e){let t=this._children.get(this.value);t&&(t.present=!1);let i=this._children.get(e);if(i)i.present=!0;else{let t=l("span",{className:"symbol__value",textContent:e});this.el.appendChild(t),this._children.set(e,new P(this.flow,t,{animateIn:!0,onRemove:this._onChildRemove(e)}))}}this.value=e}didUpdate(e){if("decimal"===this.type)return;let t=this.el.getBoundingClientRect()[this.section.justify]-e[this.section.justify],i=this._prevOffset-t;i&&this.el.animate({transform:["translateX(".concat(i,"px)"),"none"]},{...this.flow.transformTiming,composite:"accumulate"})}constructor(e,t,i,n){let a=l("span",{className:"symbol__value",textContent:i});super(e,i,l("span",{part:"symbol ".concat(t),className:"symbol"},[a]),n),this.type=t,this._children=new Map,this._onChildRemove=e=>()=>{this._children.delete(e)},this._children.set(i,new P(this.flow,a,{onRemove:this._onChildRemove(i)}))}}var F=a;let M=parseInt(null==(i=F.version.match(/^(\d+)\./))?void 0:i[1])>=19;class L extends A{attributeChangedCallback(e,t,i){this[e]=JSON.parse(i)}}L.observedAttributes=M?[]:["data","digits"],((e,t)=>{customElements.get(e)||customElements.define(e,t)})("number-flow-react",L);let O={},H=M?e=>e:JSON.stringify;function z(e){let{transformTiming:t,spinTiming:i,opacityTiming:n,animated:a,respectMotionPreference:r,trend:s,plugins:o,...l}=e;return[{transformTiming:t,spinTiming:i,opacityTiming:n,animated:a,respectMotionPreference:r,trend:s,plugins:o},l]}class V extends F.Component{updateProperties(e){if(!this.el)return;this.el.batched=!this.props.isolate;let[t]=z(this.props);Object.entries(t).forEach(e=>{let[t,i]=e;this.el[t]=null!=i?i:L.defaultProps[t]}),(null==e?void 0:e.onAnimationsStart)&&this.el.removeEventListener("animationsstart",e.onAnimationsStart),this.props.onAnimationsStart&&this.el.addEventListener("animationsstart",this.props.onAnimationsStart),(null==e?void 0:e.onAnimationsFinish)&&this.el.removeEventListener("animationsfinish",e.onAnimationsFinish),this.props.onAnimationsFinish&&this.el.addEventListener("animationsfinish",this.props.onAnimationsFinish)}componentDidMount(){this.updateProperties(),M&&this.el&&(this.el.digits=this.props.digits,this.el.data=this.props.data)}getSnapshotBeforeUpdate(e){if(this.updateProperties(e),e.data!==this.props.data){if(this.props.group)return this.props.group.willUpdate(),()=>{var e;return null==(e=this.props.group)?void 0:e.didUpdate()};if(!this.props.isolate){var t;return null==(t=this.el)||t.willUpdate(),()=>{var e;return null==(e=this.el)?void 0:e.didUpdate()}}}return null}componentDidUpdate(e,t,i){null==i||i()}handleRef(e){this.props.innerRef&&(this.props.innerRef.current=e),this.el=e}render(){let[e,{innerRef:t,className:i,data:n,willChange:a,isolate:r,group:s,digits:o,onAnimationsStart:l,onAnimationsFinish:d,...c}]=z(this.props);return F.createElement("number-flow-react",{ref:this.handleRef,"data-will-change":a?"":void 0,class:i,...c,dangerouslySetInnerHTML:{__html:""},suppressHydrationWarning:!0,digits:H(o),data:H(n)})}constructor(e){super(e),this.handleRef=this.handleRef.bind(this)}}let B=F.forwardRef(function(e,t){let{value:i,locales:n,format:a,prefix:r,suffix:s,...o}=e;F.useImperativeHandle(t,()=>l.current,[]);let l=F.useRef(),d=F.useContext(K);null==d||d.useRegister(l);let c=F.useMemo(()=>n?JSON.stringify(n):"",[n]),u=F.useMemo(()=>a?JSON.stringify(a):"",[a]),p=F.useMemo(()=>{var e,t;return function(e,t,i,n){let a=t.formatToParts(e);i&&a.unshift({type:"prefix",value:i}),n&&a.push({type:"suffix",value:n});let r=[],s=[],o=[],l=[],d={},c=e=>{var t;return"".concat(e,":").concat(d[e]=(null!=(t=d[e])?t:-1)+1)},u="",p=!1,h=!1;for(let e of a){u+=e.value;let t="minusSign"===e.type||"plusSign"===e.type?"sign":e.type;"integer"===t?(p=!0,s.push(...e.value.split("").map(e=>({type:t,value:parseInt(e)})))):"group"===t?s.push({type:t,value:e.value}):"decimal"===t?(h=!0,o.push({type:t,value:e.value,key:c(t)})):"fraction"===t?o.push(...e.value.split("").map(e=>({type:t,value:parseInt(e),key:c(t),pos:-1-d[t]}))):(p||h?l:r).push({type:t,value:e.value,key:c(t)})}let f=[];for(let e=s.length-1;e>=0;e--){let t=s[e];f.unshift("integer"===t.type?{...t,key:c(t.type),pos:d[t.type]}:{...t,key:c(t.type)})}return{pre:r,integer:f,fraction:o,post:l,valueAsString:u,value:"string"==typeof e?parseFloat(e):e}}(i,null!=(t=O[e="".concat(c,":").concat(u)])?t:O[e]=new Intl.NumberFormat(n,a),r,s)},[i,c,u,r,s]);return F.createElement(V,{...o,group:d,data:p,innerRef:l})}),K=F.createContext(void 0),X=(0,e.i(86981).default)("badge-check",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var W=e.i(81808),G=e.i(94237),Y=e.i(47163);let J=(0,G.cva)("inline-flex w-fit shrink-0 items-center justify-center gap-1 overflow-hidden whitespace-nowrap rounded-md border px-2 py-0.5 font-medium text-xs transition-[color,box-shadow] focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&>svg]:pointer-events-none [&>svg]:size-3",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40 [a&]:hover:bg-destructive/90",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function Z(e){let{className:t,variant:i,asChild:a=!1,...r}=e,s=a?W.Slot:"span";return(0,n.jsx)(s,{className:(0,Y.cn)(J({variant:i}),t),"data-slot":"badge",...r})}var q=e.i(67881);function Q(e){let{className:t,...i}=e;return(0,n.jsx)("div",{className:(0,Y.cn)("flex flex-col gap-6 rounded-xl border bg-card py-6 text-card-foreground shadow-sm",t),"data-slot":"card",...i})}function $(e){let{className:t,...i}=e;return(0,n.jsx)("div",{className:(0,Y.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),"data-slot":"card-header",...i})}function ee(e){let{className:t,...i}=e;return(0,n.jsx)("div",{className:(0,Y.cn)("font-semibold leading-none",t),"data-slot":"card-title",...i})}function et(e){let{className:t,...i}=e;return(0,n.jsx)("div",{className:(0,Y.cn)("text-muted-foreground text-sm",t),"data-slot":"card-description",...i})}function ei(e){let{className:t,...i}=e;return(0,n.jsx)("div",{className:(0,Y.cn)("px-6",t),"data-slot":"card-content",...i})}function en(e){let{className:t,...i}=e;return(0,n.jsx)("div",{className:(0,Y.cn)("flex items-center px-6 [.border-t]:pt-6",t),"data-slot":"card-footer",...i})}var ea=e.i(91967),er=e.i(46511),es=e.i(52683),eo=e.i(68768),el=e.i(24186),ed=e.i(25666),ec=e.i(38063),eu=e.i(77406),ep=e.i(77590),eh="rovingFocusGroup.onEntryFocus",ef={bubbles:!1,cancelable:!0},em="RovingFocusGroup",[ev,eg,eb]=(0,es.createCollection)(em),[ey,ex]=(0,er.createContextScope)(em,[eb]),[ew,e_]=ey(em),ej=a.forwardRef((e,t)=>(0,n.jsx)(ev.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(ev.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,n.jsx)(eC,{...e,ref:t})})}));ej.displayName=em;var eC=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:i,orientation:r,loop:s=!1,dir:o,currentTabStopId:l,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:c,onEntryFocus:u,preventScrollOnEntryFocus:p=!1,...h}=e,f=a.useRef(null),m=(0,eo.useComposedRefs)(t,f),v=(0,ep.useDirection)(o),[g,b]=(0,eu.useControllableState)({prop:l,defaultProp:null!=d?d:null,onChange:c,caller:em}),[y,x]=a.useState(!1),w=(0,ec.useCallbackRef)(u),_=eg(i),j=a.useRef(!1),[C,R]=a.useState(0);return a.useEffect(()=>{let e=f.current;if(e)return e.addEventListener(eh,w),()=>e.removeEventListener(eh,w)},[w]),(0,n.jsx)(ew,{scope:i,orientation:r,dir:v,loop:s,currentTabStopId:g,onItemFocus:a.useCallback(e=>b(e),[b]),onItemShiftTab:a.useCallback(()=>x(!0),[]),onFocusableItemAdd:a.useCallback(()=>R(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>R(e=>e-1),[]),children:(0,n.jsx)(ed.Primitive.div,{tabIndex:y||0===C?-1:0,"data-orientation":r,...h,ref:m,style:{outline:"none",...e.style},onMouseDown:(0,ea.composeEventHandlers)(e.onMouseDown,()=>{j.current=!0}),onFocus:(0,ea.composeEventHandlers)(e.onFocus,e=>{let t=!j.current;if(e.target===e.currentTarget&&t&&!y){let t=new CustomEvent(eh,ef);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);eS([e.find(e=>e.active),e.find(e=>e.id===g),...e].filter(Boolean).map(e=>e.ref.current),p)}}j.current=!1}),onBlur:(0,ea.composeEventHandlers)(e.onBlur,()=>x(!1))})})}),eR="RovingFocusGroupItem",ek=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:i,focusable:r=!0,active:s=!1,tabStopId:o,children:l,...d}=e,c=(0,el.useId)(),u=o||c,p=e_(eR,i),h=p.currentTabStopId===u,f=eg(i),{onFocusableItemAdd:m,onFocusableItemRemove:v,currentTabStopId:g}=p;return a.useEffect(()=>{if(r)return m(),()=>v()},[r,m,v]),(0,n.jsx)(ev.ItemSlot,{scope:i,id:u,focusable:r,active:s,children:(0,n.jsx)(ed.Primitive.span,{tabIndex:h?0:-1,"data-orientation":p.orientation,...d,ref:t,onMouseDown:(0,ea.composeEventHandlers)(e.onMouseDown,e=>{r?p.onItemFocus(u):e.preventDefault()}),onFocus:(0,ea.composeEventHandlers)(e.onFocus,()=>p.onItemFocus(u)),onKeyDown:(0,ea.composeEventHandlers)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,i){var n;let a=(n=e.key,"rtl"!==i?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return eA[a]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let i=f().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)i.reverse();else if("prev"===t||"next"===t){"prev"===t&&i.reverse();let n=i.indexOf(e.currentTarget);i=p.loop?function(e,t){return e.map((i,n)=>e[(t+n)%e.length])}(i,n+1):i.slice(n+1)}setTimeout(()=>eS(i))}}),children:"function"==typeof l?l({isCurrentTabStop:h,hasTabStop:null!=g}):l})})});ek.displayName=eR;var eA={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function eS(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=document.activeElement;for(let n of e)if(n===i||(n.focus({preventScroll:t}),document.activeElement!==i))return}var eN=e.i(61702),eE="Tabs",[eI,eP]=(0,er.createContextScope)(eE,[ex]),eT=ex(),[eU,eD]=eI(eE),eF=a.forwardRef((e,t)=>{let{__scopeTabs:i,value:a,onValueChange:r,defaultValue:s,orientation:o="horizontal",dir:l,activationMode:d="automatic",...c}=e,u=(0,ep.useDirection)(l),[p,h]=(0,eu.useControllableState)({prop:a,onChange:r,defaultProp:null!=s?s:"",caller:eE});return(0,n.jsx)(eU,{scope:i,baseId:(0,el.useId)(),value:p,onValueChange:h,orientation:o,dir:u,activationMode:d,children:(0,n.jsx)(ed.Primitive.div,{dir:u,"data-orientation":o,...c,ref:t})})});eF.displayName=eE;var eM="TabsList",eL=a.forwardRef((e,t)=>{let{__scopeTabs:i,loop:a=!0,...r}=e,s=eD(eM,i),o=eT(i);return(0,n.jsx)(ej,{asChild:!0,...o,orientation:s.orientation,dir:s.dir,loop:a,children:(0,n.jsx)(ed.Primitive.div,{role:"tablist","aria-orientation":s.orientation,...r,ref:t})})});eL.displayName=eM;var eO="TabsTrigger",eH=a.forwardRef((e,t)=>{let{__scopeTabs:i,value:a,disabled:r=!1,...s}=e,o=eD(eO,i),l=eT(i),d=eV(o.baseId,a),c=eB(o.baseId,a),u=a===o.value;return(0,n.jsx)(ek,{asChild:!0,...l,focusable:!r,active:u,children:(0,n.jsx)(ed.Primitive.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":c,"data-state":u?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:d,...s,ref:t,onMouseDown:(0,ea.composeEventHandlers)(e.onMouseDown,e=>{r||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(a)}),onKeyDown:(0,ea.composeEventHandlers)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(a)}),onFocus:(0,ea.composeEventHandlers)(e.onFocus,()=>{let e="manual"!==o.activationMode;u||r||!e||o.onValueChange(a)})})})});eH.displayName=eO;var ez="TabsContent";function eV(e,t){return"".concat(e,"-trigger-").concat(t)}function eB(e,t){return"".concat(e,"-content-").concat(t)}function eK(e){let{className:t,...i}=e;return(0,n.jsx)(eF,{className:(0,Y.cn)("flex flex-col gap-2",t),"data-slot":"tabs",...i})}function eX(e){let{className:t,...i}=e;return(0,n.jsx)(eL,{className:(0,Y.cn)("inline-flex h-9 w-fit items-center justify-center rounded-lg bg-muted p-[3px] text-muted-foreground",t),"data-slot":"tabs-list",...i})}function eW(e){let{className:t,...i}=e;return(0,n.jsx)(eH,{className:(0,Y.cn)("inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 whitespace-nowrap rounded-md border border-transparent px-2 py-1 font-medium text-foreground text-sm transition-[color,box-shadow] focus-visible:border-ring focus-visible:outline-1 focus-visible:outline-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:shadow-sm dark:text-muted-foreground dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 dark:data-[state=active]:text-foreground [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",t),"data-slot":"tabs-trigger",...i})}a.forwardRef((e,t)=>{let{__scopeTabs:i,value:r,forceMount:s,children:o,...l}=e,d=eD(ez,i),c=eV(d.baseId,r),u=eB(d.baseId,r),p=r===d.value,h=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,n.jsx)(eN.Presence,{present:s||p,children:i=>{let{present:a}=i;return(0,n.jsx)(ed.Primitive.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!a,id:u,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:a&&o})}})}).displayName=ez;var eG=e.i(94710);function eY(){let[e,t]=(0,a.useState)("monthly");return(0,n.jsx)("div",{className:"mb-40 flex flex-col gap-16 text-center",children:(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center gap-6",children:[(0,n.jsx)(eK,{defaultValue:e,onValueChange:t,children:(0,n.jsxs)(eX,{className:"rounded-full bg-my-background p-4 py-6",children:[(0,n.jsx)(eW,{className:"rounded-full p-4 dark:data-[state=active]:bg-muted",value:"monthly",children:"Monthly"}),(0,n.jsxs)(eW,{className:"rounded-full p-4 dark:data-[state=active]:bg-muted",value:"yearly",children:["Yearly",(0,n.jsx)(Z,{variant:"secondary",children:"20% off"})]})]})}),(0,n.jsx)("div",{className:"grid w-full grid-cols-1 gap-[15px] overflow-visible sm:grid-cols-2 sm:px-4 md:px-0 lg:grid-cols-3",children:eG.plans.map(t=>(0,n.jsxs)(Q,{className:(0,Y.cn)("relative w-full text-left",t.popular&&"ring-2 ring-primary"),children:[t.popular&&(0,n.jsx)(Z,{className:"-translate-x-1/2 -translate-y-1/2 absolute top-0 left-1/2 rounded-full",children:"Popular"}),(0,n.jsxs)($,{children:[(0,n.jsx)(ee,{className:"font-medium text-xl",children:t.name}),(0,n.jsxs)(et,{children:[(0,n.jsx)("p",{className:"mb-4",children:t.description}),"number"==typeof t.price[e]?(0,n.jsx)(B,{className:"font-medium text-foreground text-xl lg:text-3xl",format:{style:"currency",currency:"USD",maximumFractionDigits:0},suffix:"/seat",value:t.price[e]}):(0,n.jsxs)("span",{className:"font-medium text-foreground text-xl lg:text-2xl",children:[t.price[e],"."]})]})]}),(0,n.jsx)(ei,{className:"grid gap-2",children:t.features.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground text-sm",children:[(0,n.jsx)(X,{className:"size-4 text-brand-500"}),e]},t))}),(0,n.jsx)(en,{className:"mt-auto",children:(0,n.jsx)(q.Button,{className:(0,Y.cn)("w-full rounded-full",t.popular?"bg-brand-600 hover:bg-brand-500":""),size:"lg",variant:t.popular?"default":"secondary",children:t.cta})})]},t.id))})]})})}}]);