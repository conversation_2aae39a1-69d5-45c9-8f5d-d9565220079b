import {
  ChartNoAxesCombinedIcon,
  CircleCheckIcon,
  DatabaseZapIcon,
  HouseIcon,
  ImageIcon,
  MessageCircleIcon,
  NotebookPenIcon,
  PenLineIcon,
  UsersIcon,
} from 'lucide-react';
import type { TFeature, TFooterLink, TNavItem } from '@/types';

export const navItems: TNavItem[] = [
  {
    href: '/pricing',
    label: 'Pricing',
    icon: HouseIcon,
  },
  {
    href: '/about',
    label: 'About',
    icon: NotebookPenIcon,
  },
  {
    href: '/docs',
    label: 'Docs',
    icon: UsersIcon,
  },
  {
    href: '/privacy',
    label: 'Privacy',
    icon: ImageIcon,
  },
];

export const footerLinks: TFooterLink[] = [
  {
    title: 'Newsroom',
    links: [
      { name: 'Latest News', href: '/', external: false },
      { name: 'Top Stories', href: '/', external: false },
      { name: "Editor's Picks", href: '/', external: false },
    ],
  },
  {
    title: 'Company',
    links: [
      { name: 'About Us', href: '/', external: false },
      { name: 'Careers', href: '/', external: false },
      { name: 'Press', href: '/', external: false },
      { name: 'Contact', href: '/', external: false },
    ],
  },
  {
    title: 'For Business',
    links: [
      { name: 'Advertise with Us', href: '/', external: false },
      { name: 'Media Kit', href: '/', external: false },
      { name: 'Partner with Us', href: '/', external: false },
    ],
  },
  {
    title: 'More',
    links: [
      { name: 'Newsletter', href: '/', external: false },
      { name: 'Mobile App', href: '/', external: false },
      { name: 'RSS Feeds', href: '/', external: false },
      { name: 'Help Center', href: '/', external: false },
    ],
  },
  {
    title: 'Terms & Policies',
    links: [
      { name: 'Terms of Use', href: '/', external: false },
      { name: 'Privacy Policy', href: '/', external: false },
      { name: 'Cookie Policy', href: '/', external: false },
      { name: 'Editorial Policy', href: '/', external: false },
    ],
  },
  {
    title: 'Safety',
    links: [
      { name: 'Fact-Checking', href: '/', external: false },
      { name: 'Corrections', href: '/', external: false },
      { name: 'Trust & Transparency', href: '/', external: false },
    ],
  },
  {
    title: 'Follow Us',
    links: [
      { name: 'Facebook', href: '/', external: true },
      { name: 'Twitter', href: '/', external: true },
      { name: 'Instagram', href: '/', external: true },
      { name: 'YouTube', href: '/', external: true },
    ],
  },
  {
    title: 'Sections',
    links: [
      { name: 'Politics', href: '/', external: false },
      { name: 'Business', href: '/', external: false },
      { name: 'Technology', href: '/', external: false },
      { name: 'Health', href: '/', external: false },
    ],
  },
  {
    title: 'Resources',
    links: [
      { name: 'Media Resources', href: '/', external: false },
      { name: 'Author Guidelines', href: '/', external: false },
      { name: 'News Archive', href: '/', external: false },
    ],
  },
  {
    title: 'Community',
    links: [
      { name: 'Events', href: '/', external: false },
      { name: 'Reader Stories', href: '/', external: false },
      { name: 'Submit News', href: '/', external: false },
    ],
  },
];

export const features: TFeature[] = [
  {
    icon: MessageCircleIcon,
    title: 'chat',
    description: 'Chat with anyone in team.',
  },
  {
    icon: PenLineIcon,
    title: 'writing',
    description: 'Notion like editor for writing.',
  },
  {
    icon: CircleCheckIcon,
    title: 'tasks',
    description: 'Automated task tracking.',
  },
  {
    icon: UsersIcon,
    title: 'teams',
    description: 'Collaborate with your team.',
  },
  {
    icon: DatabaseZapIcon,
    title: 'storage',
    description: 'Unlimited storage for your files.',
  },
  {
    icon: ChartNoAxesCombinedIcon,
    title: 'analytics',
    description: 'Easy to track your progress.',
  },
];

export const featuresCompare = [
  {
    feature: "Doesn't train on your data",
    Highlight: true,
    ChatGPT: false,
    Claude: false,
    Raycast: true,
    Notion: true,
  },
  {
    feature: 'Works across your entire computer',
    Highlight: true,
    ChatGPT: false,
    Claude: false,
    Raycast: true,
    Notion: false,
  },
  {
    feature: 'Always one click away',
    Highlight: true,
    ChatGPT: false,
    Claude: false,
    Raycast: true,
    Notion: false,
  },
  {
    feature: 'Custom actions and automations',
    Highlight: true,
    ChatGPT: false,
    Claude: false,
    Raycast: true,
    Notion: false,
  },
  {
    feature: "Understands anything you're looking at",
    Highlight: true,
    ChatGPT: false,
    Claude: false,
    Raycast: false,
    Notion: false,
  },
  {
    feature: 'Integrated audio transcription',
    Highlight: true,
    ChatGPT: false,
    Claude: false,
    Raycast: false,
    Notion: false,
  },
  {
    feature: 'Built for both Mac & Windows',
    Highlight: true,
    ChatGPT: false,
    Claude: false,
    Raycast: false,
    Notion: false,
  },
];

export const reviews = [
  {
    name: 'Eric Glyman',
    username: 'Co-Founder at Ramp',
    body: "I've never seen anything like this before. It's amazing. I love it.",
    img: 'https://avatar.vercel.sh/jack',
  },
  {
    name: 'Eric James',
    username: 'Co-Founder at Ramp',
    body: "I don't know what to say. I'm speechless. This is amazing.",
    img: 'https://avatar.vercel.sh/jill',
  },
  {
    name: 'Eric Kagabo',
    username: 'Co-Founder at Ramp',
    body: "I'm at a loss for words. This is amazing. I love it.",
    img: 'https://avatar.vercel.sh/john',
  },
  {
    name: 'Eric Mugisha',
    username: 'Co-Founder at Ramp',
    body: "I'm at a loss for words. This is amazing. I love it.",
    img: 'https://avatar.vercel.sh/jane',
  },
  {
    name: 'Eric David',
    username: 'Co-Founder at Ramp',
    body: "I'm at a loss for words. This is amazing. I love it.",
    img: 'https://avatar.vercel.sh/jenny',
  },
  {
    name: 'Eric Tony',
    username: 'Co-Founder at Ramp',
    body: "I'm at a loss for words. This is amazing. I love it.",
    img: 'https://avatar.vercel.sh/james',
  },
];

export const links = [
  {
    group: 'Company',
    items: [
      {
        title: 'About',
        href: '/about',
        external: false,
      },
      {
        title: 'Blog',
        href: '/blogs',
        external: false,
      },
      {
        title: 'Pricing',
        href: '/#pricing',
        external: false,
      },
      {
        title: 'Book a Call',
        href: '/book',
        external: false,
      },
    ],
  },
  {
    group: 'Resources',
    items: [
      {
        title: 'Careers',
        href: '/contact',
        external: false,
      },

      {
        title: 'Support',
        href: '/book',
        external: false,
      },
      {
        title: 'Sitemap',
        href: '/sitemap.xml',
        external: false,
      },
      {
        title: 'llm.txt',
        href: '/llm.txt',
        external: false,
      },
    ],
  },
  {
    group: 'Legal',
    items: [
      {
        title: 'Privacy',
        href: '/privacy',
        external: false,
      },
      {
        title: 'Terms',
        href: '/terms',
        external: false,
      },
    ],
  },
];

export const plans = [
  {
    id: 'free',
    name: 'Free',
    price: {
      monthly: 0,
      yearly: 0,
    },
    description:
      'The perfect starting place for your web app or personal project.',
    features: [
      '50 API calls / month',
      '60 second checks',
      'Single-user account',
      '5 monitors',
      'Basic email support',
    ],
    cta: 'Get started for free',
  },
  {
    id: 'pro',
    name: 'Pro',
    price: {
      monthly: 90,
      yearly: 75,
    },
    description: 'Everything you need to build and scale your business.',
    features: [
      'Unlimited API calls',
      '30 second checks',
      'Multi-user account',
      '10 monitors',
      'Priority email support',
    ],
    cta: 'Subscribe to Pro',
    popular: true,
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: {
      monthly: 'Get in touch for pricing',
      yearly: 'Get in touch for pricing',
    },
    description: 'Critical security, performance, observability and support.',
    features: [
      'You can DDOS our API.',
      'Nano-second checks.',
      'Invite your extended family.',
      'Unlimited monitors.',
      "We'll sit on your desk.",
    ],
    cta: 'Contact us',
  },
];
