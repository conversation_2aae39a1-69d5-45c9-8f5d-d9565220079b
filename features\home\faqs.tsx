import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

const faqs = [
  {
    question: 'Does Highlight work on my device?',
    answer:
      "We are the only AI assistant that works across both Windows and Mac. Our team is fully committed to developing a product that meets users wherever they're at.",
  },
  {
    question: 'How is my data handled?',
    answer:
      "We don't see your data. Period. The only time your data leaves your computer is when you attach it to a cloud LLM query or if you had enabled task detection, and then only the cloud LLM - GPT4o, Claude, etc - is able to see your information.",
  },
  {
    question: 'Is Highlight SOC-2 compliant?',
    answer:
      'We are undergoing the SOC-2 process and expect to complete certification by end of 2025. In the meantime, we can supply and complete any security questionnaires for enterprises interested in working with us, and have published our policies on trust and safety here.',
  },
  {
    question: 'How is this different from ChatGPT?',
    answer:
      "Highlight is ChatGPT-level intelligence, but with the ability to understand what you're looking at on your screen or have said or heard on your laptop. Think of it like ChatGPT, but without the need to ever explain yourself (plus a bunch of other cool features).",
  },
  {
    question: 'Do you have a mobile app?',
    answer: "Not yet, but we're working on it!",
  },
];

export default function FaqsHome() {
  return (
    <section className="mb-40">
      <div className="mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl">
        <h2
          className="mx-auto mb-14 max-w-[320px] whitespace-pre-wrap text-center font-aeonik-bold font-medium text-3xl leading-tight sm:max-w-2xl sm:text-4xl md:max-w-none md:text-4xl lg:text-5xl"
          id="faq-heading"
        >
          Common Questions
        </h2>
        <div className="h-full pb-8">
          <Accordion collapsible type="single">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="cursor-pointer font-medium text-base text-primary hover:text-primary/80 hover:no-underline">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="font-aeonik text-lg text-muted-foreground">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
}
