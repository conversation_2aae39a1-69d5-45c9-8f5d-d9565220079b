module.exports=[95223,a=>{"use strict";let b;a.s(["default",()=>aU],95223);var c,d,e=a.i(69720),f=a.i(29611);let g=(a,b,c)=>{let d=document.createElement(a),[e,f]=Array.isArray(b)?[void 0,b]:[b,c];return e&&Object.assign(d,e),null==f||f.forEach(a=>d.appendChild(a)),d},h=String.raw,i=String.raw,j="--_number-flow-d-opacity",k="--_number-flow-d-width",l="--_number-flow-dx",m="--_number-flow-d",n=(()=>{try{return CSS.registerProperty({name:j,syntax:"<number>",inherits:!1,initialValue:"0"}),CSS.registerProperty({name:l,syntax:"<length>",inherits:!0,initialValue:"0px"}),CSS.registerProperty({name:k,syntax:"<number>",inherits:!1,initialValue:"0"}),CSS.registerProperty({name:m,syntax:"<number>",inherits:!0,initialValue:"0"}),!0}catch{return!1}})(),o="var(--number-flow-char-height, 1em)",p="var(--number-flow-mask-height, 0.25em)",q=`calc(${p} / 2)`,r="var(--number-flow-mask-width, 0.5em)",s=`calc(${r} / var(--scale-x))`,t="#000 0, transparent 71%",u=i`:host{display:inline-block;direction:ltr;white-space:nowrap;isolation:isolate;line-height:${o} !important}.number,.number__inner{display:inline-block;transform-origin:left top}:host([data-will-change]) :is(.number,.number__inner,.section,.digit,.digit__num,.symbol){will-change:transform}.number{--scale-x:calc(1 + var(${k}) / var(--width));transform:translateX(var(${l})) scaleX(var(--scale-x));margin:0 calc(-1 * ${r});position:relative;-webkit-mask-image:linear-gradient(to right,transparent 0,#000 ${s},#000 calc(100% - ${s}),transparent ),linear-gradient(to bottom,transparent 0,#000 ${p},#000 calc(100% - ${p}),transparent 100% ),radial-gradient(at bottom right,${t}),radial-gradient(at bottom left,${t}),radial-gradient(at top left,${t}),radial-gradient(at top right,${t});-webkit-mask-size:100% calc(100% - ${p} * 2),calc(100% - ${s} * 2) 100%,${s} ${p},${s} ${p},${s} ${p},${s} ${p};-webkit-mask-position:center,center,top left,top right,bottom right,bottom left;-webkit-mask-repeat:no-repeat}.number__inner{padding:${q} ${r};transform:scaleX(calc(1 / var(--scale-x))) translateX(calc(-1 * var(${l})))}:host > :not(.number){z-index:5}.section,.symbol{display:inline-block;position:relative;isolation:isolate}.section::after{content:'\200b';display:inline-block}.section--justify-left{transform-origin:center left}.section--justify-right{transform-origin:center right}.section > [inert],.symbol > [inert]{margin:0 !important;position:absolute !important;z-index:-1}.digit{display:inline-block;position:relative;--c:var(--current) + var(${m})}.digit__num,.number .section::after{padding:${q} 0}.digit__num{display:inline-block;--offset-raw:mod(var(--length) + var(--n) - mod(var(--c),var(--length)),var(--length));--offset:calc( var(--offset-raw) - var(--length) * round(down,var(--offset-raw) / (var(--length) / 2),1) );--y:clamp(-100%,var(--offset) * 100%,100%);transform:translateY(var(--y))}.digit__num[inert]{position:absolute;top:0;left:50%;transform:translateX(-50%) translateY(var(--y))}.digit:not(.is-spinning) .digit__num[inert]{display:none}.symbol__value{display:inline-block;mix-blend-mode:plus-lighter;white-space:pre}.section--justify-left .symbol > [inert]{left:0}.section--justify-right .symbol > [inert]{right:0}.animate-presence{opacity:calc(1 + var(${j}))}`,v=class{},w=i`:host{display:inline-block;direction:ltr;white-space:nowrap;line-height:${o} !important}span{display:inline-block}:host([data-will-change]) span{will-change:transform}.number,.digit{padding:${q} 0}.symbol{white-space:pre}`,x=(a,b)=>`<span part="${b}">${a.reduce((a,b)=>a+(a=>`<span class="${"integer"===a.type||"fraction"===a.type?"digit":"symbol"}" part="${"integer"===a.type||"fraction"===a.type?`digit ${a.type}-digit`:`symbol ${a.type}`}">${a.value}</span>`)(b),"")}</span>`,y=!1;class z extends v{constructor(){super(),this.created=!1,this.batched=!1;let{animated:a,...b}=this.constructor.defaultProps;this._animated=this.computedAnimated=a,Object.assign(this,b)}get animated(){return this._animated}set animated(a){var b;this.animated!==a&&(this._animated=a,null==(b=this.shadowRoot)||b.getAnimations().forEach(a=>a.finish()))}set data(a){var c;if(null==a)return;let{pre:d,integer:e,fraction:f,post:g,value:h}=a;if(this.created){let b=this._data;this._data=a,this.computedTrend="function"==typeof this.trend?this.trend(b.value,h):this.trend,this.computedAnimated=y&&this._animated&&(!this.respectMotionPreference||true)&&this.offsetWidth>0&&this.offsetHeight>0,null==(c=this.plugins)||c.forEach(c=>{var d;return null==(d=c.onUpdate)?void 0:d.call(c,a,b,this)}),this.batched||this.willUpdate(),this._pre.update(d),this._num.update({integer:e,fraction:f}),this._post.update(g),this.batched||this.didUpdate()}else{this._data=a,this.attachShadow({mode:"open"});try{this._internals??(this._internals=this.attachInternals()),this._internals.role="img"}catch{}if("u">typeof CSSStyleSheet&&this.shadowRoot.adoptedStyleSheets)b||(b=new CSSStyleSheet).replaceSync(u),this.shadowRoot.adoptedStyleSheets=[b];else{let a=document.createElement("style");a.textContent=u,this.shadowRoot.appendChild(a)}this._pre=new D(this,d,{justify:"right",part:"left"}),this.shadowRoot.appendChild(this._pre.el),this._num=new A(this,e,f),this.shadowRoot.appendChild(this._num.el),this._post=new D(this,g,{justify:"left",part:"right"}),this.shadowRoot.appendChild(this._post.el),this.created=!0}try{this._internals.ariaLabel=a.valueAsString}catch{}}willUpdate(){this._pre.willUpdate(),this._num.willUpdate(),this._post.willUpdate()}didUpdate(){if(!this.computedAnimated)return;this._abortAnimationsFinish?this._abortAnimationsFinish.abort():this.dispatchEvent(new Event("animationsstart")),this._pre.didUpdate(),this._num.didUpdate(),this._post.didUpdate();let a=new AbortController;Promise.all(this.shadowRoot.getAnimations().map(a=>a.finished)).then(()=>{a.signal.aborted||(this.dispatchEvent(new Event("animationsfinish")),this._abortAnimationsFinish=void 0)}),this._abortAnimationsFinish=a}}z.defaultProps={transformTiming:{duration:900,easing:"linear(0,.005,.019,.039,.066,.096,.129,.165,.202,.24,.278,.316,.354,.39,.426,.461,.494,.526,.557,.586,.614,.64,.665,.689,.711,.731,.751,.769,.786,.802,.817,.831,.844,.856,.867,.877,.887,.896,.904,.912,.919,.925,.931,.937,.942,.947,.951,.955,.959,.962,.965,.968,.971,.973,.976,.978,.98,.981,.983,.984,.986,.987,.988,.989,.99,.991,.992,.992,.993,.994,.994,.995,.995,.996,.996,.9963,.9967,.9969,.9972,.9975,.9977,.9979,.9981,.9982,.9984,.9985,.9987,.9988,.9989,1)"},spinTiming:void 0,opacityTiming:{duration:450,easing:"ease-out"},animated:!0,trend:(a,b)=>Math.sign(b-a),respectMotionPreference:!0,plugins:void 0,digits:void 0};class A{constructor(a,b,c,{className:d,...e}={}){this.flow=a,this._integer=new C(a,b,{justify:"right",part:"integer"}),this._fraction=new C(a,c,{justify:"left",part:"fraction"}),this._inner=g("span",{className:"number__inner"},[this._integer.el,this._fraction.el]),this.el=g("span",{...e,part:"number",className:`number ${d??""}`},[this._inner])}willUpdate(){this._prevWidth=this.el.offsetWidth,this._prevLeft=this.el.getBoundingClientRect().left,this._integer.willUpdate(),this._fraction.willUpdate()}update({integer:a,fraction:b}){this._integer.update(a),this._fraction.update(b)}didUpdate(){let a=this.el.getBoundingClientRect();this._integer.didUpdate(),this._fraction.didUpdate();let b=this._prevLeft-a.left,c=this.el.offsetWidth,d=this._prevWidth-c;this.el.style.setProperty("--width",String(c)),this.el.animate({[l]:[`${b}px`,"0px"],[k]:[d,0]},{...this.flow.transformTiming,composite:"accumulate"})}}class B{constructor(a,b,{justify:c,className:d,...e},f){this.flow=a,this.children=new Map,this.onCharRemove=a=>()=>{this.children.delete(a)},this.justify=c;let h=b.map(a=>this.addChar(a).el);this.el=g("span",{...e,className:`section section--justify-${c} ${d??""}`},f?f(h):h)}addChar(a,{startDigitsAtZero:b=!1,...c}={}){let d="integer"===a.type||"fraction"===a.type?new G(this,a.type,b?0:a.value,a.pos,{...c,onRemove:this.onCharRemove(a.key)}):new H(this,a.type,a.value,{...c,onRemove:this.onCharRemove(a.key)});return this.children.set(a.key,d),d}unpop(a){a.el.removeAttribute("inert"),a.el.style.top="",a.el.style[this.justify]=""}pop(a){a.forEach(a=>{var b,c;a.el.style.top=`${a.el.offsetTop}px`,a.el.style[this.justify]=`${b=a.el,"left"===this.justify?b.offsetLeft:((null==(c=b.offsetParent instanceof HTMLElement?b.offsetParent:null)?void 0:c.offsetWidth)??0)-b.offsetWidth-b.offsetLeft}px`}),a.forEach(a=>{a.el.setAttribute("inert",""),a.present=!1})}addNewAndUpdateExisting(a){let b=new Map,c=new Map,d="left"===this.justify,e=d?"prepend":"append";if(function(a,b,{reverse:c=!1}={}){let d=a.length;for(let e=c?d-1:0;c?e>=0:e<d;c?e--:e++)b(a[e],e)}(a,a=>{let d;this.children.has(a.key)?(d=this.children.get(a.key),c.set(a,d),this.unpop(d),d.present=!0):(d=this.addChar(a,{startDigitsAtZero:!0,animateIn:!0}),b.set(a,d)),this.el[e](d.el)},{reverse:d}),this.flow.computedAnimated){let a=this.el.getBoundingClientRect();b.forEach(b=>{b.willUpdate(a)})}b.forEach((a,b)=>{a.update(b.value)}),c.forEach((a,b)=>{a.update(b.value)})}willUpdate(){let a=this.el.getBoundingClientRect();this._prevOffset=a[this.justify],this.children.forEach(b=>b.willUpdate(a))}didUpdate(){let a=this.el.getBoundingClientRect();this.children.forEach(b=>b.didUpdate(a));let b=a[this.justify],c=this._prevOffset-b;c&&this.children.size&&this.el.animate({transform:[`translateX(${c}px)`,"none"]},{...this.flow.transformTiming,composite:"accumulate"})}}class C extends B{update(a){let b=new Map;this.children.forEach((c,d)=>{a.find(a=>a.key===d)||b.set(d,c),this.unpop(c)}),this.addNewAndUpdateExisting(a),b.forEach(a=>{a instanceof G&&a.update(0)}),this.pop(b)}}class D extends B{update(a){let b=new Map;this.children.forEach((c,d)=>{a.find(a=>a.key===d)||b.set(d,c)}),this.pop(b),this.addNewAndUpdateExisting(a)}}class E{constructor(a,b,{onRemove:c,animateIn:d=!1}={}){this.flow=a,this.el=b,this._present=!0,this._remove=()=>{var a;this.el.remove(),null==(a=this._onRemove)||a.call(this)},this.el.classList.add("animate-presence"),this.flow.computedAnimated&&d&&this.el.animate({[j]:[-.9999,0]},{...this.flow.opacityTiming,composite:"accumulate"}),this._onRemove=c}get present(){return this._present}set present(a){if(this._present!==a){if(this._present=a,a?this.el.removeAttribute("inert"):this.el.setAttribute("inert",""),!this.flow.computedAnimated){a||this._remove();return}this.el.style.setProperty("--_number-flow-d-opacity",a?"0":"-.999"),this.el.animate({[j]:a?[-.9999,0]:[.999,0]},{...this.flow.opacityTiming,composite:"accumulate"}),a?this.flow.removeEventListener("animationsfinish",this._remove):this.flow.addEventListener("animationsfinish",this._remove,{once:!0})}}}class F extends E{constructor(a,b,c,d){super(a.flow,c,d),this.section=a,this.value=b,this.el=c}}class G extends F{constructor(a,b,c,d,e){var f,h;let i=((null==(h=null==(f=a.flow.digits)?void 0:f[d])?void 0:h.max)??9)+1,j=Array.from({length:i}).map((a,b)=>{let d=g("span",{className:"digit__num"},[document.createTextNode(String(b))]);return b!==c&&d.setAttribute("inert",""),d.style.setProperty("--n",String(b)),d}),k=g("span",{part:`digit ${b}-digit`,className:"digit"},j);k.style.setProperty("--current",String(c)),k.style.setProperty("--length",String(i)),super(a,c,k,e),this.pos=d,this._onAnimationsFinish=()=>{this.el.classList.remove("is-spinning")},this._numbers=j,this.length=i}willUpdate(a){let b=this.el.getBoundingClientRect();this._prevValue=this.value;let c=b[this.section.justify]-a[this.section.justify],d=b.width/2;this._prevCenter="left"===this.section.justify?c+d:c-d}update(a){this.el.style.setProperty("--current",String(a)),this._numbers.forEach((b,c)=>c===a?b.removeAttribute("inert"):b.setAttribute("inert","")),this.value=a}didUpdate(a){let b=this.el.getBoundingClientRect(),c=b[this.section.justify]-a[this.section.justify],d=b.width/2,e="left"===this.section.justify?c+d:c-d,f=this._prevCenter-e;f&&this.el.animate({transform:[`translateX(${f}px)`,"none"]},{...this.flow.transformTiming,composite:"accumulate"});let g=this.getDelta();g&&(this.el.classList.add("is-spinning"),this.el.animate({[m]:[-g,0]},{...this.flow.spinTiming??this.flow.transformTiming,composite:"accumulate"}),this.flow.addEventListener("animationsfinish",this._onAnimationsFinish,{once:!0}))}getDelta(){var a;if(this.flow.plugins)for(let b of this.flow.plugins){let c=null==(a=b.getDelta)?void 0:a.call(b,this.value,this._prevValue,this);if(null!=c)return c}let b=this.value-this._prevValue,c=this.flow.computedTrend||Math.sign(b);return c<0&&this.value>this._prevValue?this.value-this.length-this._prevValue:c>0&&this.value<this._prevValue?this.length-this._prevValue+this.value:b}}class H extends F{constructor(a,b,c,d){let e=g("span",{className:"symbol__value",textContent:c});super(a,c,g("span",{part:`symbol ${b}`,className:"symbol"},[e]),d),this.type=b,this._children=new Map,this._onChildRemove=a=>()=>{this._children.delete(a)},this._children.set(c,new E(this.flow,e,{onRemove:this._onChildRemove(c)}))}willUpdate(a){if("decimal"===this.type)return;let b=this.el.getBoundingClientRect();this._prevOffset=b[this.section.justify]-a[this.section.justify]}update(a){if(this.value!==a){let b=this._children.get(this.value);b&&(b.present=!1);let c=this._children.get(a);if(c)c.present=!0;else{let b=g("span",{className:"symbol__value",textContent:a});this.el.appendChild(b),this._children.set(a,new E(this.flow,b,{animateIn:!0,onRemove:this._onChildRemove(a)}))}}this.value=a}didUpdate(a){if("decimal"===this.type)return;let b=this.el.getBoundingClientRect()[this.section.justify]-a[this.section.justify],c=this._prevOffset-b;c&&this.el.animate({transform:[`translateX(${c}px)`,"none"]},{...this.flow.transformTiming,composite:"accumulate"})}}var I=f;let J=parseInt(I.version.match(/^(\d+)\./)?.[1])>=19;class K extends z{attributeChangedCallback(a,b,c){this[a]=JSON.parse(c)}}K.observedAttributes=J?[]:["data","digits"],c=0,d=0;let L={},M=J?a=>a:JSON.stringify;function N(a){let{transformTiming:b,spinTiming:c,opacityTiming:d,animated:e,respectMotionPreference:f,trend:g,plugins:h,...i}=a;return[{transformTiming:b,spinTiming:c,opacityTiming:d,animated:e,respectMotionPreference:f,trend:g,plugins:h},i]}class O extends I.Component{updateProperties(a){if(!this.el)return;this.el.batched=!this.props.isolate;let[b]=N(this.props);Object.entries(b).forEach(([a,b])=>{this.el[a]=b??K.defaultProps[a]}),a?.onAnimationsStart&&this.el.removeEventListener("animationsstart",a.onAnimationsStart),this.props.onAnimationsStart&&this.el.addEventListener("animationsstart",this.props.onAnimationsStart),a?.onAnimationsFinish&&this.el.removeEventListener("animationsfinish",a.onAnimationsFinish),this.props.onAnimationsFinish&&this.el.addEventListener("animationsfinish",this.props.onAnimationsFinish)}componentDidMount(){this.updateProperties(),J&&this.el&&(this.el.digits=this.props.digits,this.el.data=this.props.data)}getSnapshotBeforeUpdate(a){if(this.updateProperties(a),a.data!==this.props.data){if(this.props.group)return this.props.group.willUpdate(),()=>this.props.group?.didUpdate();if(!this.props.isolate)return this.el?.willUpdate(),()=>this.el?.didUpdate()}return null}componentDidUpdate(a,b,c){c?.()}handleRef(a){this.props.innerRef&&(this.props.innerRef.current=a),this.el=a}render(){let[a,{innerRef:b,className:c,data:d,willChange:e,isolate:f,group:g,digits:i,onAnimationsStart:j,onAnimationsFinish:k,...l}]=N(this.props);return I.createElement("number-flow-react",{ref:this.handleRef,"data-will-change":e?"":void 0,class:c,...l,dangerouslySetInnerHTML:{__html:h`<template shadowroot="open" shadowrootmode="open"
			><style>
				${w}</style
			><span role="img" aria-label="${d.valueAsString}"
				>${x(d.pre,"left")}<span part="number" class="number"
					>${x(d.integer,"integer")}${x(d.fraction,"fraction")}</span
				>${x(d.post,"right")}</span
			></template
		><span
			style="font-kerning: none; display: inline-block; line-height: ${o} !important; padding: ${p} 0;"
			>${d.valueAsString}</span
		>`},suppressHydrationWarning:!0,digits:M(i),data:M(d)})}constructor(a){super(a),this.handleRef=this.handleRef.bind(this)}}let P=I.forwardRef(function({value:a,locales:b,format:c,prefix:d,suffix:e,...f},g){I.useImperativeHandle(g,()=>h.current,[]);let h=I.useRef(),i=I.useContext(Q);i?.useRegister(h);let j=I.useMemo(()=>b?JSON.stringify(b):"",[b]),k=I.useMemo(()=>c?JSON.stringify(c):"",[c]),l=I.useMemo(()=>(function(a,b,c,d){let e=b.formatToParts(a);c&&e.unshift({type:"prefix",value:c}),d&&e.push({type:"suffix",value:d});let f=[],g=[],h=[],i=[],j={},k=a=>`${a}:${j[a]=(j[a]??-1)+1}`,l="",m=!1,n=!1;for(let a of e){l+=a.value;let b="minusSign"===a.type||"plusSign"===a.type?"sign":a.type;"integer"===b?(m=!0,g.push(...a.value.split("").map(a=>({type:b,value:parseInt(a)})))):"group"===b?g.push({type:b,value:a.value}):"decimal"===b?(n=!0,h.push({type:b,value:a.value,key:k(b)})):"fraction"===b?h.push(...a.value.split("").map(a=>({type:b,value:parseInt(a),key:k(b),pos:-1-j[b]}))):(m||n?i:f).push({type:b,value:a.value,key:k(b)})}let o=[];for(let a=g.length-1;a>=0;a--){let b=g[a];o.unshift("integer"===b.type?{...b,key:k(b.type),pos:j[b.type]}:{...b,key:k(b.type)})}return{pre:f,integer:o,fraction:h,post:i,valueAsString:l,value:"string"==typeof a?parseFloat(a):a}})(a,L[`${j}:${k}`]??=new Intl.NumberFormat(b,c),d,e),[a,j,k,d,e]);return I.createElement(O,{...f,group:i,data:l,innerRef:h})}),Q=I.createContext(void 0),R=(0,a.i(51827).default)("badge-check",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var S=a.i(54152),T=a.i(42261),U=a.i(97895);let V=(0,T.cva)("inline-flex w-fit shrink-0 items-center justify-center gap-1 overflow-hidden whitespace-nowrap rounded-md border px-2 py-0.5 font-medium text-xs transition-[color,box-shadow] focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 [&>svg]:pointer-events-none [&>svg]:size-3",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white focus-visible:ring-destructive/20 dark:bg-destructive/60 dark:focus-visible:ring-destructive/40 [a&]:hover:bg-destructive/90",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function W({className:a,variant:b,asChild:c=!1,...d}){let f=c?S.Slot:"span";return(0,e.jsx)(f,{className:(0,U.cn)(V({variant:b}),a),"data-slot":"badge",...d})}var X=a.i(40695);function Y({className:a,...b}){return(0,e.jsx)("div",{className:(0,U.cn)("flex flex-col gap-6 rounded-xl border bg-card py-6 text-card-foreground shadow-sm",a),"data-slot":"card",...b})}function Z({className:a,...b}){return(0,e.jsx)("div",{className:(0,U.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),"data-slot":"card-header",...b})}function $({className:a,...b}){return(0,e.jsx)("div",{className:(0,U.cn)("font-semibold leading-none",a),"data-slot":"card-title",...b})}function _({className:a,...b}){return(0,e.jsx)("div",{className:(0,U.cn)("text-muted-foreground text-sm",a),"data-slot":"card-description",...b})}function aa({className:a,...b}){return(0,e.jsx)("div",{className:(0,U.cn)("px-6",a),"data-slot":"card-content",...b})}function ab({className:a,...b}){return(0,e.jsx)("div",{className:(0,U.cn)("flex items-center px-6 [.border-t]:pt-6",a),"data-slot":"card-footer",...b})}var ac=a.i(59653),ad=a.i(21380),ae=a.i(97836),af=a.i(39003),ag=a.i(29239),ah=a.i(53834),ai=a.i(64098),aj=a.i(9806),ak=a.i(38442),al="rovingFocusGroup.onEntryFocus",am={bubbles:!1,cancelable:!0},an="RovingFocusGroup",[ao,ap,aq]=(0,ae.createCollection)(an),[ar,as]=(0,ad.createContextScope)(an,[aq]),[at,au]=ar(an),av=f.forwardRef((a,b)=>(0,e.jsx)(ao.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,e.jsx)(ao.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,e.jsx)(aw,{...a,ref:b})})}));av.displayName=an;var aw=f.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:d,loop:g=!1,dir:h,currentTabStopId:i,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:k,onEntryFocus:l,preventScrollOnEntryFocus:m=!1,...n}=a,o=f.useRef(null),p=(0,af.useComposedRefs)(b,o),q=(0,ak.useDirection)(h),[r,s]=(0,aj.useControllableState)({prop:i,defaultProp:j??null,onChange:k,caller:an}),[t,u]=f.useState(!1),v=(0,ai.useCallbackRef)(l),w=ap(c),x=f.useRef(!1),[y,z]=f.useState(0);return f.useEffect(()=>{let a=o.current;if(a)return a.addEventListener(al,v),()=>a.removeEventListener(al,v)},[v]),(0,e.jsx)(at,{scope:c,orientation:d,dir:q,loop:g,currentTabStopId:r,onItemFocus:f.useCallback(a=>s(a),[s]),onItemShiftTab:f.useCallback(()=>u(!0),[]),onFocusableItemAdd:f.useCallback(()=>z(a=>a+1),[]),onFocusableItemRemove:f.useCallback(()=>z(a=>a-1),[]),children:(0,e.jsx)(ah.Primitive.div,{tabIndex:t||0===y?-1:0,"data-orientation":d,...n,ref:p,style:{outline:"none",...a.style},onMouseDown:(0,ac.composeEventHandlers)(a.onMouseDown,()=>{x.current=!0}),onFocus:(0,ac.composeEventHandlers)(a.onFocus,a=>{let b=!x.current;if(a.target===a.currentTarget&&b&&!t){let b=new CustomEvent(al,am);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=w().filter(a=>a.focusable);aA([a.find(a=>a.active),a.find(a=>a.id===r),...a].filter(Boolean).map(a=>a.ref.current),m)}}x.current=!1}),onBlur:(0,ac.composeEventHandlers)(a.onBlur,()=>u(!1))})})}),ax="RovingFocusGroupItem",ay=f.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:d=!0,active:g=!1,tabStopId:h,children:i,...j}=a,k=(0,ag.useId)(),l=h||k,m=au(ax,c),n=m.currentTabStopId===l,o=ap(c),{onFocusableItemAdd:p,onFocusableItemRemove:q,currentTabStopId:r}=m;return f.useEffect(()=>{if(d)return p(),()=>q()},[d,p,q]),(0,e.jsx)(ao.ItemSlot,{scope:c,id:l,focusable:d,active:g,children:(0,e.jsx)(ah.Primitive.span,{tabIndex:n?0:-1,"data-orientation":m.orientation,...j,ref:b,onMouseDown:(0,ac.composeEventHandlers)(a.onMouseDown,a=>{d?m.onItemFocus(l):a.preventDefault()}),onFocus:(0,ac.composeEventHandlers)(a.onFocus,()=>m.onItemFocus(l)),onKeyDown:(0,ac.composeEventHandlers)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void m.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return az[e]}(a,m.orientation,m.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=o().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=m.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>aA(c))}}),children:"function"==typeof i?i({isCurrentTabStop:n,hasTabStop:null!=r}):i})})});ay.displayName=ax;var az={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function aA(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var aB=a.i(91802),aC="Tabs",[aD,aE]=(0,ad.createContextScope)(aC,[as]),aF=as(),[aG,aH]=aD(aC),aI=f.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,onValueChange:f,defaultValue:g,orientation:h="horizontal",dir:i,activationMode:j="automatic",...k}=a,l=(0,ak.useDirection)(i),[m,n]=(0,aj.useControllableState)({prop:d,onChange:f,defaultProp:g??"",caller:aC});return(0,e.jsx)(aG,{scope:c,baseId:(0,ag.useId)(),value:m,onValueChange:n,orientation:h,dir:l,activationMode:j,children:(0,e.jsx)(ah.Primitive.div,{dir:l,"data-orientation":h,...k,ref:b})})});aI.displayName=aC;var aJ="TabsList",aK=f.forwardRef((a,b)=>{let{__scopeTabs:c,loop:d=!0,...f}=a,g=aH(aJ,c),h=aF(c);return(0,e.jsx)(av,{asChild:!0,...h,orientation:g.orientation,dir:g.dir,loop:d,children:(0,e.jsx)(ah.Primitive.div,{role:"tablist","aria-orientation":g.orientation,...f,ref:b})})});aK.displayName=aJ;var aL="TabsTrigger",aM=f.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,disabled:f=!1,...g}=a,h=aH(aL,c),i=aF(c),j=aO(h.baseId,d),k=aP(h.baseId,d),l=d===h.value;return(0,e.jsx)(ay,{asChild:!0,...i,focusable:!f,active:l,children:(0,e.jsx)(ah.Primitive.button,{type:"button",role:"tab","aria-selected":l,"aria-controls":k,"data-state":l?"active":"inactive","data-disabled":f?"":void 0,disabled:f,id:j,...g,ref:b,onMouseDown:(0,ac.composeEventHandlers)(a.onMouseDown,a=>{f||0!==a.button||!1!==a.ctrlKey?a.preventDefault():h.onValueChange(d)}),onKeyDown:(0,ac.composeEventHandlers)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&h.onValueChange(d)}),onFocus:(0,ac.composeEventHandlers)(a.onFocus,()=>{let a="manual"!==h.activationMode;l||f||!a||h.onValueChange(d)})})})});aM.displayName=aL;var aN="TabsContent";function aO(a,b){return`${a}-trigger-${b}`}function aP(a,b){return`${a}-content-${b}`}function aQ({className:a,...b}){return(0,e.jsx)(aI,{className:(0,U.cn)("flex flex-col gap-2",a),"data-slot":"tabs",...b})}function aR({className:a,...b}){return(0,e.jsx)(aK,{className:(0,U.cn)("inline-flex h-9 w-fit items-center justify-center rounded-lg bg-muted p-[3px] text-muted-foreground",a),"data-slot":"tabs-list",...b})}function aS({className:a,...b}){return(0,e.jsx)(aM,{className:(0,U.cn)("inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 whitespace-nowrap rounded-md border border-transparent px-2 py-1 font-medium text-foreground text-sm transition-[color,box-shadow] focus-visible:border-ring focus-visible:outline-1 focus-visible:outline-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:shadow-sm dark:text-muted-foreground dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 dark:data-[state=active]:text-foreground [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0",a),"data-slot":"tabs-trigger",...b})}f.forwardRef((a,b)=>{let{__scopeTabs:c,value:d,forceMount:g,children:h,...i}=a,j=aH(aN,c),k=aO(j.baseId,d),l=aP(j.baseId,d),m=d===j.value,n=f.useRef(m);return f.useEffect(()=>{let a=requestAnimationFrame(()=>n.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,e.jsx)(aB.Presence,{present:g||m,children:({present:c})=>(0,e.jsx)(ah.Primitive.div,{"data-state":m?"active":"inactive","data-orientation":j.orientation,role:"tabpanel","aria-labelledby":k,hidden:!c,id:l,tabIndex:0,...i,ref:b,style:{...a.style,animationDuration:n.current?"0s":void 0},children:c&&h})})}).displayName=aN;var aT=a.i(91300);function aU(){let[a,b]=(0,f.useState)("monthly");return(0,e.jsx)("div",{className:"mb-40 flex flex-col gap-16 text-center",children:(0,e.jsxs)("div",{className:"flex flex-col items-center justify-center gap-6",children:[(0,e.jsx)(aQ,{defaultValue:a,onValueChange:b,children:(0,e.jsxs)(aR,{className:"rounded-full bg-my-background p-4 py-6",children:[(0,e.jsx)(aS,{className:"rounded-full p-4 dark:data-[state=active]:bg-muted",value:"monthly",children:"Monthly"}),(0,e.jsxs)(aS,{className:"rounded-full p-4 dark:data-[state=active]:bg-muted",value:"yearly",children:["Yearly",(0,e.jsx)(W,{variant:"secondary",children:"20% off"})]})]})}),(0,e.jsx)("div",{className:"grid w-full grid-cols-1 gap-[15px] overflow-visible sm:grid-cols-2 sm:px-4 md:px-0 lg:grid-cols-3",children:aT.plans.map(b=>(0,e.jsxs)(Y,{className:(0,U.cn)("relative w-full text-left",b.popular&&"ring-2 ring-primary"),children:[b.popular&&(0,e.jsx)(W,{className:"-translate-x-1/2 -translate-y-1/2 absolute top-0 left-1/2 rounded-full",children:"Popular"}),(0,e.jsxs)(Z,{children:[(0,e.jsx)($,{className:"font-medium text-xl",children:b.name}),(0,e.jsxs)(_,{children:[(0,e.jsx)("p",{className:"mb-4",children:b.description}),"number"==typeof b.price[a]?(0,e.jsx)(P,{className:"font-medium text-foreground text-xl lg:text-3xl",format:{style:"currency",currency:"USD",maximumFractionDigits:0},suffix:"/seat",value:b.price[a]}):(0,e.jsxs)("span",{className:"font-medium text-foreground text-xl lg:text-2xl",children:[b.price[a],"."]})]})]}),(0,e.jsx)(aa,{className:"grid gap-2",children:b.features.map((a,b)=>(0,e.jsxs)("div",{className:"flex items-center gap-2 text-muted-foreground text-sm",children:[(0,e.jsx)(R,{className:"size-4 text-brand-500"}),a]},b))}),(0,e.jsx)(ab,{className:"mt-auto",children:(0,e.jsx)(X.Button,{className:(0,U.cn)("w-full rounded-full",b.popular?"bg-brand-600 hover:bg-brand-500":""),size:"lg",variant:b.popular?"default":"secondary",children:b.cta})})]},b.id))})]})})}}];

//# sourceMappingURL=features_pricing_pricing-cards_tsx_beda9b0a._.js.map