module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},54691,a=>{"use strict";function b(a){return a.isOnDemandRevalidate?"on-demand":a.isRevalidate?"stale":void 0}a.s(["getRevalidateReason",()=>b])},1220,a=>{"use strict";a.s(["NodeNextRequest",()=>k,"NodeNextResponse",()=>l],1220);var b,c=a.i(67982);class d extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new d}}class e extends Headers{constructor(a){super(),this.headers=new Proxy(a,{get(b,d,e){if("symbol"==typeof d)return c.ReflectAdapter.get(b,d,e);let f=d.toLowerCase(),g=Object.keys(a).find(a=>a.toLowerCase()===f);if(void 0!==g)return c.ReflectAdapter.get(b,g,e)},set(b,d,e,f){if("symbol"==typeof d)return c.ReflectAdapter.set(b,d,e,f);let g=d.toLowerCase(),h=Object.keys(a).find(a=>a.toLowerCase()===g);return c.ReflectAdapter.set(b,h??d,e,f)},has(b,d){if("symbol"==typeof d)return c.ReflectAdapter.has(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0!==f&&c.ReflectAdapter.has(b,f)},deleteProperty(b,d){if("symbol"==typeof d)return c.ReflectAdapter.deleteProperty(b,d);let e=d.toLowerCase(),f=Object.keys(a).find(a=>a.toLowerCase()===e);return void 0===f||c.ReflectAdapter.deleteProperty(b,f)}})}static seal(a){return new Proxy(a,{get(a,b,e){switch(b){case"append":case"delete":case"set":return d.callable;default:return c.ReflectAdapter.get(a,b,e)}}})}merge(a){return Array.isArray(a)?a.join(", "):a}static from(a){return a instanceof Headers?a:new e(a)}append(a,b){let c=this.headers[a];"string"==typeof c?this.headers[a]=[c,b]:Array.isArray(c)?c.push(b):this.headers[a]=b}delete(a){delete this.headers[a]}get(a){let b=this.headers[a];return void 0!==b?this.merge(b):null}has(a){return void 0!==this.headers[a]}set(a,b){this.headers[a]=b}forEach(a,b){for(let[c,d]of this.entries())a.call(b,d,c,this)}*entries(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase(),c=this.get(b);yield[b,c]}}*keys(){for(let a of Object.keys(this.headers)){let b=a.toLowerCase();yield b}}*values(){for(let a of Object.keys(this.headers)){let b=this.get(a);yield b}}[Symbol.iterator](){return this.entries()}}a.i(49442),a.i(6188),a.i(41949),Symbol("__next_preview_data");let f=Symbol("__prerender_bypass");var g=a.i(21230),h=a.i(25106);class i{constructor(a,b,c){this.method=a,this.url=b,this.body=c}get cookies(){var b;return this._cookies?this._cookies:this._cookies=(b=this.headers,function(){let{cookie:c}=b;if(!c)return{};let{parse:d}=a.r(72002);return d(Array.isArray(c)?c.join("; "):c)})()}}class j{constructor(a){this.destination=a}redirect(a,b){return this.setHeader("Location",a),this.statusCode=b,b===h.RedirectStatusCode.PermanentRedirect&&this.setHeader("Refresh",`0;url=${a}`),this}}class k extends i{static #a=b=g.NEXT_REQUEST_META;constructor(a){var c;super(a.method.toUpperCase(),a.url,a),this._req=a,this.headers=this._req.headers,this.fetchMetrics=null==(c=this._req)?void 0:c.fetchMetrics,this[b]=this._req[g.NEXT_REQUEST_META]||{},this.streaming=!1}get originalRequest(){return this._req[g.NEXT_REQUEST_META]=this[g.NEXT_REQUEST_META],this._req.url=this.url,this._req.cookies=this.cookies,this._req}set originalRequest(a){this._req=a}stream(){if(this.streaming)throw Object.defineProperty(Error("Invariant: NodeNextRequest.stream() can only be called once"),"__NEXT_ERROR_CODE",{value:"E467",enumerable:!1,configurable:!0});return this.streaming=!0,new ReadableStream({start:a=>{this._req.on("data",b=>{a.enqueue(new Uint8Array(b))}),this._req.on("end",()=>{a.close()}),this._req.on("error",b=>{a.error(b)})}})}}class l extends j{get originalResponse(){return f in this&&(this._res[f]=this[f]),this._res}constructor(a){super(a),this._res=a,this.textBody=void 0}get sent(){return this._res.finished||this._res.headersSent}get statusCode(){return this._res.statusCode}set statusCode(a){this._res.statusCode=a}get statusMessage(){return this._res.statusMessage}set statusMessage(a){this._res.statusMessage=a}setHeader(a,b){return this._res.setHeader(a,b),this}removeHeader(a){return this._res.removeHeader(a),this}getHeaderValues(a){let b=this._res.getHeader(a);if(void 0!==b)return(Array.isArray(b)?b:[b]).map(a=>a.toString())}hasHeader(a){return this._res.hasHeader(a)}getHeader(a){let b=this.getHeaderValues(a);return Array.isArray(b)?b.join(","):void 0}getHeaders(){return this._res.getHeaders()}appendHeader(a,b){let c=this.getHeaderValues(a)??[];return c.includes(b)||this._res.setHeader(a,[...c,b]),this}body(a){return this.textBody=a,this}send(){this._res.end(this.textBody)}onClose(a){this.originalResponse.on("close",a)}}},99403,a=>{"use strict";a.s(["normalizeAppPath",()=>c],99403);var b=a.i(42427);function c(a){var c;return(c=a.split("/").reduce((a,c,d,e)=>!c||(0,b.isGroupSegment)(c)||"@"===c[0]||("page"===c||"route"===c)&&d===e.length-1?a:a+"/"+c,"")).startsWith("/")?c:"/"+c}},20883,a=>{"use strict";a.s(["getCacheControlHeader",()=>c]);var b=a.i(49442);function c({revalidate:a,expire:c}){let d="number"==typeof a&&void 0!==c&&a<c?`, stale-while-revalidate=${c-a}`:"";return 0===a?"private, no-cache, no-store, max-age=0, must-revalidate":"number"==typeof a?`s-maxage=${a}${d}`:`s-maxage=${b.CACHE_ONE_YEAR}${d}`}},98726,a=>{a.n(a.i(26949))},14030,a=>{a.n(a.i(69486))},28566,a=>{"use strict";a.s(["Accordion",()=>c,"AccordionContent",()=>d,"AccordionItem",()=>e,"AccordionTrigger",()=>f]);var b=a.i(62e3);let c=(0,b.registerClientReference)(function(){throw Error("Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/accordion.tsx <module evaluation>","Accordion"),d=(0,b.registerClientReference)(function(){throw Error("Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/accordion.tsx <module evaluation>","AccordionContent"),e=(0,b.registerClientReference)(function(){throw Error("Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/accordion.tsx <module evaluation>","AccordionItem"),f=(0,b.registerClientReference)(function(){throw Error("Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/accordion.tsx <module evaluation>","AccordionTrigger")},18368,a=>{"use strict";a.s(["Accordion",()=>c,"AccordionContent",()=>d,"AccordionItem",()=>e,"AccordionTrigger",()=>f]);var b=a.i(62e3);let c=(0,b.registerClientReference)(function(){throw Error("Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/accordion.tsx","Accordion"),d=(0,b.registerClientReference)(function(){throw Error("Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/accordion.tsx","AccordionContent"),e=(0,b.registerClientReference)(function(){throw Error("Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/accordion.tsx","AccordionItem"),f=(0,b.registerClientReference)(function(){throw Error("Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/accordion.tsx","AccordionTrigger")},11555,a=>{"use strict";a.i(28566);var b=a.i(18368);a.n(b)},52110,a=>{"use strict";a.s(["default",()=>e]);var b=a.i(38470),c=a.i(11555);let d=[{question:"Does Highlight work on my device?",answer:"We are the only AI assistant that works across both Windows and Mac. Our team is fully committed to developing a product that meets users wherever they're at."},{question:"How is my data handled?",answer:"We don't see your data. Period. The only time your data leaves your computer is when you attach it to a cloud LLM query or if you had enabled task detection, and then only the cloud LLM - GPT4o, Claude, etc - is able to see your information."},{question:"Is Highlight SOC-2 compliant?",answer:"We are undergoing the SOC-2 process and expect to complete certification by end of 2025. In the meantime, we can supply and complete any security questionnaires for enterprises interested in working with us, and have published our policies on trust and safety here."},{question:"How is this different from ChatGPT?",answer:"Highlight is ChatGPT-level intelligence, but with the ability to understand what you're looking at on your screen or have said or heard on your laptop. Think of it like ChatGPT, but without the need to ever explain yourself (plus a bunch of other cool features)."},{question:"Do you have a mobile app?",answer:"Not yet, but we're working on it!"}];function e(){return(0,b.jsx)("section",{className:"mb-40",children:(0,b.jsxs)("div",{className:"mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl",children:[(0,b.jsx)("h2",{className:"mx-auto mb-14 max-w-[320px] whitespace-pre-wrap text-center font-aeonik-bold font-medium text-3xl leading-tight sm:max-w-2xl sm:text-4xl md:max-w-none md:text-4xl lg:text-5xl",id:"faq-heading",children:"Common Questions"}),(0,b.jsx)("div",{className:"h-full pb-8",children:(0,b.jsx)(c.Accordion,{collapsible:!0,type:"single",children:d.map((a,d)=>(0,b.jsxs)(c.AccordionItem,{value:`item-${d}`,children:[(0,b.jsx)(c.AccordionTrigger,{className:"cursor-pointer font-medium text-base text-primary hover:text-primary/80 hover:no-underline",children:a.question}),(0,b.jsx)(c.AccordionContent,{className:"font-aeonik text-lg text-muted-foreground",children:a.answer})]},d))})})]})})}},6659,a=>{"use strict";a.s(["401553368a882499aede4e05ebfdb3ca7882231805",()=>c.subscribe],6659),a.s([],82114);var b=a.i(17426);a.i(82114);var c=b},87893,a=>{a.v(b=>Promise.all(["server/chunks/ssr/b8cd5_next_dist_compiled_react-dom_server_node_6dd8ed06.js","server/chunks/ssr/[root-of-the-server]__f24dc009._.js"].map(b=>a.l(b))).then(()=>b(32709)))}];

//# sourceMappingURL=%5Broot-of-the-server%5D__6e669fbf._.js.map