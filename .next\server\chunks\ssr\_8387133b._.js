module.exports=[32584,2796,a=>{"use strict";a.s(["Check",()=>c],32584);var b=a.i(51827);let c=(0,b.default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);a.s(["X",()=>d],2796);let d=(0,b.default)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},1650,a=>{"use strict";a.s(["default",()=>h]);var b=a.i(69720),c=a.i(32584),d=a.i(2796),e=a.i(91300),f=a.i(97895);let g=["Highlight","ChatGPT","Claude","Raycast","Notion"];function h(){return(0,b.jsx)("div",{className:"no-scrollbar hidden w-full overflow-x-auto md:block",children:(0,b.jsxs)("table",{className:"w-full min-w-[700px] border-collapse border-spacing-0",style:{borderSpacing:"0px"},children:[(0,b.jsx)("thead",{children:(0,b.jsxs)("tr",{children:[(0,b.jsx)("th",{className:"sticky top-0 left-0 z-40 w-[200px] min-w-[200px] bg-[#090909] text-left font-normal text-sm md:w-[260px] md:min-w-[260px] md:text-base",scope:"col",children:(0,b.jsx)("div",{className:"flex flex-row items-center gap-3 rounded-t-xl py-2",children:(0,b.jsx)("span",{className:"font-medium font-sans text-lg text-white",children:"Features"})})}),g.map((a,c)=>(0,b.jsx)("th",{className:"sticky top-0 left-[200px] z-30 w-[120px] min-w-[120px] bg-[#090909] px-0 text-center font-normal text-sm md:left-auto md:text-base",children:(0,b.jsx)("div",{className:(0,f.cn)("flex flex-col items-center font-medium font-sans",0===c?"rounded-t-xl py-2 text-primary":"p-4 text-muted-foreground"),children:(0,b.jsx)("div",{className:"flex items-center gap-1.5",children:(0,b.jsx)("a",{className:"flex items-center gap-1.5",href:"/",children:(0,b.jsx)("span",{className:"pb-[1.5px] font-medium text-lg",children:a})})})})},a))]})}),(0,b.jsx)("tbody",{children:e.featuresCompare.map((a,e)=>(0,b.jsxs)("tr",{className:"border-0.5 border-muted/60 border-b",children:[(0,b.jsx)("td",{className:"sticky left-0 z-30 w-[200px] min-w-[200px] bg-[#090909] py-3 pr-2 align-middle md:w-[260px] md:min-w-[260px] md:pr-8",children:(0,b.jsx)("span",{className:"text-nowrap font-medium text-base text-muted-foreground leading-tight",children:a.feature})}),g.map((e,g)=>(0,b.jsx)("td",{className:(0,f.cn)("w-[120px] min-w-[120px] p-4 text-center align-middle",0===g&&"sticky left-[200px] z-20 bg-[#090909] md:static"),children:(0,b.jsx)("div",{className:(0,f.cn)("mx-auto flex size-6 items-center justify-center rounded-lg text-black md:size-7",a[e]?"bg-brand-400":"bg-brand-100"),children:a[e]?(0,b.jsx)(c.Check,{className:"mx-auto size-4 text-black"}):(0,b.jsx)(d.X,{className:"mx-auto size-4 text-brand-600"})})},e))]},e))})]})})}},78422,a=>{"use strict";a.s(["default",()=>h]);var b=a.i(69720),c=a.i(32584),d=a.i(2796),e=a.i(91300),f=a.i(97895);let g=["Highlight","ChatGPT","Claude","Raycast","Notion"];function h(){return(0,b.jsx)("div",{className:"no-scrollbar block w-full overflow-x-auto md:hidden",children:(0,b.jsxs)("table",{className:"w-max min-w-[700px] border-collapse border-spacing-0",style:{borderSpacing:"0px"},children:[(0,b.jsx)("thead",{children:(0,b.jsxs)("tr",{children:[(0,b.jsx)("th",{className:"sticky top-0 left-0 z-50 w-[120px] min-w-[120px] bg-[#090909] text-left"}),e.featuresCompare.map((a,c)=>(0,b.jsx)("th",{className:"sticky top-0 z-40 w-[120px] min-w-[120px] bg-[#090909] text-center font-normal text-muted-foreground text-sm",children:(0,b.jsx)("span",{className:"block px-3 py-2 text-base leading-tight",children:a.feature})},c))]})}),(0,b.jsx)("tbody",{children:g.map((a,g)=>(0,b.jsxs)("tr",{className:"border-0.5 border-muted/60 border-b",children:[(0,b.jsx)("td",{className:"sticky left-0 z-40 w-[120px] min-w-[120px] bg-[#090909] px-3 py-3 pr-4 align-middle",children:(0,b.jsx)("span",{className:"text-nowrap font-medium font-sans text-base text-primary leading-tight",children:a})}),e.featuresCompare.map((e,g)=>(0,b.jsx)("td",{className:(0,f.cn)("w-[120px] min-w-[120px] p-4 text-center align-middle"),children:(0,b.jsx)("div",{className:(0,f.cn)("mx-auto flex size-6 items-center justify-center rounded-lg text-black md:size-7",e[a]?"bg-brand-400":"bg-brand-100"),children:e[a]?(0,b.jsx)(c.Check,{className:"mx-auto size-4 text-black"}):(0,b.jsx)(d.X,{className:"mx-auto size-4 text-brand-600"})})},g))]},g))})]})})}},85806,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{default:function(){return i},getImageProps:function(){return h}});let d=a.r(63652),e=a.r(48552),f=a.r(19140),g=d._(a.r(20202));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},70342,(a,b,c)=>{b.exports=a.r(85806)},88787,a=>{"use strict";a.s(["default",()=>z],88787);var b=a.i(69720),c=a.i(70342),d=a.i(29611),e=d,f=a.i(83312);let g=(a,b)=>a===b?.tagName?.toUpperCase?.(),h=a=>g("DIV",a)||g("SPAN",a),i=a=>g("IMG",a),j=a=>a.complete&&0!==a.naturalHeight,k=a=>g("SVG",a),l=({height:a,offset:b,width:c})=>Math.min((window.innerWidth-2*b)/c,(window.innerHeight-2*b)/a),m=({containerHeight:a,containerWidth:b,hasScalableSrc:c,offset:d,targetHeight:e,targetWidth:f})=>a&&b?!c&&e&&f?(({containerHeight:a,containerWidth:b,offset:c,targetHeight:d,targetWidth:e})=>{let f=l({height:d,offset:c,width:e}),g=e>d?e/b:d/a;return f>1?g:f*g})({containerHeight:a,containerWidth:b,offset:d,targetHeight:e,targetWidth:f}):l({height:a,offset:d,width:b}):1,n=/url(?:\(['"]?)(.*?)(?:['"]?\))/,o=a=>{if(a){if(i(a))return a.currentSrc;else if(h(a)){let b=window.getComputedStyle(a).backgroundImage;if(b)return n.exec(b)?.[1]}}},p=({position:a,relativeNum:b})=>{let c=parseFloat(a);return a.endsWith("%")?b*c/100:c},q=/\.svg$/i,r=a=>{if(!a)return{};if(!k(a))return{height:a.offsetHeight,left:a.offsetLeft,width:a.offsetWidth,top:a.offsetTop};{let b=a.parentElement,c=a.getBoundingClientRect();if(!b)return{height:c.height,left:c.left,width:c.width,top:c.top};{let a=b.getBoundingClientRect();return{height:c.height,left:a.left-c.left,top:a.top-c.top,width:c.width}}}},s=["img","svg",'[role="img"]',"[data-zoom]"].map(a=>`${a}:not([aria-hidden="true"])`).join(","),t={overflow:"",width:""};function u(a){return e.default.createElement(v,{...a})}class v extends e.default.Component{constructor(){super(...arguments),this.state={id:"",isZoomImgLoaded:!1,loadedImgEl:void 0,modalState:"UNLOADED",shouldRefresh:!1,styleGhost:{}},this.refContent=e.default.createRef(),this.refDialog=e.default.createRef(),this.refModalContent=e.default.createRef(),this.refModalImg=e.default.createRef(),this.refWrap=e.default.createRef(),this.imgEl=null,this.isScaling=!1,this.prevBodyAttrs=t,this.styleModalImg={},this.handleModalStateChange=a=>{let{modalState:b}=this.state;"LOADING"!==a&&"LOADING"===b?(this.loadZoomImg(),window.addEventListener("resize",this.handleResize,{passive:!0}),window.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),window.addEventListener("touchmove",this.handleTouchMove,{passive:!0}),window.addEventListener("touchend",this.handleTouchEnd,{passive:!0}),window.addEventListener("touchcancel",this.handleTouchCancel,{passive:!0}),document.addEventListener("keydown",this.handleKeyDown,!0)):"LOADED"!==a&&"LOADED"===b?window.addEventListener("wheel",this.handleWheel,{passive:!0}):"UNLOADING"!==a&&"UNLOADING"===b?(this.ensureImgTransitionEnd(),window.removeEventListener("wheel",this.handleWheel),window.removeEventListener("touchstart",this.handleTouchStart),window.removeEventListener("touchmove",this.handleTouchMove),window.removeEventListener("touchend",this.handleTouchEnd),window.removeEventListener("touchcancel",this.handleTouchCancel),document.removeEventListener("keydown",this.handleKeyDown,!0)):"UNLOADED"!==a&&"UNLOADED"===b&&(this.bodyScrollEnable(),window.removeEventListener("resize",this.handleResize),this.refModalImg.current?.removeEventListener?.("transitionend",this.handleImgTransitionEnd),this.refDialog.current?.close?.())},this.getDialogContainer=()=>{let a=document.querySelector("[data-rmiz-portal]");return null==a&&((a=document.createElement("div")).setAttribute("data-rmiz-portal",""),document.body.appendChild(a)),a},this.setId=()=>{let a=()=>Math.random().toString(16).slice(-4);this.setState({id:a()+a()+a()})},this.setAndTrackImg=()=>{let a=this.refContent.current;a&&(this.imgEl=a.querySelector(s),this.imgEl?(this.contentNotFoundChangeObserver?.disconnect?.(),this.imgEl.addEventListener("load",this.handleImgLoad),this.imgEl.addEventListener("click",this.handleZoom),this.state.loadedImgEl||this.handleImgLoad(),this.imgElResizeObserver=new ResizeObserver(a=>{let b=a[0];b?.target&&(this.imgEl=b.target,this.setState({styleGhost:r(this.imgEl)}))}),this.imgElResizeObserver.observe(this.imgEl),this.contentChangeObserver||(this.contentChangeObserver=new MutationObserver(()=>{this.setState({styleGhost:r(this.imgEl)})}),this.contentChangeObserver.observe(a,{attributes:!0,childList:!0,subtree:!0}))):this.contentNotFoundChangeObserver||(this.contentNotFoundChangeObserver=new MutationObserver(this.setAndTrackImg),this.contentNotFoundChangeObserver.observe(a,{childList:!0,subtree:!0})))},this.handleIfZoomChanged=a=>{let{isZoomed:b}=this.props;!a&&b?this.zoom():a&&!b&&this.unzoom()},this.handleImgLoad=()=>{let a=o(this.imgEl);if(!a)return;let b=new Image;i(this.imgEl)&&(b.sizes=this.imgEl.sizes,b.srcset=this.imgEl.srcset,b.crossOrigin=this.imgEl.crossOrigin),b.src=a;let c=()=>{this.setState({loadedImgEl:b,styleGhost:r(this.imgEl)})};b.decode().then(c).catch(()=>{if(j(b))return void c();b.onload=c})},this.handleZoom=()=>{!this.props.isDisabled&&this.hasImage()&&this.props.onZoomChange?.(!0)},this.handleUnzoom=()=>{this.props.isDisabled||this.props.onZoomChange?.(!1)},this.handleBtnUnzoomClick=a=>{a.preventDefault(),a.stopPropagation(),this.handleUnzoom()},this.handleDialogCancel=a=>{a.preventDefault()},this.handleDialogClick=a=>{(a.target===this.refModalContent.current||a.target===this.refModalImg.current)&&(a.stopPropagation(),this.handleUnzoom())},this.handleDialogClose=a=>{a.stopPropagation(),this.handleUnzoom()},this.handleKeyDown=a=>{("Escape"===a.key||27===a.keyCode)&&(a.preventDefault(),a.stopPropagation(),this.handleUnzoom())},this.handleWheel=a=>{a.ctrlKey||(a.stopPropagation(),queueMicrotask(()=>{this.handleUnzoom()}))},this.handleTouchStart=a=>{if(a.touches.length>1){this.isScaling=!0;return}1===a.changedTouches.length&&a.changedTouches[0]&&(this.touchYStart=a.changedTouches[0].screenY)},this.handleTouchMove=a=>{let b=window.visualViewport?.scale??1;this.props.canSwipeToUnzoom&&!this.isScaling&&b<=1&&null!=this.touchYStart&&a.changedTouches[0]&&(this.touchYEnd=a.changedTouches[0].screenY,Math.abs(Math.max(this.touchYStart,this.touchYEnd)-Math.min(this.touchYStart,this.touchYEnd))>this.props.swipeToUnzoomThreshold&&(this.touchYStart=void 0,this.touchYEnd=void 0,this.handleUnzoom()))},this.handleTouchEnd=()=>{this.isScaling=!1,this.touchYStart=void 0,this.touchYEnd=void 0},this.handleTouchCancel=()=>{this.isScaling=!1,this.touchYStart=void 0,this.touchYEnd=void 0},this.handleResize=()=>{this.setState({shouldRefresh:!0})},this.hasImage=()=>this.imgEl&&(this.state.loadedImgEl||k(this.imgEl))&&"none"!==window.getComputedStyle(this.imgEl).display,this.zoom=()=>{this.bodyScrollDisable(),this.refDialog.current?.showModal?.(),this.refModalImg.current?.addEventListener?.("transitionend",this.handleImgTransitionEnd),this.setState({modalState:"LOADING"})},this.unzoom=()=>{this.setState({modalState:"UNLOADING"})},this.handleImgTransitionEnd=()=>{clearTimeout(this.timeoutTransitionEnd),"LOADING"===this.state.modalState?this.setState({modalState:"LOADED"}):"UNLOADING"===this.state.modalState&&this.setState({shouldRefresh:!1,modalState:"UNLOADED"})},this.ensureImgTransitionEnd=()=>{if(this.refModalImg.current){let a=window.getComputedStyle(this.refModalImg.current).transitionDuration,b=parseFloat(a);if(b){let c=b*(a.endsWith("ms")?1:1e3)+50;this.timeoutTransitionEnd=setTimeout(this.handleImgTransitionEnd,c)}}},this.bodyScrollDisable=()=>{this.prevBodyAttrs={overflow:document.body.style.overflow,width:document.body.style.width};let a=document.body.clientWidth;document.body.style.overflow="hidden",document.body.style.width=`${a}px`},this.bodyScrollEnable=()=>{document.body.style.width=this.prevBodyAttrs.width,document.body.style.overflow=this.prevBodyAttrs.overflow,this.prevBodyAttrs=t},this.loadZoomImg=()=>{let{props:{zoomImg:a}}=this,b=a?.src;if(b){let c=new Image;c.sizes=a?.sizes??"",c.srcset=a?.srcSet??"",c.crossOrigin=a?.crossOrigin??void 0,c.src=b;let d=()=>{this.setState({isZoomImgLoaded:!0})};c.decode().then(d).catch(()=>{if(j(c))return void d();c.onload=d})}},this.UNSAFE_handleSvg=()=>{let{imgEl:a,refModalImg:b,styleModalImg:c}=this;if(k(a)){let d=a.cloneNode(!0);(a=>{let b="-zoom",c=["clip-path","fill","mask","marker-start","marker-mid","marker-end"],d=new Map;if(a.hasAttribute("id")){let c=a.id,e=c+b;d.set(c,e),a.id=e}a.querySelectorAll("[id]").forEach(a=>{let c=a.id,e=c+b;d.set(c,e),a.id=e}),d.forEach((b,d)=>{let e=`url(#${d})`,f=`url(#${b})`,g=c.map(a=>`[${a}="${e}"]`).join(", ");a.querySelectorAll(g).forEach(a=>{c.forEach(b=>{a.getAttribute(b)===e&&a.setAttribute(b,f)})})}),a.querySelectorAll("style").forEach(a=>{d.forEach((b,c)=>{a.textContent&&(a.textContent=a.textContent.replaceAll(`#${c}`,`#${b}`))})})})(d),d.style.width=`${c.width||0}px`,d.style.height=`${c.height||0}px`,d.addEventListener("click",this.handleUnzoom),b.current?.firstChild?.remove?.(),b.current?.appendChild?.(d)}}}render(){let{handleBtnUnzoomClick:a,handleDialogCancel:b,handleDialogClick:c,handleDialogClose:d,handleUnzoom:g,handleZoom:j,imgEl:l,props:{a11yNameButtonUnzoom:n,a11yNameButtonZoom:r,children:s,classDialog:t,IconUnzoom:u,IconZoom:v,isZoomed:w,wrapElement:x,ZoomContent:y,zoomImg:z,zoomMargin:A},refContent:B,refDialog:C,refModalContent:D,refModalImg:E,refWrap:F,state:{id:G,isZoomImgLoaded:H,loadedImgEl:I,modalState:J,shouldRefresh:K,styleGhost:L}}=this,M=`rmiz-modal-${G}`,N=`rmiz-modal-img-${G}`,O=h(l),P=i(l),Q=k(l),R=(a=>{if(a)if(i(a))return a.alt??void 0;else return a.getAttribute("aria-label")??void 0})(l),S=o(l),T=P?l.sizes:void 0,U=P?l.srcset:void 0,V=P?l.crossOrigin:void 0,W=!!z?.src,X=this.hasImage(),Y=R?`${r}: ${R}`:r,Z="LOADING"===J||"LOADED"===J,$=X?"found":"not-found",_="UNLOADED"===J||"UNLOADING"===J?"hidden":"visible";this.styleModalImg=X?(({hasZoomImg:a,imgSrc:b,isSvg:c,isZoomed:d,loadedImgEl:e,offset:f,shouldRefresh:g,targetEl:i})=>{let j=c||b?.slice?.(0,18)==="data:image/svg+xml"||a||!!(b&&q.test(b)),k=i.getBoundingClientRect(),l=window.getComputedStyle(i),n=null!=e&&h(i),o=null!=e&&!n,r=(({containerHeight:a,containerLeft:b,containerTop:c,containerWidth:d,hasScalableSrc:e,offset:f,targetHeight:g,targetWidth:h})=>{let i=m({containerHeight:a,containerWidth:d,hasScalableSrc:e,offset:f,targetHeight:g,targetWidth:h});return{top:c,left:b,width:d*i,height:a*i,transform:`translate(0,0) scale(${1/i})`}})({containerHeight:k.height,containerLeft:k.left,containerTop:k.top,containerWidth:k.width,hasScalableSrc:j,offset:f,targetHeight:e?.naturalHeight||k.height,targetWidth:e?.naturalWidth||k.width}),s=Object.assign({},r,o?(({containerHeight:a,containerLeft:b,containerTop:c,containerWidth:d,hasScalableSrc:e,objectFit:f,objectPosition:g,offset:h,targetHeight:i,targetWidth:j})=>{if("scale-down"===f&&(f=j<=d&&i<=a?"none":"contain"),"cover"===f||"contain"===f){let k=d/j,l=a/i,n="cover"===f?Math.max(k,l):Math.min(k,l),[o="50%",q="50%"]=g.split(" "),r=p({position:o,relativeNum:d-j*n}),s=p({position:q,relativeNum:a-i*n}),t=m({containerHeight:i*n,containerWidth:j*n,hasScalableSrc:e,offset:h,targetHeight:i,targetWidth:j});return{top:c+s,left:b+r,width:j*n*t,height:i*n*t,transform:`translate(0,0) scale(${1/t})`}}if("none"===f){let[f="50%",k="50%"]=g.split(" "),l=p({position:f,relativeNum:d-j}),n=p({position:k,relativeNum:a-i}),o=m({containerHeight:i,containerWidth:j,hasScalableSrc:e,offset:h,targetHeight:i,targetWidth:j});return{top:c+n,left:b+l,width:j*o,height:i*o,transform:`translate(0,0) scale(${1/o})`}}if("fill"!==f)return{};{let b=Math.max(d/j,a/i),c=m({containerHeight:i*b,containerWidth:j*b,hasScalableSrc:e,offset:h,targetHeight:i,targetWidth:j});return{width:d*c,height:a*c,transform:`translate(0,0) scale(${1/c})`}}})({containerHeight:k.height,containerLeft:k.left,containerTop:k.top,containerWidth:k.width,hasScalableSrc:j,objectFit:l.objectFit,objectPosition:l.objectPosition,offset:f,targetHeight:e?.naturalHeight||k.height,targetWidth:e?.naturalWidth||k.width}):void 0,n?(({backgroundPosition:a,backgroundSize:b,containerHeight:c,containerLeft:d,containerTop:e,containerWidth:f,hasScalableSrc:g,offset:h,targetHeight:i,targetWidth:j})=>{if("cover"===b||"contain"===b){let k=f/j,l=c/i,n="cover"===b?Math.max(k,l):Math.min(k,l),[o="50%",q="50%"]=a.split(" "),r=p({position:o,relativeNum:f-j*n}),s=p({position:q,relativeNum:c-i*n}),t=m({containerHeight:i*n,containerWidth:j*n,hasScalableSrc:g,offset:h,targetHeight:i,targetWidth:j});return{top:e+s,left:d+r,width:j*n*t,height:i*n*t,transform:`translate(0,0) scale(${1/t})`}}if("auto"===b){let[b="50%",k="50%"]=a.split(" "),l=p({position:b,relativeNum:f-j}),n=p({position:k,relativeNum:c-i}),o=m({containerHeight:i,containerWidth:j,hasScalableSrc:g,offset:h,targetHeight:i,targetWidth:j});return{top:e+n,left:d+l,width:j*o,height:i*o,transform:`translate(0,0) scale(${1/o})`}}{let[k="50%",l="50%"]=b.split(" "),n=p({position:k,relativeNum:f}),o=Math.min(n/j,p({position:l,relativeNum:c})/i),[q="50%",r="50%"]=a.split(" "),s=p({position:q,relativeNum:f-j*o}),t=p({position:r,relativeNum:c-i*o}),u=m({containerHeight:i*o,containerWidth:j*o,hasScalableSrc:g,offset:h,targetHeight:i,targetWidth:j});return{top:e+t,left:d+s,width:j*o*u,height:i*o*u,transform:`translate(0,0) scale(${1/u})`}}})({backgroundPosition:l.backgroundPosition,backgroundSize:l.backgroundSize,containerHeight:k.height,containerLeft:k.left,containerTop:k.top,containerWidth:k.width,hasScalableSrc:j,offset:f,targetHeight:e?.naturalHeight||k.height,targetWidth:e?.naturalWidth||k.width}):void 0);if(d){let a=window.innerWidth/2,b=window.innerHeight/2,c=parseFloat(String(s.left||0))+parseFloat(String(s.width||0))/2,d=parseFloat(String(s.top||0))+parseFloat(String(s.height||0))/2;g&&(s.transitionDuration="0.01ms"),s.transform=`translate(${a-c}px,${b-d}px) scale(1)`}return s})({hasZoomImg:W,imgSrc:S,isSvg:Q,isZoomed:w&&Z,loadedImgEl:I,offset:A,shouldRefresh:K,targetEl:l}):{};let aa=null;if(X){let b=P||O?e.default.createElement("img",{alt:R,crossOrigin:V,sizes:T,src:S,srcSet:U,...H&&"LOADED"===J?z:{},"data-rmiz-modal-img":"",height:this.styleModalImg.height||void 0,id:N,ref:E,style:this.styleModalImg,width:this.styleModalImg.width||void 0}):Q?e.default.createElement("div",{"data-rmiz-modal-img":!0,ref:E,style:this.styleModalImg}):null,c=e.default.createElement("button",{"aria-label":n,"data-rmiz-btn-unzoom":"",onClick:a,type:"button"},e.default.createElement(u,null));aa=y?e.default.createElement(y,{buttonUnzoom:c,modalState:J,img:b,isZoomImgLoaded:H,onUnzoom:g}):e.default.createElement(e.default.Fragment,null,b,c)}return e.default.createElement(x,{"aria-owns":M,"data-rmiz":"",ref:F},e.default.createElement(x,{"data-rmiz-content":$,ref:B,style:{visibility:"UNLOADED"===J?"visible":"hidden"}},s),X&&e.default.createElement(x,{"data-rmiz-ghost":"",style:L},e.default.createElement("button",{"aria-label":Y,"data-rmiz-btn-zoom":"",onClick:j,type:"button"},e.default.createElement(v,null))),X&&f.default.createPortal(e.default.createElement("dialog",{"aria-labelledby":N,"aria-modal":"true",className:t,"data-rmiz-modal":"",id:M,onClick:c,onClose:d,onCancel:b,ref:C,role:"dialog"},e.default.createElement("div",{"data-rmiz-modal-overlay":_}),e.default.createElement("div",{"data-rmiz-modal-content":"",ref:D},aa)),this.getDialogContainer()))}componentDidMount(){this.setId(),this.setAndTrackImg(),this.handleImgLoad(),this.UNSAFE_handleSvg()}componentWillUnmount(){"UNLOADED"!==this.state.modalState&&this.bodyScrollEnable(),this.contentChangeObserver?.disconnect?.(),this.contentNotFoundChangeObserver?.disconnect?.(),this.imgElResizeObserver?.disconnect?.(),this.imgEl?.removeEventListener?.("load",this.handleImgLoad),this.imgEl?.removeEventListener?.("click",this.handleZoom),this.refModalImg.current?.removeEventListener?.("transitionend",this.handleImgTransitionEnd),window.removeEventListener("wheel",this.handleWheel),window.removeEventListener("touchstart",this.handleTouchStart),window.removeEventListener("touchmove",this.handleTouchMove),window.removeEventListener("touchend",this.handleTouchEnd),window.removeEventListener("touchcancel",this.handleTouchCancel),window.removeEventListener("resize",this.handleResize),document.removeEventListener("keydown",this.handleKeyDown,!0)}componentDidUpdate(a,b){this.handleModalStateChange(b.modalState),this.UNSAFE_handleSvg(),this.handleIfZoomChanged(a.isZoomed)}}function w(a){let[b,c]=e.default.useState(!1);return e.default.createElement(u,{...a,isZoomed:b,onZoomChange:c})}v.defaultProps={a11yNameButtonUnzoom:"Minimize image",a11yNameButtonZoom:"Expand image",canSwipeToUnzoom:!0,IconUnzoom:function(){return e.default.createElement("svg",{"aria-hidden":"true","data-rmiz-btn-unzoom-icon":!0,fill:"currentColor",focusable:"false",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},e.default.createElement("path",{d:"M 14.144531 1.148438 L 9 6.292969 L 9 3 L 8 3 L 8 8 L 13 8 L 13 7 L 9.707031 7 L 14.855469 1.851563 Z M 8 8 L 3 8 L 3 9 L 6.292969 9 L 1.148438 14.144531 L 1.851563 14.855469 L 7 9.707031 L 7 13 L 8 13 Z"}))},IconZoom:function(){return e.default.createElement("svg",{"aria-hidden":"true","data-rmiz-btn-zoom-icon":!0,fill:"currentColor",focusable:"false",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},e.default.createElement("path",{d:"M 9 1 L 9 2 L 12.292969 2 L 2 12.292969 L 2 9 L 1 9 L 1 14 L 6 14 L 6 13 L 2.707031 13 L 13 2.707031 L 13 6 L 14 6 L 14 1 Z"}))},isDisabled:!1,swipeToUnzoomThreshold:10,wrapElement:"div",zoomMargin:0};var x=a.i(97895);let y=({className:a,backdropClassName:c,...d})=>(0,b.jsx)("div",{className:(0,x.cn)("relative","[&_[data-rmiz-ghost]]:pointer-events-none [&_[data-rmiz-ghost]]:absolute","[&_[data-rmiz-btn-zoom]]:m-0 [&_[data-rmiz-btn-zoom]]:size-10 [&_[data-rmiz-btn-zoom]]:touch-manipulation [&_[data-rmiz-btn-zoom]]:appearance-none [&_[data-rmiz-btn-zoom]]:rounded-[50%] [&_[data-rmiz-btn-zoom]]:border-none [&_[data-rmiz-btn-zoom]]:bg-foreground/70 [&_[data-rmiz-btn-zoom]]:p-2 [&_[data-rmiz-btn-zoom]]:text-background [&_[data-rmiz-btn-zoom]]:outline-offset-2","[&_[data-rmiz-btn-unzoom]]:m-0 [&_[data-rmiz-btn-unzoom]]:size-10 [&_[data-rmiz-btn-unzoom]]:touch-manipulation [&_[data-rmiz-btn-unzoom]]:appearance-none [&_[data-rmiz-btn-unzoom]]:rounded-[50%] [&_[data-rmiz-btn-unzoom]]:border-none [&_[data-rmiz-btn-unzoom]]:bg-foreground/70 [&_[data-rmiz-btn-unzoom]]:p-2 [&_[data-rmiz-btn-unzoom]]:text-background [&_[data-rmiz-btn-unzoom]]:outline-offset-2","[&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:pointer-events-none [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:absolute [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:size-px [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:overflow-hidden [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:whitespace-nowrap [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:[clip-path:inset(50%)] [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:[clip:rect(0_0_0_0)]","[&_[data-rmiz-btn-zoom]]:absolute [&_[data-rmiz-btn-zoom]]:top-2.5 [&_[data-rmiz-btn-zoom]]:right-2.5 [&_[data-rmiz-btn-zoom]]:bottom-auto [&_[data-rmiz-btn-zoom]]:left-auto [&_[data-rmiz-btn-zoom]]:cursor-zoom-in","[&_[data-rmiz-btn-unzoom]]:absolute [&_[data-rmiz-btn-unzoom]]:top-5 [&_[data-rmiz-btn-unzoom]]:right-5 [&_[data-rmiz-btn-unzoom]]:bottom-auto [&_[data-rmiz-btn-unzoom]]:left-auto [&_[data-rmiz-btn-unzoom]]:z-[1] [&_[data-rmiz-btn-unzoom]]:cursor-zoom-out",'[&_[data-rmiz-content="found"]_img]:cursor-zoom-in','[&_[data-rmiz-content="found"]_svg]:cursor-zoom-in','[&_[data-rmiz-content="found"]_[role="img"]]:cursor-zoom-in','[&_[data-rmiz-content="found"]_[data-zoom]]:cursor-zoom-in',a),children:(0,b.jsx)(w,{classDialog:(0,x.cn)("[&::backdrop]:hidden","[&[open]]:fixed [&[open]]:m-0 [&[open]]:h-dvh [&[open]]:max-h-none [&[open]]:w-dvw [&[open]]:max-w-none [&[open]]:overflow-hidden [&[open]]:border-0 [&[open]]:bg-transparent [&[open]]:p-0","[&_[data-rmiz-modal-overlay]]:absolute [&_[data-rmiz-modal-overlay]]:inset-0 [&_[data-rmiz-modal-overlay]]:transition-all",'[&_[data-rmiz-modal-overlay="hidden"]]:bg-transparent','[&_[data-rmiz-modal-overlay="visible"]]:bg-background/80 [&_[data-rmiz-modal-overlay="visible"]]:backdrop-blur-md',"[&_[data-rmiz-modal-content]]:relative [&_[data-rmiz-modal-content]]:size-full","[&_[data-rmiz-modal-img]]:absolute [&_[data-rmiz-modal-img]]:origin-top-left [&_[data-rmiz-modal-img]]:cursor-zoom-out [&_[data-rmiz-modal-img]]:transition-transform","motion-reduce:[&_[data-rmiz-modal-img]]:transition-none motion-reduce:[&_[data-rmiz-modal-overlay]]:transition-none",c),...d})});function z(){let[a,e]=(0,d.useState)(!1);return(0,d.useEffect)(()=>{let a=()=>{e(window.innerWidth>=1024)};return a(),window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)},[]),(0,b.jsx)("section",{className:"mb-40",children:(0,b.jsx)("div",{className:"mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl",children:(0,b.jsxs)("div",{className:"flex flex-col items-center gap-[80px] font-aeonik",children:[(0,b.jsxs)("div",{className:"flex w-full flex-col items-start gap-[60px]",children:[(0,b.jsxs)("div",{className:"flex w-full flex-col items-start gap-5",children:[(0,b.jsx)("div",{className:"font-medium text-brand-500 text-xl",children:"Desktop Intelligence"}),(0,b.jsx)("h2",{className:"max-w-[320px] font-aeonik font-medium text-3xl text-white/80 leading-none tracking-[-1px] sm:max-w-2xl md:max-w-none md:text-[42px]",children:"Highlight understands what you see and hear."})]}),(0,b.jsx)("a",{className:"group relative flex aspect-square w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030] md:aspect-auto",href:"/chat",children:(0,b.jsx)(c.default,{alt:"Intelligence Hero",className:"h-full w-full rounded-[20px] object-cover transition-transform duration-300 group-hover:scale-105 md:h-auto md:object-contain",height:"622",src:"/desktop.webp",width:"1200"})})]}),(0,b.jsxs)("div",{className:"grid w-full grid-cols-1 items-start gap-16 lg:grid-cols-2",children:[(0,b.jsxs)("div",{className:"flex flex-1 flex-col gap-[50px]",children:[(0,b.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,b.jsx)("h3",{className:"font-aeonik font-medium text-2xl text-white leading-none tracking-[-1.2px] md:text-[30px]",children:"Record Meetings"}),(0,b.jsx)("p",{className:"mx-auto mb-2 max-w-3xl font-aeonik text-lg text-muted-foreground sm:text-xl",children:(0,b.jsx)("span",{children:"Record meetings, lessons, interviews, and more. Instant transcripts & summaries."})}),(0,b.jsxs)("a",{className:"flex items-center gap-2 font-medium text-[16px] text-brand-500 transition-colors hover:text-brand-500/80",href:"/meetings",children:["Learn More",(0,b.jsxs)("svg",{className:"lucide lucide-chevron-right h-4 w-4",fill:"none",height:"24",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg",children:[(0,b.jsx)("title",{children:"Right Arrow"}),(0,b.jsx)("path",{d:"m9 18 6-6-6-6"})]})]})]}),(0,b.jsxs)("div",{className:"flex flex-col gap-2 md:gap-[25px]",children:[(0,b.jsx)("div",{className:"group relative flex h-[450px] w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030]",children:(0,b.jsx)(y,{backdropClassName:(0,x.cn)('[&_[data-rmiz-modal-overlay="visible"]]:bg-brand-100/80'),className:"relative aspect-square h-full w-full overflow-hidden rounded-lg",zoomMargin:100*!!a,children:(0,b.jsx)(c.default,{alt:"Recording interface",className:"h-full w-full object-cover transition-transform duration-300 group-hover:scale-105",height:"450",loading:"lazy",sizes:"100vw",src:"/desktop.webp",style:{position:"absolute",height:"100%",width:"100%",inset:"0px",color:"transparent"},width:"450"})})}),(0,b.jsx)("div",{className:"flex flex-col gap-3 px-3 py-2 md:flex-row md:items-center md:justify-between md:gap-0",children:(0,b.jsx)("span",{className:"font-aeonik font-medium text-base text-white/50 tracking-[-0.4px] md:text-[20px]",children:"Works with all your meetings platforms."})})]})]}),(0,b.jsxs)("div",{className:"flex flex-1 flex-col gap-[50px]",children:[(0,b.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,b.jsx)("h3",{className:"font-aeonik font-medium text-2xl text-white leading-none tracking-[-1.2px] md:text-[30px]",children:"Record Meetings"}),(0,b.jsx)("p",{className:"mx-auto mb-2 max-w-3xl font-aeonik text-lg text-muted-foreground sm:text-xl",children:(0,b.jsx)("span",{children:"Record meetings, lessons, interviews, and more. Instant transcripts & summaries."})}),(0,b.jsxs)("a",{className:"flex items-center gap-2 font-medium text-[16px] text-brand-500 transition-colors hover:text-brand-500/80",href:"/meetings",children:["Learn More",(0,b.jsxs)("svg",{className:"lucide lucide-chevron-right h-4 w-4",fill:"none",height:"24",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg",children:[(0,b.jsx)("title",{children:"Right Arrow"}),(0,b.jsx)("path",{d:"m9 18 6-6-6-6"})]})]})]}),(0,b.jsxs)("div",{className:"flex flex-col gap-2 md:gap-[25px]",children:[(0,b.jsx)("div",{className:"group relative flex h-[450px] w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030]",children:(0,b.jsx)(y,{backdropClassName:(0,x.cn)('[&_[data-rmiz-modal-overlay="visible"]]:bg-brand-100/80'),className:"relative aspect-square h-full w-full overflow-hidden rounded-lg",zoomMargin:100*!!a,children:(0,b.jsx)(c.default,{alt:"Recording interface",className:"h-full w-full object-cover transition-transform duration-300 group-hover:scale-105",height:"450",loading:"lazy",sizes:"100vw",src:"/desktop.webp",style:{position:"absolute",height:"100%",width:"100%",inset:"0px",color:"transparent"},width:"450"})})}),(0,b.jsx)("div",{className:"flex flex-col gap-3 px-3 py-2 md:flex-row md:items-center md:justify-between md:gap-0",children:(0,b.jsx)("span",{className:"font-aeonik font-medium text-base text-white/50 tracking-[-0.4px] md:text-[20px]",children:"Works with all your meetings platforms."})})]})]})]})]})})})}},8305,82923,86057,66273,92732,71948,8564,3810,52077,92472,42415,47091,69949,55259,42933,6109,35731,48309,79667,75476,40284,80644,1889,12559,69053,48283,a=>{"use strict";let b;function c(a,b){-1===a.indexOf(b)&&a.push(b)}function d(a,b){let c=a.indexOf(b);c>-1&&a.splice(c,1)}a.s(["addUniqueItem",()=>c,"removeItem",()=>d],8305),a.s(["spring",()=>s],92732),a.s(["millisecondsToSeconds",()=>f,"secondsToMilliseconds",()=>e],82923);let e=a=>1e3*a,f=a=>a/1e3,g=(a,b,c)=>c>b?b:c<a?a:c,h=(a,b,c=10)=>{let d="",e=Math.max(Math.round(b/c),2);for(let b=0;b<e;b++)d+=Math.round(1e4*a(b/(e-1)))/1e4+", ";return`linear(${d.substring(0,d.length-2)})`};function i(a){let b=0,c=a.next(b);for(;!c.done&&b<2e4;)b+=50,c=a.next(b);return b>=2e4?1/0:b}function j(a,b=100,c){let d=c({...a,keyframes:[0,b]}),e=Math.min(i(d),2e4);return{type:"keyframes",ease:a=>d.next(e*a).value/b,duration:f(e)}}function k(a,b,c){var d,e;let f=Math.max(b-5,0);return d=c-a(f),(e=b-f)?1e3/e*d:0}a.s(["createGeneratorEasing",()=>j],86057);let l={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};a.s(["invariant",()=>n,"warning",()=>m],66273);let m=()=>{},n=()=>{};function o(a,b){return a*Math.sqrt(1-b*b)}let p=["duration","bounce"],q=["stiffness","damping","mass"];function r(a,b){return b.some(b=>void 0!==a[b])}function s(a=l.visualDuration,b=l.bounce){let c,d="object"!=typeof a?{visualDuration:a,keyframes:[0,1],bounce:b}:a,{restSpeed:j,restDelta:n}=d,t=d.keyframes[0],u=d.keyframes[d.keyframes.length-1],v={done:!1,value:t},{stiffness:w,damping:x,mass:y,duration:z,velocity:A,isResolvedFromDuration:B}=function(a){let b={velocity:l.velocity,stiffness:l.stiffness,damping:l.damping,mass:l.mass,isResolvedFromDuration:!1,...a};if(!r(a,q)&&r(a,p))if(a.visualDuration){let c=2*Math.PI/(1.2*a.visualDuration),d=c*c,e=2*g(.05,1,1-(a.bounce||0))*Math.sqrt(d);b={...b,mass:l.mass,stiffness:d,damping:e}}else{let c=function({duration:a=l.duration,bounce:b=l.bounce,velocity:c=l.velocity,mass:d=l.mass}){let h,i;m(a<=e(l.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let j=1-b;j=g(l.minDamping,l.maxDamping,j),a=g(l.minDuration,l.maxDuration,f(a)),j<1?(h=b=>{let d=b*j,e=d*a;return .001-(d-c)/o(b,j)*Math.exp(-e)},i=b=>{let d=b*j*a,e=Math.pow(j,2)*Math.pow(b,2)*a,f=Math.exp(-d),g=o(Math.pow(b,2),j);return(d*c+c-e)*f*(-h(b)+.001>0?-1:1)/g}):(h=b=>-.001+Math.exp(-b*a)*((b-c)*a+1),i=b=>a*a*(c-b)*Math.exp(-b*a));let k=function(a,b,c){let d=c;for(let c=1;c<12;c++)d-=a(d)/b(d);return d}(h,i,5/a);if(a=e(a),isNaN(k))return{stiffness:l.stiffness,damping:l.damping,duration:a};{let b=Math.pow(k,2)*d;return{stiffness:b,damping:2*j*Math.sqrt(d*b),duration:a}}}(a);(b={...b,...c,mass:l.mass}).isResolvedFromDuration=!0}return b}({...d,velocity:-f(d.velocity||0)}),C=A||0,D=x/(2*Math.sqrt(w*y)),E=u-t,F=f(Math.sqrt(w/y)),G=5>Math.abs(E);if(j||(j=G?l.restSpeed.granular:l.restSpeed.default),n||(n=G?l.restDelta.granular:l.restDelta.default),D<1){let a=o(F,D);c=b=>u-Math.exp(-D*F*b)*((C+D*F*E)/a*Math.sin(a*b)+E*Math.cos(a*b))}else if(1===D)c=a=>u-Math.exp(-F*a)*(E+(C+F*E)*a);else{let a=F*Math.sqrt(D*D-1);c=b=>{let c=Math.exp(-D*F*b),d=Math.min(a*b,300);return u-c*((C+D*F*E)*Math.sinh(d)+a*E*Math.cosh(d))/a}}let H={calculatedDuration:B&&z||null,next:a=>{let b=c(a);if(B)v.done=a>=z;else{let d=0===a?C:0;D<1&&(d=0===a?e(C):k(c,a,b));let f=Math.abs(u-b)<=n;v.done=Math.abs(d)<=j&&f}return v.value=v.done?u:b,v},toString:()=>{let a=Math.min(i(H),2e4),b=h(b=>H.next(a*b).value,a,30);return a+"ms "+b},toTransition:()=>{}};return H}s.applyToOptions=a=>{let b=j(a,100,s);return a.ease=b.ease,a.duration=e(b.duration),a.type="keyframes",a},a.s(["isMotionValue",()=>t],71948);let t=a=>!!(a&&a.getVelocity);a.s(["defaultOffset",()=>x],92472),a.s(["fillOffset",()=>w],52077),a.s(["progress",()=>u],8564);let u=(a,b,c)=>{let d=b-a;return 0===d?1:(c-a)/d};a.s(["mixNumber",()=>v],3810);let v=(a,b,c)=>a+(b-a)*c;function w(a,b){let c=a[a.length-1];for(let d=1;d<=b;d++){let e=u(0,b,d);a.push(v(c,1,e))}}function x(a){let b=[0];return w(b,a.length-1),b}function y(a){return"function"==typeof a&&"applyToOptions"in a}a.s(["isGenerator",()=>y],42415),a.s(["isEasingArray",()=>z],47091);let z=a=>Array.isArray(a)&&"number"!=typeof a[0];function A(a,b,c){if(a instanceof EventTarget)return[a];if("string"==typeof a){let d=document;b&&(d=b.current);let e=c?.[a]??d.querySelectorAll(a);return e?Array.from(e):[]}return Array.from(a)}a.s(["resolveElements",()=>A],69949),a.s(["visualElementStore",()=>B],55259);let B=new WeakMap;function C(a,b){return a?.[b]??a?.default??a}a.s(["animateTarget",()=>b_],6109);let D=a=>a,E={},F=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],G={value:null,addProjectionMetrics:null};function H(a,b){let c=!1,d=!0,e={delta:0,timestamp:0,isProcessing:!1},f=()=>c=!0,g=F.reduce((a,c)=>(a[c]=function(a,b){let c=new Set,d=new Set,e=!1,f=!1,g=new WeakSet,h={delta:0,timestamp:0,isProcessing:!1},i=0;function j(b){g.has(b)&&(k.schedule(b),a()),i++,b(h)}let k={schedule:(a,b=!1,f=!1)=>{let h=f&&e?c:d;return b&&g.add(a),h.has(a)||h.add(a),a},cancel:a=>{d.delete(a),g.delete(a)},process:a=>{if(h=a,e){f=!0;return}e=!0,[c,d]=[d,c],c.forEach(j),b&&G.value&&G.value.frameloop[b].push(i),i=0,c.clear(),e=!1,f&&(f=!1,k.process(a))}};return k}(f,b?c:void 0),a),{}),{setup:h,read:i,resolveKeyframes:j,preUpdate:k,update:l,preRender:m,render:n,postRender:o}=g,p=()=>{let f=E.useManualTiming?e.timestamp:performance.now();c=!1,E.useManualTiming||(e.delta=d?1e3/60:Math.max(Math.min(f-e.timestamp,40),1)),e.timestamp=f,e.isProcessing=!0,h.process(e),i.process(e),j.process(e),k.process(e),l.process(e),m.process(e),n.process(e),o.process(e),e.isProcessing=!1,c&&b&&(d=!1,a(p))};return{schedule:F.reduce((b,f)=>{let h=g[f];return b[f]=(b,f=!1,g=!1)=>(!c&&(c=!0,d=!0,e.isProcessing||a(p)),h.schedule(b,f,g)),b},{}),cancel:a=>{for(let b=0;b<F.length;b++)g[F[b]].cancel(a)},state:e,steps:g}}let{schedule:I,cancel:J,state:K,steps:L}=H("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:D,!0),M=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],N=new Set(M),O=new Set(["width","height","top","left","right","bottom",...M]);a.s(["motionValue",()=>U],42933);class P{constructor(){this.subscriptions=[]}add(a){return c(this.subscriptions,a),()=>d(this.subscriptions,a)}notify(a,b,c){let d=this.subscriptions.length;if(d)if(1===d)this.subscriptions[0](a,b,c);else for(let e=0;e<d;e++){let d=this.subscriptions[e];d&&d(a,b,c)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Q(){b=void 0}let R={now:()=>(void 0===b&&R.set(K.isProcessing||E.useManualTiming?K.timestamp:performance.now()),b),set:a=>{b=a,queueMicrotask(Q)}},S={current:void 0};class T{constructor(a,b={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=a=>{let b=R.now();if(this.updatedAt!==b&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(a),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let a of this.dependents)a.dirty()},this.hasAnimated=!1,this.setCurrent(a),this.owner=b.owner}setCurrent(a){this.current=a,this.updatedAt=R.now(),null===this.canTrackVelocity&&void 0!==a&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,b){this.events[a]||(this.events[a]=new P);let c=this.events[a].add(b);return"change"===a?()=>{c(),I.read(()=>{this.events.change.getSize()||this.stop()})}:c}clearListeners(){for(let a in this.events)this.events[a].clear()}attach(a,b){this.passiveEffect=a,this.stopPassiveEffect=b}set(a){this.passiveEffect?this.passiveEffect(a,this.updateAndNotify):this.updateAndNotify(a)}setWithVelocity(a,b,c){this.set(b),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-c}jump(a,b=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,b&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return S.current&&S.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var a;let b=R.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||b-this.updatedAt>30)return 0;let c=Math.min(this.updatedAt-this.prevUpdatedAt,30);return a=parseFloat(this.current)-parseFloat(this.prevFrameValue),c?1e3/c*a:0}start(a){return this.stop(),new Promise(b=>{this.hasAnimated=!0,this.animation=a(b),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function U(a,b){return new T(a,b)}let V=a=>Array.isArray(a);function W(a){let b=[{},{}];return a?.values.forEach((a,c)=>{b[0][c]=a.get(),b[1][c]=a.getVelocity()}),b}function X(a,b,c,d){if("function"==typeof b){let[e,f]=W(d);b=b(void 0!==c?c:a.custom,e,f)}if("string"==typeof b&&(b=a.variants&&a.variants[b]),"function"==typeof b){let[e,f]=W(d);b=b(void 0!==c?c:a.custom,e,f)}return b}function Y(a,b,c){let d=a.getProps();return X(d,b,void 0!==c?c:d.custom,a)}function Z(a,b){let c=a.getValue("willChange");if(t(c)&&c.add)return c.add(b);if(!c&&E.WillChange){let c=new E.WillChange("auto");a.addValue("willChange",c),c.add(b)}}let $=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),_="data-"+$("framerAppearId");function aa(a){a.duration=0,a.type}let ab=(a,b)=>c=>b(a(c)),ac=(...a)=>a.reduce(ab),ad={layout:0,mainThread:0,waapi:0},ae=a=>b=>"string"==typeof b&&b.startsWith(a),af=ae("--"),ag=ae("var(--"),ah=a=>!!ag(a)&&ai.test(a.split("/*")[0].trim()),ai=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,aj={test:a=>"number"==typeof a,parse:parseFloat,transform:a=>a},ak={...aj,transform:a=>g(0,1,a)},al={...aj,default:1},am=a=>Math.round(1e5*a)/1e5,an=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,ao=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,ap=(a,b)=>c=>!!("string"==typeof c&&ao.test(c)&&c.startsWith(a)||b&&null!=c&&Object.prototype.hasOwnProperty.call(c,b)),aq=(a,b,c)=>d=>{if("string"!=typeof d)return d;let[e,f,g,h]=d.match(an);return{[a]:parseFloat(e),[b]:parseFloat(f),[c]:parseFloat(g),alpha:void 0!==h?parseFloat(h):1}},ar={...aj,transform:a=>Math.round(g(0,255,a))},as={test:ap("rgb","red"),parse:aq("red","green","blue"),transform:({red:a,green:b,blue:c,alpha:d=1})=>"rgba("+ar.transform(a)+", "+ar.transform(b)+", "+ar.transform(c)+", "+am(ak.transform(d))+")"},at={test:ap("#"),parse:function(a){let b="",c="",d="",e="";return a.length>5?(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7),e=a.substring(7,9)):(b=a.substring(1,2),c=a.substring(2,3),d=a.substring(3,4),e=a.substring(4,5),b+=b,c+=c,d+=d,e+=e),{red:parseInt(b,16),green:parseInt(c,16),blue:parseInt(d,16),alpha:e?parseInt(e,16)/255:1}},transform:as.transform},au=a=>({test:b=>"string"==typeof b&&b.endsWith(a)&&1===b.split(" ").length,parse:parseFloat,transform:b=>`${b}${a}`}),av=au("deg"),aw=au("%"),ax=au("px"),ay=au("vh"),az=au("vw"),aA={...aw,parse:a=>aw.parse(a)/100,transform:a=>aw.transform(100*a)},aB={test:ap("hsl","hue"),parse:aq("hue","saturation","lightness"),transform:({hue:a,saturation:b,lightness:c,alpha:d=1})=>"hsla("+Math.round(a)+", "+aw.transform(am(b))+", "+aw.transform(am(c))+", "+am(ak.transform(d))+")"},aC={test:a=>as.test(a)||at.test(a)||aB.test(a),parse:a=>as.test(a)?as.parse(a):aB.test(a)?aB.parse(a):at.parse(a),transform:a=>"string"==typeof a?a:a.hasOwnProperty("red")?as.transform(a):aB.transform(a),getAnimatableNone:a=>{let b=aC.parse(a);return b.alpha=0,aC.transform(b)}},aD=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,aE="number",aF="color",aG=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function aH(a){let b=a.toString(),c=[],d={color:[],number:[],var:[]},e=[],f=0,g=b.replace(aG,a=>(aC.test(a)?(d.color.push(f),e.push(aF),c.push(aC.parse(a))):a.startsWith("var(")?(d.var.push(f),e.push("var"),c.push(a)):(d.number.push(f),e.push(aE),c.push(parseFloat(a))),++f,"${}")).split("${}");return{values:c,split:g,indexes:d,types:e}}function aI(a){return aH(a).values}function aJ(a){let{split:b,types:c}=aH(a),d=b.length;return a=>{let e="";for(let f=0;f<d;f++)if(e+=b[f],void 0!==a[f]){let b=c[f];b===aE?e+=am(a[f]):b===aF?e+=aC.transform(a[f]):e+=a[f]}return e}}let aK=a=>"number"==typeof a?0:aC.test(a)?aC.getAnimatableNone(a):a,aL={test:function(a){return isNaN(a)&&"string"==typeof a&&(a.match(an)?.length||0)+(a.match(aD)?.length||0)>0},parse:aI,createTransformer:aJ,getAnimatableNone:function(a){let b=aI(a);return aJ(a)(b.map(aK))}};function aM(a,b,c){return(c<0&&(c+=1),c>1&&(c-=1),c<1/6)?a+(b-a)*6*c:c<.5?b:c<2/3?a+(b-a)*(2/3-c)*6:a}function aN(a,b){return c=>c>0?b:a}let aO=(a,b,c)=>{let d=a*a,e=c*(b*b-d)+d;return e<0?0:Math.sqrt(e)},aP=[at,as,aB];function aQ(a){let b=aP.find(b=>b.test(a));if(m(!!b,`'${a}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!b)return!1;let c=b.parse(a);return b===aB&&(c=function({hue:a,saturation:b,lightness:c,alpha:d}){a/=360,c/=100;let e=0,f=0,g=0;if(b/=100){let d=c<.5?c*(1+b):c+b-c*b,h=2*c-d;e=aM(h,d,a+1/3),f=aM(h,d,a),g=aM(h,d,a-1/3)}else e=f=g=c;return{red:Math.round(255*e),green:Math.round(255*f),blue:Math.round(255*g),alpha:d}}(c)),c}let aR=(a,b)=>{let c=aQ(a),d=aQ(b);if(!c||!d)return aN(a,b);let e={...c};return a=>(e.red=aO(c.red,d.red,a),e.green=aO(c.green,d.green,a),e.blue=aO(c.blue,d.blue,a),e.alpha=v(c.alpha,d.alpha,a),as.transform(e))},aS=new Set(["none","hidden"]);function aT(a,b){return c=>v(a,b,c)}function aU(a){return"number"==typeof a?aT:"string"==typeof a?ah(a)?aN:aC.test(a)?aR:aX:Array.isArray(a)?aV:"object"==typeof a?aC.test(a)?aR:aW:aN}function aV(a,b){let c=[...a],d=c.length,e=a.map((a,c)=>aU(a)(a,b[c]));return a=>{for(let b=0;b<d;b++)c[b]=e[b](a);return c}}function aW(a,b){let c={...a,...b},d={};for(let e in c)void 0!==a[e]&&void 0!==b[e]&&(d[e]=aU(a[e])(a[e],b[e]));return a=>{for(let b in d)c[b]=d[b](a);return c}}let aX=(a,b)=>{let c=aL.createTransformer(b),d=aH(a),e=aH(b);return d.indexes.var.length===e.indexes.var.length&&d.indexes.color.length===e.indexes.color.length&&d.indexes.number.length>=e.indexes.number.length?aS.has(a)&&!e.values.length||aS.has(b)&&!d.values.length?function(a,b){return aS.has(a)?c=>c<=0?a:b:c=>c>=1?b:a}(a,b):ac(aV(function(a,b){let c=[],d={color:0,var:0,number:0};for(let e=0;e<b.values.length;e++){let f=b.types[e],g=a.indexes[f][d[f]],h=a.values[g]??0;c[e]=h,d[f]++}return c}(d,e),e.values),c):(m(!0,`Complex values '${a}' and '${b}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),aN(a,b))};function aY(a,b,c){return"number"==typeof a&&"number"==typeof b&&"number"==typeof c?v(a,b,c):aU(a)(a,b)}let aZ=a=>{let b=({timestamp:b})=>a(b);return{start:(a=!0)=>I.update(b,a),stop:()=>J(b),now:()=>K.isProcessing?K.timestamp:R.now()}};function a$({keyframes:a,velocity:b=0,power:c=.8,timeConstant:d=325,bounceDamping:e=10,bounceStiffness:f=500,modifyTarget:g,min:h,max:i,restDelta:j=.5,restSpeed:l}){let m,n,o=a[0],p={done:!1,value:o},q=c*b,r=o+q,t=void 0===g?r:g(r);t!==r&&(q=t-o);let u=a=>-q*Math.exp(-a/d),v=a=>t+u(a),w=a=>{let b=u(a),c=v(a);p.done=Math.abs(b)<=j,p.value=p.done?t:c},x=a=>{let b;if(b=p.value,void 0!==h&&b<h||void 0!==i&&b>i){var c;m=a,n=s({keyframes:[p.value,(c=p.value,void 0===h?i:void 0===i||Math.abs(h-c)<Math.abs(i-c)?h:i)],velocity:k(v,a,p.value),damping:e,stiffness:f,restDelta:j,restSpeed:l})}};return x(0),{calculatedDuration:null,next:a=>{let b=!1;return(n||void 0!==m||(b=!0,w(a),x(a)),void 0!==m&&a>=m)?n.next(a-m):(b||w(a),p)}}}let a_=(a,b,c)=>(((1-3*c+3*b)*a+(3*c-6*b))*a+3*b)*a;function a0(a,b,c,d){return a===b&&c===d?D:e=>0===e||1===e?e:a_(function(a,b,c,d,e){let f,g,h=0;do(f=a_(g=b+(c-b)/2,d,e)-a)>0?c=g:b=g;while(Math.abs(f)>1e-7&&++h<12)return g}(e,0,1,a,c),b,d)}let a1=a0(.42,0,1,1),a2=a0(0,0,.58,1),a3=a0(.42,0,.58,1),a4=a=>b=>b<=.5?a(2*b)/2:(2-a(2*(1-b)))/2,a5=a=>b=>1-a(1-b),a6=a0(.33,1.53,.69,.99),a7=a5(a6),a8=a4(a7),a9=a=>(a*=2)<1?.5*a7(a):.5*(2-Math.pow(2,-10*(a-1))),ba=a=>1-Math.sin(Math.acos(a)),bb=a5(ba),bc=a4(ba),bd=a=>Array.isArray(a)&&"number"==typeof a[0],be={linear:D,easeIn:a1,easeInOut:a3,easeOut:a2,circIn:ba,circInOut:bc,circOut:bb,backIn:a7,backInOut:a8,backOut:a6,anticipate:a9},bf=a=>{if(bd(a)){n(4===a.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[b,c,d,e]=a;return a0(b,c,d,e)}return"string"==typeof a?(n(void 0!==be[a],`Invalid easing type '${a}'`,"invalid-easing-type"),be[a]):a};function bg({duration:a=300,keyframes:b,times:c,ease:d="easeInOut"}){var e;let f=z(d)?d.map(bf):bf(d),h={done:!1,value:b[0]},i=function(a,b,{clamp:c=!0,ease:d,mixer:e}={}){let f=a.length;if(n(f===b.length,"Both input and output ranges must be the same length","range-length"),1===f)return()=>b[0];if(2===f&&b[0]===b[1])return()=>b[1];let h=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),b=[...b].reverse());let i=function(a,b,c){let d=[],e=c||E.mix||aY,f=a.length-1;for(let c=0;c<f;c++){let f=e(a[c],a[c+1]);b&&(f=ac(Array.isArray(b)?b[c]||D:b,f)),d.push(f)}return d}(b,d,e),j=i.length,k=c=>{if(h&&c<a[0])return b[0];let d=0;if(j>1)for(;d<a.length-2&&!(c<a[d+1]);d++);let e=u(a[d],a[d+1],c);return i[d](e)};return c?b=>k(g(a[0],a[f-1],b)):k}((e=c&&c.length===b.length?c:x(b),e.map(b=>b*a)),b,{ease:Array.isArray(f)?f:b.map(()=>f||a3).splice(0,b.length-1)});return{calculatedDuration:a,next:b=>(h.value=i(b),h.done=b>=a,h)}}let bh=a=>null!==a;function bi(a,{repeat:b,repeatType:c="loop"},d,e=1){let f=a.filter(bh),g=e<0||b&&"loop"!==c&&b%2==1?0:f.length-1;return g&&void 0!==d?d:f[g]}let bj={decay:a$,inertia:a$,tween:bg,keyframes:bg,spring:s};function bk(a){"string"==typeof a.type&&(a.type=bj[a.type])}class bl{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,b){return this.finished.then(a,b)}}let bm=a=>a/100;class bn extends bl{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:a}=this.options;a&&a.updatedAt!==R.now()&&this.tick(R.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},ad.mainThread++,this.options=a,this.initAnimation(),this.play(),!1===a.autoplay&&this.pause()}initAnimation(){let{options:a}=this;bk(a);let{type:b=bg,repeat:c=0,repeatDelay:d=0,repeatType:e,velocity:f=0}=a,{keyframes:g}=a,h=b||bg;h!==bg&&"number"!=typeof g[0]&&(this.mixKeyframes=ac(bm,aY(g[0],g[1])),g=[0,100]);let j=h({...a,keyframes:g});"mirror"===e&&(this.mirroredGenerator=h({...a,keyframes:[...g].reverse(),velocity:-f})),null===j.calculatedDuration&&(j.calculatedDuration=i(j));let{calculatedDuration:k}=j;this.calculatedDuration=k,this.resolvedDuration=k+d,this.totalDuration=this.resolvedDuration*(c+1)-d,this.generator=j}updateTime(a){let b=Math.round(a-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=b}tick(a,b=!1){let{generator:c,totalDuration:d,mixKeyframes:e,mirroredGenerator:f,resolvedDuration:h,calculatedDuration:i}=this;if(null===this.startTime)return c.next(0);let{delay:j=0,keyframes:k,repeat:l,repeatType:m,repeatDelay:n,type:o,onUpdate:p,finalKeyframe:q}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-d/this.speed,this.startTime)),b?this.currentTime=a:this.updateTime(a);let r=this.currentTime-j*(this.playbackSpeed>=0?1:-1),s=this.playbackSpeed>=0?r<0:r>d;this.currentTime=Math.max(r,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let t=this.currentTime,u=c;if(l){let a=Math.min(this.currentTime,d)/h,b=Math.floor(a),c=a%1;!c&&a>=1&&(c=1),1===c&&b--,(b=Math.min(b,l+1))%2&&("reverse"===m?(c=1-c,n&&(c-=n/h)):"mirror"===m&&(u=f)),t=g(0,1,c)*h}let v=s?{done:!1,value:k[0]}:u.next(t);e&&(v.value=e(v.value));let{done:w}=v;s||null===i||(w=this.playbackSpeed>=0?this.currentTime>=d:this.currentTime<=0);let x=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return x&&o!==a$&&(v.value=bi(k,this.options,q,this.speed)),p&&p(v.value),x&&this.finish(),v}then(a,b){return this.finished.then(a,b)}get duration(){return f(this.calculatedDuration)}get time(){return f(this.currentTime)}set time(a){a=e(a),this.currentTime=a,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(R.now());let b=this.playbackSpeed!==a;this.playbackSpeed=a,b&&(this.time=f(this.currentTime))}play(){if(this.isStopped)return;let{driver:a=aZ,startTime:b}=this.options;this.driver||(this.driver=a(a=>this.tick(a))),this.options.onPlay?.();let c=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=c):null!==this.holdTime?this.startTime=c-this.holdTime:this.startTime||(this.startTime=b??c),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(R.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,ad.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),a.observe(this)}}let bo=a=>180*a/Math.PI,bp=a=>br(bo(Math.atan2(a[1],a[0]))),bq={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:bp,rotateZ:bp,skewX:a=>bo(Math.atan(a[1])),skewY:a=>bo(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},br=a=>((a%=360)<0&&(a+=360),a),bs=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),bt=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),bu={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:bs,scaleY:bt,scale:a=>(bs(a)+bt(a))/2,rotateX:a=>br(bo(Math.atan2(a[6],a[5]))),rotateY:a=>br(bo(Math.atan2(-a[2],a[0]))),rotateZ:bp,rotate:bp,skewX:a=>bo(Math.atan(a[4])),skewY:a=>bo(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function bv(a){return+!!a.includes("scale")}function bw(a,b){let c,d;if(!a||"none"===a)return bv(b);let e=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(e)c=bu,d=e;else{let b=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);c=bq,d=b}if(!d)return bv(b);let f=c[b],g=d[1].split(",").map(bx);return"function"==typeof f?f(g):g[f]}function bx(a){return parseFloat(a.trim())}let by=a=>a===aj||a===ax,bz=new Set(["x","y","z"]),bA=M.filter(a=>!bz.has(a)),bB={width:({x:a},{paddingLeft:b="0",paddingRight:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),height:({y:a},{paddingTop:b="0",paddingBottom:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),top:(a,{top:b})=>parseFloat(b),left:(a,{left:b})=>parseFloat(b),bottom:({y:a},{top:b})=>parseFloat(b)+(a.max-a.min),right:({x:a},{left:b})=>parseFloat(b)+(a.max-a.min),x:(a,{transform:b})=>bw(b,"x"),y:(a,{transform:b})=>bw(b,"y")};bB.translateX=bB.x,bB.translateY=bB.y;let bC=new Set,bD=!1,bE=!1,bF=!1;function bG(){if(bE){let a=Array.from(bC).filter(a=>a.needsMeasurement),b=new Set(a.map(a=>a.element)),c=new Map;b.forEach(a=>{let b=function(a){let b=[];return bA.forEach(c=>{let d=a.getValue(c);void 0!==d&&(b.push([c,d.get()]),d.set(+!!c.startsWith("scale")))}),b}(a);b.length&&(c.set(a,b),a.render())}),a.forEach(a=>a.measureInitialState()),b.forEach(a=>{a.render();let b=c.get(a);b&&b.forEach(([b,c])=>{a.getValue(b)?.set(c)})}),a.forEach(a=>a.measureEndState()),a.forEach(a=>{void 0!==a.suspendedScrollY&&window.scrollTo(0,a.suspendedScrollY)})}bE=!1,bD=!1,bC.forEach(a=>a.complete(bF)),bC.clear()}function bH(){bC.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(bE=!0)})}class bI{constructor(a,b,c,d,e,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=b,this.name=c,this.motionValue=d,this.element=e,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(bC.add(this),bD||(bD=!0,I.read(bH),I.resolveKeyframes(bG))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:a,name:b,element:c,motionValue:d}=this;if(null===a[0]){let e=d?.get(),f=a[a.length-1];if(void 0!==e)a[0]=e;else if(c&&b){let d=c.readValue(b,f);null!=d&&(a[0]=d)}void 0===a[0]&&(a[0]=f),d&&void 0===e&&d.set(a[0])}for(let b=1;b<a.length;b++)a[b]??(a[b]=a[b-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),bC.delete(this)}cancel(){"scheduled"===this.state&&(bC.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}function bJ(a){let b;return()=>(void 0===b&&(b=a()),b)}let bK=bJ(()=>void 0!==window.ScrollTimeline),bL={},bM=function(a,b){let c=bJ(a);return()=>bL[b]??c()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(a){return!1}return!0},"linearEasing"),bN=([a,b,c,d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`,bO={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:bN([0,.65,.55,1]),circOut:bN([.55,0,1,.45]),backIn:bN([.31,.01,.66,-.59]),backOut:bN([.33,1.53,.69,.99])};class bP extends bl{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;let{element:b,name:c,keyframes:d,pseudoElement:e,allowFlatten:f=!1,finalKeyframe:g,onComplete:i}=a;this.isPseudoElement=!!e,this.allowFlatten=f,this.options=a,n("string"!=typeof a.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let j=function({type:a,...b}){return y(a)&&bM()?a.applyToOptions(b):(b.duration??(b.duration=300),b.ease??(b.ease="easeOut"),b)}(a);this.animation=function(a,b,c,{delay:d=0,duration:e=300,repeat:f=0,repeatType:g="loop",ease:i="easeOut",times:j}={},k){let l={[b]:c};j&&(l.offset=j);let m=function a(b,c){if(b)return"function"==typeof b?bM()?h(b,c):"ease-out":bd(b)?bN(b):Array.isArray(b)?b.map(b=>a(b,c)||bO.easeOut):bO[b]}(i,e);Array.isArray(m)&&(l.easing=m),G.value&&ad.waapi++;let n={delay:d,duration:e,easing:Array.isArray(m)?"linear":m,fill:"both",iterations:f+1,direction:"reverse"===g?"alternate":"normal"};k&&(n.pseudoElement=k);let o=a.animate(l,n);return G.value&&o.finished.finally(()=>{ad.waapi--}),o}(b,c,d,j,e),!1===j.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!e){let a=bi(d,this.options,g,this.speed);this.updateMotionValue?this.updateMotionValue(a):function(a,b,c){b.startsWith("--")?a.style.setProperty(b,c):a.style[b]=c}(b,c,a),this.animation.cancel()}i?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:a}=this;"idle"!==a&&"finished"!==a&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return f(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return f(Number(this.animation.currentTime)||0)}set time(a){this.finishedTime=null,this.animation.currentTime=e(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:b}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,a&&bK())?(this.animation.timeline=a,D):b(this)}}let bQ={anticipate:a9,backInOut:a8,circInOut:bc};class bR extends bP{constructor(a){!function(a){"string"==typeof a.ease&&a.ease in bQ&&(a.ease=bQ[a.ease])}(a),bk(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){let{motionValue:b,onUpdate:c,onComplete:d,element:f,...g}=this.options;if(!b)return;if(void 0!==a)return void b.set(a);let h=new bn({...g,autoplay:!1}),i=e(this.finishedTime??this.time);b.setWithVelocity(h.sample(i-10).value,h.sample(i).value,10),h.stop()}}let bS=(a,b)=>"zIndex"!==b&&!!("number"==typeof a||Array.isArray(a)||"string"==typeof a&&(aL.test(a)||"0"===a)&&!a.startsWith("url(")),bT=new Set(["opacity","clipPath","filter","transform"]),bU=bJ(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class bV extends bl{constructor({autoplay:a=!0,delay:b=0,type:c="keyframes",repeat:d=0,repeatDelay:e=0,repeatType:f="loop",keyframes:g,name:h,motionValue:i,element:j,...k}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=R.now();let l={autoplay:a,delay:b,type:c,repeat:d,repeatDelay:e,repeatType:f,name:h,motionValue:i,element:j,...k},m=j?.KeyframeResolver||bI;this.keyframeResolver=new m(g,(a,b,c)=>this.onKeyframesResolved(a,b,l,!c),h,i,j),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(a,b,c,d){this.keyframeResolver=void 0;let{name:e,type:f,velocity:g,delay:h,isHandoff:i,onUpdate:j}=c;this.resolvedAt=R.now(),!function(a,b,c,d){let e=a[0];if(null===e)return!1;if("display"===b||"visibility"===b)return!0;let f=a[a.length-1],g=bS(e,b),h=bS(f,b);return m(g===h,`You are trying to animate ${b} from "${e}" to "${f}". "${g?f:e}" is not an animatable value.`,"value-not-animatable"),!!g&&!!h&&(function(a){let b=a[0];if(1===a.length)return!0;for(let c=0;c<a.length;c++)if(a[c]!==b)return!0}(a)||("spring"===c||y(c))&&d)}(a,e,f,g)&&((E.instantAnimations||!h)&&j?.(bi(a,c,b)),a[0]=a[a.length-1],aa(c),c.repeat=0);let k={startTime:d?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:b,...c,keyframes:a},l=!i&&function(a){let{motionValue:b,name:c,repeatDelay:d,repeatType:e,damping:f,type:g}=a;if(!(b?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:h,transformTemplate:i}=b.owner.getProps();return bU()&&c&&bT.has(c)&&("transform"!==c||!i)&&!h&&!d&&"mirror"!==e&&0!==f&&"inertia"!==g}(k)?new bR({...k,element:k.motionValue.owner.current}):new bn(k);l.finished.then(()=>this.notifyFinished()).catch(D),this.pendingTimeline&&(this.stopTimeline=l.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=l}get finished(){return this._animation?this.animation.finished:this._finished}then(a,b){return this.finished.finally(a).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),bF=!0,bH(),bG(),bF=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let bW=a=>null!==a,bX={type:"spring",stiffness:500,damping:25,restSpeed:10},bY={type:"keyframes",duration:.8},bZ={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},b$=(a,b,c,d={},f,g)=>h=>{let i=C(d,a)||{},j=i.delay||d.delay||0,{elapsed:k=0}=d;k-=e(j);let l={keyframes:Array.isArray(c)?c:[null,c],ease:"easeOut",velocity:b.getVelocity(),...i,delay:-k,onUpdate:a=>{b.set(a),i.onUpdate&&i.onUpdate(a)},onComplete:()=>{h(),i.onComplete&&i.onComplete()},name:a,motionValue:b,element:g?void 0:f};!function({when:a,delay:b,delayChildren:c,staggerChildren:d,staggerDirection:e,repeat:f,repeatType:g,repeatDelay:h,from:i,elapsed:j,...k}){return!!Object.keys(k).length}(i)&&Object.assign(l,((a,{keyframes:b})=>b.length>2?bY:N.has(a)?a.startsWith("scale")?{type:"spring",stiffness:550,damping:0===b[1]?2*Math.sqrt(550):30,restSpeed:10}:bX:bZ)(a,l)),l.duration&&(l.duration=e(l.duration)),l.repeatDelay&&(l.repeatDelay=e(l.repeatDelay)),void 0!==l.from&&(l.keyframes[0]=l.from);let m=!1;if(!1!==l.type&&(0!==l.duration||l.repeatDelay)||(aa(l),0===l.delay&&(m=!0)),(E.instantAnimations||E.skipAnimations)&&(m=!0,aa(l),l.delay=0),l.allowFlatten=!i.type&&!i.ease,m&&!g&&void 0!==b.get()){let a=function(a,{repeat:b,repeatType:c="loop"},d){let e=a.filter(bW),f=b&&"loop"!==c&&b%2==1?0:e.length-1;return e[f]}(l.keyframes,i);if(void 0!==a)return void I.update(()=>{l.onUpdate(a),l.onComplete()})}return i.isSync?new bn(l):new bV(l)};function b_(a,b,{delay:c=0,transitionOverride:d,type:e}={}){let{transition:f=a.getDefaultTransition(),transitionEnd:g,...h}=b;d&&(f=d);let i=[],j=e&&a.animationState&&a.animationState.getState()[e];for(let b in h){let d=a.getValue(b,a.latestValues[b]??null),e=h[b];if(void 0===e||j&&function({protectedKeys:a,needsAnimating:b},c){let d=a.hasOwnProperty(c)&&!0!==b[c];return b[c]=!1,d}(j,b))continue;let g={delay:c,...C(f||{},b)},k=d.get();if(void 0!==k&&!d.isAnimating&&!Array.isArray(e)&&e===k&&!g.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let c=a.props[_];if(c){let a=window.MotionHandoffAnimation(c,b,I);null!==a&&(g.startTime=a,l=!0)}}Z(a,b),d.start(b$(b,d,e,a.shouldReduceMotion&&O.has(b)?{type:!1}:g,a,l));let m=d.animation;m&&i.push(m)}return g&&Promise.all(i).then(()=>{I.update(()=>{g&&function(a,b){let{transitionEnd:c={},transition:d={},...e}=Y(a,b)||{};for(let b in e={...e,...c}){var f;let c=V(f=e[b])?f[f.length-1]||0:f;a.hasValue(b)?a.getValue(b).set(c):a.addValue(b,U(c))}}(a,g)})}),i}function b0(a){return"object"==typeof a&&null!==a}function b1(a){return b0(a)&&"ownerSVGElement"in a}function b2(a){return b1(a)&&"svg"===a.tagName}function b3({top:a,left:b,right:c,bottom:d}){return{x:{min:b,max:c},y:{min:a,max:d}}}function b4(a){return void 0===a||1===a}function b5({scale:a,scaleX:b,scaleY:c}){return!b4(a)||!b4(b)||!b4(c)}function b6(a){return b5(a)||b7(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function b7(a){var b,c;return(b=a.x)&&"0%"!==b||(c=a.y)&&"0%"!==c}a.s(["isSVGElement",()=>b1],35731),a.s(["isSVGSVGElement",()=>b2],48309),a.s(["HTMLVisualElement",()=>cX],40284);function b8(a,b,c,d,e){return void 0!==e&&(a=d+e*(a-d)),d+c*(a-d)+b}function b9(a,b=0,c=1,d,e){a.min=b8(a.min,b,c,d,e),a.max=b8(a.max,b,c,d,e)}function ca(a,{x:b,y:c}){b9(a.x,b.translate,b.scale,b.originPoint),b9(a.y,c.translate,c.scale,c.originPoint)}function cb(a,b){a.min=a.min+b,a.max=a.max+b}function cc(a,b,c,d,e=.5){let f=v(a.min,a.max,e);b9(a,b,c,f,d)}function cd(a,b){cc(a.x,b.x,b.scaleX,b.scale,b.originX),cc(a.y,b.y,b.scaleY,b.scale,b.originY)}function ce(a,b){return b3(function(a,b){if(!b)return a;let c=b({x:a.left,y:a.top}),d=b({x:a.right,y:a.bottom});return{top:c.y,left:c.x,bottom:d.y,right:d.x}}(a.getBoundingClientRect(),b))}let cf=a=>b=>b.test(a),cg=[aj,ax,aw,av,az,ay,{test:a=>"auto"===a,parse:a=>a}],ch=a=>cg.find(cf(a)),ci=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a),cj=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ck=a=>/^0[^.\s]+$/u.test(a),cl=new Set(["brightness","contrast","saturate","opacity"]);function cm(a){let[b,c]=a.slice(0,-1).split("(");if("drop-shadow"===b)return a;let[d]=c.match(an)||[];if(!d)return a;let e=c.replace(d,""),f=+!!cl.has(b);return d!==c&&(f*=100),b+"("+f+e+")"}let cn=/\b([a-z-]*)\(.*?\)/gu,co={...aL,getAnimatableNone:a=>{let b=a.match(cn);return b?b.map(cm).join(" "):a}},cp={...aj,transform:Math.round},cq={borderWidth:ax,borderTopWidth:ax,borderRightWidth:ax,borderBottomWidth:ax,borderLeftWidth:ax,borderRadius:ax,radius:ax,borderTopLeftRadius:ax,borderTopRightRadius:ax,borderBottomRightRadius:ax,borderBottomLeftRadius:ax,width:ax,maxWidth:ax,height:ax,maxHeight:ax,top:ax,right:ax,bottom:ax,left:ax,padding:ax,paddingTop:ax,paddingRight:ax,paddingBottom:ax,paddingLeft:ax,margin:ax,marginTop:ax,marginRight:ax,marginBottom:ax,marginLeft:ax,backgroundPositionX:ax,backgroundPositionY:ax,rotate:av,rotateX:av,rotateY:av,rotateZ:av,scale:al,scaleX:al,scaleY:al,scaleZ:al,skew:av,skewX:av,skewY:av,distance:ax,translateX:ax,translateY:ax,translateZ:ax,x:ax,y:ax,z:ax,perspective:ax,transformPerspective:ax,opacity:ak,originX:aA,originY:aA,originZ:ax,zIndex:cp,fillOpacity:ak,strokeOpacity:ak,numOctaves:cp},cr={...cq,color:aC,backgroundColor:aC,outlineColor:aC,fill:aC,stroke:aC,borderColor:aC,borderTopColor:aC,borderRightColor:aC,borderBottomColor:aC,borderLeftColor:aC,filter:co,WebkitFilter:co},cs=a=>cr[a];function ct(a,b){let c=cs(a);return c!==co&&(c=aL),c.getAnimatableNone?c.getAnimatableNone(b):void 0}let cu=new Set(["auto","none","0"]);class cv extends bI{constructor(a,b,c,d,e){super(a,b,c,d,e,!0)}readKeyframes(){let{unresolvedKeyframes:a,element:b,name:c}=this;if(!b||!b.current)return;super.readKeyframes();for(let c=0;c<a.length;c++){let d=a[c];if("string"==typeof d&&ah(d=d.trim())){let e=function a(b,c,d=1){n(d<=4,`Max CSS variable fallback depth detected in property "${b}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[e,f]=function(a){let b=cj.exec(a);if(!b)return[,];let[,c,d,e]=b;return[`--${c??d}`,e]}(b);if(!e)return;let g=window.getComputedStyle(c).getPropertyValue(e);if(g){let a=g.trim();return ci(a)?parseFloat(a):a}return ah(f)?a(f,c,d+1):f}(d,b.current);void 0!==e&&(a[c]=e),c===a.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!O.has(c)||2!==a.length)return;let[d,e]=a,f=ch(d),g=ch(e);if(f!==g)if(by(f)&&by(g))for(let b=0;b<a.length;b++){let c=a[b];"string"==typeof c&&(a[b]=parseFloat(c))}else bB[c]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:a,name:b}=this,c=[];for(let b=0;b<a.length;b++){var d;(null===a[b]||("number"==typeof(d=a[b])?0===d:null===d||"none"===d||"0"===d||ck(d)))&&c.push(b)}c.length&&function(a,b,c){let d,e=0;for(;e<a.length&&!d;){let b=a[e];"string"==typeof b&&!cu.has(b)&&aH(b).values.length&&(d=a[e]),e++}if(d&&c)for(let e of b)a[e]=ct(c,d)}(a,c,b)}measureInitialState(){let{element:a,unresolvedKeyframes:b,name:c}=this;if(!a||!a.current)return;"height"===c&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=bB[c](a.measureViewportBox(),window.getComputedStyle(a.current)),b[0]=this.measuredOrigin;let d=b[b.length-1];void 0!==d&&a.getValue(c,d).jump(d,!1)}measureEndState(){let{element:a,name:b,unresolvedKeyframes:c}=this;if(!a||!a.current)return;let d=a.getValue(b);d&&d.jump(this.measuredOrigin,!1);let e=c.length-1,f=c[e];c[e]=bB[b](a.measureViewportBox(),window.getComputedStyle(a.current)),null!==f&&void 0===this.finalKeyframe&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([b,c])=>{a.getValue(b).set(c)}),this.resolveNoneKeyframes()}}a.s(["VisualElement",()=>cN],75476);let cw=[...cg,aC,aL],{schedule:cx}=H(queueMicrotask,!1),cy={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},cz={};for(let a in cy)cz[a]={isEnabled:b=>cy[a].some(a=>!!b[a])};a.s(["createBox",()=>cD,"createDelta",()=>cB],79667);let cA=()=>({translate:0,scale:1,origin:0,originPoint:0}),cB=()=>({x:cA(),y:cA()}),cC=()=>({min:0,max:0}),cD=()=>({x:cC(),y:cC()}),cE={current:null},cF={current:!1};function cG(a){return null!==a&&"object"==typeof a&&"function"==typeof a.start}function cH(a){return"string"==typeof a||Array.isArray(a)}let cI=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],cJ=["initial",...cI];function cK(a){return cG(a.animate)||cJ.some(b=>cH(a[b]))}function cL(a){return!!(cK(a)||a.variants)}let cM=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class cN{scrapeMotionValuesFromProps(a,b,c){return{}}constructor({parent:a,props:b,presenceContext:c,reducedMotionConfig:d,blockInitialAnimation:e,visualState:f},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=bI,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let a=R.now();this.renderScheduledAt<a&&(this.renderScheduledAt=a,I.render(this.render,!1,!0))};let{latestValues:h,renderState:i}=f;this.latestValues=h,this.baseTarget={...h},this.initialValues=b.initial?{...h}:{},this.renderState=i,this.parent=a,this.props=b,this.presenceContext=c,this.depth=a?a.depth+1:0,this.reducedMotionConfig=d,this.options=g,this.blockInitialAnimation=!!e,this.isControllingVariants=cK(b),this.isVariantNode=cL(b),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);let{willChange:j,...k}=this.scrapeMotionValuesFromProps(b,{},this);for(let a in k){let b=k[a];void 0!==h[a]&&t(b)&&b.set(h[a])}}mount(a){this.current=a,B.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((a,b)=>this.bindToMotionValue(b,a)),cF.current||(cF.current=!0),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||cE.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let a in this.projection&&this.projection.unmount(),J(this.notifyUpdate),J(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[a].clear();for(let a in this.features){let b=this.features[a];b&&(b.unmount(),b.isMounted=!1)}this.current=null}addChild(a){this.children.add(a),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(a)}removeChild(a){this.children.delete(a),this.enteringChildren&&this.enteringChildren.delete(a)}bindToMotionValue(a,b){let c;this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();let d=N.has(a);d&&this.onBindTransform&&this.onBindTransform();let e=b.on("change",b=>{this.latestValues[a]=b,this.props.onUpdate&&I.preRender(this.notifyUpdate),d&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(c=window.MotionCheckAppearSync(this,a,b)),this.valueSubscriptions.set(a,()=>{e(),c&&c(),b.owner&&b.stop()})}sortNodePosition(a){return this.current&&this.sortInstanceNodePosition&&this.type===a.type?this.sortInstanceNodePosition(this.current,a.current):0}updateFeatures(){let a="animation";for(a in cz){let b=cz[a];if(!b)continue;let{isEnabled:c,Feature:d}=b;if(!this.features[a]&&d&&c(this.props)&&(this.features[a]=new d(this)),this.features[a]){let b=this.features[a];b.isMounted?b.update():(b.mount(),b.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):cD()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,b){this.latestValues[a]=b}update(a,b){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=b;for(let b=0;b<cM.length;b++){let c=cM[b];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);let d=a["on"+c];d&&(this.propEventSubscriptions[c]=this.on(c,d))}this.prevMotionValues=function(a,b,c){for(let d in b){let e=b[d],f=c[d];if(t(e))a.addValue(d,e);else if(t(f))a.addValue(d,U(e,{owner:a}));else if(f!==e)if(a.hasValue(d)){let b=a.getValue(d);!0===b.liveStyle?b.jump(e):b.hasAnimated||b.set(e)}else{let b=a.getStaticValue(d);a.addValue(d,U(void 0!==b?b:e,{owner:a}))}}for(let d in c)void 0===b[d]&&a.removeValue(d);return b}(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){let b=this.getClosestVariantNode();if(b)return b.variantChildren&&b.variantChildren.add(a),()=>b.variantChildren.delete(a)}addValue(a,b){let c=this.values.get(a);b!==c&&(c&&this.removeValue(a),this.bindToMotionValue(a,b),this.values.set(a,b),this.latestValues[a]=b.get())}removeValue(a){this.values.delete(a);let b=this.valueSubscriptions.get(a);b&&(b(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,b){if(this.props.values&&this.props.values[a])return this.props.values[a];let c=this.values.get(a);return void 0===c&&void 0!==b&&(c=U(null===b?void 0:b,{owner:this}),this.addValue(a,c)),c}readValue(a,b){let c=void 0===this.latestValues[a]&&this.current?this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options):this.latestValues[a];if(null!=c){if("string"==typeof c&&(ci(c)||ck(c)))c=parseFloat(c);else{let d;d=c,!cw.find(cf(d))&&aL.test(b)&&(c=ct(a,b))}this.setBaseTarget(a,t(c)?c.get():c)}return t(c)?c.get():c}setBaseTarget(a,b){this.baseTarget[a]=b}getBaseTarget(a){let b,{initial:c}=this.props;if("string"==typeof c||"object"==typeof c){let d=X(this.props,c,this.presenceContext?.custom);d&&(b=d[a])}if(c&&void 0!==b)return b;let d=this.getBaseTargetFromProps(this.props,a);return void 0===d||t(d)?void 0!==this.initialValues[a]&&void 0===b?void 0:this.baseTarget[a]:d}on(a,b){return this.events[a]||(this.events[a]=new P),this.events[a].add(b)}notify(a,...b){this.events[a]&&this.events[a].notify(...b)}scheduleRenderMicrotask(){cx.render(this.render)}}class cO extends cN{constructor(){super(...arguments),this.KeyframeResolver=cv}sortInstanceNodePosition(a,b){return 2&a.compareDocumentPosition(b)?1:-1}getBaseTargetFromProps(a,b){return a.style?a.style[b]:void 0}removeValueFromRenderState(a,{vars:b,style:c}){delete b[a],delete c[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:a}=this.props;t(a)&&(this.childSubscription=a.on("change",a=>{this.current&&(this.current.textContent=`${a}`)}))}}let cP=(a,b)=>b&&"number"==typeof a?b.transform(a):a,cQ={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},cR=M.length;function cS(a,b,c){let{style:d,vars:e,transformOrigin:f}=a,g=!1,h=!1;for(let a in b){let c=b[a];if(N.has(a)){g=!0;continue}if(af(a)){e[a]=c;continue}{let b=cP(c,cq[a]);a.startsWith("origin")?(h=!0,f[a]=b):d[a]=b}}if(!b.transform&&(g||c?d.transform=function(a,b,c){let d="",e=!0;for(let f=0;f<cR;f++){let g=M[f],h=a[g];if(void 0===h)continue;let i=!0;if(!(i="number"==typeof h?h===+!!g.startsWith("scale"):0===parseFloat(h))||c){let a=cP(h,cq[g]);if(!i){e=!1;let b=cQ[g]||g;d+=`${b}(${a}) `}c&&(b[g]=a)}}return d=d.trim(),c?d=c(b,e?"":d):e&&(d="none"),d}(b,a.transform,c):d.transform&&(d.transform="none")),h){let{originX:a="50%",originY:b="50%",originZ:c=0}=f;d.transformOrigin=`${a} ${b} ${c}`}}function cT(a,{style:b,vars:c},d,e){let f,g=a.style;for(f in b)g[f]=b[f];for(f in e?.applyProjectionStyles(g,d),c)g.setProperty(f,c[f])}let cU={};function cV(a,{layout:b,layoutId:c}){return N.has(a)||a.startsWith("origin")||(b||void 0!==c)&&(!!cU[a]||"opacity"===a)}function cW(a,b,c){let{style:d}=a,e={};for(let f in d)(t(d[f])||b.style&&t(b.style[f])||cV(f,a)||c?.getValue(f)?.liveStyle!==void 0)&&(e[f]=d[f]);return e}class cX extends cO{constructor(){super(...arguments),this.type="html",this.renderInstance=cT}readValueFromInstance(a,b){if(N.has(b))return this.projection?.isProjecting?bv(b):((a,b)=>{let{transform:c="none"}=getComputedStyle(a);return bw(c,b)})(a,b);{let c=window.getComputedStyle(a),d=(af(b)?c.getPropertyValue(b):c[b])||0;return"string"==typeof d?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:b}){return ce(a,b)}build(a,b,c){cS(a,b,c.transformTemplate)}scrapeMotionValuesFromProps(a,b,c){return cW(a,b,c)}}a.s(["SVGVisualElement",()=>c2],80644);let cY={offset:"stroke-dashoffset",array:"stroke-dasharray"},cZ={offset:"strokeDashoffset",array:"strokeDasharray"};function c$(a,{attrX:b,attrY:c,attrScale:d,pathLength:e,pathSpacing:f=1,pathOffset:g=0,...h},i,j,k){if(cS(a,h,j),i){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};let{attrs:l,style:m}=a;l.transform&&(m.transform=l.transform,delete l.transform),(m.transform||l.transformOrigin)&&(m.transformOrigin=l.transformOrigin??"50% 50%",delete l.transformOrigin),m.transform&&(m.transformBox=k?.transformBox??"fill-box",delete l.transformBox),void 0!==b&&(l.x=b),void 0!==c&&(l.y=c),void 0!==d&&(l.scale=d),void 0!==e&&function(a,b,c=1,d=0,e=!0){a.pathLength=1;let f=e?cY:cZ;a[f.offset]=ax.transform(-d);let g=ax.transform(b),h=ax.transform(c);a[f.array]=`${g} ${h}`}(l,e,f,g,!1)}let c_=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),c0=a=>"string"==typeof a&&"svg"===a.toLowerCase();function c1(a,b,c){let d=cW(a,b,c);for(let c in a)(t(a[c])||t(b[c]))&&(d[-1!==M.indexOf(c)?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c]=a[c]);return d}class c2 extends cO{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=cD}getBaseTargetFromProps(a,b){return a[b]}readValueFromInstance(a,b){if(N.has(b)){let a=cs(b);return a&&a.default||0}return b=c_.has(b)?b:$(b),a.getAttribute(b)}scrapeMotionValuesFromProps(a,b,c){return c1(a,b,c)}build(a,b,c){c$(a,b,this.isSVGTag,c.transformTemplate,c.style)}renderInstance(a,b,c,d){for(let c in cT(a,b,void 0,d),b.attrs)a.setAttribute(c_.has(c)?c:$(c),b.attrs[c])}mount(a){this.isSVGTag=c0(a.tagName),super.mount(a)}}function c3(a,b,c){let d=t(a)?a:U(a);return d.start(b$("",d,b,c)),d.animation}a.s(["animateSingleValue",()=>c3],1889),a.s(["motion",()=>fh],48283);var c4=a.i(29611);let c5=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function c6(a){if("string"!=typeof a||a.includes("-"));else if(c5.indexOf(a)>-1||/[A-Z]/u.test(a))return!0;return!1}var c7=a.i(69720);let c8=(0,c4.createContext)({});(0,c4.createContext)({strict:!1}),a.s(["MotionConfigContext",()=>c9],12559);let c9=(0,c4.createContext)({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"}),da=(0,c4.createContext)({});function db(a){return Array.isArray(a)?a.join(" "):a}let dc=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function dd(a,b,c){for(let d in b)t(b[d])||cV(d,c)||(a[d]=b[d])}let de=()=>({...dc(),attrs:{}}),df=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function dg(a){return a.startsWith("while")||a.startsWith("drag")&&"draggable"!==a||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||df.has(a)}let dh=a=>!dg(a);try{!function(a){"function"==typeof a&&(dh=b=>b.startsWith("on")?!dg(b):a(b))}((()=>{let a=Error("Cannot find module '@emotion/is-prop-valid'");throw a.code="MODULE_NOT_FOUND",a})().default)}catch{}let di=(0,c4.createContext)(null);function dj(a){let b=(0,c4.useRef)(null);return null===b.current&&(b.current=a()),b.current}function dk(a){return t(a)?a.get():a}a.s(["useConstant",()=>dj],69053);let dl=a=>(b,c)=>{let d=(0,c4.useContext)(da),e=(0,c4.useContext)(di),f=()=>(function({scrapeMotionValuesFromProps:a,createRenderState:b},c,d,e){return{latestValues:function(a,b,c,d){let e={},f=d(a,{});for(let a in f)e[a]=dk(f[a]);let{initial:g,animate:h}=a,i=cK(a),j=cL(a);b&&j&&!i&&!1!==a.inherit&&(void 0===g&&(g=b.initial),void 0===h&&(h=b.animate));let k=!!c&&!1===c.initial,l=(k=k||!1===g)?h:g;if(l&&"boolean"!=typeof l&&!cG(l)){let b=Array.isArray(l)?l:[l];for(let c=0;c<b.length;c++){let d=X(a,b[c]);if(d){let{transitionEnd:a,transition:b,...c}=d;for(let a in c){let b=c[a];if(Array.isArray(b)){let a=k?b.length-1:0;b=b[a]}null!==b&&(e[a]=b)}for(let b in a)e[b]=a[b]}}}return e}(c,d,e,a),renderState:b()}})(a,b,d,e);return c?f():dj(f)},dm=dl({scrapeMotionValuesFromProps:cW,createRenderState:dc}),dn=dl({scrapeMotionValuesFromProps:c1,createRenderState:de}),dp=Symbol.for("motionComponentSymbol");function dq(a){return a&&"object"==typeof a&&Object.prototype.hasOwnProperty.call(a,"current")}let dr=(0,c4.createContext)({});function ds(a,{forwardMotionProps:b=!1}={},c,d){c&&function(a){for(let b in a)cz[b]={...cz[b],...a[b]}}(c);let e=c6(a)?dn:dm;function f(c,d){var f;let g,h={...(0,c4.useContext)(c9),...c,layoutId:function({layoutId:a}){let b=(0,c4.useContext)(c8).id;return b&&void 0!==a?b+"-"+a:a}(c)},{isStatic:i}=h,j=function(a){let{initial:b,animate:c}=function(a,b){if(cK(a)){let{initial:b,animate:c}=a;return{initial:!1===b||cH(b)?b:void 0,animate:cH(c)?c:void 0}}return!1!==a.inherit?b:{}}(a,(0,c4.useContext)(da));return(0,c4.useMemo)(()=>({initial:b,animate:c}),[db(b),db(c)])}(c),k=e(c,i);return(0,c7.jsxs)(da.Provider,{value:j,children:[g&&j.visualElement?(0,c7.jsx)(g,{visualElement:j.visualElement,...h}):null,function(a,b,c,{latestValues:d},e,f=!1){let g=(c6(a)?function(a,b,c,d){let e=(0,c4.useMemo)(()=>{let c=de();return c$(c,b,c0(d),a.transformTemplate,a.style),{...c.attrs,style:{...c.style}}},[b]);if(a.style){let b={};dd(b,a.style,a),e.style={...b,...e.style}}return e}:function(a,b){let c={},d=function(a,b){let c=a.style||{},d={};return dd(d,c,a),Object.assign(d,function({transformTemplate:a},b){return(0,c4.useMemo)(()=>{let c=dc();return cS(c,b,a),Object.assign({},c.vars,c.style)},[b])}(a,b)),d}(a,b);return a.drag&&!1!==a.dragListener&&(c.draggable=!1,d.userSelect=d.WebkitUserSelect=d.WebkitTouchCallout="none",d.touchAction=!0===a.drag?"none":`pan-${"x"===a.drag?"y":"x"}`),void 0===a.tabIndex&&(a.onTap||a.onTapStart||a.whileTap)&&(c.tabIndex=0),c.style=d,c})(b,d,e,a),h=function(a,b,c){let d={};for(let e in a)("values"!==e||"object"!=typeof a.values)&&(dh(e)||!0===c&&dg(e)||!b&&!dg(e)||a.draggable&&e.startsWith("onDrag"))&&(d[e]=a[e]);return d}(b,"string"==typeof a,f),i=a!==c4.Fragment?{...h,...g,ref:c}:{},{children:j}=b,k=(0,c4.useMemo)(()=>t(j)?j.get():j,[j]);return(0,c4.createElement)(a,{...i,children:k})}(a,c,(f=j.visualElement,(0,c4.useCallback)(a=>{a&&k.onMount&&k.onMount(a),f&&(a?f.mount(a):f.unmount()),d&&("function"==typeof d?d(a):dq(d)&&(d.current=a))},[f])),k,i,b)]})}f.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;let g=(0,c4.forwardRef)(f);return g[dp]=a,g}function dt(a,b,c,d=0,e=1){let f=Array.from(a).sort((a,b)=>a.sortNodePosition(b)).indexOf(b),g=a.size,h=(g-1)*d;return"function"==typeof c?c(f,g):1===e?f*d:h-f*d}function du(a,b,c={}){let d=Y(a,b,"exit"===c.type?a.presenceContext?.custom:void 0),{transition:e=a.getDefaultTransition()||{}}=d||{};c.transitionOverride&&(e=c.transitionOverride);let f=d?()=>Promise.all(b_(a,d,c)):()=>Promise.resolve(),g=a.variantChildren&&a.variantChildren.size?(d=0)=>{let{delayChildren:f=0,staggerChildren:g,staggerDirection:h}=e;return function(a,b,c=0,d=0,e=0,f=1,g){let h=[];for(let i of a.variantChildren)i.notify("AnimationStart",b),h.push(du(i,b,{...g,delay:c+("function"==typeof d?0:d)+dt(a.variantChildren,i,d,e,f)}).then(()=>i.notify("AnimationComplete",b)));return Promise.all(h)}(a,b,d,f,g,h,c)}:()=>Promise.resolve(),{when:h}=e;if(!h)return Promise.all([f(),g(c.delay)]);{let[a,b]="beforeChildren"===h?[f,g]:[g,f];return a().then(()=>b())}}function dv(a,b){if(!Array.isArray(b))return!1;let c=b.length;if(c!==a.length)return!1;for(let d=0;d<c;d++)if(b[d]!==a[d])return!1;return!0}c4.useEffect;let dw=cJ.length,dx=[...cI].reverse(),dy=cI.length;function dz(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function dA(){return{animate:dz(!0),whileInView:dz(),whileHover:dz(),whileTap:dz(),whileDrag:dz(),whileFocus:dz(),exit:dz()}}class dB{constructor(a){this.isMounted=!1,this.node=a}update(){}}let dC=0,dD={x:!1,y:!1};function dE(a,b,c,d={passive:!0}){return a.addEventListener(b,c,d),()=>a.removeEventListener(b,c)}let dF=a=>"mouse"===a.pointerType?"number"!=typeof a.button||a.button<=0:!1!==a.isPrimary;function dG(a){return{point:{x:a.pageX,y:a.pageY}}}function dH(a,b,c,d){return dE(a,b,a=>dF(a)&&c(a,dG(a)),d)}function dI(a){return a.max-a.min}function dJ(a,b,c,d=.5){a.origin=d,a.originPoint=v(b.min,b.max,a.origin),a.scale=dI(c)/dI(b),a.translate=v(c.min,c.max,a.origin)-a.originPoint,(a.scale>=.9999&&a.scale<=1.0001||isNaN(a.scale))&&(a.scale=1),(a.translate>=-.01&&a.translate<=.01||isNaN(a.translate))&&(a.translate=0)}function dK(a,b,c,d){dJ(a.x,b.x,c.x,d?d.originX:void 0),dJ(a.y,b.y,c.y,d?d.originY:void 0)}function dL(a,b,c){a.min=c.min+b.min,a.max=a.min+dI(b)}function dM(a,b,c){a.min=b.min-c.min,a.max=a.min+dI(b)}function dN(a,b,c){dM(a.x,b.x,c.x),dM(a.y,b.y,c.y)}function dO(a){return[a("x"),a("y")]}let dP=({current:a})=>a?a.ownerDocument.defaultView:null,dQ=(a,b)=>Math.abs(a-b);class dR{constructor(a,b,{transformPagePoint:c,contextWindow:d=window,dragSnapToOrigin:e=!1,distanceThreshold:f=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=dU(this.lastMoveEventInfo,this.history),b=null!==this.startEvent,c=function(a,b){return Math.sqrt(dQ(a.x,b.x)**2+dQ(a.y,b.y)**2)}(a.offset,{x:0,y:0})>=this.distanceThreshold;if(!b&&!c)return;let{point:d}=a,{timestamp:e}=K;this.history.push({...d,timestamp:e});let{onStart:f,onMove:g}=this.handlers;b||(f&&f(this.lastMoveEvent,a),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,a)},this.handlePointerMove=(a,b)=>{this.lastMoveEvent=a,this.lastMoveEventInfo=dS(b,this.transformPagePoint),I.update(this.updatePoint,!0)},this.handlePointerUp=(a,b)=>{this.end();let{onEnd:c,onSessionEnd:d,resumeAnimation:e}=this.handlers;if(this.dragSnapToOrigin&&e&&e(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let f=dU("pointercancel"===a.type?this.lastMoveEventInfo:dS(b,this.transformPagePoint),this.history);this.startEvent&&c&&c(a,f),d&&d(a,f)},!dF(a))return;this.dragSnapToOrigin=e,this.handlers=b,this.transformPagePoint=c,this.distanceThreshold=f,this.contextWindow=d||window;let g=dS(dG(a),this.transformPagePoint),{point:h}=g,{timestamp:i}=K;this.history=[{...h,timestamp:i}];let{onSessionStart:j}=b;j&&j(a,dU(g,this.history)),this.removeListeners=ac(dH(this.contextWindow,"pointermove",this.handlePointerMove),dH(this.contextWindow,"pointerup",this.handlePointerUp),dH(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),J(this.updatePoint)}}function dS(a,b){return b?{point:b(a.point)}:a}function dT(a,b){return{x:a.x-b.x,y:a.y-b.y}}function dU({point:a},b){return{point:a,delta:dT(a,dV(b)),offset:dT(a,b[0]),velocity:function(a,b){if(a.length<2)return{x:0,y:0};let c=a.length-1,d=null,g=dV(a);for(;c>=0&&(d=a[c],!(g.timestamp-d.timestamp>e(.1)));)c--;if(!d)return{x:0,y:0};let h=f(g.timestamp-d.timestamp);if(0===h)return{x:0,y:0};let i={x:(g.x-d.x)/h,y:(g.y-d.y)/h};return i.x===1/0&&(i.x=0),i.y===1/0&&(i.y=0),i}(b,.1)}}function dV(a){return a[a.length-1]}function dW(a,b,c){return{min:void 0!==b?a.min+b:void 0,max:void 0!==c?a.max+c-(a.max-a.min):void 0}}function dX(a,b){let c=b.min-a.min,d=b.max-a.max;return b.max-b.min<a.max-a.min&&([c,d]=[d,c]),{min:c,max:d}}function dY(a,b,c){return{min:dZ(a,b),max:dZ(a,c)}}function dZ(a,b){return"number"==typeof a?a:a[b]||0}let d$=new WeakMap;class d_{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=cD(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=a}start(a,{snapToCursor:b=!1,distanceThreshold:c}={}){let{presenceContext:d}=this.visualElement;if(d&&!1===d.isPresent)return;let e=a=>{let{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),b&&this.snapToCursor(dG(a).point)},f=(a,b)=>{let{drag:c,dragPropagation:d,onDragStart:e}=this.getProps();if(c&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(a){if("x"===a||"y"===a)if(dD[a])return null;else return dD[a]=!0,()=>{dD[a]=!1};return dD.x||dD.y?null:(dD.x=dD.y=!0,()=>{dD.x=dD.y=!1})}(c),!this.openDragLock))return;this.latestPointerEvent=a,this.latestPanInfo=b,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),dO(a=>{let b=this.getAxisMotionValue(a).get()||0;if(aw.test(b)){let{projection:c}=this.visualElement;if(c&&c.layout){let d=c.layout.layoutBox[a];d&&(b=dI(d)*(parseFloat(b)/100))}}this.originPoint[a]=b}),e&&I.postRender(()=>e(a,b)),Z(this.visualElement,"transform");let{animationState:f}=this.visualElement;f&&f.setActive("whileDrag",!0)},g=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b;let{dragPropagation:c,dragDirectionLock:d,onDirectionLock:e,onDrag:f}=this.getProps();if(!c&&!this.openDragLock)return;let{offset:g}=b;if(d&&null===this.currentDirection){this.currentDirection=function(a,b=10){let c=null;return Math.abs(a.y)>b?c="y":Math.abs(a.x)>b&&(c="x"),c}(g),null!==this.currentDirection&&e&&e(this.currentDirection);return}this.updateAxis("x",b.point,g),this.updateAxis("y",b.point,g),this.visualElement.render(),f&&f(a,b)},h=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b,this.stop(a,b),this.latestPointerEvent=null,this.latestPanInfo=null},i=()=>dO(a=>"paused"===this.getAnimationState(a)&&this.getAxisMotionValue(a).animation?.play()),{dragSnapToOrigin:j}=this.getProps();this.panSession=new dR(a,{onSessionStart:e,onStart:f,onMove:g,onSessionEnd:h,resumeAnimation:i},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:j,distanceThreshold:c,contextWindow:dP(this.visualElement)})}stop(a,b){let c=a||this.latestPointerEvent,d=b||this.latestPanInfo,e=this.isDragging;if(this.cancel(),!e||!d||!c)return;let{velocity:f}=d;this.startAnimation(f);let{onDragEnd:g}=this.getProps();g&&I.postRender(()=>g(c,d))}cancel(){this.isDragging=!1;let{projection:a,animationState:b}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:c}=this.getProps();!c&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),b&&b.setActive("whileDrag",!1)}updateAxis(a,b,c){let{drag:d}=this.getProps();if(!c||!d0(a,d,this.currentDirection))return;let e=this.getAxisMotionValue(a),f=this.originPoint[a]+c[a];this.constraints&&this.constraints[a]&&(f=function(a,{min:b,max:c},d){return void 0!==b&&a<b?a=d?v(b,a,d.min):Math.max(a,b):void 0!==c&&a>c&&(a=d?v(c,a,d.max):Math.min(a,c)),a}(f,this.constraints[a],this.elastic[a])),e.set(f)}resolveConstraints(){let{dragConstraints:a,dragElastic:b}=this.getProps(),c=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,d=this.constraints;a&&dq(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&c?this.constraints=function(a,{top:b,left:c,bottom:d,right:e}){return{x:dW(a.x,c,e),y:dW(a.y,b,d)}}(c.layoutBox,a):this.constraints=!1,this.elastic=function(a=.35){return!1===a?a=0:!0===a&&(a=.35),{x:dY(a,"left","right"),y:dY(a,"top","bottom")}}(b),d!==this.constraints&&c&&this.constraints&&!this.hasMutatedConstraints&&dO(a=>{!1!==this.constraints&&this.getAxisMotionValue(a)&&(this.constraints[a]=function(a,b){let c={};return void 0!==b.min&&(c.min=b.min-a.min),void 0!==b.max&&(c.max=b.max-a.min),c}(c.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){var a;let{dragConstraints:b,onMeasureDragConstraints:c}=this.getProps();if(!b||!dq(b))return!1;let d=b.current;n(null!==d,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:e}=this.visualElement;if(!e||!e.layout)return!1;let f=function(a,b,c){let d=ce(a,c),{scroll:e}=b;return e&&(cb(d.x,e.offset.x),cb(d.y,e.offset.y)),d}(d,e.root,this.visualElement.getTransformPagePoint()),g=(a=e.layout.layoutBox,{x:dX(a.x,f.x),y:dX(a.y,f.y)});if(c){let a=c(function({x:a,y:b}){return{top:b.min,right:a.max,bottom:b.max,left:a.min}}(g));this.hasMutatedConstraints=!!a,a&&(g=b3(a))}return g}startAnimation(a){let{drag:b,dragMomentum:c,dragElastic:d,dragTransition:e,dragSnapToOrigin:f,onDragTransitionEnd:g}=this.getProps(),h=this.constraints||{};return Promise.all(dO(g=>{if(!d0(g,b,this.currentDirection))return;let i=h&&h[g]||{};f&&(i={min:0,max:0});let j={type:"inertia",velocity:c?a[g]:0,bounceStiffness:d?200:1e6,bounceDamping:d?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...e,...i};return this.startAxisValueAnimation(g,j)})).then(g)}startAxisValueAnimation(a,b){let c=this.getAxisMotionValue(a);return Z(this.visualElement,a),c.start(b$(a,c,0,b,this.visualElement,!1))}stopAnimation(){dO(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){dO(a=>this.getAxisMotionValue(a).animation?.pause())}getAnimationState(a){return this.getAxisMotionValue(a).animation?.state}getAxisMotionValue(a){let b=`_drag${a.toUpperCase()}`,c=this.visualElement.getProps();return c[b]||this.visualElement.getValue(a,(c.initial?c.initial[a]:void 0)||0)}snapToCursor(a){dO(b=>{let{drag:c}=this.getProps();if(!d0(b,c,this.currentDirection))return;let{projection:d}=this.visualElement,e=this.getAxisMotionValue(b);if(d&&d.layout){let{min:c,max:f}=d.layout.layoutBox[b];e.set(a[b]-v(c,f,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:a,dragConstraints:b}=this.getProps(),{projection:c}=this.visualElement;if(!dq(b)||!c||!this.constraints)return;this.stopAnimation();let d={x:0,y:0};dO(a=>{let b=this.getAxisMotionValue(a);if(b&&!1!==this.constraints){let c=b.get();d[a]=function(a,b){let c=.5,d=dI(a),e=dI(b);return e>d?c=u(b.min,b.max-d,a.min):d>e&&(c=u(a.min,a.max-e,b.min)),g(0,1,c)}({min:c,max:c},this.constraints[a])}});let{transformTemplate:e}=this.visualElement.getProps();this.visualElement.current.style.transform=e?e({},""):"none",c.root&&c.root.updateScroll(),c.updateLayout(),this.resolveConstraints(),dO(b=>{if(!d0(b,a,null))return;let c=this.getAxisMotionValue(b),{min:e,max:f}=this.constraints[b];c.set(v(e,f,d[b]))})}addListeners(){if(!this.visualElement.current)return;d$.set(this.visualElement,this);let a=dH(this.visualElement.current,"pointerdown",a=>{let{drag:b,dragListener:c=!0}=this.getProps();b&&c&&this.start(a)}),b=()=>{let{dragConstraints:a}=this.getProps();dq(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",b);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),I.read(b);let e=dE(window,"resize",()=>this.scalePositionWithinConstraints()),f=c.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b})=>{this.isDragging&&b&&(dO(b=>{let c=this.getAxisMotionValue(b);c&&(this.originPoint[b]+=a[b].translate,c.set(c.get()+a[b].translate))}),this.visualElement.render())});return()=>{e(),a(),d(),f&&f()}}getProps(){let a=this.visualElement.getProps(),{drag:b=!1,dragDirectionLock:c=!1,dragPropagation:d=!1,dragConstraints:e=!1,dragElastic:f=.35,dragMomentum:g=!0}=a;return{...a,drag:b,dragDirectionLock:c,dragPropagation:d,dragConstraints:e,dragElastic:f,dragMomentum:g}}}function d0(a,b,c){return(!0===b||b===a)&&(null===c||c===a)}let d1=a=>(b,c)=>{a&&I.postRender(()=>a(b,c))};var d2=c4;let d3={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function d4(a,b){return b.max===b.min?0:a/(b.max-b.min)*100}let d5={correct:(a,b)=>{if(!b.target)return a;if("string"==typeof a)if(!ax.test(a))return a;else a=parseFloat(a);let c=d4(a,b.target.x),d=d4(a,b.target.y);return`${c}% ${d}%`}},d6=!1;class d7 extends d2.Component{componentDidMount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c,layoutId:d}=this.props,{projection:e}=a;for(let a in d9)cU[a]=d9[a],af(a)&&(cU[a].isCSSVariable=!0);e&&(b.group&&b.group.add(e),c&&c.register&&d&&c.register(e),d6&&e.root.didUpdate(),e.addEventListener("animationComplete",()=>{this.safeToRemove()}),e.setOptions({...e.options,onExitComplete:()=>this.safeToRemove()})),d3.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){let{layoutDependency:b,visualElement:c,drag:d,isPresent:e}=this.props,{projection:f}=c;return f&&(f.isPresent=e,d6=!0,d||a.layoutDependency!==b||void 0===b||a.isPresent!==e?f.willUpdate():this.safeToRemove(),a.isPresent!==e&&(e?f.promote():f.relegate()||I.postRender(()=>{let a=f.getStack();a&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),cx.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c}=this.props,{projection:d}=a;d6=!0,d&&(d.scheduleCheckAfterUnmount(),b&&b.group&&b.group.remove(d),c&&c.deregister&&c.deregister(d))}safeToRemove(){let{safeToRemove:a}=this.props;a&&a()}render(){return null}}function d8(a){let[b,c]=function(a=!0){let b=(0,c4.useContext)(di);if(null===b)return[!0,null];let{isPresent:c,onExitComplete:d,register:e}=b,f=(0,c4.useId)();(0,c4.useEffect)(()=>{if(a)return e(f)},[a]);let g=(0,c4.useCallback)(()=>a&&d&&d(f),[f,d,a]);return!c&&d?[!1,g]:[!0]}(),d=(0,d2.useContext)(c8);return(0,c7.jsx)(d7,{...a,layoutGroup:d,switchLayoutGroup:(0,d2.useContext)(dr),isPresent:b,safeToRemove:c})}let d9={borderRadius:{...d5,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:d5,borderTopRightRadius:d5,borderBottomLeftRadius:d5,borderBottomRightRadius:d5,boxShadow:{correct:(a,{treeScale:b,projectionDelta:c})=>{let d=aL.parse(a);if(d.length>5)return a;let e=aL.createTransformer(a),f=+("number"!=typeof d[0]),g=c.x.scale*b.x,h=c.y.scale*b.y;d[0+f]/=g,d[1+f]/=h;let i=v(g,h,.5);return"number"==typeof d[2+f]&&(d[2+f]/=i),"number"==typeof d[3+f]&&(d[3+f]/=i),e(d)}}},ea=(a,b)=>a.depth-b.depth;class eb{constructor(){this.children=[],this.isDirty=!1}add(a){c(this.children,a),this.isDirty=!0}remove(a){d(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(ea),this.isDirty=!1,this.children.forEach(a)}}let ec=["TopLeft","TopRight","BottomLeft","BottomRight"],ed=ec.length,ee=a=>"string"==typeof a?parseFloat(a):a,ef=a=>"number"==typeof a||ax.test(a);function eg(a,b){return void 0!==a[b]?a[b]:a.borderRadius}let eh=ej(0,.5,bb),ei=ej(.5,.95,D);function ej(a,b,c){return d=>d<a?0:d>b?1:c(u(a,b,d))}function ek(a,b){a.min=b.min,a.max=b.max}function el(a,b){ek(a.x,b.x),ek(a.y,b.y)}function em(a,b){a.translate=b.translate,a.scale=b.scale,a.originPoint=b.originPoint,a.origin=b.origin}function en(a,b,c,d,e){return a-=b,a=d+1/c*(a-d),void 0!==e&&(a=d+1/e*(a-d)),a}function eo(a,b,[c,d,e],f,g){!function(a,b=0,c=1,d=.5,e,f=a,g=a){if(aw.test(b)&&(b=parseFloat(b),b=v(g.min,g.max,b/100)-g.min),"number"!=typeof b)return;let h=v(f.min,f.max,d);a===f&&(h-=b),a.min=en(a.min,b,c,h,e),a.max=en(a.max,b,c,h,e)}(a,b[c],b[d],b[e],b.scale,f,g)}let ep=["x","scaleX","originX"],eq=["y","scaleY","originY"];function er(a,b,c,d){eo(a.x,b,ep,c?c.x:void 0,d?d.x:void 0),eo(a.y,b,eq,c?c.y:void 0,d?d.y:void 0)}function es(a){return 0===a.translate&&1===a.scale}function et(a){return es(a.x)&&es(a.y)}function eu(a,b){return a.min===b.min&&a.max===b.max}function ev(a,b){return Math.round(a.min)===Math.round(b.min)&&Math.round(a.max)===Math.round(b.max)}function ew(a,b){return ev(a.x,b.x)&&ev(a.y,b.y)}function ex(a){return dI(a.x)/dI(a.y)}function ey(a,b){return a.translate===b.translate&&a.scale===b.scale&&a.originPoint===b.originPoint}class ez{constructor(){this.members=[]}add(a){c(this.members,a),a.scheduleRender()}remove(a){if(d(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){let a=this.members[this.members.length-1];a&&this.promote(a)}}relegate(a){let b,c=this.members.findIndex(b=>a===b);if(0===c)return!1;for(let a=c;a>=0;a--){let c=this.members[a];if(!1!==c.isPresent){b=c;break}}return!!b&&(this.promote(b),!0)}promote(a,b){let c=this.lead;if(a!==c&&(this.prevLead=c,this.lead=a,a.show(),c)){c.instance&&c.scheduleRender(),a.scheduleRender(),a.resumeFrom=c,b&&(a.resumeFrom.preserveOpacity=!0),c.snapshot&&(a.snapshot=c.snapshot,a.snapshot.latestValues=c.animationValues||c.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);let{crossfade:d}=a.options;!1===d&&c.hide()}}exitAnimationComplete(){this.members.forEach(a=>{let{options:b,resumingFrom:c}=a;b.onExitComplete&&b.onExitComplete(),c&&c.options.onExitComplete&&c.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let eA={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},eB=["","X","Y","Z"],eC=0;function eD(a,b,c,d){let{latestValues:e}=b;e[a]&&(c[a]=e[a],b.setStaticValue(a,0),d&&(d[a]=0))}function eE({attachResizeListener:a,defaultParent:b,measureScroll:c,checkIsScrollRoot:d,resetTransform:e}){return class{constructor(a={},c=b?.()){this.id=eC++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,G.value&&(eA.nodes=eA.calculatedTargetDeltas=eA.calculatedProjections=0),this.nodes.forEach(eH),this.nodes.forEach(eO),this.nodes.forEach(eP),this.nodes.forEach(eI),G.addProjectionMetrics&&G.addProjectionMetrics(eA)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new eb)}addEventListener(a,b){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new P),this.eventHandlers.get(a).add(b)}notifyListeners(a,...b){let c=this.eventHandlers.get(a);c&&c.notify(...b)}hasListeners(a){return this.eventHandlers.has(a)}mount(b){if(this.instance)return;this.isSVG=b1(b)&&!b2(b),this.instance=b;let{layoutId:c,layout:d,visualElement:e}=this.options;if(e&&!e.current&&e.mount(b),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(d||c)&&(this.isLayoutDirty=!0),a){let c,d=0,e=()=>this.root.updateBlockedByResize=!1;I.read(()=>{d=window.innerWidth}),a(b,()=>{let a=window.innerWidth;a!==d&&(d=a,this.root.updateBlockedByResize=!0,c&&c(),c=function(a,b){let c=R.now(),d=({timestamp:b})=>{let e=b-c;e>=250&&(J(d),a(e-250))};return I.setup(d,!0),()=>J(d)}(e,250),d3.hasAnimatedSinceResize&&(d3.hasAnimatedSinceResize=!1,this.nodes.forEach(eN)))})}c&&this.root.registerSharedNode(c,this),!1!==this.options.animate&&e&&(c||d)&&this.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b,hasRelativeLayoutChanged:c,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let f=this.options.transition||e.getDefaultTransition()||eV,{onLayoutAnimationStart:g,onLayoutAnimationComplete:h}=e.getProps(),i=!this.targetLayout||!ew(this.targetLayout,d),j=!b&&c;if(this.options.layoutRoot||this.resumeFrom||j||b&&(i||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let b={...C(f,"layout"),onPlay:g,onComplete:h};(e.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(a,j)}else b||eN(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),J(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(eQ),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function a(b){if(b.hasCheckedOptimisedAppear=!0,b.root===b)return;let{visualElement:c}=b.options;if(!c)return;let d=c.props[_];if(window.MotionHasOptimisedAnimation(d,"transform")){let{layout:a,layoutId:c}=b.options;window.MotionCancelOptimisedAnimation(d,"transform",I,!(a||c))}let{parent:e}=b;e&&!e.hasCheckedOptimisedAppear&&a(e)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){let b=this.path[a];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}let{layoutId:b,layout:c}=this.options;if(void 0===b&&!c)return;let d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(eK);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(eL);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(eM),this.nodes.forEach(eF),this.nodes.forEach(eG)):this.nodes.forEach(eL),this.clearAllSnapshots();let a=R.now();K.delta=g(0,1e3/60,a-K.timestamp),K.timestamp=a,K.isProcessing=!0,L.update.process(K),L.preRender.process(K),L.render.process(K),K.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,cx.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(eJ),this.sharedNodes.forEach(eR)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,I.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){I.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||dI(this.snapshot.measuredBox.x)||dI(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=cD(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:b}=this.options;b&&b.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let b=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(b=!1),b&&this.instance){let b=d(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:b,offset:c(this.instance),wasRoot:this.scroll?this.scroll.isRoot:b}}}resetTransform(){if(!e)return;let a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,b=this.projectionDelta&&!et(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,f=d!==this.prevTransformTemplateValue;a&&this.instance&&(b||b6(this.latestValues)||f)&&(e(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){var b;let c=this.measurePageBox(),d=this.removeElementScroll(c);return a&&(d=this.removeTransform(d)),eY((b=d).x),eY(b.y),{animationId:this.root.animationId,measuredBox:c,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return cD();let b=a.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(e$))){let{scroll:a}=this.root;a&&(cb(b.x,a.offset.x),cb(b.y,a.offset.y))}return b}removeElementScroll(a){let b=cD();if(el(b,a),this.scroll?.wasRoot)return b;for(let c=0;c<this.path.length;c++){let d=this.path[c],{scroll:e,options:f}=d;d!==this.root&&e&&f.layoutScroll&&(e.wasRoot&&el(b,a),cb(b.x,e.offset.x),cb(b.y,e.offset.y))}return b}applyTransform(a,b=!1){let c=cD();el(c,a);for(let a=0;a<this.path.length;a++){let d=this.path[a];!b&&d.options.layoutScroll&&d.scroll&&d!==d.root&&cd(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),b6(d.latestValues)&&cd(c,d.latestValues)}return b6(this.latestValues)&&cd(c,this.latestValues),c}removeTransform(a){let b=cD();el(b,a);for(let a=0;a<this.path.length;a++){let c=this.path[a];if(!c.instance||!b6(c.latestValues))continue;b5(c.latestValues)&&c.updateSnapshot();let d=cD();el(d,c.measurePageBox()),er(b,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return b6(this.latestValues)&&er(b,this.latestValues),b}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:void 0===a.crossfade||a.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==K.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){let b=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=b.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=b.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=b.isSharedProjectionDirty);let c=!!this.resumingFrom||this!==b;if(!(a||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:d,layoutId:e}=this.options;if(this.layout&&(d||e)){if(this.resolvedRelativeTargetAt=K.timestamp,!this.targetDelta&&!this.relativeTarget){let a=this.getClosestProjectingParent();a&&a.layout&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=cD(),this.relativeTargetOrigin=cD(),dN(this.relativeTargetOrigin,this.layout.layoutBox,a.layout.layoutBox),el(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=cD(),this.targetWithTransforms=cD()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var f,g,h;this.forceRelativeParentToResolveTarget(),f=this.target,g=this.relativeTarget,h=this.relativeParent.target,dL(f.x,g.x,h.x),dL(f.y,g.y,h.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):el(this.target,this.layout.layoutBox),ca(this.target,this.targetDelta)):el(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let a=this.getClosestProjectingParent();a&&!!a.resumingFrom==!!this.resumingFrom&&!a.options.layoutScroll&&a.target&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=cD(),this.relativeTargetOrigin=cD(),dN(this.relativeTargetOrigin,this.target,a.target),el(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}G.value&&eA.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||b5(this.parent.latestValues)||b7(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let a=this.getLead(),b=!!this.resumingFrom||this!==a,c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),b&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===K.timestamp&&(c=!1),c)return;let{layout:d,layoutId:e}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||e))return;el(this.layoutCorrected,this.layout.layoutBox);let f=this.treeScale.x,g=this.treeScale.y;!function(a,b,c,d=!1){let e,f,g=c.length;if(g){b.x=b.y=1;for(let h=0;h<g;h++){f=(e=c[h]).projectionDelta;let{visualElement:g}=e.options;(!g||!g.props.style||"contents"!==g.props.style.display)&&(d&&e.options.layoutScroll&&e.scroll&&e!==e.root&&cd(a,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),f&&(b.x*=f.x.scale,b.y*=f.y.scale,ca(a,f)),d&&b6(e.latestValues)&&cd(a,e.latestValues))}b.x<1.0000000000001&&b.x>.999999999999&&(b.x=1),b.y<1.0000000000001&&b.y>.999999999999&&(b.y=1)}}(this.layoutCorrected,this.treeScale,this.path,b),a.layout&&!a.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=cD());let{target:h}=a;if(!h){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(em(this.prevProjectionDelta.x,this.projectionDelta.x),em(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),dK(this.projectionDelta,this.layoutCorrected,h,this.latestValues),this.treeScale.x===f&&this.treeScale.y===g&&ey(this.projectionDelta.x,this.prevProjectionDelta.x)&&ey(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h)),G.value&&eA.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.visualElement?.scheduleRender(),a){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=cB(),this.projectionDelta=cB(),this.projectionDeltaWithTransform=cB()}setAnimationOrigin(a,b=!1){let c,d=this.snapshot,e=d?d.latestValues:{},f={...this.latestValues},g=cB();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!b;let h=cD(),i=(d?d.source:void 0)!==(this.layout?this.layout.source:void 0),j=this.getStack(),k=!j||j.members.length<=1,l=!!(i&&!k&&!0===this.options.crossfade&&!this.path.some(eU));this.animationProgress=0,this.mixTargetDelta=b=>{let d=b/1e3;if(eS(g.x,a.x,d),eS(g.y,a.y,d),this.setTargetDelta(g),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var j,m,n,o,p,q;dN(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),n=this.relativeTarget,o=this.relativeTargetOrigin,p=h,q=d,eT(n.x,o.x,p.x,q),eT(n.y,o.y,p.y,q),c&&(j=this.relativeTarget,m=c,eu(j.x,m.x)&&eu(j.y,m.y))&&(this.isProjectionDirty=!1),c||(c=cD()),el(c,this.relativeTarget)}i&&(this.animationValues=f,function(a,b,c,d,e,f){e?(a.opacity=v(0,c.opacity??1,eh(d)),a.opacityExit=v(b.opacity??1,0,ei(d))):f&&(a.opacity=v(b.opacity??1,c.opacity??1,d));for(let e=0;e<ed;e++){let f=`border${ec[e]}Radius`,g=eg(b,f),h=eg(c,f);(void 0!==g||void 0!==h)&&(g||(g=0),h||(h=0),0===g||0===h||ef(g)===ef(h)?(a[f]=Math.max(v(ee(g),ee(h),d),0),(aw.test(h)||aw.test(g))&&(a[f]+="%")):a[f]=h)}(b.rotate||c.rotate)&&(a.rotate=v(b.rotate||0,c.rotate||0,d))}(f,e,this.latestValues,d,l,k)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=d},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(J(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=I.update(()=>{d3.hasAnimatedSinceResize=!0,ad.layout++,this.motionValue||(this.motionValue=U(0)),this.currentAnimation=c3(this.motionValue,[0,1e3],{...a,velocity:0,isSync:!0,onUpdate:b=>{this.mixTargetDelta(b),a.onUpdate&&a.onUpdate(b)},onStop:()=>{ad.layout--},onComplete:()=>{ad.layout--,a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:b,target:c,layout:d,latestValues:e}=a;if(b&&c&&d){if(this!==a&&this.layout&&d&&eZ(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||cD();let b=dI(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+b;let d=dI(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+d}el(b,c),cd(b,e),dK(this.projectionDeltaWithTransform,this.layoutCorrected,b,e)}}registerSharedNode(a,b){this.sharedNodes.has(a)||this.sharedNodes.set(a,new ez),this.sharedNodes.get(a).add(b);let c=b.options.initialPromotionConfig;b.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(b):void 0})}isLead(){let a=this.getStack();return!a||a.lead===this}getLead(){let{layoutId:a}=this.options;return a&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:a}=this.options;return a?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:b,preserveFollowOpacity:c}={}){let d=this.getStack();d&&d.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),b&&this.setOptions({transition:b})}relegate(){let a=this.getStack();return!!a&&a.relegate(this)}resetSkewAndRotation(){let{visualElement:a}=this.options;if(!a)return;let b=!1,{latestValues:c}=a;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(b=!0),!b)return;let d={};c.z&&eD("z",a,d,this.animationValues);for(let b=0;b<eB.length;b++)eD(`rotate${eB[b]}`,a,d,this.animationValues),eD(`skew${eB[b]}`,a,d,this.animationValues);for(let b in a.render(),d)a.setStaticValue(b,d[b]),this.animationValues&&(this.animationValues[b]=d[b]);a.scheduleRender()}applyProjectionStyles(a,b){if(!this.instance||this.isSVG)return;if(!this.isVisible){a.visibility="hidden";return}let c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,a.visibility="",a.opacity="",a.pointerEvents=dk(b?.pointerEvents)||"",a.transform=c?c(this.latestValues,""):"none";return}let d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){this.options.layoutId&&(a.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,a.pointerEvents=dk(b?.pointerEvents)||""),this.hasProjected&&!b6(this.latestValues)&&(a.transform=c?c({},""):"none",this.hasProjected=!1);return}a.visibility="";let e=d.animationValues||d.latestValues;this.applyTransformsToTarget();let f=function(a,b,c){let d="",e=a.x.translate/b.x,f=a.y.translate/b.y,g=c?.z||0;if((e||f||g)&&(d=`translate3d(${e}px, ${f}px, ${g}px) `),(1!==b.x||1!==b.y)&&(d+=`scale(${1/b.x}, ${1/b.y}) `),c){let{transformPerspective:a,rotate:b,rotateX:e,rotateY:f,skewX:g,skewY:h}=c;a&&(d=`perspective(${a}px) ${d}`),b&&(d+=`rotate(${b}deg) `),e&&(d+=`rotateX(${e}deg) `),f&&(d+=`rotateY(${f}deg) `),g&&(d+=`skewX(${g}deg) `),h&&(d+=`skewY(${h}deg) `)}let h=a.x.scale*b.x,i=a.y.scale*b.y;return(1!==h||1!==i)&&(d+=`scale(${h}, ${i})`),d||"none"}(this.projectionDeltaWithTransform,this.treeScale,e);c&&(f=c(e,f)),a.transform=f;let{x:g,y:h}=this.projectionDelta;for(let b in a.transformOrigin=`${100*g.origin}% ${100*h.origin}% 0`,d.animationValues?a.opacity=d===this?e.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:e.opacityExit:a.opacity=d===this?void 0!==e.opacity?e.opacity:"":void 0!==e.opacityExit?e.opacityExit:0,cU){if(void 0===e[b])continue;let{correct:c,applyTo:g,isCSSVariable:h}=cU[b],i="none"===f?e[b]:c(e[b],d);if(g){let b=g.length;for(let c=0;c<b;c++)a[g[c]]=i}else h?this.options.visualElement.renderState.vars[b]=i:a[b]=i}this.options.layoutId&&(a.pointerEvents=d===this?dk(b?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>a.currentAnimation?.stop()),this.root.nodes.forEach(eK),this.root.sharedNodes.clear()}}}function eF(a){a.updateLayout()}function eG(a){let b=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&b&&a.hasListeners("didUpdate")){let{layoutBox:c,measuredBox:d}=a.layout,{animationType:e}=a.options,f=b.source!==a.layout.source;"size"===e?dO(a=>{let d=f?b.measuredBox[a]:b.layoutBox[a],e=dI(d);d.min=c[a].min,d.max=d.min+e}):eZ(e,b.layoutBox,c)&&dO(d=>{let e=f?b.measuredBox[d]:b.layoutBox[d],g=dI(c[d]);e.max=e.min+g,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[d].max=a.relativeTarget[d].min+g)});let g=cB();dK(g,c,b.layoutBox);let h=cB();f?dK(h,a.applyTransform(d,!0),b.measuredBox):dK(h,c,b.layoutBox);let i=!et(g),j=!1;if(!a.resumeFrom){let d=a.getClosestProjectingParent();if(d&&!d.resumeFrom){let{snapshot:e,layout:f}=d;if(e&&f){let g=cD();dN(g,b.layoutBox,e.layoutBox);let h=cD();dN(h,c,f.layoutBox),ew(g,h)||(j=!0),d.options.layoutRoot&&(a.relativeTarget=h,a.relativeTargetOrigin=g,a.relativeParent=d)}}}a.notifyListeners("didUpdate",{layout:c,snapshot:b,delta:h,layoutDelta:g,hasLayoutChanged:i,hasRelativeLayoutChanged:j})}else if(a.isLead()){let{onExitComplete:b}=a.options;b&&b()}a.options.transition=void 0}function eH(a){G.value&&eA.nodes++,a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function eI(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function eJ(a){a.clearSnapshot()}function eK(a){a.clearMeasurements()}function eL(a){a.isLayoutDirty=!1}function eM(a){let{visualElement:b}=a.options;b&&b.getProps().onBeforeLayoutMeasure&&b.notify("BeforeLayoutMeasure"),a.resetTransform()}function eN(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function eO(a){a.resolveTargetDelta()}function eP(a){a.calcProjection()}function eQ(a){a.resetSkewAndRotation()}function eR(a){a.removeLeadSnapshot()}function eS(a,b,c){a.translate=v(b.translate,0,c),a.scale=v(b.scale,1,c),a.origin=b.origin,a.originPoint=b.originPoint}function eT(a,b,c,d){a.min=v(b.min,c.min,d),a.max=v(b.max,c.max,d)}function eU(a){return a.animationValues&&void 0!==a.animationValues.opacityExit}let eV={duration:.45,ease:[.4,0,.1,1]},eW=a=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),eX=eW("applewebkit/")&&!eW("chrome/")?Math.round:D;function eY(a){a.min=eX(a.min),a.max=eX(a.max)}function eZ(a,b,c){return"position"===a||"preserve-aspect"===a&&!(.2>=Math.abs(ex(b)-ex(c)))}function e$(a){return a!==a.root&&a.scroll?.wasRoot}let e_=eE({attachResizeListener:(a,b)=>dE(a,"resize",b),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),e0={current:void 0},e1=eE({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!e0.current){let a=new e_({});a.mount(window),a.setOptions({layoutScroll:!0}),e0.current=a}return e0.current},resetTransform:(a,b)=>{a.style.transform=void 0!==b?b:"none"},checkIsScrollRoot:a=>"fixed"===window.getComputedStyle(a).position});function e2(a,b){let c=A(a),d=new AbortController;return[c,{passive:!0,...b,signal:d.signal},()=>d.abort()]}function e3(a){return!("touch"===a.pointerType||dD.x||dD.y)}function e4(a,b,c){let{props:d}=a;a.animationState&&d.whileHover&&a.animationState.setActive("whileHover","Start"===c);let e=d["onHover"+c];e&&I.postRender(()=>e(b,dG(b)))}let e5=(a,b)=>!!b&&(a===b||e5(a,b.parentElement)),e6=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),e7=new WeakSet;function e8(a){return b=>{"Enter"===b.key&&a(b)}}function e9(a,b){a.dispatchEvent(new PointerEvent("pointer"+b,{isPrimary:!0,bubbles:!0}))}function fa(a){return dF(a)&&!(dD.x||dD.y)}function fb(a,b,c){let{props:d}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&d.whileTap&&a.animationState.setActive("whileTap","Start"===c);let e=d["onTap"+("End"===c?"":c)];e&&I.postRender(()=>e(b,dG(b)))}let fc=new WeakMap,fd=new WeakMap,fe=a=>{let b=fc.get(a.target);b&&b(a)},ff=a=>{a.forEach(fe)},fg={some:0,all:1},fh=function(a,b){if("undefined"==typeof Proxy)return ds;let c=new Map,d=(c,d)=>ds(c,d,a,b);return new Proxy((a,b)=>d(a,b),{get:(e,f)=>"create"===f?d:(c.has(f)||c.set(f,ds(f,void 0,a,b)),c.get(f))})}({animation:{Feature:class extends dB{constructor(a){super(a),a.animationState||(a.animationState=function(a){let b=b=>Promise.all(b.map(({animation:b,options:c})=>(function(a,b,c={}){let d;if(a.notify("AnimationStart",b),Array.isArray(b))d=Promise.all(b.map(b=>du(a,b,c)));else if("string"==typeof b)d=du(a,b,c);else{let e="function"==typeof b?Y(a,b,c.custom):b;d=Promise.all(b_(a,e,c))}return d.then(()=>{a.notify("AnimationComplete",b)})})(a,b,c))),c=dA(),d=!0,e=b=>(c,d)=>{let e=Y(a,d,"exit"===b?a.presenceContext?.custom:void 0);if(e){let{transition:a,transitionEnd:b,...d}=e;c={...c,...d,...b}}return c};function f(f){let{props:g}=a,h=function a(b){if(!b)return;if(!b.isControllingVariants){let c=b.parent&&a(b.parent)||{};return void 0!==b.props.initial&&(c.initial=b.props.initial),c}let c={};for(let a=0;a<dw;a++){let d=cJ[a],e=b.props[d];(cH(e)||!1===e)&&(c[d]=e)}return c}(a.parent)||{},i=[],j=new Set,k={},l=1/0;for(let b=0;b<dy;b++){var m,n;let o=dx[b],p=c[o],q=void 0!==g[o]?g[o]:h[o],r=cH(q),s=o===f?p.isActive:null;!1===s&&(l=b);let t=q===h[o]&&q!==g[o]&&r;if(t&&d&&a.manuallyAnimateOnMount&&(t=!1),p.protectedKeys={...k},!p.isActive&&null===s||!q&&!p.prevProp||cG(q)||"boolean"==typeof q)continue;let u=(m=p.prevProp,"string"==typeof(n=q)?n!==m:!!Array.isArray(n)&&!dv(n,m)),v=u||o===f&&p.isActive&&!t&&r||b>l&&r,w=!1,x=Array.isArray(q)?q:[q],y=x.reduce(e(o),{});!1===s&&(y={});let{prevResolvedValues:z={}}=p,A={...z,...y},B=b=>{v=!0,j.has(b)&&(w=!0,j.delete(b)),p.needsAnimating[b]=!0;let c=a.getValue(b);c&&(c.liveStyle=!1)};for(let a in A){let b=y[a],c=z[a];if(!k.hasOwnProperty(a))(V(b)&&V(c)?dv(b,c):b===c)?void 0!==b&&j.has(a)?B(a):p.protectedKeys[a]=!0:null!=b?B(a):j.add(a)}p.prevProp=q,p.prevResolvedValues=y,p.isActive&&(k={...k,...y}),d&&a.blockInitialAnimation&&(v=!1);let C=t&&u,D=!C||w;v&&D&&i.push(...x.map(b=>{let c={type:o};if("string"==typeof b&&d&&!C&&a.manuallyAnimateOnMount&&a.parent){let{parent:d}=a,e=Y(d,b);if(d.enteringChildren&&e){let{delayChildren:b}=e.transition||{};c.delay=dt(d.enteringChildren,a,b)}}return{animation:b,options:c}}))}if(j.size){let b={};if("boolean"!=typeof g.initial){let c=Y(a,Array.isArray(g.initial)?g.initial[0]:g.initial);c&&c.transition&&(b.transition=c.transition)}j.forEach(c=>{let d=a.getBaseTarget(c),e=a.getValue(c);e&&(e.liveStyle=!0),b[c]=d??null}),i.push({animation:b})}let o=!!i.length;return d&&(!1===g.initial||g.initial===g.animate)&&!a.manuallyAnimateOnMount&&(o=!1),d=!1,o?b(i):Promise.resolve()}return{animateChanges:f,setActive:function(b,d){if(c[b].isActive===d)return Promise.resolve();a.variantChildren?.forEach(a=>a.animationState?.setActive(b,d)),c[b].isActive=d;let e=f(b);for(let a in c)c[a].protectedKeys={};return e},setAnimateFunction:function(c){b=c(a)},getState:()=>c,reset:()=>{c=dA(),d=!0}}}(a))}updateAnimationControlsSubscription(){let{animate:a}=this.node.getProps();cG(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:a}=this.node.getProps(),{animate:b}=this.node.prevProps||{};a!==b&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends dB{constructor(){super(...arguments),this.id=dC++}update(){if(!this.node.presenceContext)return;let{isPresent:a,onExitComplete:b}=this.node.presenceContext,{isPresent:c}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===c)return;let d=this.node.animationState.setActive("exit",!a);b&&!a&&d.then(()=>{b(this.id)})}mount(){let{register:a,onExitComplete:b}=this.node.presenceContext||{};b&&b(this.id),a&&(this.unmount=a(this.id))}unmount(){}}},inView:{Feature:class extends dB{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:a={}}=this.node.getProps(),{root:b,margin:c,amount:d="some",once:e}=a,f={root:b?b.current:void 0,rootMargin:c,threshold:"number"==typeof d?d:fg[d]},g=a=>{let{isIntersecting:b}=a;if(this.isInView===b||(this.isInView=b,e&&!b&&this.hasEnteredView))return;b&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",b);let{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=b?c:d;f&&f(a)};var h=this.node.current;let i=function({root:a,...b}){let c=a||document;fd.has(c)||fd.set(c,{});let d=fd.get(c),e=JSON.stringify(b);return d[e]||(d[e]=new IntersectionObserver(ff,{root:a,...b})),d[e]}(f);return fc.set(h,g),i.observe(h),()=>{fc.delete(h),i.unobserve(h)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:a,prevProps:b}=this.node;["amount","margin","root"].some(function({viewport:a={}},{viewport:b={}}={}){return c=>a[c]!==b[c]}(a,b))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends dB{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=e2(a,c),g=a=>{let d=a.currentTarget;if(!fa(a))return;e7.add(d);let f=b(d,a),g=(a,b)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",i),e7.has(d)&&e7.delete(d),fa(a)&&"function"==typeof f&&f(a,{success:b})},h=a=>{g(a,d===window||d===document||c.useGlobalTarget||e5(d,a.target))},i=a=>{g(a,!1)};window.addEventListener("pointerup",h,e),window.addEventListener("pointercancel",i,e)};return d.forEach(a=>{(c.useGlobalTarget?window:a).addEventListener("pointerdown",g,e),b0(a)&&"offsetHeight"in a&&(a.addEventListener("focus",a=>((a,b)=>{let c=a.currentTarget;if(!c)return;let d=e8(()=>{if(e7.has(c))return;e9(c,"down");let a=e8(()=>{e9(c,"up")});c.addEventListener("keyup",a,b),c.addEventListener("blur",()=>e9(c,"cancel"),b)});c.addEventListener("keydown",d,b),c.addEventListener("blur",()=>c.removeEventListener("keydown",d),b)})(a,e)),e6.has(a.tagName)||-1!==a.tabIndex||a.hasAttribute("tabindex")||(a.tabIndex=0))}),f}(a,(a,b)=>(fb(this.node,b,"Start"),(a,{success:b})=>fb(this.node,a,b?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends dB{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(b){a=!0}a&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ac(dE(this.node.current,"focus",()=>this.onFocus()),dE(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends dB{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=e2(a,c),g=a=>{if(!e3(a))return;let{target:c}=a,d=b(c,a);if("function"!=typeof d||!c)return;let f=a=>{e3(a)&&(d(a),c.removeEventListener("pointerleave",f))};c.addEventListener("pointerleave",f,e)};return d.forEach(a=>{a.addEventListener("pointerenter",g,e)}),f}(a,(a,b)=>(e4(this.node,b,"Start"),a=>e4(this.node,a,"End"))))}unmount(){}}},pan:{Feature:class extends dB{constructor(){super(...arguments),this.removePointerDownListener=D}onPointerDown(a){this.session=new dR(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:dP(this.node)})}createPanHandlers(){let{onPanSessionStart:a,onPanStart:b,onPan:c,onPanEnd:d}=this.node.getProps();return{onSessionStart:d1(a),onStart:d1(b),onMove:c,onEnd:(a,b)=>{delete this.session,d&&I.postRender(()=>d(a,b))}}}mount(){this.removePointerDownListener=dH(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends dB{constructor(a){super(a),this.removeGroupControls=D,this.removeListeners=D,this.controls=new d_(a)}mount(){let{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||D}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:e1,MeasureLayout:d8},layout:{ProjectionNode:e1,MeasureLayout:d8}},(a,b)=>c6(a)?new c2(b):new cX(b,{allowProjection:a!==c4.Fragment}))},60806,a=>{"use strict";a.s(["InfiniteSlider",()=>W],60806);var b=a.i(69720);class c{constructor(a){this.stop=()=>this.runAll("stop"),this.animations=a.filter(Boolean)}get finished(){return Promise.all(this.animations.map(a=>a.finished))}getAll(a){return this.animations[0][a]}setAll(a,b){for(let c=0;c<this.animations.length;c++)this.animations[c][a]=b}attachTimeline(a){let b=this.animations.map(b=>b.attachTimeline(a));return()=>{b.forEach((a,b)=>{a&&a(),this.animations[b].stop()})}}get time(){return this.getAll("time")}set time(a){this.setAll("time",a)}get speed(){return this.getAll("speed")}set speed(a){this.setAll("speed",a)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let a=0;for(let b=0;b<this.animations.length;b++)a=Math.max(a,this.animations[b].duration);return a}runAll(a){this.animations.forEach(b=>b[a]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class d extends c{then(a,b){return this.finished.finally(a).then(()=>{})}}var e=a.i(8305),f=a.i(92732),g=a.i(71948),h=a.i(92472),i=a.i(42415),j=a.i(86057),k=a.i(52077),l=a.i(8564),m=a.i(82923),n=a.i(66273),o=a.i(47091);function p(a,b){return(0,o.isEasingArray)(a)?a[((a,b,c)=>{let d=b-a;return((c-a)%d+d)%d+a})(0,a.length,b)]:a}var q=a.i(69949);function r(a){return"object"==typeof a&&!Array.isArray(a)}function s(a,b,c,d){return"string"==typeof a&&r(b)?(0,q.resolveElements)(a,c,d):a instanceof NodeList?Array.from(a):Array.isArray(a)?a:[a]}function t(a,b,c,d){return"number"==typeof b?b:b.startsWith("-")||b.startsWith("+")?Math.max(0,a+parseFloat(b)):"<"===b?c:b.startsWith("<")?Math.max(0,c+parseFloat(b.slice(1))):d.get(b)??a}var u=a.i(3810);function v(a,b){return a.at!==b.at?a.at-b.at:null===a.value?1:null===b.value?-1:0}function w(a,b){return b.has(a)||b.set(a,{}),b.get(a)}function x(a,b){return b[a]||(b[a]=[]),b[a]}let y=a=>"number"==typeof a,z=a=>a.every(y);var A=a.i(55259),B=a.i(6109),C=a.i(35731),D=a.i(48309),E=a.i(40284),F=a.i(79667),G=a.i(75476);class H extends G.VisualElement{constructor(){super(...arguments),this.type="object"}readValueFromInstance(a,b){if(b in a){let c=a[b];if("string"==typeof c||"number"==typeof c)return c}}getBaseTargetFromProps(){}removeValueFromRenderState(a,b){delete b.output[a]}measureInstanceViewportBox(){return(0,F.createBox)()}build(a,b){Object.assign(a.output,b)}renderInstance(a,{output:b}){Object.assign(a,b)}sortInstanceNodePosition(){return 0}}var I=a.i(80644);function J(a){let b={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},c=(0,C.isSVGElement)(a)&&!(0,D.isSVGSVGElement)(a)?new I.SVGVisualElement(b):new E.HTMLVisualElement(b);c.mount(a),A.visualElementStore.set(a,c)}function K(a){let b=new H({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});b.mount(a),A.visualElementStore.set(a,b)}var L=a.i(1889);function M(a,b,c,d){let e=[];if((0,g.isMotionValue)(a)||"number"==typeof a||"string"==typeof a&&!r(b))e.push((0,L.animateSingleValue)(a,r(b)&&b.default||b,c&&c.default||c));else{let f=s(a,b,d),g=f.length;(0,n.invariant)(!!g,"No valid elements provided.","no-valid-elements");for(let a=0;a<g;a++){let d=f[a];(0,n.invariant)(null!==d,"You're trying to perform an animation on null. Ensure that selectors are correctly finding elements and refs are correctly hydrated.","animate-null");let h=d instanceof Element?J:K;A.visualElementStore.has(d)||h(d);let i=A.visualElementStore.get(d),j={...c};"delay"in j&&"function"==typeof j.delay&&(j.delay=j.delay(a,g)),e.push(...(0,B.animateTarget)(i,{...b,transition:j},{}))}}return e}let N=function(a){return function(b,c,o){let q=[],r=new d(q=Array.isArray(b)&&b.some(Array.isArray)?function(a,b,c){let d=[];return(function(a,{defaultTransition:b={},...c}={},d,f){let o=b.duration||.3,q=new Map,r=new Map,y={},A=new Map,B=0,C=0,D=0;for(let c=0;c<a.length;c++){let l=a[c];if("string"==typeof l){A.set(l,C);continue}if(!Array.isArray(l)){A.set(l.name,t(C,l.at,B,A));continue}let[q,v,G={}]=l;void 0!==G.at&&(C=t(C,G.at,B,A));let H=0,I=(a,c,d,g=0,l=0)=>{var q;let r=Array.isArray(q=a)?q:[q],{delay:s=0,times:t=(0,h.defaultOffset)(r),type:v="keyframes",repeat:w,repeatType:x,repeatDelay:y=0,...A}=c,{ease:B=b.ease||"easeOut",duration:E}=c,F="function"==typeof s?s(g,l):s,G=r.length,I=(0,i.isGenerator)(v)?v:f?.[v||"keyframes"];if(G<=2&&I){let a=100;2===G&&z(r)&&(a=Math.abs(r[1]-r[0]));let b={...A};void 0!==E&&(b.duration=(0,m.secondsToMilliseconds)(E));let c=(0,j.createGeneratorEasing)(b,a,I);B=c.ease,E=c.duration}E??(E=o);let J=C+F;1===t.length&&0===t[0]&&(t[1]=1);let K=t.length-r.length;if(K>0&&(0,k.fillOffset)(t,K),1===r.length&&r.unshift(null),w){(0,n.invariant)(w<20,"Repeat count too high, must be less than 20","repeat-count-high");E*=w+1;let a=[...r],b=[...t],c=[...B=Array.isArray(B)?[...B]:[B]];for(let d=0;d<w;d++){r.push(...a);for(let e=0;e<a.length;e++)t.push(b[e]+(d+1)),B.push(0===e?"linear":p(c,e-1))}for(let a=0;a<t.length;a++)t[a]=t[a]/(w+1)}let L=J+E;!function(a,b,c,d,f,g){for(let b=0;b<a.length;b++){let c=a[b];c.at>f&&c.at<g&&((0,e.removeItem)(a,c),b--)}for(let e=0;e<b.length;e++)a.push({value:b[e],at:(0,u.mixNumber)(f,g,d[e]),easing:p(c,e)})}(d,r,B,t,J,L),H=Math.max(F+E,H),D=Math.max(L,D)};if((0,g.isMotionValue)(q))I(v,G,x("default",w(q,r)));else{let a=s(q,v,d,y),b=a.length;for(let c=0;c<b;c++){let d=w(a[c],r);for(let a in v){var E,F;I(v[a],(E=G,F=a,E&&E[F]?{...E,...E[F]}:{...E}),x(a,d),c,b)}}}B=C,C+=H}return r.forEach((a,d)=>{for(let e in a){let f=a[e];f.sort(v);let g=[],h=[],i=[];for(let a=0;a<f.length;a++){let{at:b,value:c,easing:d}=f[a];g.push(c),h.push((0,l.progress)(0,D,b)),i.push(d||"easeOut")}0!==h[0]&&(h.unshift(0),g.unshift(g[0]),i.unshift("easeInOut")),1!==h[h.length-1]&&(h.push(1),g.push(null)),q.has(d)||q.set(d,{keyframes:{},transition:{}});let j=q.get(d);j.keyframes[e]=g,j.transition[e]={...b,duration:D,ease:i,times:h,...c}}}),q})(a,b,c,{spring:f.spring}).forEach(({keyframes:a,transition:b},c)=>{d.push(...M(c,a,b))}),d}(b,c,void 0):M(b,c,o,void 0));return a&&(a.animations.push(r),r.finished.then(()=>{(0,e.removeItem)(a.animations,r)})),r}}();var O=a.i(48283),P=a.i(42933),Q=a.i(29611),R=a.i(12559),S=a.i(69053);function T(a,b){let c;return(...d)=>{window.clearTimeout(c),c=window.setTimeout(()=>a(...d),b)}}let U=["x","y","top","bottom","left","right","width","height"];var V=a.i(97895);function W({children:a,gap:c=16,speed:d=100,speedOnHover:e,direction:f="horizontal",reverse:g=!1,className:h}){let[i,j]=(0,Q.useState)(d),[k,{width:l,height:m}]=function({debounce:a,scroll:b,polyfill:c,offsetSize:d}={debounce:0,scroll:!1,offsetSize:!1}){var e,f,g;let h=c||class{};if(!h)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[i,j]=(0,Q.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),k=(0,Q.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:i,orientationHandler:null}),l=a?"number"==typeof a?a:a.scroll:null,m=a?"number"==typeof a?a:a.resize:null,n=(0,Q.useRef)(!1);(0,Q.useEffect)(()=>(n.current=!0,()=>void(n.current=!1)));let[o,p,q]=(0,Q.useMemo)(()=>{let a=()=>{let a,b;if(!k.current.element)return;let{left:c,top:e,width:f,height:g,bottom:h,right:i,x:l,y:m}=k.current.element.getBoundingClientRect(),o={left:c,top:e,width:f,height:g,bottom:h,right:i,x:l,y:m};k.current.element instanceof HTMLElement&&d&&(o.height=k.current.element.offsetHeight,o.width=k.current.element.offsetWidth),Object.freeze(o),n.current&&(a=k.current.lastBounds,b=o,!U.every(c=>a[c]===b[c]))&&j(k.current.lastBounds=o)};return[a,m?T(a,m):a,l?T(a,l):a]},[j,d,l,m]);function r(){k.current.scrollContainers&&(k.current.scrollContainers.forEach(a=>a.removeEventListener("scroll",q,!0)),k.current.scrollContainers=null),k.current.resizeObserver&&(k.current.resizeObserver.disconnect(),k.current.resizeObserver=null),k.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",k.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",k.current.orientationHandler))}function s(){k.current.element&&(k.current.resizeObserver=new h(q),k.current.resizeObserver.observe(k.current.element),b&&k.current.scrollContainers&&k.current.scrollContainers.forEach(a=>a.addEventListener("scroll",q,{capture:!0,passive:!0})),k.current.orientationHandler=()=>{q()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",k.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",k.current.orientationHandler))}return e=q,f=!!b,(0,Q.useEffect)(()=>{if(f)return window.addEventListener("scroll",e,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",e,!0)},[e,f]),g=p,(0,Q.useEffect)(()=>(window.addEventListener("resize",g),()=>void window.removeEventListener("resize",g)),[g]),(0,Q.useEffect)(()=>{r(),s()},[b,q,p]),(0,Q.useEffect)(()=>r,[]),[a=>{a&&a!==k.current.element&&(r(),k.current.element=a,k.current.scrollContainers=function a(b){let c=[];if(!b||b===document.body)return c;let{overflow:d,overflowX:e,overflowY:f}=window.getComputedStyle(b);return[d,e,f].some(a=>"auto"===a||"scroll"===a)&&c.push(b),[...c,...a(b.parentElement)]}(a),s())},i,o]}(),n=function(a){let b=(0,S.useConstant)(()=>(0,P.motionValue)(0)),{isStatic:c}=(0,Q.useContext)(R.MotionConfigContext);if(c){let[,a]=(0,Q.useState)(0);(0,Q.useEffect)(()=>b.on("change",a),[])}return b}(0),[o,p]=(0,Q.useState)(!1),[q,r]=(0,Q.useState)(0);(0,Q.useEffect)(()=>{let a,b=("horizontal"===f?l:m)+c,d=g?-b/2:0,e=g?0:-b/2,h=Math.abs(e-d);if(o){let b=Math.abs(n.get()-e);a=N(n,[n.get(),e],{ease:"linear",duration:b/i,onComplete:()=>{p(!1),r(a=>a+1)}})}else a=N(n,[d,e],{ease:"linear",duration:h/i,repeat:1/0,repeatType:"loop",repeatDelay:0,onRepeat:()=>{n.set(d)}});return a?.stop},[q,n,i,l,m,c,o,f,g]);let s=e?{onHoverStart:()=>{p(!0),j(e)},onHoverEnd:()=>{p(!0),j(d)}}:{};return(0,b.jsx)("div",{className:(0,V.cn)("overflow-hidden",h),children:(0,b.jsxs)(O.motion.div,{className:"flex w-max",ref:k,style:{..."horizontal"===f?{x:n}:{y:n},gap:`${c}px`,flexDirection:"horizontal"===f?"row":"column"},...s,children:[a,a]})})}},28813,a=>{"use strict";a.s(["GRADIENT_ANGLES",()=>e,"ProgressiveBlur",()=>f]);var b=a.i(69720),c=a.i(48283),d=a.i(97895);let e={top:0,right:90,bottom:180,left:270};function f({direction:a="bottom",blurLayers:f=8,className:g,blurIntensity:h=.25,...i}){let j=Math.max(f,2),k=1/(f+1);return(0,b.jsx)("div",{className:(0,d.cn)("relative",g),children:Array.from({length:j}).map((d,f)=>{let g=e[a],j=[f*k,(f+1)*k,(f+2)*k,(f+3)*k].map((a,b)=>`rgba(255, 255, 255, ${+(1===b||2===b)}) ${100*a}%`),l=`linear-gradient(${g}deg, ${j.join(", ")})`;return(0,b.jsx)(c.motion.div,{className:"pointer-events-none absolute inset-0 rounded-[inherit]",style:{maskImage:l,WebkitMaskImage:l,backdropFilter:`blur(${f*h}px)`,WebkitBackdropFilter:`blur(${f*h}px)`},...i},f)})})}},50911,a=>{"use strict";a.s(["default",()=>P],50911);var b=a.i(69720),c=a.i(29611);function d(a){return"[object Object]"===Object.prototype.toString.call(a)||Array.isArray(a)}function e(a,b){let c=Object.keys(a),f=Object.keys(b);return c.length===f.length&&JSON.stringify(Object.keys(a.breakpoints||{}))===JSON.stringify(Object.keys(b.breakpoints||{}))&&c.every(c=>{let f=a[c],g=b[c];return"function"==typeof f?`${f}`==`${g}`:d(f)&&d(g)?e(f,g):f===g})}function f(a){return a.concat().sort((a,b)=>a.name>b.name?1:-1).map(a=>a.options)}function g(a){return"number"==typeof a}function h(a){return"string"==typeof a}function i(a){return"boolean"==typeof a}function j(a){return"[object Object]"===Object.prototype.toString.call(a)}function k(a){return Math.abs(a)}function l(a){return Math.sign(a)}function m(a){return q(a).map(Number)}function n(a){return a[o(a)]}function o(a){return Math.max(0,a.length-1)}function p(a,b=0){return Array.from(Array(a),(a,c)=>b+c)}function q(a){return Object.keys(a)}function r(a,b){return void 0!==b.MouseEvent&&a instanceof b.MouseEvent}function s(){let a=[],b={add:function(c,d,e,f={passive:!0}){let g;return"addEventListener"in c?(c.addEventListener(d,e,f),g=()=>c.removeEventListener(d,e,f)):(c.addListener(e),g=()=>c.removeListener(e)),a.push(g),b},clear:function(){a=a.filter(a=>a())}};return b}function t(a=0,b=0){let c=k(a-b);function d(c){return c<a||c>b}return{length:c,max:b,min:a,constrain:function(c){return d(c)?c<a?a:b:c},reachedAny:d,reachedMax:function(a){return a>b},reachedMin:function(b){return b<a},removeOffset:function(a){return c?a-c*Math.ceil((a-b)/c):a}}}function u(a){let b=a;function c(a){return g(a)?a:a.get()}return{get:function(){return b},set:function(a){b=c(a)},add:function(a){b+=c(a)},subtract:function(a){b-=c(a)}}}function v(a,b){let c="x"===a.scroll?function(a){return`translate3d(${a}px,0px,0px)`}:function(a){return`translate3d(0px,${a}px,0px)`},d=b.style,e=null,f=!1;return{clear:function(){!f&&(d.transform="",b.getAttribute("style")||b.removeAttribute("style"))},to:function(b){if(f)return;let g=Math.round(100*a.direction(b))/100;g!==e&&(d.transform=c(g),e=g)},toggleActive:function(a){f=!a}}}let w={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function x(a,b,c){let d,e,f,y,z,A=a.ownerDocument,B=A.defaultView,C=function(a){function b(a,b){return function a(b,c){return[b,c].reduce((b,c)=>(q(c).forEach(d=>{let e=b[d],f=c[d],g=j(e)&&j(f);b[d]=g?a(e,f):f}),b),{})}(a,b||{})}return{mergeOptions:b,optionsAtMedia:function(c){let d=c.breakpoints||{},e=q(d).filter(b=>a.matchMedia(b).matches).map(a=>d[a]).reduce((a,c)=>b(a,c),{});return b(c,e)},optionsMediaQueries:function(b){return b.map(a=>q(a.breakpoints||{})).reduce((a,b)=>a.concat(b),[]).map(a.matchMedia)}}}(B),D=(z=[],{init:function(a,b){return(z=b.filter(({options:a})=>!1!==C.optionsAtMedia(a).active)).forEach(b=>b.init(a,C)),b.reduce((a,b)=>Object.assign(a,{[b.name]:b}),{})},destroy:function(){z=z.filter(a=>a.destroy())}}),E=s(),F=function(){let a,b={},c={init:function(b){a=b},emit:function(d){return(b[d]||[]).forEach(b=>b(a,d)),c},off:function(a,d){return b[a]=(b[a]||[]).filter(a=>a!==d),c},on:function(a,d){return b[a]=(b[a]||[]).concat([d]),c},clear:function(){b={}}};return c}(),{mergeOptions:G,optionsAtMedia:H,optionsMediaQueries:I}=C,{on:J,off:K,emit:L}=F,M=!1,N=G(w,x.globalOptions),O=G(N),P=[];function Q(b,c){if(M)return;O=H(N=G(N,b)),P=c||P;let{container:j,slides:w}=O;f=(h(j)?a.querySelector(j):j)||a.children[0];let x=h(w)?f.querySelectorAll(w):w;y=[].slice.call(x||f.children),d=function b(c){let d=function(a,b,c,d,e,f,j){let w,x,{align:y,axis:z,direction:A,startIndex:B,loop:C,duration:D,dragFree:E,dragThreshold:F,inViewThreshold:G,slidesToScroll:H,skipSnaps:I,containScroll:J,watchResize:K,watchSlides:L,watchDrag:M,watchFocus:N}=f,O={measure:function(a){let{offsetTop:b,offsetLeft:c,offsetWidth:d,offsetHeight:e}=a;return{top:b,right:c+d,bottom:b+e,left:c,width:d,height:e}}},P=O.measure(b),Q=c.map(O.measure),R=function(a,b){let c="rtl"===b,d="y"===a,e=!d&&c?-1:1;return{scroll:d?"y":"x",cross:d?"x":"y",startEdge:d?"top":c?"right":"left",endEdge:d?"bottom":c?"left":"right",measureSize:function(a){let{height:b,width:c}=a;return d?b:c},direction:function(a){return a*e}}}(z,A),S=R.measureSize(P),T={measure:function(a){return a/100*S}},U=function(a,b){let c={start:function(){return 0},center:function(a){return(b-a)/2},end:function(a){return b-a}};return{measure:function(d,e){return h(a)?c[a](d):a(b,d,e)}}}(y,S),V=!C&&!!J,{slideSizes:W,slideSizesWithGaps:X,startGap:Y,endGap:Z}=function(a,b,c,d,e,f){let{measureSize:g,startEdge:h,endEdge:i}=a,j=c[0]&&e,l=function(){if(!j)return 0;let a=c[0];return k(b[h]-a[h])}(),m=j?parseFloat(f.getComputedStyle(n(d)).getPropertyValue(`margin-${i}`)):0,p=c.map(g),q=c.map((a,b,c)=>{let d=b===o(c);return b?d?p[b]+m:c[b+1][h]-a[h]:p[b]+l}).map(k);return{slideSizes:p,slideSizesWithGaps:q,startGap:l,endGap:m}}(R,P,Q,c,C||!!J,e),$=function(a,b,c,d,e,f,h,i,j){let{startEdge:l,endEdge:p,direction:q}=a,r=g(c);return{groupSlides:function(a){return r?m(a).filter(a=>a%c==0).map(b=>a.slice(b,b+c)):a.length?m(a).reduce((c,g,j)=>{let m=n(c)||0,r=g===o(a),s=e[l]-f[m][l],t=e[l]-f[g][p],u=d||0!==m?0:q(h),v=k(t-(!d&&r?q(i):0)-(s+u));return j&&v>b+2&&c.push(g),r&&c.push(a.length),c},[]).map((b,c,d)=>{let e=Math.max(d[c-1]||0);return a.slice(e,b)}):[]}}}(R,S,H,C,P,Q,Y,Z,0),{snaps:_,snapsAligned:aa}=function(a,b,c,d,e){let{startEdge:f,endEdge:g}=a,{groupSlides:h}=e,i=h(d).map(a=>n(a)[g]-a[0][f]).map(k).map(b.measure),j=d.map(a=>c[f]-a[f]).map(a=>-k(a)),l=h(j).map(a=>a[0]).map((a,b)=>a+i[b]);return{snaps:j,snapsAligned:l}}(R,U,P,Q,$),ab=-n(_)+n(X),{snapsContained:ac,scrollContainLimit:ad}=function(a,b,c,d,e){let f=t(-b+a,0),g=c.map((a,b)=>{let{min:d,max:e}=f,g=f.constrain(a),h=b===o(c);return b?h||function(a,b){return 1>=k(a-b)}(d,g)?d:function(a,b){return 1>=k(a-b)}(e,g)?e:g:e}).map(a=>parseFloat(a.toFixed(3))),h=function(){let a=g[0],b=n(g);return t(g.lastIndexOf(a),g.indexOf(b)+1)}();return{snapsContained:function(){if(b<=a+2)return[f.max];if("keepSnaps"===d)return g;let{min:c,max:e}=h;return g.slice(c,e)}(),scrollContainLimit:h}}(S,ab,aa,J,0),ae=V?ac:aa,{limit:af}=function(a,b,c){let d=b[0];return{limit:t(c?d-a:n(b),d)}}(ab,ae,C),ag=function a(b,c,d){let{constrain:e}=t(0,b),f=b+1,g=h(c);function h(a){return d?k((f+a)%f):e(a)}function i(){return a(b,g,d)}let j={get:function(){return g},set:function(a){return g=h(a),j},add:function(a){return i().set(g+a)},clone:i};return j}(o(ae),B,C),ah=ag.clone(),ai=m(c),aj=function(a,b,c,d){let e=s(),f=1e3/60,g=null,h=0,i=0;function j(a){if(!i)return;g||(g=a,c(),c());let e=a-g;for(g=a,h+=e;h>=f;)c(),h-=f;d(h/f),i&&(i=b.requestAnimationFrame(j))}function k(){b.cancelAnimationFrame(i),g=null,h=0,i=0}return{init:function(){e.add(a,"visibilitychange",()=>{a.hidden&&(g=null,h=0)})},destroy:function(){k(),e.clear()},start:function(){i||(i=b.requestAnimationFrame(j))},stop:k,update:c,render:d}}(d,e,()=>(({dragHandler:a,scrollBody:b,scrollBounds:c,options:{loop:d}})=>{d||c.constrain(a.pointerDown()),b.seek()})(ax),a=>(({scrollBody:a,translate:b,location:c,offsetLocation:d,previousLocation:e,scrollLooper:f,slideLooper:g,dragHandler:h,animation:i,eventHandler:j,scrollBounds:k,options:{loop:l}},m)=>{let n=a.settled(),o=!k.shouldConstrain(),p=l?n:n&&o,q=p&&!h.pointerDown();q&&i.stop();let r=c.get()*m+e.get()*(1-m);d.set(r),l&&(f.loop(a.direction()),g.loop()),b.to(d.get()),q&&j.emit("settle"),p||j.emit("scroll")})(ax,a)),ak=ae[ag.get()],al=u(ak),am=u(ak),an=u(ak),ao=u(ak),ap=function(a,b,c,d,e,f){let g=0,h=0,i=e,j=.68,m=a.get(),n=0;function o(a){return i=a,q}function p(a){return j=a,q}let q={direction:function(){return h},duration:function(){return i},velocity:function(){return g},seek:function(){let b=d.get()-a.get(),e=0;return i?(c.set(a),g+=b/i,g*=j,m+=g,a.add(g),e=m-n):(g=0,c.set(d),a.set(d),e=b),h=l(e),n=m,q},settled:function(){return .001>k(d.get()-b.get())},useBaseFriction:function(){return p(.68)},useBaseDuration:function(){return o(e)},useFriction:p,useDuration:o};return q}(al,an,am,ao,D,.68),aq=function(a,b,c,d,e){let{reachedAny:f,removeOffset:g,constrain:h}=d;function i(a){return a.concat().sort((a,b)=>k(a)-k(b))[0]}function j(b,d){let e=[b,b+c,b-c];if(!a)return b;if(!d)return i(e);let f=e.filter(a=>l(a)===d);return f.length?i(f):n(e)-c}return{byDistance:function(c,d){let i=e.get()+c,{index:l,distance:m}=function(c){let d=a?g(c):h(c),{index:e}=b.map((a,b)=>({diff:j(a-d,0),index:b})).sort((a,b)=>k(a.diff)-k(b.diff))[0];return{index:e,distance:d}}(i),n=!a&&f(i);if(!d||n)return{index:l,distance:c};let o=c+j(b[l]-m,0);return{index:l,distance:o}},byIndex:function(a,c){let d=j(b[a]-e.get(),c);return{index:a,distance:d}},shortcut:j}}(C,ae,ab,af,ao),ar=function(a,b,c,d,e,f,g){function h(e){let h=e.distance,i=e.index!==b.get();f.add(h),h&&(d.duration()?a.start():(a.update(),a.render(1),a.update())),i&&(c.set(b.get()),b.set(e.index),g.emit("select"))}return{distance:function(a,b){h(e.byDistance(a,b))},index:function(a,c){let d=b.clone().set(a);h(e.byIndex(d.get(),c))}}}(aj,ag,ah,ap,aq,ao,j),as=function(a){let{max:b,length:c}=a;return{get:function(a){return c?-((a-b)/c):0}}}(af),at=s(),au=function(a,b,c,d){let e,f={},g=null,h=null,i=!1;return{init:function(){e=new IntersectionObserver(a=>{i||(a.forEach(a=>{f[b.indexOf(a.target)]=a}),g=null,h=null,c.emit("slidesInView"))},{root:a.parentElement,threshold:d}),b.forEach(a=>e.observe(a))},destroy:function(){e&&e.disconnect(),i=!0},get:function(a=!0){if(a&&g)return g;if(!a&&h)return h;let b=q(f).reduce((b,c)=>{let d=parseInt(c),{isIntersecting:e}=f[d];return(a&&e||!a&&!e)&&b.push(d),b},[]);return a&&(g=b),a||(h=b),b}}}(b,c,j,G),{slideRegistry:av}=function(a,b,c,d,e,f){let{groupSlides:g}=e,{min:h,max:i}=d;return{slideRegistry:function(){let d=g(f);return 1===c.length?[f]:a&&"keepSnaps"!==b?d.slice(h,i).map((a,b,c)=>{let d=b===o(c);return b?d?p(o(f)-n(c)[0]+1,n(c)[0]):a:p(n(c[0])+1)}):d}()}}(V,J,ae,ad,$,ai),aw=function(a,b,c,d,e,f,h,j){let k={passive:!0,capture:!0},l=0;function m(a){"Tab"===a.code&&(l=new Date().getTime())}return{init:function(n){j&&(f.add(document,"keydown",m,!1),b.forEach((b,m)=>{f.add(b,"focus",b=>{(i(j)||j(n,b))&&function(b){if(new Date().getTime()-l>10)return;h.emit("slideFocusStart"),a.scrollLeft=0;let f=c.findIndex(a=>a.includes(b));g(f)&&(e.useDuration(0),d.index(f,0),h.emit("slideFocus"))}(m)},k)}))}}}(a,c,av,ar,ap,at,j,N),ax={ownerDocument:d,ownerWindow:e,eventHandler:j,containerRect:P,slideRects:Q,animation:aj,axis:R,dragHandler:function(a,b,c,d,e,f,g,h,j,m,n,o,p,q,u,v,w,x,y){let{cross:z,direction:A}=a,B=["INPUT","SELECT","TEXTAREA"],C={passive:!1},D=s(),E=s(),F=t(50,225).constrain(q.measure(20)),G={mouse:300,touch:400},H={mouse:500,touch:600},I=u?43:25,J=!1,K=0,L=0,M=!1,N=!1,O=!1,P=!1;function Q(a){if(!r(a,d)&&a.touches.length>=2)return R(a);let b=f.readPoint(a),c=f.readPoint(a,z),g=k(b-K),i=k(c-L);if(!N&&!P&&(!a.cancelable||!(N=g>i)))return R(a);let j=f.pointerMove(a);g>v&&(O=!0),m.useFriction(.3).useDuration(.75),h.start(),e.add(A(j)),a.preventDefault()}function R(a){let b=n.byDistance(0,!1).index!==o.get(),c=f.pointerUp(a)*(u?H:G)[P?"mouse":"touch"],d=function(a,b){let c=o.add(-1*l(a)),d=n.byDistance(a,!u).distance;return u||k(a)<F?d:w&&b?.5*d:n.byIndex(c.get(),0).distance}(A(c),b),e=function(a,b){var c,d;if(0===a||0===b||k(a)<=k(b))return 0;let e=(c=k(a),d=k(b),k(c-d));return k(e/a)}(c,d);N=!1,M=!1,E.clear(),m.useDuration(I-10*e).useFriction(.68+e/50),j.distance(d,!u),P=!1,p.emit("pointerUp")}function S(a){O&&(a.stopPropagation(),a.preventDefault(),O=!1)}return{init:function(a){y&&D.add(b,"dragstart",a=>a.preventDefault(),C).add(b,"touchmove",()=>void 0,C).add(b,"touchend",()=>void 0).add(b,"touchstart",h).add(b,"mousedown",h).add(b,"touchcancel",R).add(b,"contextmenu",R).add(b,"click",S,!0);function h(h){(i(y)||y(a,h))&&function(a){let h=r(a,d);if((P=h,O=u&&h&&!a.buttons&&J,J=k(e.get()-g.get())>=2,!h||0===a.button)&&!function(a){let b=a.nodeName||"";return B.includes(b)}(a.target)){M=!0,f.pointerDown(a),m.useFriction(0).useDuration(0),e.set(g);let d=P?c:b;E.add(d,"touchmove",Q,C).add(d,"touchend",R).add(d,"mousemove",Q,C).add(d,"mouseup",R),K=f.readPoint(a),L=f.readPoint(a,z),p.emit("pointerDown")}}(h)}},destroy:function(){D.clear(),E.clear()},pointerDown:function(){return M}}}(R,a,d,e,ao,function(a,b){let c,d;function e(a){return a.timeStamp}function f(c,d){let e=d||a.scroll,f=`client${"x"===e?"X":"Y"}`;return(r(c,b)?c:c.touches[0])[f]}return{pointerDown:function(a){return c=a,d=a,f(a)},pointerMove:function(a){let b=f(a)-f(d),g=e(a)-e(c)>170;return d=a,g&&(c=a),b},pointerUp:function(a){if(!c||!d)return 0;let b=f(d)-f(c),g=e(a)-e(c),h=e(a)-e(d)>170,i=b/g;return g&&!h&&k(i)>.1?i:0},readPoint:f}}(R,e),al,aj,ar,ap,aq,ag,j,T,E,F,I,0,M),eventStore:at,percentOfView:T,index:ag,indexPrevious:ah,limit:af,location:al,offsetLocation:an,previousLocation:am,options:f,resizeHandler:function(a,b,c,d,e,f,g){let h,j,l=[a].concat(d),m=[],n=!1;function o(a){return e.measureSize(g.measure(a))}return{init:function(e){f&&(j=o(a),m=d.map(o),h=new ResizeObserver(c=>{(i(f)||f(e,c))&&function(c){for(let f of c){if(n)return;let c=f.target===a,g=d.indexOf(f.target),h=c?j:m[g];if(k(o(c?a:d[g])-h)>=.5){e.reInit(),b.emit("resize");break}}}(c)}),c.requestAnimationFrame(()=>{l.forEach(a=>h.observe(a))}))},destroy:function(){n=!0,h&&h.disconnect()}}}(b,j,e,c,R,K,O),scrollBody:ap,scrollBounds:function(a,b,c,d,e){let f=e.measure(10),g=e.measure(50),h=t(.1,.99),i=!1;function j(){return!i&&!!a.reachedAny(c.get())&&!!a.reachedAny(b.get())}return{shouldConstrain:j,constrain:function(e){if(!j())return;let i=a.reachedMin(b.get())?"min":"max",l=k(a[i]-b.get()),m=c.get()-b.get(),n=h.constrain(l/g);c.subtract(m*n),!e&&k(m)<f&&(c.set(a.constrain(c.get())),d.useDuration(25).useBaseFriction())},toggleActive:function(a){i=!a}}}(af,an,ao,ap,T),scrollLooper:function(a,b,c,d){let{reachedMin:e,reachedMax:f}=t(b.min+.1,b.max+.1);return{loop:function(b){if(!(1===b?f(c.get()):-1===b&&e(c.get())))return;let g=-1*b*a;d.forEach(a=>a.add(g))}}}(ab,af,an,[al,an,am,ao]),scrollProgress:as,scrollSnapList:ae.map(as.get),scrollSnaps:ae,scrollTarget:aq,scrollTo:ar,slideLooper:function(a,b,c,d,e,f,g,h,i){let j=m(e),k=m(e).reverse(),l=p(o(k,g[0]),c,!1).concat(p(o(j,b-g[0]-1),-c,!0));function n(a,b){return a.reduce((a,b)=>a-e[b],b)}function o(a,b){return a.reduce((a,c)=>n(a,b)>0?a.concat([c]):a,[])}function p(e,g,j){let k=f.map((a,c)=>({start:a-d[c]+.5+g,end:a+b-.5+g}));return e.map(b=>{let d=j?0:-c,e=j?c:0,f=k[b][j?"end":"start"];return{index:b,loopPoint:f,slideLocation:u(-1),translate:v(a,i[b]),target:()=>h.get()>f?d:e}})}return{canLoop:function(){return l.every(({index:a})=>.1>=n(j.filter(b=>b!==a),b))},clear:function(){l.forEach(a=>a.translate.clear())},loop:function(){l.forEach(a=>{let{target:b,translate:c,slideLocation:d}=a,e=b();e!==d.get()&&(c.to(e),d.set(e))})},loopPoints:l}}(R,S,ab,W,X,_,ae,an,c),slideFocus:aw,slidesHandler:(x=!1,{init:function(a){L&&(w=new MutationObserver(b=>{!x&&(i(L)||L(a,b))&&function(b){for(let c of b)if("childList"===c.type){a.reInit(),j.emit("slidesChanged");break}}(b)})).observe(b,{childList:!0})},destroy:function(){w&&w.disconnect(),x=!0}}),slidesInView:au,slideIndexes:ai,slideRegistry:av,slidesToScroll:$,target:ao,translate:v(R,b)};return ax}(a,f,y,A,B,c,F);return c.loop&&!d.slideLooper.canLoop()?b(Object.assign({},c,{loop:!1})):d}(O),I([N,...P.map(({options:a})=>a)]).forEach(a=>E.add(a,"change",R)),O.active&&(d.translate.to(d.location.get()),d.animation.init(),d.slidesInView.init(),d.slideFocus.init(V),d.eventHandler.init(V),d.resizeHandler.init(V),d.slidesHandler.init(V),d.options.loop&&d.slideLooper.loop(),f.offsetParent&&y.length&&d.dragHandler.init(V),e=D.init(V,P))}function R(a,b){let c=U();S(),Q(G({startIndex:c},a),b),F.emit("reInit")}function S(){d.dragHandler.destroy(),d.eventStore.clear(),d.translate.clear(),d.slideLooper.clear(),d.resizeHandler.destroy(),d.slidesHandler.destroy(),d.slidesInView.destroy(),d.animation.destroy(),D.destroy(),E.clear()}function T(a,b,c){O.active&&!M&&(d.scrollBody.useBaseFriction().useDuration(!0===b?0:O.duration),d.scrollTo.index(a,c||0))}function U(){return d.index.get()}let V={canScrollNext:function(){return d.index.add(1).get()!==U()},canScrollPrev:function(){return d.index.add(-1).get()!==U()},containerNode:function(){return f},internalEngine:function(){return d},destroy:function(){M||(M=!0,E.clear(),S(),F.emit("destroy"),F.clear())},off:K,on:J,emit:L,plugins:function(){return e},previousScrollSnap:function(){return d.indexPrevious.get()},reInit:R,rootNode:function(){return a},scrollNext:function(a){T(d.index.add(1).get(),a,-1)},scrollPrev:function(a){T(d.index.add(-1).get(),a,1)},scrollProgress:function(){return d.scrollProgress.get(d.offsetLocation.get())},scrollSnapList:function(){return d.scrollSnapList},scrollTo:T,selectedScrollSnap:U,slideNodes:function(){return y},slidesInView:function(){return d.slidesInView.get()},slidesNotInView:function(){return d.slidesInView.get(!1)}};return Q(b,c),setTimeout(()=>F.emit("init"),0),V}function y(a={},b=[]){let d=(0,c.useRef)(a),g=(0,c.useRef)(b),[h,i]=(0,c.useState)(),[j,k]=(0,c.useState)(),l=(0,c.useCallback)(()=>{h&&h.reInit(d.current,g.current)},[h]);return(0,c.useEffect)(()=>{e(d.current,a)||(d.current=a,l())},[a,l]),(0,c.useEffect)(()=>{!function(a,b){if(a.length!==b.length)return!1;let c=f(a),d=f(b);return c.every((a,b)=>e(a,d[b]))}(g.current,b)&&(g.current=b,l())},[b,l]),(0,c.useEffect)(()=>{i(void 0)},[j,i]),[k,h]}x.globalOptions=void 0,y.globalOptions=void 0;var z=a.i(51827);let A=(0,z.default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),B=(0,z.default)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var C=a.i(40695),D=a.i(97895);let E=c.default.createContext(null);function F(){let a=c.default.useContext(E);if(!a)throw Error("useCarousel must be used within a <Carousel />");return a}function G({orientation:a="horizontal",opts:d,setApi:e,plugins:f,className:g,children:h,...i}){let[j,k]=y({...d,axis:"horizontal"===a?"x":"y"},f),[l,m]=c.default.useState(!1),[n,o]=c.default.useState(!1),p=c.default.useCallback(a=>{a&&(m(a.canScrollPrev()),o(a.canScrollNext()))},[]),q=c.default.useCallback(()=>{k?.scrollPrev()},[k]),r=c.default.useCallback(()=>{k?.scrollNext()},[k]),s=c.default.useCallback(a=>{"ArrowLeft"===a.key?(a.preventDefault(),q()):"ArrowRight"===a.key&&(a.preventDefault(),r())},[q,r]);return c.default.useEffect(()=>{k&&e&&e(k)},[k,e]),c.default.useEffect(()=>{if(k)return p(k),k.on("reInit",p),k.on("select",p),()=>{k?.off("select",p)}},[k,p]),(0,b.jsx)(E.Provider,{value:{carouselRef:j,api:k,opts:d,orientation:a||(d?.axis==="y"?"vertical":"horizontal"),scrollPrev:q,scrollNext:r,canScrollPrev:l,canScrollNext:n},children:(0,b.jsx)("div",{"aria-roledescription":"carousel",className:(0,D.cn)("relative",g),"data-slot":"carousel",onKeyDownCapture:s,role:"region",...i,children:h})})}function H({className:a,...c}){let{carouselRef:d,orientation:e}=F();return(0,b.jsx)("div",{className:"overflow-hidden","data-slot":"carousel-content",ref:d,children:(0,b.jsx)("div",{className:(0,D.cn)("flex","horizontal"===e?"-ml-4":"-mt-4 flex-col",a),...c})})}function I({className:a,...c}){let{orientation:d}=F();return(0,b.jsx)("div",{"aria-roledescription":"slide",className:(0,D.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===d?"pl-4":"pt-4",a),"data-slot":"carousel-item",role:"group",...c})}function J({className:a,variant:c="outline",size:d="icon",...e}){let{orientation:f,scrollPrev:g,canScrollPrev:h}=F();return(0,b.jsxs)(C.Button,{className:(0,D.cn)("absolute size-8 rounded-full","horizontal"===f?"-left-12 -translate-y-1/2 top-1/2":"-top-12 -translate-x-1/2 left-1/2 rotate-90",a),"data-slot":"carousel-previous",disabled:!h,onClick:g,size:d,variant:c,...e,children:[(0,b.jsx)(A,{}),(0,b.jsx)("span",{className:"sr-only",children:"Previous slide"})]})}function K({className:a,variant:c="outline",size:d="icon",...e}){let{orientation:f,scrollNext:g,canScrollNext:h}=F();return(0,b.jsxs)(C.Button,{className:(0,D.cn)("absolute size-8 rounded-full","horizontal"===f?"-right-12 -translate-y-1/2 top-1/2":"-bottom-12 -translate-x-1/2 left-1/2 rotate-90",a),"data-slot":"carousel-next",disabled:!h,onClick:g,size:d,variant:c,...e,children:[(0,b.jsx)(B,{}),(0,b.jsx)("span",{className:"sr-only",children:"Next slide"})]})}var L=a.i(91300),M=a.i(70342);let N=({img:a,name:c,username:d,body:e})=>(0,b.jsxs)("figure",{className:(0,D.cn)("group flex max-h-72 w-fit flex-shrink-0 flex-col justify-between gap-8 rounded-xl bg-muted/40 p-4 px-5 opacity-60 shadow-sm transition-all duration-300 hover:bg-[url('/gradient.webp')] hover:bg-center hover:bg-cover hover:bg-no-repeat hover:opacity-100 hover:shadow-md md:w-[28rem] md:p-6 md:px-7 [&:hover_.avatar-ring]:ring-brandYellow [&:hover_.external-link-icon]:opacity-100 [&:hover_.social-icon-border]:border-brand-600"),children:[(0,b.jsxs)("blockquote",{className:"mt-2 font-medium text-md text-primary tracking-normal",children:[e,"One of the most delightful, inventive, powerful new interfaces I've tried in years. Actually feels like an AI native computer."]}),(0,b.jsxs)("div",{className:"relative flex w-full items-center gap-3 text-left transition-all duration-300 hover:scale-[98%]",children:[(0,b.jsx)(M.default,{alt:"",className:"size-12 rounded-full",height:"48",src:a,width:"48"}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsx)("figcaption",{className:"font-aeonik font-medium text-lg text-primary/80 tracking-normal",children:c}),(0,b.jsx)("p",{className:"font-aeonik font-normal text-md text-primary/50 tracking-tight",children:d})]})]})]});function O(){let{api:a}=F(),[d,e]=(0,c.useState)(0),[f,g]=(0,c.useState)(0);return(0,c.useEffect)(()=>{if(!a)return;g(a.scrollSnapList().length);let b=()=>{e(a.selectedScrollSnap())};return a.on("select",b),b(),()=>{a.off("select",b)}},[a]),(0,b.jsx)("div",{className:"mt-4 flex justify-center gap-2",children:Array.from({length:f}).map((c,e)=>(0,b.jsx)("button",{className:`h-2 rounded-full transition-all ${e===d?"w-8 bg-brand-600":"w-2 bg-muted"}`,onClick:()=>a?.scrollTo(e),type:"button"},e))})}function P(){return(0,b.jsx)("div",{className:"md:hidden",children:(0,b.jsxs)(G,{children:[(0,b.jsx)(H,{children:L.reviews.map(a=>(0,b.jsx)(I,{children:(0,b.jsx)(N,{...a})},a.name))}),(0,b.jsx)(J,{}),(0,b.jsx)(K,{}),(0,b.jsx)(O,{})]})})}}];

//# sourceMappingURL=_8387133b._.js.map