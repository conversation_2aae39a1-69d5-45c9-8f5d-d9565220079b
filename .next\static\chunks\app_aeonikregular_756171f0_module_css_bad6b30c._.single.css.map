{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/aeonikregular_756171f0.module.css"], "sourcesContent": ["@font-face {\n    font-family: 'aeonikRegular';\n    src: url('@vercel/turbopack-next/internal/font/local/font?{%22path%22:%22./fonts/fonnts.com-Aeonik-Regular.ttf%22,%22preload%22:true,%22has_size_adjust%22:true}') format('truetype');\n    font-display: swap;\n    \n}\n\n@font-face {\n    font-family: 'aeonikRegular Fallback';\n    src: local(\"Arial\");\n    ascent-override: 98.63%;\ndescent-override: 19.73%;\nline-gap-override: 0.00%;\nsize-adjust: 101.39%;\n\n}\n\n.className {\n    font-family: 'aeonikRegular', 'aeonikRegular Fallback';\n    \n}\n.variable {\n    --font-aeonik-regular: 'aeonikRegular', 'aeonikRegular Fallback';\n}\n\n"], "names": [], "mappings": "AAAA;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA"}}]}