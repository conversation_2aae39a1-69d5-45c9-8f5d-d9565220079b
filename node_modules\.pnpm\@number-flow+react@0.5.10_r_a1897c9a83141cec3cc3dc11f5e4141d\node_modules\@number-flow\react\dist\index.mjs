import * as React from 'react';
import { canAnimate, prefersReducedMotion } from 'number-flow/lite';
export * from 'number-flow/plugins';
export { a as NumberFlowElement, b as NumberFlowGroup, N as default } from './NumberFlow-client-48rw3j0J.mjs';

const useIsSupported = ()=>React.useSyncExternalStore(()=>()=>{}, ()=>canAnimate, ()=>false);
const usePrefersReducedMotion = ()=>React.useSyncExternalStore((cb)=>{
        prefersReducedMotion?.addEventListener('change', cb);
        return ()=>prefersReducedMotion?.removeEventListener('change', cb);
    }, ()=>prefersReducedMotion.matches, ()=>false);
function useCanAnimate({ respectMotionPreference = true } = {}) {
    const isSupported = useIsSupported();
    const reducedMotion = usePrefersReducedMotion();
    return isSupported && (!respectMotionPreference || !reducedMotion);
}

export { useCanAnimate, useIsSupported, usePrefersReducedMotion };
