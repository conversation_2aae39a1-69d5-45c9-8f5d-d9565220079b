"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const l=(e,n)=>e==null?n:n==null?e:Math.max(e,n),a=new WeakMap,f={onUpdate(e,n,o){if(a.set(o,void 0),!o.computedTrend)return;const s=n.integer.concat(n.fraction).filter(t=>t.type==="integer"||t.type==="fraction"),r=e.integer.concat(e.fraction).filter(t=>t.type==="integer"||t.type==="fraction"),u=s.find(t=>!r.find(c=>c.pos===t.pos&&c.value===t.value)),i=r.find(t=>!s.find(c=>t.pos===c.pos&&t.value===c.value));a.set(o,l(u==null?void 0:u.pos,i==null?void 0:i.pos))},getD<PERSON><PERSON>(e,n,o){const s=e-n,r=a.get(o.flow);if(!s&&r!=null&&r>=o.pos)return o.length*o.flow.computedTrend}};exports.continuous=f;
