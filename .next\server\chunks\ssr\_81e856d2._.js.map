{"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/cjs/_interop_require_default.cjs", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/utils/warn-once.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/image-blur-svg.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/image-config.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/get-img-props.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/image-component.js/__nextjs-internal-proxy.cjs", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/client/image-component.tsx", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/image-loader.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/image-external.tsx", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/image.js", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js/__nextjs-internal-proxy.cjs", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/client/app-dir/link.tsx", "turbopack:///[project]/components/ui/separator.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/components/layout/footer-subscribe-form.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/components/layout/site-header.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/components/layout/footer-cta.tsx", "turbopack:///[project]/components/layout/bg-cover.tsx", "turbopack:///[project]/public/gradient.webp.mjs (structured image object with data url)", "turbopack:///[project]/config/site.ts", "turbopack:///[project]/components/layout/site-footer.tsx", "turbopack:///[project]/app/(root)/layout.tsx", "turbopack:///[project]/node_modules/.pnpm/lucide-react@0.542.0_react@19.1.0/node_modules/lucide-react/src/icons/armchair.ts"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n", "let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n", "/**\n * A shared function, used on both client and server, to generate a SVG blur placeholder.\n */\nexport function getImageBlurSvg({\n  widthInt,\n  heightInt,\n  blurWidth,\n  blurHeight,\n  blurDataURL,\n  objectFit,\n}: {\n  widthInt?: number\n  heightInt?: number\n  blurWidth?: number\n  blurHeight?: number\n  blurDataURL: string\n  objectFit?: string\n}): string {\n  const std = 20\n  const svgWidth = blurWidth ? blurWidth * 40 : widthInt\n  const svgHeight = blurHeight ? blurHeight * 40 : heightInt\n\n  const viewBox =\n    svgWidth && svgHeight ? `viewBox='0 0 ${svgWidth} ${svgHeight}'` : ''\n  const preserveAspectRatio = viewBox\n    ? 'none'\n    : objectFit === 'contain'\n      ? 'xMidYMid'\n      : objectFit === 'cover'\n        ? 'xMidYMid slice'\n        : 'none'\n\n  return `%3Csvg xmlns='http://www.w3.org/2000/svg' ${viewBox}%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='${preserveAspectRatio}' style='filter: url(%23b);' href='${blurDataURL}'/%3E%3C/svg%3E`\n}\n", "export const VALID_LOADERS = [\n  'default',\n  'imgix',\n  'cloudinary',\n  'akamai',\n  'custom',\n] as const\n\nexport type LoaderValue = (typeof VALID_LOADERS)[number]\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\nexport type ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nexport type LocalPattern = {\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\nexport type RemotePattern = {\n  /**\n   * Must be `http` or `https`.\n   */\n  protocol?: 'http' | 'https'\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single subdomain.\n   * Double `**` matches any number of subdomains.\n   */\n  hostname: string\n\n  /**\n   * Can be literal port such as `8080` or empty string\n   * meaning no port.\n   */\n  port?: string\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\ntype ImageFormat = 'image/avif' | 'image/webp'\n\n/**\n * Image configurations\n *\n * @see [Image configuration options](https://nextjs.org/docs/api-reference/next/image#configuration-options)\n */\nexport type ImageConfigComplete = {\n  /** @see [Device sizes documentation](https://nextjs.org/docs/api-reference/next/image#device-sizes) */\n  deviceSizes: number[]\n\n  /** @see [Image sizing documentation](https://nextjs.org/docs/app/building-your-application/optimizing/images#image-sizing) */\n  imageSizes: number[]\n\n  /** @see [Image loaders configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader) */\n  loader: LoaderValue\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/app/api-reference/components/image#path) */\n  path: string\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/image#loader-configuration) */\n  loaderFile: string\n\n  /**\n   * @deprecated Use `remotePatterns` instead.\n   */\n  domains: string[]\n\n  /** @see [Disable static image import configuration](https://nextjs.org/docs/api-reference/next/image#disable-static-imports) */\n  disableStaticImages: boolean\n\n  /** @see [Cache behavior](https://nextjs.org/docs/api-reference/next/image#caching-behavior) */\n  minimumCacheTTL: number\n\n  /** @see [Acceptable formats](https://nextjs.org/docs/api-reference/next/image#acceptable-formats) */\n  formats: ImageFormat[]\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  dangerouslyAllowSVG: boolean\n\n  /** @see [Content Security Policy](https://nextjs.org/docs/api-reference/next/image#contentsecuritypolicy) */\n  contentSecurityPolicy: string\n\n  /** @see [Content Disposition Type](https://nextjs.org/docs/api-reference/next/image#contentdispositiontype) */\n  contentDispositionType: 'inline' | 'attachment'\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#remotepatterns) */\n  remotePatterns: Array<URL | RemotePattern>\n\n  /** @see [Local Patterns](https://nextjs.org/docs/api-reference/next/image#localPatterns) */\n  localPatterns: LocalPattern[] | undefined\n\n  /** @see [Qualities](https://nextjs.org/docs/api-reference/next/image#qualities) */\n  qualities: number[] | undefined\n\n  /** @see [Unoptimized](https://nextjs.org/docs/api-reference/next/image#unoptimized) */\n  unoptimized: boolean\n}\n\nexport type ImageConfig = Partial<ImageConfigComplete>\n\nexport const imageConfigDefault: ImageConfigComplete = {\n  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],\n  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],\n  path: '/_next/image',\n  loader: 'default',\n  loaderFile: '',\n  domains: [],\n  disableStaticImages: false,\n  minimumCacheTTL: 60,\n  formats: ['image/webp'],\n  dangerouslyAllowSVG: false,\n  contentSecurityPolicy: `script-src 'none'; frame-src 'none'; sandbox;`,\n  contentDispositionType: 'attachment',\n  localPatterns: undefined, // default: allow all local images\n  remotePatterns: [], // default: allow no remote images\n  qualities: undefined, // default: allow all qualities\n  unoptimized: false,\n}\n", "import { warnOnce } from './utils/warn-once'\nimport { getImageBlurSvg } from './image-blur-svg'\nimport { imageConfigDefault } from './image-config'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n  ImageLoaderPropsWithConfig,\n} from './image-config'\n\nimport type { CSSProperties, JSX } from 'react'\n\nexport interface StaticImageData {\n  src: string\n  height: number\n  width: number\n  blurDataURL?: string\n  blurWidth?: number\n  blurHeight?: number\n}\n\nexport interface StaticRequire {\n  default: StaticImageData\n}\n\nexport type StaticImport = StaticRequire | StaticImageData\n\nexport type ImageProps = Omit<\n  JSX.IntrinsicElements['img'],\n  'src' | 'srcSet' | 'ref' | 'alt' | 'width' | 'height' | 'loading'\n> & {\n  src: string | StaticImport\n  alt: string\n  width?: number | `${number}`\n  height?: number | `${number}`\n  fill?: boolean\n  loader?: ImageLoader\n  quality?: number | `${number}`\n  priority?: boolean\n  loading?: LoadingValue\n  placeholder?: PlaceholderValue\n  blurDataURL?: string\n  unoptimized?: boolean\n  overrideSrc?: string\n  /**\n   * @deprecated Use `onLoad` instead.\n   * @see https://nextjs.org/docs/app/api-reference/components/image#onload\n   */\n  onLoadingComplete?: OnLoadingComplete\n  /**\n   * @deprecated Use `fill` prop instead of `layout=\"fill\"` or change import to `next/legacy/image`.\n   * @see https://nextjs.org/docs/api-reference/next/legacy/image\n   */\n  layout?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectFit?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectPosition?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyBoundary?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyRoot?: string\n}\n\nexport type ImgProps = Omit<ImageProps, 'src' | 'loader'> & {\n  loading: LoadingValue\n  width: number | undefined\n  height: number | undefined\n  style: NonNullable<JSX.IntrinsicElements['img']['style']>\n  sizes: string | undefined\n  srcSet: string | undefined\n  src: string\n}\n\nconst VALID_LOADING_VALUES = ['lazy', 'eager', undefined] as const\n\n// Object-fit values that are not valid background-size values\nconst INVALID_BACKGROUND_SIZE_VALUES = [\n  '-moz-initial',\n  'fill',\n  'none',\n  'scale-down',\n  undefined,\n]\ntype LoadingValue = (typeof VALID_LOADING_VALUES)[number]\ntype ImageConfig = ImageConfigComplete & {\n  allSizes: number[]\n  output?: 'standalone' | 'export'\n}\n\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\n// Do not export - this is an internal type only\n// because `next.config.js` is only meant for the\n// built-in loaders, not for a custom loader() prop.\ntype ImageLoaderWithConfig = (p: ImageLoaderPropsWithConfig) => string\n\nexport type PlaceholderValue = 'blur' | 'empty' | `data:image/${string}`\nexport type OnLoad = React.ReactEventHandler<HTMLImageElement> | undefined\nexport type OnLoadingComplete = (img: HTMLImageElement) => void\n\nexport type PlaceholderStyle = Partial<\n  Pick<\n    CSSProperties,\n    | 'backgroundSize'\n    | 'backgroundPosition'\n    | 'backgroundRepeat'\n    | 'backgroundImage'\n  >\n>\n\nfunction isStaticRequire(\n  src: StaticRequire | StaticImageData\n): src is StaticRequire {\n  return (src as StaticRequire).default !== undefined\n}\n\nfunction isStaticImageData(\n  src: StaticRequire | StaticImageData\n): src is StaticImageData {\n  return (src as StaticImageData).src !== undefined\n}\n\nfunction isStaticImport(src: string | StaticImport): src is StaticImport {\n  return (\n    !!src &&\n    typeof src === 'object' &&\n    (isStaticRequire(src as StaticImport) ||\n      isStaticImageData(src as StaticImport))\n  )\n}\n\nconst allImgs = new Map<\n  string,\n  { src: string; priority: boolean; placeholder: PlaceholderValue }\n>()\nlet perfObserver: PerformanceObserver | undefined\n\nfunction getInt(x: unknown): number | undefined {\n  if (typeof x === 'undefined') {\n    return x\n  }\n  if (typeof x === 'number') {\n    return Number.isFinite(x) ? x : NaN\n  }\n  if (typeof x === 'string' && /^[0-9]+$/.test(x)) {\n    return parseInt(x, 10)\n  }\n  return NaN\n}\n\nfunction getWidths(\n  { deviceSizes, allSizes }: ImageConfig,\n  width: number | undefined,\n  sizes: string | undefined\n): { widths: number[]; kind: 'w' | 'x' } {\n  if (sizes) {\n    // Find all the \"vw\" percent sizes used in the sizes prop\n    const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g\n    const percentSizes = []\n    for (let match; (match = viewportWidthRe.exec(sizes)); match) {\n      percentSizes.push(parseInt(match[2]))\n    }\n    if (percentSizes.length) {\n      const smallestRatio = Math.min(...percentSizes) * 0.01\n      return {\n        widths: allSizes.filter((s) => s >= deviceSizes[0] * smallestRatio),\n        kind: 'w',\n      }\n    }\n    return { widths: allSizes, kind: 'w' }\n  }\n  if (typeof width !== 'number') {\n    return { widths: deviceSizes, kind: 'w' }\n  }\n\n  const widths = [\n    ...new Set(\n      // > This means that most OLED screens that say they are 3x resolution,\n      // > are actually 3x in the green color, but only 1.5x in the red and\n      // > blue colors. Showing a 3x resolution image in the app vs a 2x\n      // > resolution image will be visually the same, though the 3x image\n      // > takes significantly more data. Even true 3x resolution screens are\n      // > wasteful as the human eye cannot see that level of detail without\n      // > something like a magnifying glass.\n      // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n      [width, width * 2 /*, width * 3*/].map(\n        (w) => allSizes.find((p) => p >= w) || allSizes[allSizes.length - 1]\n      )\n    ),\n  ]\n  return { widths, kind: 'x' }\n}\n\ntype GenImgAttrsData = {\n  config: ImageConfig\n  src: string\n  unoptimized: boolean\n  loader: ImageLoaderWithConfig\n  width?: number\n  quality?: number\n  sizes?: string\n}\n\ntype GenImgAttrsResult = {\n  src: string\n  srcSet: string | undefined\n  sizes: string | undefined\n}\n\nfunction generateImgAttrs({\n  config,\n  src,\n  unoptimized,\n  width,\n  quality,\n  sizes,\n  loader,\n}: GenImgAttrsData): GenImgAttrsResult {\n  if (unoptimized) {\n    return { src, srcSet: undefined, sizes: undefined }\n  }\n\n  const { widths, kind } = getWidths(config, width, sizes)\n  const last = widths.length - 1\n\n  return {\n    sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n    srcSet: widths\n      .map(\n        (w, i) =>\n          `${loader({ config, src, quality, width: w })} ${\n            kind === 'w' ? w : i + 1\n          }${kind}`\n      )\n      .join(', '),\n\n    // It's intended to keep `src` the last attribute because React updates\n    // attributes in order. If we keep `src` the first one, Safari will\n    // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n    // updated by React. That causes multiple unnecessary requests if `srcSet`\n    // and `sizes` are defined.\n    // This bug cannot be reproduced in Chrome or Firefox.\n    src: loader({ config, src, quality, width: widths[last] }),\n  }\n}\n\n/**\n * A shared function, used on both client and server, to generate the props for <img>.\n */\nexport function getImgProps(\n  {\n    src,\n    sizes,\n    unoptimized = false,\n    priority = false,\n    loading,\n    className,\n    quality,\n    width,\n    height,\n    fill = false,\n    style,\n    overrideSrc,\n    onLoad,\n    onLoadingComplete,\n    placeholder = 'empty',\n    blurDataURL,\n    fetchPriority,\n    decoding = 'async',\n    layout,\n    objectFit,\n    objectPosition,\n    lazyBoundary,\n    lazyRoot,\n    ...rest\n  }: ImageProps,\n  _state: {\n    defaultLoader: ImageLoaderWithConfig\n    imgConf: ImageConfigComplete\n    showAltText?: boolean\n    blurComplete?: boolean\n  }\n): {\n  props: ImgProps\n  meta: {\n    unoptimized: boolean\n    priority: boolean\n    placeholder: NonNullable<ImageProps['placeholder']>\n    fill: boolean\n  }\n} {\n  const { imgConf, showAltText, blurComplete, defaultLoader } = _state\n  let config: ImageConfig\n  let c = imgConf || imageConfigDefault\n  if ('allSizes' in c) {\n    config = c as ImageConfig\n  } else {\n    const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n    const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n    const qualities = c.qualities?.sort((a, b) => a - b)\n    config = { ...c, allSizes, deviceSizes, qualities }\n  }\n\n  if (typeof defaultLoader === 'undefined') {\n    throw new Error(\n      'images.loaderFile detected but the file is missing default export.\\nRead more: https://nextjs.org/docs/messages/invalid-images-config'\n    )\n  }\n  let loader: ImageLoaderWithConfig = rest.loader || defaultLoader\n\n  // Remove property so it's not spread on <img> element\n  delete rest.loader\n  delete (rest as any).srcSet\n\n  // This special value indicates that the user\n  // didn't define a \"loader\" prop or \"loader\" config.\n  const isDefaultLoader = '__next_img_default' in loader\n\n  if (isDefaultLoader) {\n    if (config.loader === 'custom') {\n      throw new Error(\n        `Image with src \"${src}\" is missing \"loader\" prop.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`\n      )\n    }\n  } else {\n    // The user defined a \"loader\" prop or config.\n    // Since the config object is internal only, we\n    // must not pass it to the user-defined \"loader\".\n    const customImageLoader = loader as ImageLoader\n    loader = (obj) => {\n      const { config: _, ...opts } = obj\n      return customImageLoader(opts)\n    }\n  }\n\n  if (layout) {\n    if (layout === 'fill') {\n      fill = true\n    }\n    const layoutToStyle: Record<string, Record<string, string> | undefined> = {\n      intrinsic: { maxWidth: '100%', height: 'auto' },\n      responsive: { width: '100%', height: 'auto' },\n    }\n    const layoutToSizes: Record<string, string | undefined> = {\n      responsive: '100vw',\n      fill: '100vw',\n    }\n    const layoutStyle = layoutToStyle[layout]\n    if (layoutStyle) {\n      style = { ...style, ...layoutStyle }\n    }\n    const layoutSizes = layoutToSizes[layout]\n    if (layoutSizes && !sizes) {\n      sizes = layoutSizes\n    }\n  }\n\n  let staticSrc = ''\n  let widthInt = getInt(width)\n  let heightInt = getInt(height)\n  let blurWidth: number | undefined\n  let blurHeight: number | undefined\n  if (isStaticImport(src)) {\n    const staticImageData = isStaticRequire(src) ? src.default : src\n\n    if (!staticImageData.src) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n    if (!staticImageData.height || !staticImageData.width) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n\n    blurWidth = staticImageData.blurWidth\n    blurHeight = staticImageData.blurHeight\n    blurDataURL = blurDataURL || staticImageData.blurDataURL\n    staticSrc = staticImageData.src\n\n    if (!fill) {\n      if (!widthInt && !heightInt) {\n        widthInt = staticImageData.width\n        heightInt = staticImageData.height\n      } else if (widthInt && !heightInt) {\n        const ratio = widthInt / staticImageData.width\n        heightInt = Math.round(staticImageData.height * ratio)\n      } else if (!widthInt && heightInt) {\n        const ratio = heightInt / staticImageData.height\n        widthInt = Math.round(staticImageData.width * ratio)\n      }\n    }\n  }\n  src = typeof src === 'string' ? src : staticSrc\n\n  let isLazy =\n    !priority && (loading === 'lazy' || typeof loading === 'undefined')\n  if (!src || src.startsWith('data:') || src.startsWith('blob:')) {\n    // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n    unoptimized = true\n    isLazy = false\n  }\n  if (config.unoptimized) {\n    unoptimized = true\n  }\n  if (\n    isDefaultLoader &&\n    !config.dangerouslyAllowSVG &&\n    src.split('?', 1)[0].endsWith('.svg')\n  ) {\n    // Special case to make svg serve as-is to avoid proxying\n    // through the built-in Image Optimization API.\n    unoptimized = true\n  }\n\n  const qualityInt = getInt(quality)\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (config.output === 'export' && isDefaultLoader && !unoptimized) {\n      throw new Error(\n        `Image Optimization using the default loader is not compatible with \\`{ output: 'export' }\\`.\n  Possible solutions:\n    - Remove \\`{ output: 'export' }\\` and run \"next start\" to run server mode including the Image Optimization API.\n    - Configure \\`{ images: { unoptimized: true } }\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n    if (!src) {\n      // React doesn't show the stack trace and there's\n      // no `src` to help identify which image, so we\n      // instead console.error(ref) during mount.\n      unoptimized = true\n    } else {\n      if (fill) {\n        if (width) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"width\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (height) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"height\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (style?.position && style.position !== 'absolute') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.`\n          )\n        }\n        if (style?.width && style.width !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.`\n          )\n        }\n        if (style?.height && style.height !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.`\n          )\n        }\n      } else {\n        if (typeof widthInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"width\" property.`\n          )\n        } else if (isNaN(widthInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"width\" property. Expected a numeric value in pixels but received \"${width}\".`\n          )\n        }\n        if (typeof heightInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"height\" property.`\n          )\n        } else if (isNaN(heightInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"height\" property. Expected a numeric value in pixels but received \"${height}\".`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/^[\\x00-\\x20]/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot start with a space or control character. Use src.trimStart() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/[\\x00-\\x20]$/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot end with a space or control character. Use src.trimEnd() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n      }\n    }\n    if (!VALID_LOADING_VALUES.includes(loading)) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(\n          String\n        ).join(',')}.`\n      )\n    }\n    if (priority && loading === 'lazy') {\n      throw new Error(\n        `Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`\n      )\n    }\n    if (\n      placeholder !== 'empty' &&\n      placeholder !== 'blur' &&\n      !placeholder.startsWith('data:image/')\n    ) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"placeholder\" property \"${placeholder}\".`\n      )\n    }\n    if (placeholder !== 'empty') {\n      if (widthInt && heightInt && widthInt * heightInt < 1600) {\n        warnOnce(\n          `Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder\" property to improve performance.`\n        )\n      }\n    }\n    if (qualityInt && qualityInt !== 75 && !config.qualities) {\n      warnOnce(\n        `Image with src \"${src}\" is using quality \"${qualityInt}\" which is not configured in images.qualities. This config will be required starting in Next.js 16.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n    if (\n      src.startsWith('/') &&\n      src.includes('?') &&\n      (!config?.localPatterns?.length ||\n        (config.localPatterns.length === 1 &&\n          config.localPatterns[0].pathname === '/_next/static/media/**'))\n    ) {\n      warnOnce(\n        `Image with src \"${src}\" is using a query string which is not configured in images.localPatterns. This config will be required starting in Next.js 16.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n      )\n    }\n    if (placeholder === 'blur' && !blurDataURL) {\n      const VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match next-image-loader\n\n      throw new Error(\n        `Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n        Possible solutions:\n          - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n          - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\n            ','\n          )} (animated images not supported)\n          - Remove the \"placeholder\" property, effectively no blur effect\n        Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`\n      )\n    }\n    if ('ref' in rest) {\n      warnOnce(\n        `Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoad\" property instead.`\n      )\n    }\n\n    if (!unoptimized && !isDefaultLoader) {\n      const urlStr = loader({\n        config,\n        src,\n        width: widthInt || 400,\n        quality: qualityInt || 75,\n      })\n      let url: URL | undefined\n      try {\n        url = new URL(urlStr)\n      } catch (err) {}\n      if (urlStr === src || (url && url.pathname === src && !url.search)) {\n        warnOnce(\n          `Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`\n        )\n      }\n    }\n\n    if (onLoadingComplete) {\n      warnOnce(\n        `Image with src \"${src}\" is using deprecated \"onLoadingComplete\" property. Please use the \"onLoad\" property instead.`\n      )\n    }\n\n    for (const [legacyKey, legacyValue] of Object.entries({\n      layout,\n      objectFit,\n      objectPosition,\n      lazyBoundary,\n      lazyRoot,\n    })) {\n      if (legacyValue) {\n        warnOnce(\n          `Image with src \"${src}\" has legacy prop \"${legacyKey}\". Did you forget to run the codemod?` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-upgrade-to-13`\n        )\n      }\n    }\n\n    if (\n      typeof window !== 'undefined' &&\n      !perfObserver &&\n      window.PerformanceObserver\n    ) {\n      perfObserver = new PerformanceObserver((entryList) => {\n        for (const entry of entryList.getEntries()) {\n          // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n          const imgSrc = entry?.element?.src || ''\n          const lcpImage = allImgs.get(imgSrc)\n          if (\n            lcpImage &&\n            !lcpImage.priority &&\n            lcpImage.placeholder === 'empty' &&\n            !lcpImage.src.startsWith('data:') &&\n            !lcpImage.src.startsWith('blob:')\n          ) {\n            // https://web.dev/lcp/#measure-lcp-in-javascript\n            warnOnce(\n              `Image with src \"${lcpImage.src}\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.` +\n                `\\nRead more: https://nextjs.org/docs/api-reference/next/image#priority`\n            )\n          }\n        }\n      })\n      try {\n        perfObserver.observe({\n          type: 'largest-contentful-paint',\n          buffered: true,\n        })\n      } catch (err) {\n        // Log error but don't crash the app\n        console.error(err)\n      }\n    }\n  }\n  const imgStyle = Object.assign(\n    fill\n      ? {\n          position: 'absolute',\n          height: '100%',\n          width: '100%',\n          left: 0,\n          top: 0,\n          right: 0,\n          bottom: 0,\n          objectFit,\n          objectPosition,\n        }\n      : {},\n    showAltText ? {} : { color: 'transparent' },\n    style\n  )\n\n  const backgroundImage =\n    !blurComplete && placeholder !== 'empty'\n      ? placeholder === 'blur'\n        ? `url(\"data:image/svg+xml;charset=utf-8,${getImageBlurSvg({\n            widthInt,\n            heightInt,\n            blurWidth,\n            blurHeight,\n            blurDataURL: blurDataURL || '', // assume not undefined\n            objectFit: imgStyle.objectFit,\n          })}\")`\n        : `url(\"${placeholder}\")` // assume `data:image/`\n      : null\n\n  const backgroundSize = !INVALID_BACKGROUND_SIZE_VALUES.includes(\n    imgStyle.objectFit\n  )\n    ? imgStyle.objectFit\n    : imgStyle.objectFit === 'fill'\n      ? '100% 100%' // the background-size equivalent of `fill`\n      : 'cover'\n\n  let placeholderStyle: PlaceholderStyle = backgroundImage\n    ? {\n        backgroundSize,\n        backgroundPosition: imgStyle.objectPosition || '50% 50%',\n        backgroundRepeat: 'no-repeat',\n        backgroundImage,\n      }\n    : {}\n\n  if (process.env.NODE_ENV === 'development') {\n    if (\n      placeholderStyle.backgroundImage &&\n      placeholder === 'blur' &&\n      blurDataURL?.startsWith('/')\n    ) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      placeholderStyle.backgroundImage = `url(\"${blurDataURL}\")`\n    }\n  }\n\n  const imgAttributes = generateImgAttrs({\n    config,\n    src,\n    unoptimized,\n    width: widthInt,\n    quality: qualityInt,\n    sizes,\n    loader,\n  })\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      let fullUrl: URL\n      try {\n        fullUrl = new URL(imgAttributes.src)\n      } catch (e) {\n        fullUrl = new URL(imgAttributes.src, window.location.href)\n      }\n      allImgs.set(fullUrl.href, { src, priority, placeholder })\n    }\n  }\n\n  const props: ImgProps = {\n    ...rest,\n    loading: isLazy ? 'lazy' : loading,\n    fetchPriority,\n    width: widthInt,\n    height: heightInt,\n    decoding,\n    className,\n    style: { ...imgStyle, ...placeholderStyle },\n    sizes: imgAttributes.sizes,\n    srcSet: imgAttributes.srcSet,\n    src: overrideSrc || imgAttributes.src,\n  }\n  const meta = { unoptimized, priority, placeholder, fill }\n  return { props, meta }\n}\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nconst { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/image-component.js\"));\n", "'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  forwardRef,\n  use,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport Head from '../shared/lib/head'\nimport { getImgProps } from '../shared/lib/get-img-props'\nimport type {\n  ImageProps,\n  ImgProps,\n  OnLoad,\n  OnLoadingComplete,\n  PlaceholderValue,\n} from '../shared/lib/get-img-props'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n} from '../shared/lib/image-config'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../shared/lib/utils/warn-once'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\n\n// This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\nimport { useMergedRef } from './use-merged-ref'\n\n// This is replaced by webpack define plugin\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nexport type { ImageLoaderProps }\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\ntype ImageElementProps = ImgProps & {\n  unoptimized: boolean\n  placeholder: PlaceholderValue\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setShowAltText: (b: boolean) => void\n  sizesInput: string | undefined\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  placeholder: PlaceholderValue,\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void,\n  unoptimized: boolean,\n  sizesInput: string | undefined\n) {\n  const src = img?.src\n  if (!img || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentElement || !img.isConnected) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    if (placeholder !== 'empty') {\n      setBlurComplete(true)\n    }\n    if (onLoadRef?.current) {\n      // Since we don't have the SyntheticEvent here,\n      // we must create one with the same shape.\n      // See https://reactjs.org/docs/events.html\n      const event = new Event('load')\n      Object.defineProperty(event, 'target', { writable: false, value: img })\n      let prevented = false\n      let stopped = false\n      onLoadRef.current({\n        ...event,\n        nativeEvent: event,\n        currentTarget: img,\n        target: img,\n        isDefaultPrevented: () => prevented,\n        isPropagationStopped: () => stopped,\n        persist: () => {},\n        preventDefault: () => {\n          prevented = true\n          event.preventDefault()\n        },\n        stopPropagation: () => {\n          stopped = true\n          event.stopPropagation()\n        },\n      })\n    }\n    if (onLoadingCompleteRef?.current) {\n      onLoadingCompleteRef.current(img)\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const origSrc = new URL(src, 'http://n').searchParams.get('url') || src\n      if (img.getAttribute('data-nimg') === 'fill') {\n        if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n          let widthViewportRatio =\n            img.getBoundingClientRect().width / window.innerWidth\n          if (widthViewportRatio < 0.6) {\n            if (sizesInput === '100vw') {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            } else {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            }\n          }\n        }\n        if (img.parentElement) {\n          const { position } = window.getComputedStyle(img.parentElement)\n          const valid = ['absolute', 'fixed', 'relative']\n          if (!valid.includes(position)) {\n            warnOnce(\n              `Image with src \"${origSrc}\" has \"fill\" and parent element with invalid \"position\". Provided \"${position}\" should be one of ${valid\n                .map(String)\n                .join(',')}.`\n            )\n          }\n        }\n        if (img.height === 0) {\n          warnOnce(\n            `Image with src \"${origSrc}\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.`\n          )\n        }\n      }\n\n      const heightModified =\n        img.height.toString() !== img.getAttribute('height')\n      const widthModified = img.width.toString() !== img.getAttribute('width')\n      if (\n        (heightModified && !widthModified) ||\n        (!heightModified && widthModified)\n      ) {\n        warnOnce(\n          `Image with src \"${origSrc}\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles 'width: \"auto\"' or 'height: \"auto\"' to maintain the aspect ratio.`\n        )\n      }\n    }\n  })\n}\n\nfunction getDynamicProps(\n  fetchPriority?: string\n): Record<string, string | undefined> {\n  if (Boolean(use)) {\n    // In React 19.0.0 or newer, we must use camelCase\n    // prop to avoid \"Warning: Invalid DOM property\".\n    // See https://github.com/facebook/react/pull/25927\n    return { fetchPriority }\n  }\n  // In React 18.2.0 or older, we must use lowercase prop\n  // to avoid \"Warning: Invalid DOM property\".\n  return { fetchpriority: fetchPriority }\n}\n\nconst ImageElement = forwardRef<HTMLImageElement | null, ImageElementProps>(\n  (\n    {\n      src,\n      srcSet,\n      sizes,\n      height,\n      width,\n      decoding,\n      className,\n      style,\n      fetchPriority,\n      placeholder,\n      loading,\n      unoptimized,\n      fill,\n      onLoadRef,\n      onLoadingCompleteRef,\n      setBlurComplete,\n      setShowAltText,\n      sizesInput,\n      onLoad,\n      onError,\n      ...rest\n    },\n    forwardedRef\n  ) => {\n    const ownRef = useCallback(\n      (img: ImgElementWithDataProp | null) => {\n        if (!img) {\n          return\n        }\n        if (onError) {\n          // If the image has an error before react hydrates, then the error is lost.\n          // The workaround is to wait until the image is mounted which is after hydration,\n          // then we set the src again to trigger the error handler (if there was an error).\n          // eslint-disable-next-line no-self-assign\n          img.src = img.src\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          if (!src) {\n            console.error(`Image is missing required \"src\" property:`, img)\n          }\n          if (img.getAttribute('alt') === null) {\n            console.error(\n              `Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.`\n            )\n          }\n        }\n        if (img.complete) {\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }\n      },\n      [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput,\n      ]\n    )\n\n    const ref = useMergedRef(forwardedRef, ownRef)\n\n    return (\n      <img\n        {...rest}\n        {...getDynamicProps(fetchPriority)}\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading={loading}\n        width={width}\n        height={height}\n        decoding={decoding}\n        data-nimg={fill ? 'fill' : '1'}\n        className={className}\n        style={style}\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes={sizes}\n        srcSet={srcSet}\n        src={src}\n        ref={ref}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }}\n        onError={(event) => {\n          // if the real image fails to load, this will ensure \"alt\" is visible\n          setShowAltText(true)\n          if (placeholder !== 'empty') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n    )\n  }\n)\n\nfunction ImagePreload({\n  isAppRouter,\n  imgAttributes,\n}: {\n  isAppRouter: boolean\n  imgAttributes: ImgProps\n}) {\n  const opts: ReactDOM.PreloadOptions = {\n    as: 'image',\n    imageSrcSet: imgAttributes.srcSet,\n    imageSizes: imgAttributes.sizes,\n    crossOrigin: imgAttributes.crossOrigin,\n    referrerPolicy: imgAttributes.referrerPolicy,\n    ...getDynamicProps(imgAttributes.fetchPriority),\n  }\n\n  if (isAppRouter && ReactDOM.preload) {\n    ReactDOM.preload(imgAttributes.src, opts)\n    return null\n  }\n\n  return (\n    <Head>\n      <link\n        key={\n          '__nimg-' +\n          imgAttributes.src +\n          imgAttributes.srcSet +\n          imgAttributes.sizes\n        }\n        rel=\"preload\"\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n        {...opts}\n      />\n    </Head>\n  )\n}\n\n/**\n * The `Image` component is used to optimize images.\n *\n * Read more: [Next.js docs: `Image`](https://nextjs.org/docs/app/api-reference/components/image)\n */\nexport const Image = forwardRef<HTMLImageElement | null, ImageProps>(\n  (props, forwardedRef) => {\n    const pagesRouter = useContext(RouterContext)\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter\n\n    const configContext = useContext(ImageConfigContext)\n    const config = useMemo(() => {\n      const c = configEnv || configContext || imageConfigDefault\n      const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n      const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n      const qualities = c.qualities?.sort((a, b) => a - b)\n      return { ...c, allSizes, deviceSizes, qualities }\n    }, [configContext])\n\n    const { onLoad, onLoadingComplete } = props\n    const onLoadRef = useRef(onLoad)\n\n    useEffect(() => {\n      onLoadRef.current = onLoad\n    }, [onLoad])\n\n    const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n    useEffect(() => {\n      onLoadingCompleteRef.current = onLoadingComplete\n    }, [onLoadingComplete])\n\n    const [blurComplete, setBlurComplete] = useState(false)\n    const [showAltText, setShowAltText] = useState(false)\n\n    const { props: imgAttributes, meta: imgMeta } = getImgProps(props, {\n      defaultLoader,\n      imgConf: config,\n      blurComplete,\n      showAltText,\n    })\n\n    return (\n      <>\n        {\n          <ImageElement\n            {...imgAttributes}\n            unoptimized={imgMeta.unoptimized}\n            placeholder={imgMeta.placeholder}\n            fill={imgMeta.fill}\n            onLoadRef={onLoadRef}\n            onLoadingCompleteRef={onLoadingCompleteRef}\n            setBlurComplete={setBlurComplete}\n            setShowAltText={setShowAltText}\n            sizesInput={props.sizes}\n            ref={forwardedRef}\n          />\n        }\n        {imgMeta.priority ? (\n          <ImagePreload\n            isAppRouter={isAppRouter}\n            imgAttributes={imgAttributes}\n          />\n        ) : null}\n      </>\n    )\n  }\n)\n", "import type { ImageLoaderPropsWithConfig } from './image-config'\n\nconst DEFAULT_Q = 75\n\nfunction defaultLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  if (process.env.NODE_ENV !== 'production') {\n    const missingValues = []\n\n    // these should always be provided but make sure they are\n    if (!src) missingValues.push('src')\n    if (!width) missingValues.push('width')\n\n    if (missingValues.length > 0) {\n      throw new Error(\n        `Next Image Optimization requires ${missingValues.join(\n          ', '\n        )} to be provided. Make sure you pass them as props to the \\`next/image\\` component. Received: ${JSON.stringify(\n          { src, width, quality }\n        )}`\n      )\n    }\n\n    if (src.startsWith('//')) {\n      throw new Error(\n        `Failed to parse src \"${src}\" on \\`next/image\\`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)`\n      )\n    }\n\n    if (src.startsWith('/') && config.localPatterns) {\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasLocalMatch } =\n          require('./match-local-pattern') as typeof import('./match-local-pattern')\n        if (!hasLocalMatch(config.localPatterns, src)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\` does not match \\`images.localPatterns\\` configured in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n          )\n        }\n      }\n    }\n\n    if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n      let parsedSrc: URL\n      try {\n        parsedSrc = new URL(src)\n      } catch (err) {\n        console.error(err)\n        throw new Error(\n          `Failed to parse src \"${src}\" on \\`next/image\\`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)`\n        )\n      }\n\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasRemoteMatch } =\n          require('./match-remote-pattern') as typeof import('./match-remote-pattern')\n        if (\n          !hasRemoteMatch(config.domains!, config.remotePatterns!, parsedSrc)\n        ) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\`, hostname \"${parsedSrc.hostname}\" is not configured under images in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host`\n          )\n        }\n      }\n    }\n\n    if (quality && config.qualities && !config.qualities.includes(quality)) {\n      throw new Error(\n        `Invalid quality prop (${quality}) on \\`next/image\\` does not match \\`images.qualities\\` configured in your \\`next.config.js\\`\\n` +\n          `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n  }\n\n  const q =\n    quality ||\n    config.qualities?.reduce((prev, cur) =>\n      Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev\n    ) ||\n    DEFAULT_Q\n\n  return `${config.path}?url=${encodeURIComponent(src)}&w=${width}&q=${q}${\n    src.startsWith('/_next/static/media/') && process.env.NEXT_DEPLOYMENT_ID\n      ? `&dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n      : ''\n  }`\n}\n\n// We use this to determine if the import is the default loader\n// or a custom loader defined by the user in next.config.js\ndefaultLoader.__next_img_default = true\n\nexport default defaultLoader\n", "import type { ImageConfigComplete, ImageLoaderProps } from './image-config'\nimport type { ImageProps, ImageLoader, StaticImageData } from './get-img-props'\n\nimport { getImgProps } from './get-img-props'\nimport { Image } from '../../client/image-component'\n\n// This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\n\n/**\n * For more advanced use cases, you can call `getImageProps()`\n * to get the props that would be passed to the underlying `<img>` element,\n * and instead pass to them to another component, style, canvas, etc.\n *\n * Read more: [Next.js docs: `getImageProps`](https://nextjs.org/docs/app/api-reference/components/image#getimageprops)\n */\nexport function getImageProps(imgProps: ImageProps) {\n  const { props } = getImgProps(imgProps, {\n    defaultLoader,\n    // This is replaced by webpack define plugin\n    imgConf: process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete,\n  })\n  // Normally we don't care about undefined props because we pass to JSX,\n  // but this exported function could be used by the end user for anything\n  // so we delete undefined props to clean it up a little.\n  for (const [key, value] of Object.entries(props)) {\n    if (value === undefined) {\n      delete props[key as keyof typeof props]\n    }\n  }\n  return { props }\n}\n\nexport default Image\n\nexport type { ImageProps, ImageLoaderProps, ImageLoader, StaticImageData }\n", "module.exports = require('./dist/shared/lib/image-external')\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nconst { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js\"));\n", "'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\nimport {\n  FetchStrategy,\n  type PrefetchTaskFetchStrategy,\n} from '../components/segment-cache'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `\"auto\"`, `null`, `undefined` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | 'auto' | null | 'unstable_forceStale'\n\n  /**\n   * (unstable) Switch to a full prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when type generation runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  if (onNavigate) {\n    let isDefaultPrevented = false\n\n    onNavigate({\n      preventDefault: () => {\n        isDefaultPrevented = true\n      },\n    })\n\n    if (isDefaultPrevented) {\n      return\n    }\n  }\n\n  React.startTransition(() => {\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  })\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n\n  const fetchStrategy =\n    prefetchProp !== false\n      ? getFetchStrategyFromPrefetchProp(prefetchProp)\n      : // TODO: it makes no sense to assign a fetchStrategy when prefetching is disabled.\n        FetchStrategy.PPR\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else if (key === 'prefetch') {\n        if (\n          props[key] != null &&\n          valType !== 'boolean' &&\n          props[key] !== 'auto' &&\n          props[key] !== 'unstable_forceStale'\n        ) {\n          throw createPropError({\n            key,\n            expected: '`boolean | \"auto\" | \"unstable_forceStale\"`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          fetchStrategy,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, fetchStrategy, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n\nfunction getFetchStrategyFromPrefetchProp(\n  prefetchProp: Exclude<LinkProps['prefetch'], undefined | false>\n): PrefetchTaskFetchStrategy {\n  if (\n    process.env.__NEXT_CACHE_COMPONENTS &&\n    process.env.__NEXT_CLIENT_SEGMENT_CACHE\n  ) {\n    // In the new implementation:\n    // - `prefetch={true}` is a runtime prefetch\n    //   (includes cached IO + params + cookies, with dynamic holes for uncached IO).\n    // - `unstable_forceStale` is a \"full\" prefetch\n    //   (forces inclusion of all dynamic data, i.e. the old behavior of `prefetch={true}`)\n    if (prefetchProp === true) {\n      return FetchStrategy.PPRRuntime\n    }\n    if (prefetchProp === 'unstable_forceStale') {\n      return FetchStrategy.Full\n    }\n\n    // `null` or `\"auto\"`: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport.\n    // This will also include invalid prop values that don't match the types specified here.\n    // (although those should've been filtered out by prop validation in dev)\n    prefetchProp satisfies null | 'auto'\n    // In `clientSegmentCache`, we default to PPR, and we'll discover whether or not the route supports it with the initial prefetch.\n    // If we're not using `clientSegmentCache`, this will be converted into a `PrefetchKind.AUTO`.\n    return FetchStrategy.PPR\n  } else {\n    return prefetchProp === null || prefetchProp === 'auto'\n      ? // In `clientSegmentCache`, we default to PPR, and we'll discover whether or not the route supports it with the initial prefetch.\n        // If we're not using `clientSegmentCache`, this will be converted into a `PrefetchKind.AUTO`.\n        FetchStrategy.PPR\n      : // In the old implementation without runtime prefetches, `prefetch={true}` forces all dynamic data to be prefetched.\n        // To preserve backwards-compatibility, anything other than `false`, `null`, or `\"auto\"` results in a full prefetch.\n        // (although invalid values should've been filtered out by prop validation in dev)\n        FetchStrategy.Full\n  }\n}\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/separator.tsx\",\n    \"Separator\",\n);\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/layout/footer-subscribe-form.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/footer-subscribe-form.tsx\",\n    \"default\",\n);\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/layout/site-header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/layout/site-header.tsx\",\n    \"default\",\n);\n", "import { Button } from '../ui/button';\n\nexport default function FooterCta() {\n  return (\n    <div className=\"overflow-hidden\">\n      <div className=\"relative w-full overflow-hidden bg-brand-600\">\n        <div className=\"container mx-auto flex flex-col items-center justify-center px-4\">\n          <div className=\"mb-8 flex flex-col items-center justify-center gap-8\">\n            <h3 className=\"mb-4 pt-36 text-center font-aeonik-bold font-semibold text-3xl text-black sm:text-4xl md:text-5xl\">\n              Available on Mac &amp; Windows\n            </h3>\n            <div className=\"mx-auto max-w-xs\">\n              <Button\n                className=\"rounded-full bg-black text-white hover:bg-black/80\"\n                size={'lg'}\n              >\n                Download for Mac\n              </Button>\n            </div>\n          </div>\n          <div className=\"-mb-32 w-full\">\n            <h5 className=\"text-nowrap font-aeonik-bold text-[250px] text-black\">\n              Better Flow\n            </h5>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "import Image from 'next/image';\nimport gradient from '@/public/gradient.webp';\n\nexport default function BgCover() {\n  return (\n    <div className=\"-translate-x-1/2 pointer-events-none absolute top-0 left-1/2 w-full max-w-none overflow-hidden\">\n      <Image\n        alt=\"my image\"\n        className=\"max-h-[900px] min-h-[500px] w-full\"\n        loading=\"eager\"\n        role=\"presentation\"\n        src={gradient}\n      />\n    </div>\n  );\n}\n", "import src from \"IMAGE\";\nexport default { src, width: 1728, height: 891, blurWidth: 8, blurHeight: 4, blurDataURL: \"data:image/webp;base64,UklGRhQBAABXRUJQVlA4TAgBAAAvB8AAEM1VICICHggACQAAAACyZwMAH2tMAhgAMAAAAAAAAAAAAACGAQAAAwMAAAAAAIDxzAMAAHggwCYAAAAA598fHXAMdIoKAgAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAADgHUHkgwCYAAAAA59+6pIACBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAdSlAgAI4qnsgwCYAAAAA538AQAAEACEAAAAAAAAAAAAAAAAAAAAAAPQAAI4EIQqAQgrglmc9xW/28WYXHlv3kzH47wBrV4LGWDveISGYvTOj14TRMr0uRecyppCtNy8ZyXaheioPmTL/03rRCTSF7CDAtqSuZ12IfxSyRiA=\"};\n", "export const siteConfig = {\n  name: '<PERSON><PERSON>',\n  url: 'https://rathon-rw.vercel.app/', // Replace with production URL\n  ogImage: 'https://rathon-rw.vercel.app/opengraph-image.png',\n  description:\n    'We don’t just build websites. We craft bold brands and powerful platforms for businesses ready to stand out, scale up, and dominate the digital space.',\n  links: {\n    twitter: 'https://x.com/rathonrw',\n    linkedin: 'https://www.linkedin.com/company/rathon',\n    instagram: 'https://www.instagram.com/rathonrw/',\n    youtube: 'https://www.youtube.com/@RathonRw',\n  },\n  keywords: [\n    'Web Design',\n    'Web Development',\n    'UI/UX Design',\n    'SEO Optimization',\n    'Website Hosting',\n    'Website Maintenance',\n    'React',\n    'TypeScript',\n    'Tailwind CSS',\n    'Next.js',\n    'Landing Pages',\n    'Creative Agency',\n    'Frontend Development',\n    'Performance Optimization',\n    'Mobile Responsive Design',\n    'Modern Web Apps',\n    'Digital Branding',\n    'Design Systems',\n    'Careers in Tech',\n    'Contact Rathon',\n    'Book a Call',\n    'Support Resources',\n    'Video Guides',\n  ],\n};\n", "import { ArmchairIcon } from 'lucide-react';\nimport Link from 'next/link';\nimport { links } from '@/config/docs';\nimport { siteConfig } from '@/config/site';\nimport { Separator } from '../ui/separator';\nimport FooterSubForm from './footer-subscribe-form';\n\nexport function SiteFooter() {\n  return (\n    <footer className=\"border-grid border-t bg-my-background pt-16\" id=\"footer\">\n      <div className=\"container relative\">\n        <div className=\"grid grid-cols-2 gap-10 md:grid-cols-4 md:px-4 lg:grid-cols-12\">\n          <div className=\"col-span-2 flex flex-col justify-between xl:col-span-3\">\n            <Link\n              aria-label=\"go home\"\n              className=\"flex size-fit items-center gap-1\"\n              href=\"/\"\n            >\n              <ArmchairIcon className=\"block size-6 text-brand-600\" />\n              <span>Better Flow</span>\n            </Link>\n            <div className=\"my-5 w-fit font-normal text-md text-muted-foreground/50\">\n              Highlight AI lets models understand your desktop activity. Get\n              stuff done faster.\n            </div>\n            <div className=\"flex gap-2\">\n              <Link\n                className=\"text-muted-foreground transition-colors duration-150 hover:text-primary\"\n                href={siteConfig.links.youtube}\n                rel=\"noreferrer\"\n                target=\"_blank\"\n              >\n                <svg\n                  className=\"size-5 fill-foreground\"\n                  height=\"24px\"\n                  viewBox=\"0 0 72 72\"\n                  width=\"24px\"\n                  x=\"0px\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  y=\"0px\"\n                >\n                  <title>Youtube</title>\n                  <path d=\"M61.115,18.856C63.666,21.503,64,25.709,64,36s-0.334,14.497-2.885,17.144C58.563,55.791,55.906,56,36,56\ts-22.563-0.209-25.115-2.856C8.334,50.497,8,46.291,8,36s0.334-14.497,2.885-17.144S16.094,16,36,16S58.563,16.209,61.115,18.856z M31.464,44.476l13.603-8.044l-13.603-7.918V44.476z\" />\n                </svg>\n              </Link>\n              <Separator orientation=\"vertical\" />\n              <Link\n                className=\"text-muted-foreground transition-colors duration-150 hover:text-primary\"\n                href={siteConfig.links.instagram}\n                rel=\"noreferrer\"\n                target=\"_blank\"\n              >\n                <svg\n                  className=\"size-5 fill-foreground\"\n                  height=\"24px\"\n                  viewBox=\"0 0 64 64\"\n                  width=\"24px\"\n                  x=\"0px\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  y=\"0px\"\n                >\n                  <title>Instagram</title>\n                  <path d=\"M 21.580078 7 C 13.541078 7 7 13.544938 7 21.585938 L 7 42.417969 C 7 50.457969 13.544938 57 21.585938 57 L 42.417969 57 C 50.457969 57 57 50.455062 57 42.414062 L 57 21.580078 C 57 13.541078 50.455062 7 42.414062 7 L 21.580078 7 z M 47 15 C 48.104 15 49 15.896 49 17 C 49 18.104 48.104 19 47 19 C 45.896 19 45 18.104 45 17 C 45 15.896 45.896 15 47 15 z M 32 19 C 39.17 19 45 24.83 45 32 C 45 39.17 39.169 45 32 45 C 24.83 45 19 39.169 19 32 C 19 24.831 24.83 19 32 19 z M 32 23 C 27.029 23 23 27.029 23 32 C 23 36.971 27.029 41 32 41 C 36.971 41 41 36.971 41 32 C 41 27.029 36.971 23 32 23 z\" />\n                </svg>\n              </Link>\n\n              <Separator orientation=\"vertical\" />\n              <Link\n                className=\"text-muted-foreground transition-colors duration-150 hover:text-primary\"\n                href={siteConfig.links.twitter}\n                rel=\"noreferrer\"\n                target=\"_blank\"\n              >\n                <svg\n                  className=\"size-5 fill-foreground\"\n                  height=\"24px\"\n                  viewBox=\"0 0 24 24\"\n                  width=\"24px\"\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                >\n                  <title>Twitter</title>\n                  <path d=\"M 2.3671875 3 L 9.4628906 13.140625 L 2.7402344 21 L 5.3808594 21 L 10.644531 14.830078 L 14.960938 21 L 21.871094 21 L 14.449219 10.375 L 20.740234 3 L 18.140625 3 L 13.271484 8.6875 L 9.2988281 3 L 2.3671875 3 z M 6.2070312 5 L 8.2558594 5 L 18.033203 19 L 16.001953 19 L 6.2070312 5 z\" />\n                </svg>\n              </Link>\n            </div>\n          </div>\n\n          {links.map((link) => (\n            <div\n              className=\"space-y-4 text-sm md:ml-5 lg:col-span-2\"\n              key={link.group}\n            >\n              <span className=\"block font-medium\">{link.group}</span>\n              <ul className=\"space-y-3\">\n                {link.items.map((item) => (\n                  <li key={item.title}>\n                    <Link\n                      href={item.href}\n                      {...(item.external\n                        ? {\n                            target: '_blank',\n                            rel: 'noopener noreferrer',\n                          }\n                        : {})}\n                      className=\"block text-muted-foreground text-sm leading-5 duration-150 hover:text-primary\"\n                    >\n                      {item.title}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n\n          <div className=\"col-span-2 space-y-4 text-sm lg:col-span-3\">\n            <span className=\"block font-medium\">\n              Subscribe to our newsletter\n            </span>\n            <div>\n              <p className=\"text-balance text-muted-foreground text-sm leading-5\">\n                Stay updated on new releases and features, guides, and case\n                studies.\n              </p>\n            </div>\n            <FooterSubForm />\n          </div>\n        </div>\n        <div className=\"my-12 flex items-center justify-between gap-5\">\n          <div className=\"space-y-3\">\n            <p className=\"text-balance text-muted-foreground text-sm leading-5\">\n              © {new Date().getFullYear()} Rathon, agency.\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n", "import BgCover from '@/components/layout/bg-cover';\nimport FooterC<PERSON> from '@/components/layout/footer-cta';\nimport { SiteFooter } from '@/components/layout/site-footer';\nimport SiteHeader from '@/components/layout/site-header';\n\nexport default function AppLayout({ children }: { children: React.ReactNode }) {\n  return (\n    <div className=\"container-wrapper dark relative z-10 flex min-h-svh flex-col bg-my-background text-white\">\n      <SiteHeader />\n      <main className=\"container relative flex flex-1 flex-col overflow-hidden\">\n        <BgCover />\n        {children}\n      </main>\n      <FooterCta />\n      <SiteFooter />\n    </div>\n  );\n}\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 9V6a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v3', key: 'irtipd' }],\n  [\n    'path',\n    {\n      d: 'M3 16a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-5a2 2 0 0 0-4 0v1.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V11a2 2 0 0 0-4 0z',\n      key: '1qyhux',\n    },\n  ],\n  ['path', { d: 'M5 18v2', key: 'ppbyun' }],\n  ['path', { d: 'M19 18v2', key: 'gy7782' }],\n];\n\n/**\n * @component @name Armchair\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgOVY2YTIgMiAwIDAgMC0yLTJIN2EyIDIgMCAwIDAtMiAydjMiIC8+CiAgPHBhdGggZD0iTTMgMTZhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi01YTIgMiAwIDAgMC00IDB2MS41YS41LjUgMCAwIDEtLjUuNWgtOWEuNS41IDAgMCAxLS41LS41VjExYTIgMiAwIDAgMC00IDB6IiAvPgogIDxwYXRoIGQ9Ik01IDE4djIiIC8+CiAgPHBhdGggZD0iTTE5IDE4djIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/armchair\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Armchair = createLucideIcon('armchair', __iconNode);\n\nexport default Armchair;\n"], "names": ["warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "add", "getImageBlurSvg", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "blurDataURL", "objectFit", "std", "svgWidth", "svgHeight", "viewBox", "preserveAspectRatio", "VALID_LOADERS", "imageConfigDefault", "deviceSizes", "imageSizes", "path", "loader", "loaderFile", "domains", "disableStaticImages", "minimumCacheTTL", "formats", "dangerouslyAllowSVG", "contentSecurityPolicy", "contentDispositionType", "localPatterns", "undefined", "remotePatterns", "qualities", "unoptimized", "getImgProps", "VALID_LOADING_VALUES", "INVALID_BACKGROUND_SIZE_VALUES", "isStaticRequire", "src", "default", "isStaticImageData", "isStaticImport", "allImgs", "Map", "perfObserver", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "getWidths", "width", "sizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "push", "length", "smallestRatio", "Math", "min", "widths", "filter", "s", "kind", "map", "w", "find", "p", "generateImgAttrs", "config", "quality", "srcSet", "last", "i", "join", "_state", "priority", "loading", "className", "height", "fill", "style", "overrideSrc", "onLoad", "onLoadingComplete", "placeholder", "fetchPriority", "decoding", "layout", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "defaultLoader", "c", "sort", "a", "b", "Error", "isDefaultLoader", "customImageLoader", "obj", "opts", "layoutToStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutToSizes", "layoutStyle", "layoutSizes", "staticSrc", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "startsWith", "split", "endsWith", "qualityInt", "output", "position", "isNaN", "includes", "String", "pathname", "VALID_BLUR_EXT", "urlStr", "url", "URL", "err", "search", "<PERSON><PERSON><PERSON>", "legacyValue", "Object", "entries", "window", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "get", "observe", "type", "buffered", "error", "imgStyle", "assign", "left", "top", "right", "bottom", "color", "backgroundImage", "backgroundSize", "placeholder<PERSON><PERSON><PERSON>", "backgroundPosition", "backgroundRepeat", "imgAttributes", "fullUrl", "e", "location", "href", "set", "props", "meta", "Image", "configEnv", "__NEXT_IMAGE_OPTS", "globalThis", "__NEXT_IMAGE_IMPORTED", "handleLoading", "img", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "sizesInput", "decode", "Promise", "resolve", "catch", "then", "parentElement", "isConnected", "current", "event", "Event", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "origSrc", "searchParams", "getAttribute", "widthViewportRatio", "getBoundingClientRect", "innerWidth", "getComputedStyle", "valid", "heightModified", "toString", "widthModified", "getDynamicProps", "Boolean", "use", "fetchpriority", "ImageElement", "forwardRef", "forwardedRef", "setShowAltText", "onError", "ownRef", "useCallback", "complete", "ref", "useMergedRef", "data-nimg", "ImagePreload", "isAppRouter", "as", "imageSrcSet", "crossOrigin", "referrerPolicy", "ReactDOM", "preload", "Head", "link", "rel", "pagesRouter", "useContext", "RouterContext", "configContext", "ImageConfigContext", "useMemo", "useRef", "useEffect", "useState", "imgMeta", "DEFAULT_Q", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_RUNTIME", "hasLocalMatch", "require", "parsedSrc", "hasRemoteMatch", "hostname", "q", "reduce", "prev", "cur", "abs", "encodeURIComponent", "NEXT_DEPLOYMENT_ID", "__next_img_default", "getImageProps", "imgProps", "key", "LinkComponent", "useLinkStatus", "isModifiedEvent", "eventTarget", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "which", "linkClicked", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "React", "startTransition", "dispatchNavigateAction", "formatStringOrUrl", "urlObjOrString", "formatUrl", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "unstable_dynamicOnHover", "restProps", "router", "AppRouterContext", "prefetchEnabled", "fetchStrategy", "getFetchStrategyFromPrefetchProp", "FetchStrategy", "PPR", "createPropError", "args", "expected", "actual", "requiredPropsGuard", "requiredProps", "keys", "for<PERSON>ach", "optionalPropsGuard", "optionalProps", "valType", "locale", "hasDynamicSegment", "some", "segment", "resolvedHref", "child", "Children", "only", "childRef", "observeLinkVisibilityOnMount", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "isAbsoluteUrl", "addBasePath", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "createContext", "__NEXT_CACHE_COMPONENTS", "__NEXT_CLIENT_SEGMENT_CACHE", "PPRRuntime", "Full"], "mappings": "6CAKA,EAAQ,CAAC,CAHT,EAGY,OAHsB,AAAzB,CAA4B,EACjC,OAAO,GAAO,EAAI,UAAU,CAAG,EAAM,CAAE,QAAS,CAAI,CACxD,yGCOSA,WAAAA,qCAAAA,KAXT,IAAIA,EAAW,AAACC,IAAe,gCCGxB,SAASW,EAAgB,CAc/B,EAd+B,GAAA,UAC9BC,CAAQ,WACRC,CAAS,WACTC,CAAS,YACTC,CAAU,aACVC,CAAW,WACXC,CAAS,CAQV,CAd+B,EAgBxBE,EAAWL,EAAwB,GAAZA,EAAiBF,EACxCQ,EAAYL,EAA0B,GAAbA,EAAkBF,EAE3CQ,EACJF,GAAYC,EAAa,gBAAeD,EAAS,IAAGC,EAAU,IAAK,GASrE,MAAQ,6CAA4CC,UAAQ,8FAA2FH,MAAI,oQAAiQA,MAAI,qEARpYG,EACxB,OACc,YAAdJ,EACE,IAKufK,OAJze,UAAdL,EACE,iBACA,MAAA,EAEygB,sCAAqCD,EAAY,iBACpkB,CA/BC,OAAA,cAAA,CAAA,EAAA,aAAA,oCACeL,kBAAAA,qCAAAA,0KCHHY,aAAa,CAAA,kBAAbA,GAiIAC,kBAAkB,CAAA,kBAAlBA,KAjIN,IAAMD,EAAgB,CAC3B,UACA,QACA,aACA,SACA,SACD,CA2HYC,EAA0C,CACrDC,YAAa,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAK,CAC1DC,WAAY,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAC/CC,KAAM,eACNC,OAAQ,UACRC,WAAY,GACZC,QAAS,EAAE,CACXC,qBAAqB,EACrBC,gBAAiB,GACjBC,QAAS,CAAC,aAAa,CACvBC,qBAAqB,EACrBC,sBAAwB,gDACxBC,uBAAwB,aACxBC,mBAAeC,EACfC,eAAgB,EAAE,CAClBC,UAAWF,OACXG,aAAa,CACf,yGC+GgBC,cAAAA,qCAAAA,OAjQS,CAAA,CAAA,IAAA,WACO,CAAA,CAAA,IAAA,OACG,CAAA,CAAA,IAAA,GAkF7BE,EAAiC,CACrC,eACA,OACA,OACA,kBACAN,EACD,CA4BD,SAASO,EACPC,CAAoC,EAEpC,OAAQA,KAAkCR,MAAZS,OAAO,AACvC,CAuBA,SAASM,EAAOC,CAAU,SACxB,AAAI,KAAa,IAANA,EACFA,EAEQ,KAHa,KAGH,AAAvB,OAAOA,EACFC,OAAOC,QAAQ,CAACF,GAAKA,EAAIG,IAE9B,AAAa,iBAANH,GAAkB,WAAWI,IAAI,CAACJ,GACpCK,CADwC,QAC/BL,EAAG,IAEdG,GACT,CAqGO,SAASf,EACd,CAyBa,CACb8C,CAKC,MAkBmBsB,IAjDpB,IA0CI5B,EAqEApE,EACAC,EAhHJ,CACE+B,KAAG,OACHgB,CAAK,aACLrB,GAAc,CAAK,UACnBgD,GAAW,CAAK,SAChBC,CAAO,WACPC,CAAS,SACTR,CAAO,OACPtB,CAAK,QACL+B,CAAM,MACNC,GAAO,CAAK,OACZC,CAAK,aACLC,CAAW,QACXC,CAAM,mBACNC,CAAiB,aACjBC,EAAc,OAAO,aACrBlF,CAAW,eACXmF,CAAa,UACbC,EAAW,OAAO,CAClBC,QAAM,WACNpF,CAAS,gBACTqF,CAAc,cACdC,CAAY,UACZC,CAAQ,CACR,GAAGC,EACQ,CAzBb,EAyCM,SAAEC,CAAO,aAAEC,CAAW,cAAEC,CAAY,eAAEC,CAAa,CAAE,CAAGrB,EAE1DsB,EAAIJ,GAAWlF,EAAAA,kBAAkB,CACrC,GAAI,aAAcsF,EAChB5B,CADmB,CACV4B,MACJ,CACL,IAAM/C,EAAW,IAAI+C,EAAErF,WAAW,IAAKqF,EAAEpF,UAAU,CAAC,CAACqF,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClExF,EAAcqF,EAAErF,WAAW,CAACsF,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAC/CzE,EAAAA,AAAuB,OAAXsE,EAAAA,EAAEtE,SAAAA,AAAS,EAAA,KAAA,EAAXsE,EAAaC,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClD/B,EAAS,CAAE,GAAG4B,CAAC,UAAE/C,cAAUtC,YAAae,CAAU,CACpD,CAEA,GAAI,KAAyB,IAAlBqE,EACT,MAAM,CADkC,MAClC,cAEL,CAFK,AAAIK,MACR,yIADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEF,IAAItF,EAAgC6E,EAAK7E,MAAM,EAAIiF,CAGnD,QAAOJ,EAAK7E,MAAM,CAClB,OAAQ6E,EAAarB,MAAM,CAI3B,IAAM+B,EAAkB,uBAAwBvF,EAEhD,GAAIuF,GACF,GAAIjC,AAAkB,UAAU,CADb,EACRtD,MAAM,CACf,MAAM,OAAA,cAGL,CAHK,AAAIsF,yBACWpE,EAAlB,IAAsB,gCACpB,kEAFC,oBAAA,OAAA,mBAAA,gBAAA,CAGN,EACF,KACK,CAIL,IAAMsE,EAAoBxF,EAC1BA,EAAS,AAACyF,IACR,GAAM,CAAEnC,OAAQlF,CAAC,CAAE,GAAGsH,EAAM,CAAGD,EAC/B,OAAOD,EAAkBE,EAC3B,CACF,CAEA,GAAIjB,EAAQ,CACK,QAAQ,CAAnBA,IACFR,GAAO,CAAA,EAUT,IAAM+B,EAAcL,AARsD,CACxEC,UAAW,CAAEC,SAAU,OAAQ7B,OAAQ,MAAO,EAC9C8B,WAAY,CAAE7D,MAAO,OAAQ+B,OAAQ,MAAO,CAC9C,CAKiC,CAACS,EAAO,CACrCuB,IACF9B,EAAQ,CAAE,GAAGA,CAAK,CAAE,CADL,EACQ8B,CAAW,CAAC,EAErC,IAAMC,EARoD,AAQtCF,CAPlBD,WAAY,QACZ7B,KAAM,OACR,CAKiC,CAACQ,EAAO,CACrCwB,GAAe,CAAC/D,GAClBA,GAAQ+D,CADiB,AACjBA,CAEZ,CAEA,IAAIC,EAAY,GACZlH,EAAWyC,EAAOQ,GAClBhD,EAAYwC,EAAOuC,GAGvB,GA/OE,CAAC,AA+OC3C,AAjPkBH,CAElBA,CA+OeA,CAjP6B,GAG/B,UAAf,EACCD,KADMC,IACND,EAAgBC,QACfE,CARoCV,IAQlBQ,AARdA,EAAwBA,GAQVA,AARa,CAQM,CA4OhB,CACvB,IAAMiF,EAAkBlF,EAAgBC,GAAOA,EAAIC,OAAO,CAAGD,EAE7D,GAAI,CAACiF,EAAgBjF,GAAG,CACtB,CADwB,KAClB,OAAA,cAIL,CAJK,AAAIoE,MACP,8IAA6Ic,KAAKC,SAAS,CAC1JF,IAFE,oBAAA,OAAA,mBAAA,gBAAA,CAIN,GAEF,GAAI,CAACA,EAAgBnC,MAAM,EAAI,CAACmC,EAAgBlE,KAAK,CACnD,CADqD,KAC/C,OAAA,cAIL,CAJSqD,AAAJ,MACH,2JAA0Jc,KAAKC,SAAS,CACvKF,IAFE,oBAAA,OAAA,kBAAA,gBAAA,CAIN,GAQF,GALAjH,EAAYiH,EAAgBjH,SAAS,CACrCC,EAAagH,EAAgBhH,UAAU,CACvCC,EAAcA,GAAe+G,EAAgB/G,WAAW,CACxD8G,EAAYC,EAAgBjF,GAAG,CAE3B,CAAC+C,EACH,GAAI,AAACjF,CADI,EACSC,GAGX,GAAID,GAHM,AAGM,CAACC,CAHK,CAGM,CACjC,IAAMqH,EAAQtH,EAAWmH,EAAgBlE,KAAK,CAC9ChD,EAAY0D,KAAK4D,KAAK,CAACJ,EAAgBnC,MAAM,CAAGsC,EAClD,MAAO,GAAI,CAACtH,GAAYC,EAAW,CACjC,IAAMqH,EAAQrH,EAAYkH,EAAgBnC,MAAM,CAChDhF,EAAW2D,KAAK4D,KAAK,CAACJ,EAAgBlE,KAAK,CAAGqE,GAChD,MAREtH,EAAWmH,EAAgBlE,KAAK,CAChChD,EAAYkH,EAAgBnC,MAAM,AASxC,CAGA,IAAIwC,EACF,CAAC3C,IAAyB,QAAZC,CAAAA,GAAsB,KAAmB,IAAZA,CAAY,CAAU,EAC/D,CAJJ5C,AAIKA,GAJgB,UAAf,OAAOA,EAAmBA,EAAMgF,CAAAA,GAI1BhF,EAAIuF,UAAU,CAAC,UAAYvF,EAAIuF,UAAU,CAAC,QAAA,GAAU,CAE9D5F,GAAc,EACd2F,GAAS,GAEPlD,EAAOzC,WAAW,EAAE,CACtBA,GAAc,CAAA,EAGd0E,GACA,CAACjC,EAAOhD,mBAAmB,EAC3BY,EAAIwF,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACC,QAAQ,CAAC,SAC9B,AAGA9F,GAAc,EAAA,EAGhB,IAAM+F,EAAanF,EAAO8B,GA2NpBmF,EAAWf,OAAOgB,MAAM,CAC5B1E,EACI,CACE6C,SAAU,WACV9C,OAAQ,OACR/B,MAAO,OACP2G,KAAM,EACNC,IAAK,EACLC,MAAO,EACPC,OAAQ,YACR1J,EACAqF,gBACF,EACA,CAAC,EACLK,EAAc,CAAC,EAAI,CAAEiE,MAAO,aAAc,EAC1C9E,GAGI+E,EACJ,AAACjE,GAAgC,UAAhBV,EAWb,KAVgB,SAAhBA,EACG,yCAAwCvF,CAAAA,EAAAA,EAAAA,eAAAA,AAAe,EAAC,CACvDC,qBACAC,YACAC,aACAC,EACAC,YAAaA,GAAe,GAC5BC,UAAWqJ,EAASrJ,SAAS,AAC/B,GAAG,KACF,QAAOiF,EAAY,KAAI,AAG1B4E,EAAiB,AAAClI,EAA+BgG,QAAQ,CAC7D0B,EAASrJ,QAJ4C,CAInC,EAGO,SAAvBqJ,EAASrJ,SAAS,CAChB,YACA,AADY,QAFdqJ,EAASrJ,SAAS,CAKlB8J,EAAqCF,EACrC,gBACEC,EACAE,CANuD,kBAMnCV,EAAShE,cAAc,EAAI,UAC/C2E,iBAAkB,4BAClBJ,CACF,EACA,CAAC,EAeCK,EAAgBjG,AA7exB,SAASA,AAAiB,CAQR,EARQ,GAAA,QACxBC,CAAM,KACNpC,CAAG,aACHL,CAAW,OACXoB,CAAK,SACLsB,CAAO,OACPrB,CAAK,CACLlC,QAAM,CACU,CARQ,EASxB,GAAIa,EACF,MAAO,KADQ,AACNK,EAAKsC,YAAQ9C,EAAWwB,WAAOxB,CAAU,EAGpD,GAAM,QAAEmC,CAAM,MAAEG,CAAI,CAAE,CAAGhB,AAxE3B,SAASA,AACP,CAAsC,CACtCC,CAAyB,CACzBC,CAAyB,EAFzB,GAAA,aAAErC,CAAW,UAAEsC,CAAQ,CAAe,CAAtC,EAIA,GAAID,EAAO,CAET,IAAME,EAAkB,qBAClBC,EAAe,EAAE,CACvB,IAAK,IAAIC,EAAQA,EAAQF,EAAgBG,IAAI,CAACL,IAC5CG,EAAaG,EADwCF,EACpC,CAACP,GAD0C,MACjCO,CAAK,CAAC,EAAE,GAErC,GAAID,EAAaI,MAAM,CAAE,CACvB,IAAMC,EAA4C,IAA5BC,KAAKC,GAAG,IAAIP,GAClC,MAAO,CACLQ,OAAQV,EAASW,MAAM,CAAC,AAACC,GAAMA,GAAKlD,CAAW,CAAC,EAAE,CAAG6C,GACrDM,KAAM,GACR,CACF,CACA,MAAO,CAAEH,OAAQV,EAAUa,KAAM,GAAI,CACvC,OACA,AAAqB,UAAU,AAA3B,OAAOf,EACF,CAAEY,OAAQhD,EAAamD,KAAM,GAAI,EAkBnC,CAAEH,OAfM,IACV,IAAIpE,IACL,AACA,AAOA,CAACwD,EAAe,EAARA,AAAU,EAAgB,CAACgB,GAAG,CACpC,AAACC,GAAMf,EAASgB,CADa,GACT,CAAC,AAACC,GAAMA,GAAKF,IAAMf,CAAQ,CAACA,EAASM,MAAM,CAAG,EAAE,GAGzE,CACgBO,KAAM,GAAI,CAC7B,EA+BqCM,EAAQrB,EAAOC,GAC5CuB,EA7CmE,AA6C5DZ,EA9C8D,AA8CvDJ,MAAM,CAAG,EAE7B,MAAO,CACLP,MAAQA,AAAD,GAAmB,MAATc,EAAyBd,EAAV,QAChCsB,OAAQX,EACLI,GAAG,CACF,CAACC,EAAGQ,IACC1D,EAAO,QAAEsD,MAAQpC,UAAKqC,EAAStB,MAAOiB,CAAE,GAAG,KACnC,CAATF,KAAAA,EAAeE,EAAIQ,GAAI,CAAA,CACtBV,GAENW,IAAI,CAAC,MAQRzC,IAAKlB,EAAO,QAAEsD,MAAQpC,UAAKqC,EAAStB,MAAOY,CAAM,CAACY,EAAK,AAAC,EAC1D,CACF,EA0cyC,QACrCH,MACApC,cACAL,EACAoB,MAAOjD,EACPuE,QAASqD,QACT1E,EACAlC,QACF,GA4BA,MAAO,CAAE4J,MAde,CACtB,GAAG/E,CAAI,CACPf,QAAS0C,EAAS,OAAS1C,gBAC3BS,EACAtC,MAAOjD,EACPgF,OAAQ/E,EACRuF,qBACAT,EACAG,MAAO,CAAE,GAAGwE,CAAQ,CAAE,GAAGS,CAAgB,AAAC,EAC1CjH,MAAOoH,EAAcpH,KAAK,CAC1BsB,OAAQ8F,EAAc9F,MAAM,CAC5BtC,IAAKiD,GAAemF,EAAcpI,GAAG,AACvC,EAEgB2I,KADH,aAAEhJ,WAAagD,cAAUS,OAAaL,CAAK,CACnC,CACvB,mBC1uBA,GAAM,yBAAE,CAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,MAEjC,EAAsB,CAAC,CAAC,EAAwB,+KAFhD,GAAM,yBAAE,CAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,MAEjC,EAAsB,CAAC,CAAC,EAAwB,mOEChD,SAASgB,EAAc,CAKM,MAkFzB3B,EAvFmB,GAAA,QACrBA,CAAM,KACNpC,CAAG,OACHe,CAAK,SACLsB,CAAO,CACoB,CALN,EAqFfgM,EACJhM,GAAAA,CACgB,MAAhBD,CADAC,EACAD,EAAO1C,SAAAA,AAAS,EAAA,KAAA,EAAhB0C,EAAkBkM,MAAM,CAAC,CAACC,EAAMC,IAC9B/M,KAAKgN,GAAG,CAACD,MAAMX,AAAapM,KAAKgN,GAAG,CAACF,MAAoBC,CAAbX,CAAmBU,EAAAA,CAAAA,EA1FnD,GA4FdV,AAEF,OAAUzL,EAAOvD,IAAI,CAAC,QAAO6P,mBAAmB1O,GAAK,MAAKe,EAAM,MAAKsN,GACnErO,CAAAA,CAAIuF,UAAU,CAAC,wBAEX,EAAC,CAFqCpI,AAI9C,QAJsDC,GAAG,AACX,CADYuR,kBAAkB,YACnE,UAAOxR,QAAQC,GAAG,CAACuR,UAS5B,UAAA,qCAAA,KAFA5K,EAAc6K,kBAAkB,EAAG,MAEnC,EAAe7K,uKC1Ef,OAAoB,CAAA,kBAApB,GAjBgB8K,aAAa,CAAA,kBAAbA,0BAbY,CAAA,CAAA,IAAA,OACN,CAAA,CAAA,IAAA,WAGI,CAAA,CAAA,IAAA,IASnB,SAASA,EAAcC,CAAoB,EAChD,GAAM,OAAEpG,CAAK,CAAE,CAAG9I,GAAAA,EAAAA,WAAAA,AAAW,EAACkP,EAAU,CACtC/K,cAAAA,EAAAA,OAAa,CAEbH,OAAAA,CAAsC,CAA7BzG,QAAQC,GAAG,CAAC0L,iBAAiB,yIACxC,GAIA,IAAK,GAAM,CAACiG,EAAK5E,EAAM,GAAI1D,OAAOC,OAAO,CAACgC,OAAQ,CAClClJ,IAAV2K,GACF,IADuB,GAChBzB,CAAK,CAACqG,EAA0B,CAG3C,MAAO,OAAErG,CAAM,CACjB,KAEA,EAAeE,EAAAA,KAAK,iBCjCpB,EAAO,OAAO,CAAA,EAAA,CAAA,CAAA,wBCCd,GAAM,yBAAE,CAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,MAEjC,EAAsB,CAAC,CAAC,EAAwB,4KAFhD,GAAM,yBAAE,CAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,MAEjC,EAAsB,CAAC,CAAC,EAAwB,gTEDzC,IAAM,EAAY,CAAA,EAAA,AADzB,EAAA,CAAA,CAAA,MACyB,uBAAA,AAAuB,EAC5C,WAAa,MAAM,AAAI,MAAM,gOAAkO,EAC/P,4DACA,8DAHG,IAAM,EAAY,CAAA,EAAA,AADzB,EAAA,CAAA,CAAA,MACyB,uBAAA,AAAuB,EAC5C,WAAa,MAAU,AAAJ,MAAU,gOAAkO,EAC/P,wCACA,6HCHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,MACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,6SAA+S,EAC5U,4EACA,gEAHW,CAAA,EAAA,AADf,EAAA,CAAA,CAAA,MACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,yRAA2R,EACxT,wDACA,2HCHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,MACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,mSAAqS,EAClU,kEACA,+DAHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,MACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,+QAAiR,EAC9S,8CACA,0IELJ,EAAA,EAAA,CAAA,CAAA,YCCe,CAAE,IADjB,AACiB,EADjB,CAAA,CAAA,OACiB,OAAG,CAAE,MAAO,KAAM,OAAQ,IAAK,UAAW,EAAG,WAAY,EAAG,YAAa,qZAAqZ,EDEhe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0GACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAK,CAAA,CACJ,IAAI,WACJ,UAAU,qCACV,QAAQ,QACR,KAAK,eACL,IAAK,KAIb,CDfA,IAAA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,wDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,iEACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6GAAoG,+BAGlH,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CACL,UAAU,qDACV,KAAM,cACP,0BAKL,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,gEAAuD,wBAQjF,CMFA,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,EAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,OAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAxBE,CAClC,AAuB4C,CAvB3C,AAuB2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAvBnC,AAuBmC,CAvBnC,AAAE,AAuBiC,CAAU,CAAA,AAvBxC,CAAA,CAAA,CAAA,CAAA,sCAA2C,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,EAEtE,CAAA,CAAA,CAAA,CAAA,CAAA,KAEK,CAAA,CAAA,CAAA,CAAA,8GACH,GAAA,CAAA,AAAK,CAAA,CAAA,QAET,CACA,CAAC,OAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAW,IAAK,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAU,CAAA,CACxC,CAAC,OAAQ,CAAA,AAAE,EAAG,CAAA,CAAA,CAAA,QAAY,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA,CAC3C,EFbA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,ODFO,IAAM,EAAa,CAMxB,MAAO,CACL,QAAS,yBACT,SAAU,0CACV,UAAW,sCACX,QAAS,mCACX,CA0BF,ECjCA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEO,SAAS,IACd,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,8CAA8C,GAAG,kBACjE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+BACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2EACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mEACb,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,OAAI,CAAA,CACH,aAAW,UACX,UAAU,mCACV,KAAK,cAEL,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAa,UAAU,gCACxB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,UAAK,mBAER,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,mEAA0D,sFAIzE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,UAAU,0EACV,KAAM,EAAW,KAAK,CAAC,OAAO,CAC9B,IAAI,aACJ,OAAO,kBAEP,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAU,yBACV,OAAO,OACP,QAAQ,YACR,MAAM,OACN,EAAE,MACF,MAAM,6BACN,EAAE,gBAEF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,UAAM,YACP,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,EAAE,+RAGZ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,YAAY,aACvB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,UAAU,0EACV,KAAM,EAAW,KAAK,CAAC,SAAS,CAChC,IAAI,aACJ,OAAO,kBAEP,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAU,yBACV,OAAO,OACP,QAAQ,YACR,MAAM,OACN,EAAE,MACF,MAAM,6BACN,EAAE,gBAEF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,UAAM,cACP,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,EAAE,0lBAIZ,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,SAAS,CAAA,CAAC,YAAY,aACvB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,UAAU,0EACV,KAAM,EAAW,KAAK,CAAC,OAAO,CAC9B,IAAI,aACJ,OAAO,kBAEP,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAU,yBACV,OAAO,OACP,QAAQ,YACR,MAAM,OACN,MAAM,uCAEN,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,UAAM,YACP,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,EAAE,+SAMf,EAAA,KAAK,CAAC,GAAG,CAAC,AAAC,GACV,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CACC,UAAU,oDAGV,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,6BAAqB,EAAK,KAAK,GAC/C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,qBACX,EAAK,KAAK,CAAC,GAAG,CAAC,AAAC,GACf,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAM,EAAK,IAAI,CACd,GAAI,EAAK,QAAQ,CACd,CACE,OAAQ,SACR,IAAK,qBACP,EACA,CAAC,CAAC,CACN,UAAU,yFAET,EAAK,KAAK,IAXN,EAAK,KAAK,OALlB,EAAK,KAAK,GAwBnB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,uDACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,6BAAoB,gCAGpC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,gEAAuD,2EAKtE,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAa,CAAA,CAAA,SAGlB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,IAAA,EAAC,IAAA,CAAE,UAAU,iEAAuD,KAC/D,IAAI,OAAO,WAAW,GAAG,8BAO1C,CCtIA,IAAA,EAAA,EAAA,CAAA,CAAA,MAEe,SAAS,EAAU,UAAE,CAAQ,CAAiC,EAC3E,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qGACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAU,CAAA,CAAA,GACX,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,oEACd,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACA,KAEH,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,KAGP", "ignoreList": [0, 1, 2, 3, 4, 5, 7, 8, 9, 10, 12, 13, 14, 21]}