module.exports=[5218,(a,b,c)=>{"use strict";c._=function(a){return a&&a.__esModule?a:{default:a}}},8171,(a,b,c)=>{b.exports=a.x("react/jsx-runtime",()=>require("react/jsx-runtime"))},27669,(a,b,c)=>{b.exports=a.x("react",()=>require("react"))},77058,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return t},MissingStaticPage:function(){return s},NormalizeError:function(){return q},PageNotFoundError:function(){return r},SP:function(){return n},ST:function(){return o},WEB_VITALS:function(){return d},execOnce:function(){return e},getDisplayName:function(){return j},getLocationOrigin:function(){return h},getURL:function(){return i},isAbsoluteUrl:function(){return g},isResSent:function(){return k},loadGetInitialProps:function(){return m},normalizeRepeatedSlashes:function(){return l},stringifyError:function(){return u}});let d=["CLS","FCP","FID","INP","LCP","TTFB"];function e(a){let b,c=!1;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return c||(c=!0,b=a(...e)),b}}let f=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,g=a=>f.test(a);function h(){let{protocol:a,hostname:b,port:c}=window.location;return a+"//"+b+(c?":"+c:"")}function i(){let{href:a}=window.location,b=h();return a.substring(b.length)}function j(a){return"string"==typeof a?a:a.displayName||a.name||"Unknown"}function k(a){return a.finished||a.headersSent}function l(a){let b=a.split("?");return b[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(b[1]?"?"+b.slice(1).join("?"):"")}async function m(a,b){let c=b.res||b.ctx&&b.ctx.res;if(!a.getInitialProps)return b.ctx&&b.Component?{pageProps:await m(b.Component,b.ctx)}:{};let d=await a.getInitialProps(b);if(c&&k(c))return d;if(!d)throw Object.defineProperty(Error('"'+j(a)+'.getInitialProps()" should resolve to an object. But found "'+d+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return d}let n="undefined"!=typeof performance,o=n&&["mark","measure","getEntriesByName"].every(a=>"function"==typeof performance[a]);class p extends Error{}class q extends Error{}class r extends Error{constructor(a){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+a}}class s extends Error{constructor(a,b){super(),this.message="Failed to load static file for page: "+a+" "+b}}class t extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function u(a){return JSON.stringify({message:a.message,stack:a.stack})}},37313,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return i}});let d=a.r(5218),e=a.r(8171),f=d._(a.r(27669)),g=a.r(77058);async function h(a){let{Component:b,ctx:c}=a;return{pageProps:await (0,g.loadGetInitialProps)(b,c)}}class i extends f.default.Component{render(){let{Component:a,pageProps:b}=this.props;return(0,e.jsx)(a,{...b})}}i.origGetInitialProps=h,i.getInitialProps=h,("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},39243,(a,b,c)=>{b.exports=a.r(37313)}];

//# sourceMappingURL=%5Broot-of-the-server%5D__dfc6ae05._.js.map