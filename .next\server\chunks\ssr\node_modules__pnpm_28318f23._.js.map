{"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/cjs/_interop_require_default.cjs", "turbopack:///[project]/node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/side-effect.tsx", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/server/route-modules/app-page/vendored/contexts/amp-context.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/server/route-modules/app-page/vendored/contexts/head-manager-context.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/amp-mode.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/utils/warn-once.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/head.tsx", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/image-blur-svg.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/image-config.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/get-img-props.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/server/route-modules/app-page/vendored/contexts/image-config-context.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/server/route-modules/app-page/vendored/contexts/router-context.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/shared/lib/image-loader.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/client/use-merged-ref.ts", "turbopack:///[project]/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/src/client/image-component.tsx"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n", "\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n", "import type React from 'react'\nimport { Children, useEffect, useLayoutEffect, type JSX } from 'react'\n\ntype State = JSX.Element[] | undefined\n\nexport type SideEffectProps = {\n  reduceComponentsToState: <T extends {}>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n  children: React.ReactNode\n}\n\nconst isServer = typeof window === 'undefined'\nconst useClientOnlyLayoutEffect = isServer ? () => {} : useLayoutEffect\nconst useClientOnlyEffect = isServer ? () => {} : useEffect\n\nexport default function SideEffect(props: SideEffectProps) {\n  const { headManager, reduceComponentsToState } = props\n\n  function emitChange() {\n    if (headManager && headManager.mountedInstances) {\n      const headElements = Children.toArray(\n        Array.from(headManager.mountedInstances as Set<React.ReactNode>).filter(\n          Boolean\n        )\n      ) as React.ReactElement[]\n      headManager.updateHead(reduceComponentsToState(headElements, props))\n    }\n  }\n\n  if (isServer) {\n    headManager?.mountedInstances?.add(props.children)\n    emitChange()\n  }\n\n  useClientOnlyLayoutEffect(() => {\n    headManager?.mountedInstances?.add(props.children)\n    return () => {\n      headManager?.mountedInstances?.delete(props.children)\n    }\n  })\n\n  // We need to call `updateHead` method whenever the `SideEffect` is trigger in all\n  // life-cycles: mount, update, unmount. However, if there are multiple `SideEffect`s\n  // being rendered, we only trigger the method from the last one.\n  // This is ensured by keeping the last unflushed `updateHead` in the `_pendingUpdate`\n  // singleton in the layout effect pass, and actually trigger it in the effect pass.\n  useClientOnlyLayoutEffect(() => {\n    if (headManager) {\n      headManager._pendingUpdate = emitChange\n    }\n    return () => {\n      if (headManager) {\n        headManager._pendingUpdate = emitChange\n      }\n    }\n  })\n\n  useClientOnlyEffect(() => {\n    if (headManager && headManager._pendingUpdate) {\n      headManager._pendingUpdate()\n      headManager._pendingUpdate = null\n    }\n    return () => {\n      if (headManager && headManager._pendingUpdate) {\n        headManager._pendingUpdate()\n        headManager._pendingUpdate = null\n      }\n    }\n  })\n\n  return null\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AmpContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HeadManagerContext\n", "export function isInAmpMode({\n  ampFirst = false,\n  hybrid = false,\n  hasQuery = false,\n} = {}): boolean {\n  return ampFirst || (hybrid && hasQuery)\n}\n", "let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n", "'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport Effect from './side-effect'\nimport { AmpStateContext } from './amp-context.shared-runtime'\nimport { HeadManagerContext } from './head-manager-context.shared-runtime'\nimport { isInAmpMode } from './amp-mode'\nimport { warnOnce } from './utils/warn-once'\n\ntype WithInAmpMode = {\n  inAmpMode?: boolean\n}\n\nexport function defaultHead(inAmpMode = false): JSX.Element[] {\n  const head = [<meta charSet=\"utf-8\" key=\"charset\" />]\n  if (!inAmpMode) {\n    head.push(\n      <meta name=\"viewport\" content=\"width=device-width\" key=\"viewport\" />\n    )\n  }\n  return head\n}\n\nfunction onlyReactElement(\n  list: Array<React.ReactElement<any>>,\n  child: React.ReactElement | number | string\n): Array<React.ReactElement<any>> {\n  // React children can be \"string\" or \"number\" in this case we ignore them for backwards compat\n  if (typeof child === 'string' || typeof child === 'number') {\n    return list\n  }\n  // Adds support for React.Fragment\n  if (child.type === React.Fragment) {\n    return list.concat(\n      // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n      React.Children.toArray(child.props.children).reduce(\n        // @ts-expect-error @types/react does not remove fragments but this could also return ReactPortal[]\n        (\n          fragmentList: Array<React.ReactElement<any>>,\n          fragmentChild: React.ReactElement | number | string\n        ): Array<React.ReactElement<any>> => {\n          if (\n            typeof fragmentChild === 'string' ||\n            typeof fragmentChild === 'number'\n          ) {\n            return fragmentList\n          }\n          return fragmentList.concat(fragmentChild)\n        },\n        []\n      )\n    )\n  }\n  return list.concat(child)\n}\n\nconst METATYPES = ['name', 'httpEquiv', 'charSet', 'itemProp']\n\n/*\n returns a function for filtering head child elements\n which shouldn't be duplicated, like <title/>\n Also adds support for deduplicated `key` properties\n*/\nfunction unique() {\n  const keys = new Set()\n  const tags = new Set()\n  const metaTypes = new Set()\n  const metaCategories: { [metatype: string]: Set<string> } = {}\n\n  return (h: React.ReactElement<any>) => {\n    let isUnique = true\n    let hasKey = false\n\n    if (h.key && typeof h.key !== 'number' && h.key.indexOf('$') > 0) {\n      hasKey = true\n      const key = h.key.slice(h.key.indexOf('$') + 1)\n      if (keys.has(key)) {\n        isUnique = false\n      } else {\n        keys.add(key)\n      }\n    }\n\n    // eslint-disable-next-line default-case\n    switch (h.type) {\n      case 'title':\n      case 'base':\n        if (tags.has(h.type)) {\n          isUnique = false\n        } else {\n          tags.add(h.type)\n        }\n        break\n      case 'meta':\n        for (let i = 0, len = METATYPES.length; i < len; i++) {\n          const metatype = METATYPES[i]\n          if (!h.props.hasOwnProperty(metatype)) continue\n\n          if (metatype === 'charSet') {\n            if (metaTypes.has(metatype)) {\n              isUnique = false\n            } else {\n              metaTypes.add(metatype)\n            }\n          } else {\n            const category = h.props[metatype]\n            const categories = metaCategories[metatype] || new Set()\n            if ((metatype !== 'name' || !hasKey) && categories.has(category)) {\n              isUnique = false\n            } else {\n              categories.add(category)\n              metaCategories[metatype] = categories\n            }\n          }\n        }\n        break\n    }\n\n    return isUnique\n  }\n}\n\n/**\n *\n * @param headChildrenElements List of children of <Head>\n */\nfunction reduceComponents<T extends {} & WithInAmpMode>(\n  headChildrenElements: Array<React.ReactElement<any>>,\n  props: T\n) {\n  const { inAmpMode } = props\n  return headChildrenElements\n    .reduce(onlyReactElement, [])\n    .reverse()\n    .concat(defaultHead(inAmpMode).reverse())\n    .filter(unique())\n    .reverse()\n    .map((c: React.ReactElement<any>, i: number) => {\n      const key = c.key || i\n      if (process.env.NODE_ENV === 'development') {\n        // omit JSON-LD structured data snippets from the warning\n        if (c.type === 'script' && c.props['type'] !== 'application/ld+json') {\n          const srcMessage = c.props['src']\n            ? `<script> tag with src=\"${c.props['src']}\"`\n            : `inline <script>`\n          warnOnce(\n            `Do not add <script> tags using next/head (see ${srcMessage}). Use next/script instead. \\nSee more info here: https://nextjs.org/docs/messages/no-script-tags-in-head-component`\n          )\n        } else if (c.type === 'link' && c.props['rel'] === 'stylesheet') {\n          warnOnce(\n            `Do not add stylesheets using next/head (see <link rel=\"stylesheet\"> tag with href=\"${c.props['href']}\"). Use Document instead. \\nSee more info here: https://nextjs.org/docs/messages/no-stylesheets-in-head-component`\n          )\n        }\n      }\n      return React.cloneElement(c, { key })\n    })\n}\n\n/**\n * This component injects elements to `<head>` of your page.\n * To avoid duplicated `tags` in `<head>` you can use the `key` property, which will make sure every tag is only rendered once.\n */\nfunction Head({ children }: { children: React.ReactNode }) {\n  const ampState = useContext(AmpStateContext)\n  const headManager = useContext(HeadManagerContext)\n  return (\n    <Effect\n      reduceComponentsToState={reduceComponents}\n      headManager={headManager}\n      inAmpMode={isInAmpMode(ampState)}\n    >\n      {children}\n    </Effect>\n  )\n}\n\nexport default Head\n", "/**\n * A shared function, used on both client and server, to generate a SVG blur placeholder.\n */\nexport function getImageBlurSvg({\n  widthInt,\n  heightInt,\n  blurWidth,\n  blurHeight,\n  blurDataURL,\n  objectFit,\n}: {\n  widthInt?: number\n  heightInt?: number\n  blurWidth?: number\n  blurHeight?: number\n  blurDataURL: string\n  objectFit?: string\n}): string {\n  const std = 20\n  const svgWidth = blurWidth ? blurWidth * 40 : widthInt\n  const svgHeight = blurHeight ? blurHeight * 40 : heightInt\n\n  const viewBox =\n    svgWidth && svgHeight ? `viewBox='0 0 ${svgWidth} ${svgHeight}'` : ''\n  const preserveAspectRatio = viewBox\n    ? 'none'\n    : objectFit === 'contain'\n      ? 'xMidYMid'\n      : objectFit === 'cover'\n        ? 'xMidYMid slice'\n        : 'none'\n\n  return `%3Csvg xmlns='http://www.w3.org/2000/svg' ${viewBox}%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='${std}'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='${preserveAspectRatio}' style='filter: url(%23b);' href='${blurDataURL}'/%3E%3C/svg%3E`\n}\n", "export const VALID_LOADERS = [\n  'default',\n  'imgix',\n  'cloudinary',\n  'akamai',\n  'custom',\n] as const\n\nexport type LoaderValue = (typeof VALID_LOADERS)[number]\n\nexport type ImageLoaderProps = {\n  src: string\n  width: number\n  quality?: number\n}\n\nexport type ImageLoaderPropsWithConfig = ImageLoaderProps & {\n  config: Readonly<ImageConfig>\n}\n\nexport type LocalPattern = {\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\nexport type RemotePattern = {\n  /**\n   * Must be `http` or `https`.\n   */\n  protocol?: 'http' | 'https'\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single subdomain.\n   * Double `**` matches any number of subdomains.\n   */\n  hostname: string\n\n  /**\n   * Can be literal port such as `8080` or empty string\n   * meaning no port.\n   */\n  port?: string\n\n  /**\n   * Can be literal or wildcard.\n   * Single `*` matches a single path segment.\n   * Double `**` matches any number of path segments.\n   */\n  pathname?: string\n\n  /**\n   * Can be literal query string such as `?v=1` or\n   * empty string meaning no query string.\n   */\n  search?: string\n}\n\ntype ImageFormat = 'image/avif' | 'image/webp'\n\n/**\n * Image configurations\n *\n * @see [Image configuration options](https://nextjs.org/docs/api-reference/next/image#configuration-options)\n */\nexport type ImageConfigComplete = {\n  /** @see [Device sizes documentation](https://nextjs.org/docs/api-reference/next/image#device-sizes) */\n  deviceSizes: number[]\n\n  /** @see [Image sizing documentation](https://nextjs.org/docs/app/building-your-application/optimizing/images#image-sizing) */\n  imageSizes: number[]\n\n  /** @see [Image loaders configuration](https://nextjs.org/docs/api-reference/next/legacy/image#loader) */\n  loader: LoaderValue\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/app/api-reference/components/image#path) */\n  path: string\n\n  /** @see [Image loader configuration](https://nextjs.org/docs/api-reference/next/image#loader-configuration) */\n  loaderFile: string\n\n  /**\n   * @deprecated Use `remotePatterns` instead.\n   */\n  domains: string[]\n\n  /** @see [Disable static image import configuration](https://nextjs.org/docs/api-reference/next/image#disable-static-imports) */\n  disableStaticImages: boolean\n\n  /** @see [Cache behavior](https://nextjs.org/docs/api-reference/next/image#caching-behavior) */\n  minimumCacheTTL: number\n\n  /** @see [Acceptable formats](https://nextjs.org/docs/api-reference/next/image#acceptable-formats) */\n  formats: ImageFormat[]\n\n  /** @see [Dangerously Allow SVG](https://nextjs.org/docs/api-reference/next/image#dangerously-allow-svg) */\n  dangerouslyAllowSVG: boolean\n\n  /** @see [Content Security Policy](https://nextjs.org/docs/api-reference/next/image#contentsecuritypolicy) */\n  contentSecurityPolicy: string\n\n  /** @see [Content Disposition Type](https://nextjs.org/docs/api-reference/next/image#contentdispositiontype) */\n  contentDispositionType: 'inline' | 'attachment'\n\n  /** @see [Remote Patterns](https://nextjs.org/docs/api-reference/next/image#remotepatterns) */\n  remotePatterns: Array<URL | RemotePattern>\n\n  /** @see [Local Patterns](https://nextjs.org/docs/api-reference/next/image#localPatterns) */\n  localPatterns: LocalPattern[] | undefined\n\n  /** @see [Qualities](https://nextjs.org/docs/api-reference/next/image#qualities) */\n  qualities: number[] | undefined\n\n  /** @see [Unoptimized](https://nextjs.org/docs/api-reference/next/image#unoptimized) */\n  unoptimized: boolean\n}\n\nexport type ImageConfig = Partial<ImageConfigComplete>\n\nexport const imageConfigDefault: ImageConfigComplete = {\n  deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],\n  imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],\n  path: '/_next/image',\n  loader: 'default',\n  loaderFile: '',\n  domains: [],\n  disableStaticImages: false,\n  minimumCacheTTL: 60,\n  formats: ['image/webp'],\n  dangerouslyAllowSVG: false,\n  contentSecurityPolicy: `script-src 'none'; frame-src 'none'; sandbox;`,\n  contentDispositionType: 'attachment',\n  localPatterns: undefined, // default: allow all local images\n  remotePatterns: [], // default: allow no remote images\n  qualities: undefined, // default: allow all qualities\n  unoptimized: false,\n}\n", "import { warnOnce } from './utils/warn-once'\nimport { getImageBlurSvg } from './image-blur-svg'\nimport { imageConfigDefault } from './image-config'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n  ImageLoaderPropsWithConfig,\n} from './image-config'\n\nimport type { CSSProperties, JSX } from 'react'\n\nexport interface StaticImageData {\n  src: string\n  height: number\n  width: number\n  blurDataURL?: string\n  blurWidth?: number\n  blurHeight?: number\n}\n\nexport interface StaticRequire {\n  default: StaticImageData\n}\n\nexport type StaticImport = StaticRequire | StaticImageData\n\nexport type ImageProps = Omit<\n  JSX.IntrinsicElements['img'],\n  'src' | 'srcSet' | 'ref' | 'alt' | 'width' | 'height' | 'loading'\n> & {\n  src: string | StaticImport\n  alt: string\n  width?: number | `${number}`\n  height?: number | `${number}`\n  fill?: boolean\n  loader?: ImageLoader\n  quality?: number | `${number}`\n  priority?: boolean\n  loading?: LoadingValue\n  placeholder?: PlaceholderValue\n  blurDataURL?: string\n  unoptimized?: boolean\n  overrideSrc?: string\n  /**\n   * @deprecated Use `onLoad` instead.\n   * @see https://nextjs.org/docs/app/api-reference/components/image#onload\n   */\n  onLoadingComplete?: OnLoadingComplete\n  /**\n   * @deprecated Use `fill` prop instead of `layout=\"fill\"` or change import to `next/legacy/image`.\n   * @see https://nextjs.org/docs/api-reference/next/legacy/image\n   */\n  layout?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectFit?: string\n  /**\n   * @deprecated Use `style` prop instead.\n   */\n  objectPosition?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyBoundary?: string\n  /**\n   * @deprecated This prop does not do anything.\n   */\n  lazyRoot?: string\n}\n\nexport type ImgProps = Omit<ImageProps, 'src' | 'loader'> & {\n  loading: LoadingValue\n  width: number | undefined\n  height: number | undefined\n  style: NonNullable<JSX.IntrinsicElements['img']['style']>\n  sizes: string | undefined\n  srcSet: string | undefined\n  src: string\n}\n\nconst VALID_LOADING_VALUES = ['lazy', 'eager', undefined] as const\n\n// Object-fit values that are not valid background-size values\nconst INVALID_BACKGROUND_SIZE_VALUES = [\n  '-moz-initial',\n  'fill',\n  'none',\n  'scale-down',\n  undefined,\n]\ntype LoadingValue = (typeof VALID_LOADING_VALUES)[number]\ntype ImageConfig = ImageConfigComplete & {\n  allSizes: number[]\n  output?: 'standalone' | 'export'\n}\n\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\n// Do not export - this is an internal type only\n// because `next.config.js` is only meant for the\n// built-in loaders, not for a custom loader() prop.\ntype ImageLoaderWithConfig = (p: ImageLoaderPropsWithConfig) => string\n\nexport type PlaceholderValue = 'blur' | 'empty' | `data:image/${string}`\nexport type OnLoad = React.ReactEventHandler<HTMLImageElement> | undefined\nexport type OnLoadingComplete = (img: HTMLImageElement) => void\n\nexport type PlaceholderStyle = Partial<\n  Pick<\n    CSSProperties,\n    | 'backgroundSize'\n    | 'backgroundPosition'\n    | 'backgroundRepeat'\n    | 'backgroundImage'\n  >\n>\n\nfunction isStaticRequire(\n  src: StaticRequire | StaticImageData\n): src is StaticRequire {\n  return (src as StaticRequire).default !== undefined\n}\n\nfunction isStaticImageData(\n  src: StaticRequire | StaticImageData\n): src is StaticImageData {\n  return (src as StaticImageData).src !== undefined\n}\n\nfunction isStaticImport(src: string | StaticImport): src is StaticImport {\n  return (\n    !!src &&\n    typeof src === 'object' &&\n    (isStaticRequire(src as StaticImport) ||\n      isStaticImageData(src as StaticImport))\n  )\n}\n\nconst allImgs = new Map<\n  string,\n  { src: string; priority: boolean; placeholder: PlaceholderValue }\n>()\nlet perfObserver: PerformanceObserver | undefined\n\nfunction getInt(x: unknown): number | undefined {\n  if (typeof x === 'undefined') {\n    return x\n  }\n  if (typeof x === 'number') {\n    return Number.isFinite(x) ? x : NaN\n  }\n  if (typeof x === 'string' && /^[0-9]+$/.test(x)) {\n    return parseInt(x, 10)\n  }\n  return NaN\n}\n\nfunction getWidths(\n  { deviceSizes, allSizes }: ImageConfig,\n  width: number | undefined,\n  sizes: string | undefined\n): { widths: number[]; kind: 'w' | 'x' } {\n  if (sizes) {\n    // Find all the \"vw\" percent sizes used in the sizes prop\n    const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g\n    const percentSizes = []\n    for (let match; (match = viewportWidthRe.exec(sizes)); match) {\n      percentSizes.push(parseInt(match[2]))\n    }\n    if (percentSizes.length) {\n      const smallestRatio = Math.min(...percentSizes) * 0.01\n      return {\n        widths: allSizes.filter((s) => s >= deviceSizes[0] * smallestRatio),\n        kind: 'w',\n      }\n    }\n    return { widths: allSizes, kind: 'w' }\n  }\n  if (typeof width !== 'number') {\n    return { widths: deviceSizes, kind: 'w' }\n  }\n\n  const widths = [\n    ...new Set(\n      // > This means that most OLED screens that say they are 3x resolution,\n      // > are actually 3x in the green color, but only 1.5x in the red and\n      // > blue colors. Showing a 3x resolution image in the app vs a 2x\n      // > resolution image will be visually the same, though the 3x image\n      // > takes significantly more data. Even true 3x resolution screens are\n      // > wasteful as the human eye cannot see that level of detail without\n      // > something like a magnifying glass.\n      // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n      [width, width * 2 /*, width * 3*/].map(\n        (w) => allSizes.find((p) => p >= w) || allSizes[allSizes.length - 1]\n      )\n    ),\n  ]\n  return { widths, kind: 'x' }\n}\n\ntype GenImgAttrsData = {\n  config: ImageConfig\n  src: string\n  unoptimized: boolean\n  loader: ImageLoaderWithConfig\n  width?: number\n  quality?: number\n  sizes?: string\n}\n\ntype GenImgAttrsResult = {\n  src: string\n  srcSet: string | undefined\n  sizes: string | undefined\n}\n\nfunction generateImgAttrs({\n  config,\n  src,\n  unoptimized,\n  width,\n  quality,\n  sizes,\n  loader,\n}: GenImgAttrsData): GenImgAttrsResult {\n  if (unoptimized) {\n    return { src, srcSet: undefined, sizes: undefined }\n  }\n\n  const { widths, kind } = getWidths(config, width, sizes)\n  const last = widths.length - 1\n\n  return {\n    sizes: !sizes && kind === 'w' ? '100vw' : sizes,\n    srcSet: widths\n      .map(\n        (w, i) =>\n          `${loader({ config, src, quality, width: w })} ${\n            kind === 'w' ? w : i + 1\n          }${kind}`\n      )\n      .join(', '),\n\n    // It's intended to keep `src` the last attribute because React updates\n    // attributes in order. If we keep `src` the first one, Safari will\n    // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n    // updated by React. That causes multiple unnecessary requests if `srcSet`\n    // and `sizes` are defined.\n    // This bug cannot be reproduced in Chrome or Firefox.\n    src: loader({ config, src, quality, width: widths[last] }),\n  }\n}\n\n/**\n * A shared function, used on both client and server, to generate the props for <img>.\n */\nexport function getImgProps(\n  {\n    src,\n    sizes,\n    unoptimized = false,\n    priority = false,\n    loading,\n    className,\n    quality,\n    width,\n    height,\n    fill = false,\n    style,\n    overrideSrc,\n    onLoad,\n    onLoadingComplete,\n    placeholder = 'empty',\n    blurDataURL,\n    fetchPriority,\n    decoding = 'async',\n    layout,\n    objectFit,\n    objectPosition,\n    lazyBoundary,\n    lazyRoot,\n    ...rest\n  }: ImageProps,\n  _state: {\n    defaultLoader: ImageLoaderWithConfig\n    imgConf: ImageConfigComplete\n    showAltText?: boolean\n    blurComplete?: boolean\n  }\n): {\n  props: ImgProps\n  meta: {\n    unoptimized: boolean\n    priority: boolean\n    placeholder: NonNullable<ImageProps['placeholder']>\n    fill: boolean\n  }\n} {\n  const { imgConf, showAltText, blurComplete, defaultLoader } = _state\n  let config: ImageConfig\n  let c = imgConf || imageConfigDefault\n  if ('allSizes' in c) {\n    config = c as ImageConfig\n  } else {\n    const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n    const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n    const qualities = c.qualities?.sort((a, b) => a - b)\n    config = { ...c, allSizes, deviceSizes, qualities }\n  }\n\n  if (typeof defaultLoader === 'undefined') {\n    throw new Error(\n      'images.loaderFile detected but the file is missing default export.\\nRead more: https://nextjs.org/docs/messages/invalid-images-config'\n    )\n  }\n  let loader: ImageLoaderWithConfig = rest.loader || defaultLoader\n\n  // Remove property so it's not spread on <img> element\n  delete rest.loader\n  delete (rest as any).srcSet\n\n  // This special value indicates that the user\n  // didn't define a \"loader\" prop or \"loader\" config.\n  const isDefaultLoader = '__next_img_default' in loader\n\n  if (isDefaultLoader) {\n    if (config.loader === 'custom') {\n      throw new Error(\n        `Image with src \"${src}\" is missing \"loader\" prop.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`\n      )\n    }\n  } else {\n    // The user defined a \"loader\" prop or config.\n    // Since the config object is internal only, we\n    // must not pass it to the user-defined \"loader\".\n    const customImageLoader = loader as ImageLoader\n    loader = (obj) => {\n      const { config: _, ...opts } = obj\n      return customImageLoader(opts)\n    }\n  }\n\n  if (layout) {\n    if (layout === 'fill') {\n      fill = true\n    }\n    const layoutToStyle: Record<string, Record<string, string> | undefined> = {\n      intrinsic: { maxWidth: '100%', height: 'auto' },\n      responsive: { width: '100%', height: 'auto' },\n    }\n    const layoutToSizes: Record<string, string | undefined> = {\n      responsive: '100vw',\n      fill: '100vw',\n    }\n    const layoutStyle = layoutToStyle[layout]\n    if (layoutStyle) {\n      style = { ...style, ...layoutStyle }\n    }\n    const layoutSizes = layoutToSizes[layout]\n    if (layoutSizes && !sizes) {\n      sizes = layoutSizes\n    }\n  }\n\n  let staticSrc = ''\n  let widthInt = getInt(width)\n  let heightInt = getInt(height)\n  let blurWidth: number | undefined\n  let blurHeight: number | undefined\n  if (isStaticImport(src)) {\n    const staticImageData = isStaticRequire(src) ? src.default : src\n\n    if (!staticImageData.src) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n    if (!staticImageData.height || !staticImageData.width) {\n      throw new Error(\n        `An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(\n          staticImageData\n        )}`\n      )\n    }\n\n    blurWidth = staticImageData.blurWidth\n    blurHeight = staticImageData.blurHeight\n    blurDataURL = blurDataURL || staticImageData.blurDataURL\n    staticSrc = staticImageData.src\n\n    if (!fill) {\n      if (!widthInt && !heightInt) {\n        widthInt = staticImageData.width\n        heightInt = staticImageData.height\n      } else if (widthInt && !heightInt) {\n        const ratio = widthInt / staticImageData.width\n        heightInt = Math.round(staticImageData.height * ratio)\n      } else if (!widthInt && heightInt) {\n        const ratio = heightInt / staticImageData.height\n        widthInt = Math.round(staticImageData.width * ratio)\n      }\n    }\n  }\n  src = typeof src === 'string' ? src : staticSrc\n\n  let isLazy =\n    !priority && (loading === 'lazy' || typeof loading === 'undefined')\n  if (!src || src.startsWith('data:') || src.startsWith('blob:')) {\n    // https://developer.mozilla.org/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n    unoptimized = true\n    isLazy = false\n  }\n  if (config.unoptimized) {\n    unoptimized = true\n  }\n  if (\n    isDefaultLoader &&\n    !config.dangerouslyAllowSVG &&\n    src.split('?', 1)[0].endsWith('.svg')\n  ) {\n    // Special case to make svg serve as-is to avoid proxying\n    // through the built-in Image Optimization API.\n    unoptimized = true\n  }\n\n  const qualityInt = getInt(quality)\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (config.output === 'export' && isDefaultLoader && !unoptimized) {\n      throw new Error(\n        `Image Optimization using the default loader is not compatible with \\`{ output: 'export' }\\`.\n  Possible solutions:\n    - Remove \\`{ output: 'export' }\\` and run \"next start\" to run server mode including the Image Optimization API.\n    - Configure \\`{ images: { unoptimized: true } }\\` in \\`next.config.js\\` to disable the Image Optimization API.\n  Read more: https://nextjs.org/docs/messages/export-image-api`\n      )\n    }\n    if (!src) {\n      // React doesn't show the stack trace and there's\n      // no `src` to help identify which image, so we\n      // instead console.error(ref) during mount.\n      unoptimized = true\n    } else {\n      if (fill) {\n        if (width) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"width\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (height) {\n          throw new Error(\n            `Image with src \"${src}\" has both \"height\" and \"fill\" properties. Only one should be used.`\n          )\n        }\n        if (style?.position && style.position !== 'absolute') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.`\n          )\n        }\n        if (style?.width && style.width !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.`\n          )\n        }\n        if (style?.height && style.height !== '100%') {\n          throw new Error(\n            `Image with src \"${src}\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.`\n          )\n        }\n      } else {\n        if (typeof widthInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"width\" property.`\n          )\n        } else if (isNaN(widthInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"width\" property. Expected a numeric value in pixels but received \"${width}\".`\n          )\n        }\n        if (typeof heightInt === 'undefined') {\n          throw new Error(\n            `Image with src \"${src}\" is missing required \"height\" property.`\n          )\n        } else if (isNaN(heightInt)) {\n          throw new Error(\n            `Image with src \"${src}\" has invalid \"height\" property. Expected a numeric value in pixels but received \"${height}\".`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/^[\\x00-\\x20]/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot start with a space or control character. Use src.trimStart() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n        // eslint-disable-next-line no-control-regex\n        if (/[\\x00-\\x20]$/.test(src)) {\n          throw new Error(\n            `Image with src \"${src}\" cannot end with a space or control character. Use src.trimEnd() to remove it or encodeURIComponent(src) to keep it.`\n          )\n        }\n      }\n    }\n    if (!VALID_LOADING_VALUES.includes(loading)) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(\n          String\n        ).join(',')}.`\n      )\n    }\n    if (priority && loading === 'lazy') {\n      throw new Error(\n        `Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`\n      )\n    }\n    if (\n      placeholder !== 'empty' &&\n      placeholder !== 'blur' &&\n      !placeholder.startsWith('data:image/')\n    ) {\n      throw new Error(\n        `Image with src \"${src}\" has invalid \"placeholder\" property \"${placeholder}\".`\n      )\n    }\n    if (placeholder !== 'empty') {\n      if (widthInt && heightInt && widthInt * heightInt < 1600) {\n        warnOnce(\n          `Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder\" property to improve performance.`\n        )\n      }\n    }\n    if (qualityInt && qualityInt !== 75 && !config.qualities) {\n      warnOnce(\n        `Image with src \"${src}\" is using quality \"${qualityInt}\" which is not configured in images.qualities. This config will be required starting in Next.js 16.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n    if (\n      src.startsWith('/') &&\n      src.includes('?') &&\n      (!config?.localPatterns?.length ||\n        (config.localPatterns.length === 1 &&\n          config.localPatterns[0].pathname === '/_next/static/media/**'))\n    ) {\n      warnOnce(\n        `Image with src \"${src}\" is using a query string which is not configured in images.localPatterns. This config will be required starting in Next.js 16.` +\n          `\\nRead more: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n      )\n    }\n    if (placeholder === 'blur' && !blurDataURL) {\n      const VALID_BLUR_EXT = ['jpeg', 'png', 'webp', 'avif'] // should match next-image-loader\n\n      throw new Error(\n        `Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n        Possible solutions:\n          - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n          - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\n            ','\n          )} (animated images not supported)\n          - Remove the \"placeholder\" property, effectively no blur effect\n        Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`\n      )\n    }\n    if ('ref' in rest) {\n      warnOnce(\n        `Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoad\" property instead.`\n      )\n    }\n\n    if (!unoptimized && !isDefaultLoader) {\n      const urlStr = loader({\n        config,\n        src,\n        width: widthInt || 400,\n        quality: qualityInt || 75,\n      })\n      let url: URL | undefined\n      try {\n        url = new URL(urlStr)\n      } catch (err) {}\n      if (urlStr === src || (url && url.pathname === src && !url.search)) {\n        warnOnce(\n          `Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`\n        )\n      }\n    }\n\n    if (onLoadingComplete) {\n      warnOnce(\n        `Image with src \"${src}\" is using deprecated \"onLoadingComplete\" property. Please use the \"onLoad\" property instead.`\n      )\n    }\n\n    for (const [legacyKey, legacyValue] of Object.entries({\n      layout,\n      objectFit,\n      objectPosition,\n      lazyBoundary,\n      lazyRoot,\n    })) {\n      if (legacyValue) {\n        warnOnce(\n          `Image with src \"${src}\" has legacy prop \"${legacyKey}\". Did you forget to run the codemod?` +\n            `\\nRead more: https://nextjs.org/docs/messages/next-image-upgrade-to-13`\n        )\n      }\n    }\n\n    if (\n      typeof window !== 'undefined' &&\n      !perfObserver &&\n      window.PerformanceObserver\n    ) {\n      perfObserver = new PerformanceObserver((entryList) => {\n        for (const entry of entryList.getEntries()) {\n          // @ts-ignore - missing \"LargestContentfulPaint\" class with \"element\" prop\n          const imgSrc = entry?.element?.src || ''\n          const lcpImage = allImgs.get(imgSrc)\n          if (\n            lcpImage &&\n            !lcpImage.priority &&\n            lcpImage.placeholder === 'empty' &&\n            !lcpImage.src.startsWith('data:') &&\n            !lcpImage.src.startsWith('blob:')\n          ) {\n            // https://web.dev/lcp/#measure-lcp-in-javascript\n            warnOnce(\n              `Image with src \"${lcpImage.src}\" was detected as the Largest Contentful Paint (LCP). Please add the \"priority\" property if this image is above the fold.` +\n                `\\nRead more: https://nextjs.org/docs/api-reference/next/image#priority`\n            )\n          }\n        }\n      })\n      try {\n        perfObserver.observe({\n          type: 'largest-contentful-paint',\n          buffered: true,\n        })\n      } catch (err) {\n        // Log error but don't crash the app\n        console.error(err)\n      }\n    }\n  }\n  const imgStyle = Object.assign(\n    fill\n      ? {\n          position: 'absolute',\n          height: '100%',\n          width: '100%',\n          left: 0,\n          top: 0,\n          right: 0,\n          bottom: 0,\n          objectFit,\n          objectPosition,\n        }\n      : {},\n    showAltText ? {} : { color: 'transparent' },\n    style\n  )\n\n  const backgroundImage =\n    !blurComplete && placeholder !== 'empty'\n      ? placeholder === 'blur'\n        ? `url(\"data:image/svg+xml;charset=utf-8,${getImageBlurSvg({\n            widthInt,\n            heightInt,\n            blurWidth,\n            blurHeight,\n            blurDataURL: blurDataURL || '', // assume not undefined\n            objectFit: imgStyle.objectFit,\n          })}\")`\n        : `url(\"${placeholder}\")` // assume `data:image/`\n      : null\n\n  const backgroundSize = !INVALID_BACKGROUND_SIZE_VALUES.includes(\n    imgStyle.objectFit\n  )\n    ? imgStyle.objectFit\n    : imgStyle.objectFit === 'fill'\n      ? '100% 100%' // the background-size equivalent of `fill`\n      : 'cover'\n\n  let placeholderStyle: PlaceholderStyle = backgroundImage\n    ? {\n        backgroundSize,\n        backgroundPosition: imgStyle.objectPosition || '50% 50%',\n        backgroundRepeat: 'no-repeat',\n        backgroundImage,\n      }\n    : {}\n\n  if (process.env.NODE_ENV === 'development') {\n    if (\n      placeholderStyle.backgroundImage &&\n      placeholder === 'blur' &&\n      blurDataURL?.startsWith('/')\n    ) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      placeholderStyle.backgroundImage = `url(\"${blurDataURL}\")`\n    }\n  }\n\n  const imgAttributes = generateImgAttrs({\n    config,\n    src,\n    unoptimized,\n    width: widthInt,\n    quality: qualityInt,\n    sizes,\n    loader,\n  })\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof window !== 'undefined') {\n      let fullUrl: URL\n      try {\n        fullUrl = new URL(imgAttributes.src)\n      } catch (e) {\n        fullUrl = new URL(imgAttributes.src, window.location.href)\n      }\n      allImgs.set(fullUrl.href, { src, priority, placeholder })\n    }\n  }\n\n  const props: ImgProps = {\n    ...rest,\n    loading: isLazy ? 'lazy' : loading,\n    fetchPriority,\n    width: widthInt,\n    height: heightInt,\n    decoding,\n    className,\n    style: { ...imgStyle, ...placeholderStyle },\n    sizes: imgAttributes.sizes,\n    srcSet: imgAttributes.srcSet,\n    src: overrideSrc || imgAttributes.src,\n  }\n  const meta = { unoptimized, priority, placeholder, fill }\n  return { props, meta }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ImageConfigContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].RouterContext\n", "import type { ImageLoaderPropsWithConfig } from './image-config'\n\nconst DEFAULT_Q = 75\n\nfunction defaultLoader({\n  config,\n  src,\n  width,\n  quality,\n}: ImageLoaderPropsWithConfig): string {\n  if (process.env.NODE_ENV !== 'production') {\n    const missingValues = []\n\n    // these should always be provided but make sure they are\n    if (!src) missingValues.push('src')\n    if (!width) missingValues.push('width')\n\n    if (missingValues.length > 0) {\n      throw new Error(\n        `Next Image Optimization requires ${missingValues.join(\n          ', '\n        )} to be provided. Make sure you pass them as props to the \\`next/image\\` component. Received: ${JSON.stringify(\n          { src, width, quality }\n        )}`\n      )\n    }\n\n    if (src.startsWith('//')) {\n      throw new Error(\n        `Failed to parse src \"${src}\" on \\`next/image\\`, protocol-relative URL (//) must be changed to an absolute URL (http:// or https://)`\n      )\n    }\n\n    if (src.startsWith('/') && config.localPatterns) {\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasLocalMatch } =\n          require('./match-local-pattern') as typeof import('./match-local-pattern')\n        if (!hasLocalMatch(config.localPatterns, src)) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\` does not match \\`images.localPatterns\\` configured in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-localpatterns`\n          )\n        }\n      }\n    }\n\n    if (!src.startsWith('/') && (config.domains || config.remotePatterns)) {\n      let parsedSrc: URL\n      try {\n        parsedSrc = new URL(src)\n      } catch (err) {\n        console.error(err)\n        throw new Error(\n          `Failed to parse src \"${src}\" on \\`next/image\\`, if using relative image it must start with a leading slash \"/\" or be an absolute URL (http:// or https://)`\n        )\n      }\n\n      if (\n        process.env.NODE_ENV !== 'test' &&\n        // micromatch isn't compatible with edge runtime\n        process.env.NEXT_RUNTIME !== 'edge'\n      ) {\n        // We use dynamic require because this should only error in development\n        const { hasRemoteMatch } =\n          require('./match-remote-pattern') as typeof import('./match-remote-pattern')\n        if (\n          !hasRemoteMatch(config.domains!, config.remotePatterns!, parsedSrc)\n        ) {\n          throw new Error(\n            `Invalid src prop (${src}) on \\`next/image\\`, hostname \"${parsedSrc.hostname}\" is not configured under images in your \\`next.config.js\\`\\n` +\n              `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-host`\n          )\n        }\n      }\n    }\n\n    if (quality && config.qualities && !config.qualities.includes(quality)) {\n      throw new Error(\n        `Invalid quality prop (${quality}) on \\`next/image\\` does not match \\`images.qualities\\` configured in your \\`next.config.js\\`\\n` +\n          `See more info: https://nextjs.org/docs/messages/next-image-unconfigured-qualities`\n      )\n    }\n  }\n\n  const q =\n    quality ||\n    config.qualities?.reduce((prev, cur) =>\n      Math.abs(cur - DEFAULT_Q) < Math.abs(prev - DEFAULT_Q) ? cur : prev\n    ) ||\n    DEFAULT_Q\n\n  return `${config.path}?url=${encodeURIComponent(src)}&w=${width}&q=${q}${\n    src.startsWith('/_next/static/media/') && process.env.NEXT_DEPLOYMENT_ID\n      ? `&dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n      : ''\n  }`\n}\n\n// We use this to determine if the import is the default loader\n// or a custom loader defined by the user in next.config.js\ndefaultLoader.__next_img_default = true\n\nexport default defaultLoader\n", "import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n", "'use client'\n\nimport React, {\n  useRef,\n  useEffect,\n  useCallback,\n  useContext,\n  useMemo,\n  useState,\n  forwardRef,\n  use,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport Head from '../shared/lib/head'\nimport { getImgProps } from '../shared/lib/get-img-props'\nimport type {\n  ImageProps,\n  ImgProps,\n  OnLoad,\n  OnLoadingComplete,\n  PlaceholderValue,\n} from '../shared/lib/get-img-props'\nimport type {\n  ImageConfigComplete,\n  ImageLoaderProps,\n} from '../shared/lib/image-config'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport { ImageConfigContext } from '../shared/lib/image-config-context.shared-runtime'\nimport { warnOnce } from '../shared/lib/utils/warn-once'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\n\n// This is replaced by webpack alias\nimport defaultLoader from 'next/dist/shared/lib/image-loader'\nimport { useMergedRef } from './use-merged-ref'\n\n// This is replaced by webpack define plugin\nconst configEnv = process.env.__NEXT_IMAGE_OPTS as any as ImageConfigComplete\n\nif (typeof window === 'undefined') {\n  ;(globalThis as any).__NEXT_IMAGE_IMPORTED = true\n}\n\nexport type { ImageLoaderProps }\nexport type ImageLoader = (p: ImageLoaderProps) => string\n\ntype ImgElementWithDataProp = HTMLImageElement & {\n  'data-loaded-src': string | undefined\n}\n\ntype ImageElementProps = ImgProps & {\n  unoptimized: boolean\n  placeholder: PlaceholderValue\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>\n  setBlurComplete: (b: boolean) => void\n  setShowAltText: (b: boolean) => void\n  sizesInput: string | undefined\n}\n\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(\n  img: ImgElementWithDataProp,\n  placeholder: PlaceholderValue,\n  onLoadRef: React.MutableRefObject<OnLoad | undefined>,\n  onLoadingCompleteRef: React.MutableRefObject<OnLoadingComplete | undefined>,\n  setBlurComplete: (b: boolean) => void,\n  unoptimized: boolean,\n  sizesInput: string | undefined\n) {\n  const src = img?.src\n  if (!img || img['data-loaded-src'] === src) {\n    return\n  }\n  img['data-loaded-src'] = src\n  const p = 'decode' in img ? img.decode() : Promise.resolve()\n  p.catch(() => {}).then(() => {\n    if (!img.parentElement || !img.isConnected) {\n      // Exit early in case of race condition:\n      // - onload() is called\n      // - decode() is called but incomplete\n      // - unmount is called\n      // - decode() completes\n      return\n    }\n    if (placeholder !== 'empty') {\n      setBlurComplete(true)\n    }\n    if (onLoadRef?.current) {\n      // Since we don't have the SyntheticEvent here,\n      // we must create one with the same shape.\n      // See https://reactjs.org/docs/events.html\n      const event = new Event('load')\n      Object.defineProperty(event, 'target', { writable: false, value: img })\n      let prevented = false\n      let stopped = false\n      onLoadRef.current({\n        ...event,\n        nativeEvent: event,\n        currentTarget: img,\n        target: img,\n        isDefaultPrevented: () => prevented,\n        isPropagationStopped: () => stopped,\n        persist: () => {},\n        preventDefault: () => {\n          prevented = true\n          event.preventDefault()\n        },\n        stopPropagation: () => {\n          stopped = true\n          event.stopPropagation()\n        },\n      })\n    }\n    if (onLoadingCompleteRef?.current) {\n      onLoadingCompleteRef.current(img)\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const origSrc = new URL(src, 'http://n').searchParams.get('url') || src\n      if (img.getAttribute('data-nimg') === 'fill') {\n        if (!unoptimized && (!sizesInput || sizesInput === '100vw')) {\n          let widthViewportRatio =\n            img.getBoundingClientRect().width / window.innerWidth\n          if (widthViewportRatio < 0.6) {\n            if (sizesInput === '100vw') {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" prop and \"sizes\" prop of \"100vw\", but image is not rendered at full viewport width. Please adjust \"sizes\" to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            } else {\n              warnOnce(\n                `Image with src \"${origSrc}\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`\n              )\n            }\n          }\n        }\n        if (img.parentElement) {\n          const { position } = window.getComputedStyle(img.parentElement)\n          const valid = ['absolute', 'fixed', 'relative']\n          if (!valid.includes(position)) {\n            warnOnce(\n              `Image with src \"${origSrc}\" has \"fill\" and parent element with invalid \"position\". Provided \"${position}\" should be one of ${valid\n                .map(String)\n                .join(',')}.`\n            )\n          }\n        }\n        if (img.height === 0) {\n          warnOnce(\n            `Image with src \"${origSrc}\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.`\n          )\n        }\n      }\n\n      const heightModified =\n        img.height.toString() !== img.getAttribute('height')\n      const widthModified = img.width.toString() !== img.getAttribute('width')\n      if (\n        (heightModified && !widthModified) ||\n        (!heightModified && widthModified)\n      ) {\n        warnOnce(\n          `Image with src \"${origSrc}\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles 'width: \"auto\"' or 'height: \"auto\"' to maintain the aspect ratio.`\n        )\n      }\n    }\n  })\n}\n\nfunction getDynamicProps(\n  fetchPriority?: string\n): Record<string, string | undefined> {\n  if (Boolean(use)) {\n    // In React 19.0.0 or newer, we must use camelCase\n    // prop to avoid \"Warning: Invalid DOM property\".\n    // See https://github.com/facebook/react/pull/25927\n    return { fetchPriority }\n  }\n  // In React 18.2.0 or older, we must use lowercase prop\n  // to avoid \"Warning: Invalid DOM property\".\n  return { fetchpriority: fetchPriority }\n}\n\nconst ImageElement = forwardRef<HTMLImageElement | null, ImageElementProps>(\n  (\n    {\n      src,\n      srcSet,\n      sizes,\n      height,\n      width,\n      decoding,\n      className,\n      style,\n      fetchPriority,\n      placeholder,\n      loading,\n      unoptimized,\n      fill,\n      onLoadRef,\n      onLoadingCompleteRef,\n      setBlurComplete,\n      setShowAltText,\n      sizesInput,\n      onLoad,\n      onError,\n      ...rest\n    },\n    forwardedRef\n  ) => {\n    const ownRef = useCallback(\n      (img: ImgElementWithDataProp | null) => {\n        if (!img) {\n          return\n        }\n        if (onError) {\n          // If the image has an error before react hydrates, then the error is lost.\n          // The workaround is to wait until the image is mounted which is after hydration,\n          // then we set the src again to trigger the error handler (if there was an error).\n          // eslint-disable-next-line no-self-assign\n          img.src = img.src\n        }\n        if (process.env.NODE_ENV !== 'production') {\n          if (!src) {\n            console.error(`Image is missing required \"src\" property:`, img)\n          }\n          if (img.getAttribute('alt') === null) {\n            console.error(\n              `Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.`\n            )\n          }\n        }\n        if (img.complete) {\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }\n      },\n      [\n        src,\n        placeholder,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        onError,\n        unoptimized,\n        sizesInput,\n      ]\n    )\n\n    const ref = useMergedRef(forwardedRef, ownRef)\n\n    return (\n      <img\n        {...rest}\n        {...getDynamicProps(fetchPriority)}\n        // It's intended to keep `loading` before `src` because React updates\n        // props in order which causes Safari/Firefox to not lazy load properly.\n        // See https://github.com/facebook/react/issues/25883\n        loading={loading}\n        width={width}\n        height={height}\n        decoding={decoding}\n        data-nimg={fill ? 'fill' : '1'}\n        className={className}\n        style={style}\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        sizes={sizes}\n        srcSet={srcSet}\n        src={src}\n        ref={ref}\n        onLoad={(event) => {\n          const img = event.currentTarget as ImgElementWithDataProp\n          handleLoading(\n            img,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            unoptimized,\n            sizesInput\n          )\n        }}\n        onError={(event) => {\n          // if the real image fails to load, this will ensure \"alt\" is visible\n          setShowAltText(true)\n          if (placeholder !== 'empty') {\n            // If the real image fails to load, this will still remove the placeholder.\n            setBlurComplete(true)\n          }\n          if (onError) {\n            onError(event)\n          }\n        }}\n      />\n    )\n  }\n)\n\nfunction ImagePreload({\n  isAppRouter,\n  imgAttributes,\n}: {\n  isAppRouter: boolean\n  imgAttributes: ImgProps\n}) {\n  const opts: ReactDOM.PreloadOptions = {\n    as: 'image',\n    imageSrcSet: imgAttributes.srcSet,\n    imageSizes: imgAttributes.sizes,\n    crossOrigin: imgAttributes.crossOrigin,\n    referrerPolicy: imgAttributes.referrerPolicy,\n    ...getDynamicProps(imgAttributes.fetchPriority),\n  }\n\n  if (isAppRouter && ReactDOM.preload) {\n    ReactDOM.preload(imgAttributes.src, opts)\n    return null\n  }\n\n  return (\n    <Head>\n      <link\n        key={\n          '__nimg-' +\n          imgAttributes.src +\n          imgAttributes.srcSet +\n          imgAttributes.sizes\n        }\n        rel=\"preload\"\n        // Note how we omit the `href` attribute, as it would only be relevant\n        // for browsers that do not support `imagesrcset`, and in those cases\n        // it would cause the incorrect image to be preloaded.\n        //\n        // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n        href={imgAttributes.srcSet ? undefined : imgAttributes.src}\n        {...opts}\n      />\n    </Head>\n  )\n}\n\n/**\n * The `Image` component is used to optimize images.\n *\n * Read more: [Next.js docs: `Image`](https://nextjs.org/docs/app/api-reference/components/image)\n */\nexport const Image = forwardRef<HTMLImageElement | null, ImageProps>(\n  (props, forwardedRef) => {\n    const pagesRouter = useContext(RouterContext)\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter\n\n    const configContext = useContext(ImageConfigContext)\n    const config = useMemo(() => {\n      const c = configEnv || configContext || imageConfigDefault\n      const allSizes = [...c.deviceSizes, ...c.imageSizes].sort((a, b) => a - b)\n      const deviceSizes = c.deviceSizes.sort((a, b) => a - b)\n      const qualities = c.qualities?.sort((a, b) => a - b)\n      return { ...c, allSizes, deviceSizes, qualities }\n    }, [configContext])\n\n    const { onLoad, onLoadingComplete } = props\n    const onLoadRef = useRef(onLoad)\n\n    useEffect(() => {\n      onLoadRef.current = onLoad\n    }, [onLoad])\n\n    const onLoadingCompleteRef = useRef(onLoadingComplete)\n\n    useEffect(() => {\n      onLoadingCompleteRef.current = onLoadingComplete\n    }, [onLoadingComplete])\n\n    const [blurComplete, setBlurComplete] = useState(false)\n    const [showAltText, setShowAltText] = useState(false)\n\n    const { props: imgAttributes, meta: imgMeta } = getImgProps(props, {\n      defaultLoader,\n      imgConf: config,\n      blurComplete,\n      showAltText,\n    })\n\n    return (\n      <>\n        {\n          <ImageElement\n            {...imgAttributes}\n            unoptimized={imgMeta.unoptimized}\n            placeholder={imgMeta.placeholder}\n            fill={imgMeta.fill}\n            onLoadRef={onLoadRef}\n            onLoadingCompleteRef={onLoadingCompleteRef}\n            setBlurComplete={setBlurComplete}\n            setShowAltText={setShowAltText}\n            sizesInput={props.sizes}\n            ref={forwardedRef}\n          />\n        }\n        {imgMeta.priority ? (\n          <ImagePreload\n            isAppRouter={isAppRouter}\n            imgAttributes={imgAttributes}\n          />\n        ) : null}\n      </>\n    )\n  }\n)\n"], "names": ["SideEffect", "isServer", "window", "useClientOnlyLayoutEffect", "useLayoutEffect", "useClientOnlyEffect", "useEffect", "props", "headManager", "reduceComponentsToState", "emitChange", "mountedInstances", "headElements", "Children", "toArray", "Array", "from", "filter", "Boolean", "updateHead", "add", "children", "delete", "_pendingUpdate", "module", "exports", "require", "vendored", "AmpContext", "HeadManagerContext", "isInAmpMode", "ampFirs<PERSON>", "hybrid", "<PERSON><PERSON><PERSON><PERSON>", "warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "defaultHead", "inAmpMode", "head", "meta", "charSet", "push", "name", "content", "onlyReactElement", "list", "child", "type", "React", "Fragment", "concat", "reduce", "fragmentList", "fragmentChild", "METATYPES", "unique", "keys", "tags", "metaTypes", "metaCategories", "h", "isUnique", "<PERSON><PERSON><PERSON>", "key", "indexOf", "slice", "i", "len", "length", "metatype", "hasOwnProperty", "category", "categories", "reduceComponents", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "map", "c", "srcMessage", "cloneElement", "Head", "ampState", "useContext", "AmpStateContext", "Effect", "getImageBlurSvg", "widthInt", "heightInt", "blur<PERSON>idth", "blurHeight", "blurDataURL", "objectFit", "std", "svgWidth", "svgHeight", "viewBox", "preserveAspectRatio", "VALID_LOADERS", "imageConfigDefault", "deviceSizes", "imageSizes", "path", "loader", "loaderFile", "domains", "disableStaticImages", "minimumCacheTTL", "formats", "dangerouslyAllowSVG", "contentSecurityPolicy", "contentDispositionType", "localPatterns", "undefined", "remotePatterns", "qualities", "unoptimized", "getImgProps", "VALID_LOADING_VALUES", "INVALID_BACKGROUND_SIZE_VALUES", "isStaticRequire", "src", "default", "isStaticImageData", "isStaticImport", "allImgs", "Map", "perfObserver", "getInt", "x", "Number", "isFinite", "NaN", "test", "parseInt", "getWidths", "width", "sizes", "allSizes", "viewportWidthRe", "percentSizes", "match", "exec", "smallestRatio", "Math", "min", "widths", "s", "kind", "w", "find", "p", "generateImgAttrs", "config", "quality", "srcSet", "last", "join", "_state", "priority", "loading", "className", "height", "fill", "style", "overrideSrc", "onLoad", "onLoadingComplete", "placeholder", "fetchPriority", "decoding", "layout", "objectPosition", "lazyBoundary", "lazyRoot", "rest", "imgConf", "showAltText", "blurComplete", "defaultLoader", "sort", "a", "b", "Error", "isDefaultLoader", "customImageLoader", "obj", "opts", "layoutToStyle", "intrinsic", "max<PERSON><PERSON><PERSON>", "responsive", "layoutToSizes", "layoutStyle", "layoutSizes", "staticSrc", "staticImageData", "JSON", "stringify", "ratio", "round", "isLazy", "startsWith", "split", "endsWith", "qualityInt", "output", "position", "isNaN", "includes", "String", "pathname", "VALID_BLUR_EXT", "urlStr", "url", "URL", "err", "search", "<PERSON><PERSON><PERSON>", "legacyValue", "Object", "entries", "PerformanceObserver", "entryList", "entry", "getEntries", "imgSrc", "element", "lcpImage", "get", "observe", "buffered", "error", "imgStyle", "assign", "left", "top", "right", "bottom", "color", "backgroundImage", "backgroundSize", "placeholder<PERSON><PERSON><PERSON>", "backgroundPosition", "backgroundRepeat", "imgAttributes", "fullUrl", "e", "location", "href", "set", "ImageConfigContext", "RouterContext", "DEFAULT_Q", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_RUNTIME", "hasLocalMatch", "parsedSrc", "hasRemoteMatch", "hostname", "q", "prev", "cur", "abs", "encodeURIComponent", "NEXT_DEPLOYMENT_ID", "__next_img_default", "useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup", "Image", "configEnv", "__NEXT_IMAGE_OPTS", "globalThis", "__NEXT_IMAGE_IMPORTED", "handleLoading", "img", "onLoadRef", "onLoadingCompleteRef", "setBlurComplete", "sizesInput", "decode", "Promise", "resolve", "catch", "then", "parentElement", "isConnected", "event", "Event", "defineProperty", "writable", "value", "prevented", "stopped", "nativeEvent", "currentTarget", "target", "isDefaultPrevented", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "origSrc", "searchParams", "getAttribute", "widthViewportRatio", "getBoundingClientRect", "innerWidth", "getComputedStyle", "valid", "heightModified", "toString", "widthModified", "getDynamicProps", "use", "fetchpriority", "ImageElement", "forwardRef", "forwardedRef", "setShowAltText", "onError", "ownRef", "complete", "ref", "data-nimg", "ImagePreload", "isAppRouter", "as", "imageSrcSet", "crossOrigin", "referrerPolicy", "ReactDOM", "preload", "link", "rel", "pagesRouter", "configContext", "useMemo", "useState", "imgMeta"], "mappings": "6CAKA,EAAQ,CAAC,CAHT,EAGY,OAHsB,AAAzB,CAA4B,EACjC,OAAO,GAAO,EAAI,UAAU,CAAG,EAAM,CAAE,QAAS,CAAI,CACxD,+BCFA,SAAS,EAAyB,CAAW,EACzC,GAAuB,YAAnB,OAAO,QAAwB,OAAO,KAE1C,IAAI,EAAoB,IAAI,QACxB,EAAmB,IAAI,QAE3B,MAAO,CAAC,EAA2B,SAAS,CAAW,EACnD,OAAO,EAAc,EAAmB,EAC5C,CAAC,CAAE,EACP,CA0BA,EAAQ,CAAC,CAzBT,EAyBY,OAzBH,AAA0B,CAAG,CAAE,CAAW,EAC/C,GAAI,CAAC,GAAe,GAAO,EAAI,UAAU,CAAE,OAAO,EAClD,GAAI,AAAQ,UAAuB,UAAf,OAAO,GAAmC,YAAf,OAAO,EAAoB,MAAO,CAAE,QAAS,CAAI,EAEhG,IAAI,EAAQ,EAAyB,GAErC,GAAI,GAAS,EAAM,GAAG,CAAC,GAAM,OAAO,EAAM,GAAG,CAAC,GAE9C,IAAI,EAAS,CAAE,UAAW,IAAK,EAC3B,EAAwB,OAAO,cAAc,EAAI,OAAO,wBAAwB,CAEpF,IAAK,IAAI,KAAO,EACZ,EADiB,CACL,AAAR,eAAqB,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAK,GAAM,CACrE,IAAI,EAAO,EAAwB,OAAO,wBAAwB,CAAC,EAAK,GAAO,KAC3E,IAAS,EAAK,EAAN,CAAS,EAAI,EAAK,GAAA,AAAG,EAAG,OAAO,cAAc,CAAC,EAAQ,EAAK,GAClE,CAAM,CAAC,EAAI,CAAG,CAAG,CAAC,EAAI,AAC/B,CAOJ,OAJA,EAAO,OAAO,CAAG,EAEb,GAAO,EAAM,GAAG,CAAC,EAAK,GAEnB,CACX,yGChBA,UAAA,qCAAwBA,aAnBuC,CAAA,CAAA,IAAA,GAgBzDG,EAAuC,KAAO,EAGrC,EAHyCC,OAGhCJ,EAAWO,CAAsB,MAerDC,CAlB8BP,CAIhC,GAAM,CAAEO,AAJ6D,aAIlD,yBAAEC,CAAuB,CAAE,CAAGF,EAEjD,SAASG,IACP,GAAIF,GAAeA,EAAYG,gBAAgB,CAAE,CAC/C,IAAMC,EAAeC,EAAAA,QAAQ,CAACC,OAAO,CACnCC,MAAMC,IAAI,CAACR,EAAYG,gBAAgB,EAA0BM,MAAM,CACrEC,UAGJV,EAAYW,UAAU,CAACV,EAAwBG,EAAcL,GAC/D,CACF,QAGEC,MAAAA,CAAAA,EAAAA,AAA6B,GAA7BA,IAAAA,AAA6B,EAA7BA,EAAaG,CAAgB,eAAA,AAAhBA,GAAbH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EACjDX,IAGFP,EAA0B,SACxBK,EACA,OADAA,MAAAA,CAAAA,EAAAA,AAA6B,GAA7BA,IAA6B,AAA7BA,EAAAA,EAAaG,CAAgB,eAAA,AAAhBA,GAAbH,EAA+BY,GAAG,CAACb,EAAMc,QAAQ,EAC1C,SACLb,CAAAA,OAAAA,GAAAA,AAA6B,EAA7BA,KAAAA,AAA6B,EAA7BA,EAAaG,CAAgB,eAAhBA,AAAgB,GAA7BH,EAA+Bc,MAAM,CAACf,EAAMc,QAAQ,CACtD,CACF,GAOAlB,EAA0B,KACpBK,IACFA,EAAYe,OADG,OACW,CAAGb,CAAAA,EAExB,KACDF,IACFA,EAAYe,OADG,OACW,CAAGb,CAAAA,CAEjC,IAgBK,IACT,+BC5EAc,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACC,UAAU,+BCFjCJ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACE,kBAAkB,+BCFlC,SAASC,EAAY,CAAA,EAAA,GAAA,CAC1BC,YAAW,CAAK,QAChBC,GAAS,CAAK,UACdC,GAAW,CAAK,CACjB,CAJ2B,KAAA,IAAA,EAIxB,CAAC,EAJuB,EAK1B,OAAOF,GAAaC,GAAUC,CAChC,0EANgBH,cAAAA,qCAAAA,4GCWPI,WAAAA,qCAAAA,KAXT,IAAIA,EAAW,AAACC,IAAe,uKCgL/B,OAAmB,CAAA,kBAAnB,GAnKgBU,WAAW,CAAA,kBAAXA,uDAX4B,CAAA,CAAA,IAAA,YACzB,CAAA,CAAA,IAAA,QACa,CAAA,CAAA,IAAA,OACG,CAAA,CAAA,IAAA,OACP,CAAA,CAAA,IAAA,GAOrB,SAASA,EAAYC,CAAiB,EAAjBA,KAAAA,IAAAA,IAAAA,GAAY,CAAA,EACtC,IAAMC,EAAO,CAAC,CAAA,EAAA,EAAA,GAAA,EAACC,OAAAA,CAAKC,QAAQ,SAAY,WAAa,CAMrD,OALI,AAACH,GACHC,EAAKG,IAAI,CACP,AADO,CACP,AAFY,EAEZ,EAAA,GAAA,EAACF,CADM,MACNA,CAAKG,KAAK,WAAWC,QAAQ,sBAAyB,aAGpDL,CACT,CAEA,SAASM,EACPC,CAAoC,CACpCC,CAA2C,QAG3C,AAAqB,UAAjB,OAAOA,GAAuC,UAAjB,AAA2B,OAApBA,EAC/BD,EAGLC,EAAMC,IAAI,GAAKC,EAAAA,OAAK,CAACC,QAAQ,CACxBJ,CAD0B,CACrBK,MAAM,CAEhBF,EAAAA,OAAK,CAAC5C,QAAQ,CAACC,OAAO,CAACyC,EAAMhD,KAAK,CAACc,QAAQ,EAAEuC,MAAM,CAEjD,CACEC,EACAC,IAG2B,AAD3B,UACE,OAAOA,EARsF,CASpE,UAAzB,AACA,OADOA,EAEAD,EAEFA,EAAaF,MAAM,CAACG,GAE7B,EAAE,GAIDR,EAAKK,MAAM,CAACJ,EACrB,GA/CyB,CAAA,CAAA,IAAA,EA6BkF,CAoB3G,IAAMQ,EAAY,CAAC,OAAQ,YAAa,UAAW,WAAW,CAsE9D,SAASmB,EACPC,CAAoD,CACpD5E,CAAQ,EAER,GAAM,WAAEuC,CAAS,CAAE,CAAGvC,EACtB,OAAO4E,EACJvB,MAAM,CAACP,EAAkB,EAAE,EAC3B+B,OAAO,GACPzB,MAAM,CAACd,EAAYC,GAAWsC,OAAO,IACrCnE,MAAM,CAAC+C,AAxEZ,SAASA,EACP,IAAMC,EAAO,IAAIzB,IACX0B,EAAO,IAAI1B,IACX2B,EAAY,IAAI3B,IAChB4B,EAAsD,CAAC,EAE7D,OAAQC,AAAD,IACL,IAAIC,GAAW,EACXC,EAAS,GAEb,GAAIF,EAAEG,GAAG,EAAqB,UAAjB,OAAOH,EAAEG,GAAG,EAAiBH,EAAEG,GAAG,CAACC,OAAO,CAAC,KAAO,EAAG,CAChEF,GAAS,EACT,IAAMC,EAAMH,EAAEG,GAAG,CAACE,KAAK,CAACL,EAAEG,GAAG,CAACC,OAAO,CAAC,KAAO,GACzCR,EAAKvB,GAAG,CAAC8B,GACXF,GAAW,AADM,EAGjBL,EAAK7C,GAAG,CAACoD,EAEb,CAGA,OAAQH,EAAEb,IAAI,EACZ,IAAK,QACL,IAAK,OACCU,EAAKxB,GAAG,CAAC2B,EAAEb,IAAI,EACjBc,CADoB,EACT,EAEXJ,EAAK9C,GAAG,CAACiD,EAAEb,IAAI,EAEjB,KACF,KAAK,OACH,IAAK,IAAImB,EAAI,EAAGC,EAAMb,EAAUc,MAAM,CAAEF,EAAIC,EAAKD,IAAK,CACpD,IAAMG,EAAWf,CAAS,CAACY,EAAE,CAC7B,GAAKN,CAAD,CAAG9D,KAAK,CAACwE,cAAc,CAACD,GAE5B,GAAiB,KAFsB,MAEX,CAAxBA,EACEX,EAAUzB,GAAG,CAACoC,GAChBR,EAAW,GAEXH,EAAU/C,CAHiB,EAGd,CAAC0D,OAEX,CACL,IAAME,EAAWX,EAAE9D,KAAK,CAACuE,EAAS,CAC5BG,EAAab,CAAc,CAACU,EAAS,EAAI,IAAItC,GAC9CsC,EAAa,SAAbA,GAAuB,CAACP,CAAAA,CAAK,EAAMU,EAAWvC,GAAG,CAACsC,GACrDV,GAAW,GAEXW,EAAW7D,AAHqD,GAGlD,CAAC4D,GACfZ,CAAc,CAACU,EAAS,CAAGG,EAE/B,CACF,CAEJ,CAEA,OAAOX,CACT,CACF,KAgBKc,OAAO,GACPC,GAAG,CAAC,CAACC,EAA4BX,KAChC,IAAMH,EAAMc,EAAEd,GAAG,EAAIG,EAgBrB,OAAA,AAAOlB,EAAAA,OAAK,CAAC+B,CAAb,WAAyB,CAACF,EAAG,KAAEd,CAAI,EACrC,EACJ,KAoBA,EAdA,SAASiB,AAAK,AAcCA,CAd0C,EAA3C,GAAA,UAAEpE,CAAQ,CAAiC,CAA3C,EACNqE,EAAWC,GAAAA,EAAAA,UAAAA,AAAU,EAACC,EAAAA,eAAe,EACrCpF,EAAcmF,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAAC9D,EAAAA,kBAAkB,EACjD,MACE,CADF,AACE,EAAA,EAAA,GAAA,EAACgE,EADH,AACGA,OAAM,CAAA,CACLpF,wBAAyByE,EACzB1E,YAAaA,EACbsC,UAAWhB,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAAC4D,YAEtBrE,GAGP,uPC3KO,SAASyE,EAAgB,CAc/B,EAd+B,GAAA,UAC9BC,CAAQ,WACRC,CAAS,WACTC,CAAS,CACTC,YAAU,aACVC,CAAW,CACXC,WAAS,CAQV,CAd+B,EAgBxBE,EAAWL,EAAwB,GAAZA,EAAiBF,EACxCQ,EAAYL,EAA0B,GAAbA,EAAkBF,EAE3CQ,EACJF,GAAYC,EAAa,gBAAeD,EAAS,IAAGC,EAAU,IAAK,GASrE,MAAQ,6CAA4CC,UAAQ,8FAA2FH,MAAI,oQAAiQA,MAAI,oEARpYG,GACxB,OACc,YAAdJ,EACE,IAKufK,OAJze,UAAdL,EACE,iBACA,MAAA,EAEygB,sCAAqCD,EAAY,iBACpkB,CA/BC,OAAA,cAAA,CAAA,EAAA,aAAA,oCACeL,kBAAAA,qCAAAA,0KCHHY,aAAa,CAAA,kBAAbA,GAiIAC,kBAAkB,CAAA,kBAAlBA,KAjIN,IAAMD,EAAgB,CAC3B,UACA,QACA,aACA,SACA,SACD,CA2HYC,EAA0C,CACrDC,YAAa,CAAC,IAAK,IAAK,IAAK,KAAM,KAAM,KAAM,KAAM,KAAK,CAC1DC,WAAY,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAI,CAC/CC,KAAM,eACNC,OAAQ,UACRC,WAAY,GACZC,QAAS,EAAE,CACXC,qBAAqB,EACrBC,gBAAiB,GACjBC,QAAS,CAAC,aAAa,CACvBC,oBAAqB,GACrBC,sBAAwB,gDACxBC,uBAAwB,aACxBC,mBAAeC,EACfC,eAAgB,EAAE,CAClBC,eAAWF,EACXG,aAAa,CACf,yGC+GgBC,cAAAA,qCAAAA,OAjQS,CAAA,CAAA,IAAA,WACO,CAAA,CAAA,IAAA,OACG,CAAA,CAAA,IAAA,GAkF7BE,EAAiC,CACrC,eACA,OACA,OACA,kBACAN,EACD,CA4BD,SAASO,EACPC,CAAoC,EAEpC,OAA0CR,SAAlCQ,EAAsBC,OAAO,AACvC,CAuBA,SAASM,EAAOC,CAAU,SACP,AAAjB,AAAI,SAAOA,EACFA,EAEQ,AAHa,UAG1B,AAAuB,OAAhBA,EACFC,OAAOC,QAAQ,CAACF,GAAKA,EAAIG,IAEjB,UAAb,OAAOH,GAAkB,WAAWI,IAAI,CAACJ,GACpCK,CADwC,QAC/BL,EAAG,IAEdG,GACT,CAqGO,SAASf,EACd,CAyBa,CACbyC,CAKC,MAkBmBhF,IAjDpB,IA0CI2E,EAqEAhE,EACAC,EAhHJ,KACE+B,CAAG,CACHgB,OAAK,aACLrB,GAAc,CAAK,UACnB2C,GAAW,CAAK,SAChBC,CAAO,WACPC,CAAS,SACTP,CAAO,CACPlB,OAAK,CACL0B,QAAM,MACNC,GAAO,CAAK,OACZC,CAAK,aACLC,CAAW,QACXC,CAAM,mBACNC,CAAiB,aACjBC,EAAc,OAAO,aACrB7E,CAAW,eACX8E,CAAa,UACbC,EAAW,OAAO,QAClBC,CAAM,WACN/E,CAAS,gBACTgF,CAAc,cACdC,CAAY,CACZC,UAAQ,CACR,GAAGC,EACQ,CAzBb,EAyCM,SAAEC,CAAO,aAAEC,CAAW,cAAEC,CAAY,eAAEC,CAAa,CAAE,CAAGrB,EAE1DhF,EAAIkG,GAAW7E,EAAAA,kBAAkB,CACrC,GAAI,aAAcrB,EAChB2E,CADmB,CACV3E,MACJ,CACL,IAAM4D,EAAW,IAAI5D,EAAEsB,WAAW,IAAKtB,EAAEuB,UAAU,CAAC,CAAC+E,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClElF,EAActB,EAAEsB,WAAW,CAACgF,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAC/CnE,EAAAA,AAAuB,OAAXrC,EAAAA,EAAEqC,SAAS,AAATA,EAAS,KAAA,EAAXrC,EAAasG,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClD7B,EAAS,CAAE,GAAG3E,CAAC,UAAE4D,cAAUtC,YAAae,CAAU,CACpD,CAEA,GAAI,KAAyB,IAAlBgE,EACT,MAAM,CADkC,MAClC,cAEL,CAFK,AAAII,MACR,yIADI,oBAAA,OAAA,mBAAA,gBAAA,CAEN,GAEF,IAAIhF,EAAgCwE,EAAKxE,MAAM,EAAI4E,CAGnD,QAAOJ,EAAKxE,MAAM,CAClB,OAAQwE,EAAapB,MAAM,CAI3B,IAAM6B,EAAkB,uBAAwBjF,EAEhD,GAAIiF,GACF,GAAsB,UAAU,CADb,AACf/B,EAAOlD,MAAM,CACf,MAAM,OAAA,cAGL,CAHK,AAAIgF,MACP,mBAAkB9D,MAAI,gCACpB,kEAFC,oBAAA,OAAA,mBAAA,gBAAA,CAGN,EACF,KACK,CAIL,IAAMgE,EAAoBlF,EAC1BA,EAAS,AAACmF,IACR,GAAM,CAAEjC,OAAQ9H,CAAC,CAAE,GAAGgK,EAAM,CAAGD,EAC/B,OAAOD,EAAkBE,EAC3B,CACF,CAEA,GAAIhB,EAAQ,CACK,QAAQ,CAAnBA,IACFR,EAAO,EAAA,EAUT,IAAM8B,EARoE,AAQtDL,CAPlBC,UAAW,CAAEC,SAAU,OAAQ5B,OAAQ,MAAO,EAC9C6B,WAAY,CAAEvD,MAAO,OAAQ0B,OAAQ,MAAO,CAC9C,CAKiC,CAACS,EAAO,CACrCsB,GACF7B,GAAQ,CAAE,GAAGA,CAAK,CAAE,CADL,EACQ6B,CAAW,CAAC,EAErC,IAAMC,EAAcF,AARsC,CACxDD,WAAY,QACZ5B,KAAM,OACR,CAKiC,CAACQ,EAAO,CACrCuB,GAAe,CAACzD,GAClBA,GAAQyD,CADiB,AACjBA,CAEZ,CAEA,IAAIC,EAAY,GACZ5G,EAAWyC,EAAOQ,GAClBhD,EAAYwC,EAAOkC,GAGvB,GA/OE,CAAC,AAFmBzC,AAiPlBG,CA/OAH,CA+OeA,CAjP6B,GAG/B,UAAf,EACCD,KADMC,IACND,EAAgBC,QACfE,CARoCV,IAAhCQ,AAQcA,EARUA,GAAG,AAQbA,CAAmB,CA4OhB,CACvB,IAAM2E,EAAkB5E,EAAgBC,GAAOA,EAAIC,OAAO,CAAGD,EAE7D,GAAI,CAAC2E,EAAgB3E,GAAG,CACtB,CADwB,KAClB,OAAA,cAIL,CAJK,AAAI8D,MACP,8IAA6Ic,KAAKC,SAAS,CAC1JF,IAFE,oBAAA,OAAA,mBAAA,gBAAA,CAIN,GAEF,GAAI,CAACA,EAAgBlC,MAAM,EAAI,CAACkC,EAAgB5D,KAAK,CACnD,CADqD,KAC/C,OAAA,cAIL,CAJK,AAAI+C,MACP,2JAA0Jc,KAAKC,SAAS,CACvKF,IAFE,oBAAA,OAAA,kBAAA,gBAAA,CAIN,GAQF,GALA3G,EAAY2G,EAAgB3G,SAAS,CACrCC,EAAa0G,EAAgB1G,UAAU,CACvCC,EAAcA,GAAeyG,EAAgBzG,WAAW,CACxDwG,EAAYC,EAAgB3E,GAAG,CAE3B,CAAC0C,EACH,GAAI,AAAC5E,CADI,EACSC,GAGX,GAAID,GAHM,AAGM,CAACC,CAHK,CAGM,CACjC,IAAM+G,EAAQhH,EAAW6G,EAAgB5D,KAAK,CAC9ChD,EAAYwD,KAAKwD,KAAK,CAACJ,EAAgBlC,MAAM,CAAGqC,EAClD,MAAO,GAAI,CAAChH,GAAYC,EAAW,CACjC,IAAM+G,EAAQ/G,EAAY4G,EAAgBlC,MAAM,CAChD3E,EAAWyD,KAAKwD,KAAK,CAACJ,EAAgB5D,KAAK,CAAG+D,GAChD,MAREhH,EAAW6G,EAAgB5D,KAAK,CAChChD,EAAY4G,EAAgBlC,MAAM,AASxC,CAGA,IAAIuC,EACF,CAAC1C,IAAyB,QAAZC,CAAAA,GAAsB,KAAmB,IAAZA,CAAY,CAAU,EAC/D,CAACvC,CAJLA,EAAqB,UAAf,OAAOA,EAAmBA,EAAM0E,CAAAA,GAI1B1E,EAAIiF,UAAU,CAAC,UAAYjF,EAAIiF,UAAU,CAAC,QAAA,GAAU,CAE9DtF,GAAc,EACdqF,GAAS,GAEPhD,EAAOrC,WAAW,EAAE,CACtBA,GAAc,CAAA,EAGdoE,GACA,CAAC/B,EAAO5C,mBAAmB,EAC3BY,EAAIkF,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACC,QAAQ,CAAC,SAC9B,CAGAxF,GAAc,CAAA,EAGhB,IAAMyF,EAAa7E,EAAO0B,GA2NpB+E,EAAWb,OAAOc,MAAM,CAC5BvE,EACI,CACE4C,SAAU,WACV7C,OAAQ,OACR1B,MAAO,OACPmG,KAAM,EACNC,IAAK,EACLC,MAAO,EACPC,OAAQ,EACRlJ,2BACAgF,CACF,EACA,CAAC,EACLK,EAAc,CAAC,EAAI,CAAE8D,MAAO,aAAc,EAC1C3E,GAGI4E,EACJ,AAAC9D,GAAgC,UAAhBV,EAWb,KAVgB,SAAhBA,EACG,yCAAwClF,GAAAA,EAAAA,eAAAA,AAAe,EAAC,UACvDC,YACAC,YACAC,aACAC,EACAC,YAAaA,GAAe,GAC5BC,UAAW6I,EAAS7I,SACtB,AAD+B,GAC5B,KACF,QAAO4E,EAAY,KAAI,AAG1ByE,EAAiB,AAAC1H,EAA+B0F,QAAQ,CAC7DwB,EAAS7I,QAJ4C,CAInC,EAGO,SAAvB6I,EAAS7I,SAAS,CAChB,YAAY,AACZ,QAHF6I,EAAS7I,SAAS,CAKlBsJ,EAAqCF,EACrC,gBACEC,EACAE,CANuD,kBAMnCV,EAAS7D,cAAc,EAAI,UAC/CwE,iBAAkB,4BAClBJ,CACF,EACA,CAAC,EAeCK,EA7eR,AA6ewB7F,SA7efA,AAAiB,CAQR,EARQ,GAAA,QACxBC,CAAM,KACNhC,CAAG,CACHL,aAAW,OACXoB,CAAK,SACLkB,CAAO,OACPjB,CAAK,QACLlC,CAAM,CACU,CARQ,EASxB,GAAIa,EACF,MAAO,KADQ,AACNK,EAAKkC,YAAQ1C,EAAWwB,WAAOxB,CAAU,EAGpD,GAAM,QAAEiC,CAAM,MAAEE,CAAI,CAAE,CAxExB,AAwE2Bb,SAxElBA,AACP,CAAsC,CACtCC,CAAyB,CACzBC,CAAyB,EAFzB,GAAA,aAAErC,CAAW,UAAEsC,CAAQ,CAAe,CAAtC,EAIA,GAAID,EAAO,CAET,IAAME,EAAkB,qBAClBC,EAAe,EAAE,CACvB,IAAK,IAAIC,EAAQA,EAAQF,EAAgBG,IAAI,CAACL,IAC5CG,EAAalG,EADwCmG,EACpC,CAACP,GAD0C,MACjCO,CAAK,CAAC,EAAE,GAErC,GAAID,EAAavE,MAAM,CAAE,CACvB,IAAM0E,EAA4C,IAA5BC,KAAKC,GAAG,IAAIL,GAClC,MAAO,CACLM,OAAQR,EAASjI,MAAM,CAAC,AAAC0I,GAAMA,GAAK/C,CAAW,CAAC,EAAE,CAAG2C,GACrDK,KAAM,GACR,CACF,CACA,MAAO,CAAEF,OAAQR,EAAUU,KAAM,GAAI,CACvC,OACA,AAAqB,UAAjB,AAA2B,OAApBZ,EACF,CAAEU,OAAQ9C,EAAagD,KAAM,GAAI,EAkBnC,CAAEF,OAfM,IACV,IAAIlH,IACL,AACA,AAOA,CAACwG,EAAe,EAAE,AAAVA,EAA0B,CAAC3D,GAAG,CACpC,AAACwE,GAAMX,EAASY,CADa,GACT,CAAC,AAACC,GAAMA,GAAKF,IAAMX,CAAQ,CAACA,EAASrE,MAAM,CAAG,EAAE,GAGzE,CACgB+E,KAAM,GAAI,CAC7B,EA+BqCK,EAAQjB,EAAOC,GAC5CmB,EAAOV,AA7C4D,EA6CrD7E,AA9CuD,MA8CjD,CAAG,EAE7B,MAAO,CACLoE,MAAO,AAACA,GAASW,AAAS,QAAgBX,EAAV,QAChCkB,OAAQT,EACLrE,GAAG,CACF,CAACwE,EAAGlF,IACCoC,EAAO,QAAEkD,MAAQhC,UAAKiC,EAASlB,MAAOa,CAAE,GAAG,KACnC,CAATD,KAAAA,EAAeC,EAAIlF,GAAI,CAAA,CACtBiF,GAENS,IAAI,CAAC,MAQRpC,IAAKlB,EAAO,QAAEkD,MAAQhC,UAAKiC,EAASlB,MAAOU,CAAM,CAACU,EAAK,AAAC,EAC1D,CACF,EA0cyC,QACrCH,MACAhC,EACAL,cACAoB,MAAOjD,EACPmE,QAASmD,QACTpE,SACAlC,CACF,GA4BA,MAAO,CAAExG,MAde,CACtB,GAAGgL,CAAI,CACPf,QAASyC,EAAS,OAASzC,gBAC3BS,EACAjC,MAAOjD,EACP2E,OAAQ1E,WACRkF,EACAT,YACAG,MAAO,CAAE,GAAGqE,CAAQ,CAAE,GAAGS,CAAgB,AAAC,EAC1CzG,MAAO4G,EAAc5G,KAAK,CAC1BkB,OAAQ0F,EAAc1F,MAAM,CAC5BlC,IAAK4C,GAAegF,EAAc5H,GAAG,AACvC,EAEgBjF,KADH,aAAE4E,WAAa2C,cAAUS,OAAaL,CAAK,CACnC,CACvB,gCC3uBAnJ,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACwO,kBAAkB,+BCFzC3O,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,QAAW,CAACyO,aAAa,+BCEpC,SAASzE,EAAc,CAKM,MAkFzB1B,EAvFmB,GAAA,QACrBA,CAAM,KACNhC,CAAG,OACHe,CAAK,SACLkB,CAAO,CACoB,CALN,EAqFf0G,EACJ1G,IAAAA,AACgB,OADhBA,AACAD,EAAAA,EAAOtC,SAAAA,AAAS,EAAA,KAAA,EAAhBsC,EAAkBrG,MAAM,CAAC,CAACiN,EAAMC,IAC9BtH,KAAKuH,GAAG,CAACD,MAAMT,AAAa7G,KAAKuH,GAAG,CAACF,MAAoBC,CAAbT,CAAmBQ,EAAAA,CAAAA,EA1FnD,GA4FdR,AAEF,OAAUpG,EAAOnD,IAAI,CAAC,QAAOkK,mBAAmB/I,GAAK,MAAKe,EAAM,MAAK4H,GACnE3I,CAAAA,CAAIiF,UAAU,CAAC,wBAEX,EAAC,CAFqC9K,AAI9C,QAJsDC,GAAG,AACX,CADY4O,kBAAkB,YACnE,UAAO7O,QAAQC,GAAG,CAAC4O,UAS5B,UAAA,qCAAA,KAFAtF,EAAcuF,kBAAkB,EAAG,MAEnC,EAAevF,yGClGCwF,eAAAA,qCAAAA,aAT8B,CAAA,CAAA,IAAA,GASvC,SAASA,EACdC,CAAmB,CACnBC,CAAmB,EAEnB,IAAMC,EAAWC,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAAsB,MACvCC,EAAWD,CAAAA,EAAAA,EAAAA,MAAM,AAANA,EAA4B,MAS7C,MAAOE,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAChB,AAACC,IACC,GAAgB,OAAZA,EAAkB,CACpB,IAAMC,EAAaL,EAASI,OAAO,CAC/BC,IACFL,EAASI,MADK,CACE,CAAG,KACnBC,KAEF,IAAMC,EAAaJ,EAASE,OAAO,CAC/BE,IACFJ,EAASE,MADK,CACE,CAAG,KACnBE,IAEJ,MACMR,CADC,GAEHE,EADQ,AACCI,OAAO,CAAGG,EAAST,EAAMM,EAAAA,EAEhCL,IACFG,EADQ,AACCE,OAAO,CAAGG,EAASR,EAAMK,EAAAA,CAGxC,EACA,CAACN,EAAMC,EAAK,CAEhB,CAEA,SAASQ,EACPT,CAAgC,CAChCM,CAAiB,EAEjB,GAAoB,YAAhB,OAAON,EAST,OADAA,EAAKM,OAAO,CAAGA,EACR,KACLN,EAAKM,OAAO,CAAG,IACjB,CAX8B,EAC9B,IAAMI,EAAUV,EAAKM,SACrB,AAAuB,YAAnB,AAA+B,OAAxBI,EACFA,EAEA,IAAMV,EAAK,KAEtB,CAMF,MANS,yTCySIW,QAAAA,qCAAAA,uDA1VN,CAAA,CAAA,IAAA,YACc,CAAA,CAAA,IAAA,YACJ,CAAA,CAAA,IAAA,QACW,CAAA,CAAA,IAAA,OAYO,CAAA,CAAA,IAAA,OACA,CAAA,CAAA,IAAA,KACV,CAAA,CAAA,IAAA,WACK,CAAA,CAAA,IAAA,WAGJ,CAAA,CAAA,IAAA,QACG,CAAA,CAAA,IAAA,GAGvBC,EAAyC,UAA7B5P,QAAQC,GAAG,CAAC4P,iBAAiB,iIAyB/C,SAASG,EACPC,CAA2B,CAC3BrH,CAA6B,CAC7BsH,CAAqD,CACrDC,CAA2E,CAC3EC,CAAqC,CACrC5K,CAAoB,CACpB6K,CAA8B,EAE9B,IAAMxK,EAAMoK,MAAAA,EAAAA,KAAAA,EAAAA,EAAKpK,GAAG,CACfoK,GAAOA,CAAG,CAAC,kBAAkB,GAAKpK,IAGvCoK,CAH4C,AAGzC,CAAC,kBAAkB,CAAGpK,EAEzB8B,CADU,WAAYsI,EAAMA,EAAIK,MAAM,GAAKC,QAAQC,OAAO,EAAA,EACxDC,KAAK,CAAC,KAAO,GAAGC,IAAI,CAAC,KACrB,GAAI,AAACT,EAAIU,aAAa,EAAKV,EAAD,AAAKW,WAAW,EAAE,AAW5C,GAHIhI,AAAgB,SAAS,IAC3BwH,GAAgB,GAEdF,QAAAA,KAAAA,EAAAA,EAAWZ,OAAO,CAAE,CAItB,IAAMuB,EAAQ,IAAIC,MAAM,QACxB9E,OAAO+E,cAAc,CAACF,EAAO,SAAU,CAAEG,SAAU,GAAOC,MAAOhB,CAAI,GACrE,IAAIiB,GAAY,EACZC,GAAU,EACdjB,EAAUZ,OAAO,CAAC,CAChB,GAAGuB,CAAK,CACRO,YAAaP,EACbQ,cAAepB,EACfqB,OAAQrB,EACRsB,mBAAoB,IAAML,EAC1BM,qBAAsB,IAAML,EAC5BM,QAAS,KAAO,EAChBC,eAAgB,KACdR,GAAY,EACZL,EAAMa,cAAc,EACtB,EACAC,gBAAiB,KACfR,GAAU,EACVN,EAAMc,eAAe,EACvB,CACF,EACF,EACIxB,MAAAA,EAAAA,KAAAA,EAAAA,EAAsBb,OAAAA,AAAO,EAAE,CACjCa,EAAqBb,OAAO,CAACW,GAkDjC,GACF,CAEA,SAASsC,EACP1J,CAAsB,SAEtB,AAAY2J,EAAAA,EAAR1T,CAAW,CAIN,CAAE+J,CAJO,cAIO,EAIlB,CAAE4J,cAAe5J,CAAc,CACxC,CA7IIiH,WAAmBC,qBAAqB,EAAG,EA+I/C,IAAM2C,EAAeC,CAAAA,EAAAA,EAAAA,QAAfD,EAAeC,AAAU,EAC7B,CAAA,EAwBEC,IAzBEF,IAEF,CACE7M,KAAG,QACHkC,CAAM,OACNlB,CAAK,QACLyB,CAAM,OACN1B,CAAK,UACLkC,CAAQ,WACRT,CAAS,OACTG,CAAK,eACLK,CAAa,CACbD,aAAW,CACXR,SAAO,aACP5C,CAAW,MACX+C,CAAI,WACJ2H,CAAS,sBACTC,CAAoB,iBACpBC,CAAe,gBACfyC,CAAc,CACdxC,YAAU,QACV3H,CAAM,SACNoK,CAAO,CACP,GAAG3J,EACJ,CAAA,EAGK4J,EAAS1D,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EACxB,AAACY,IACMA,IAGD6C,CAHM,GAQR7C,EAAIpK,GAAG,AALI,CAKDoK,EAAIpK,GAAAA,AAAG,EAYfoK,EAAI+C,QAAQ,EAAE,AAChBhD,EACEC,EACArH,EACAsH,EACAC,EACAC,EACA5K,EACA6K,GAGN,EACA,CACExK,EACA+C,EACAsH,EACAC,EACAC,EACA0C,EACAtN,EACA6K,EACD,EAGG4C,EAAMlE,CAAAA,EAAAA,EAAAA,YAAAA,AAAY,EAAC6D,EAAcG,GAEvC,MACE,CAAA,AADF,EACE,EAAA,GAAA,EAAC9C,EADH,IACGA,CACE,GAAG9G,CAAI,CACP,GAAGoJ,EAAgB1J,EAAc,CAIlCT,QAASA,EACTxB,MAAOA,EACP0B,OAAQA,EACRQ,SAAUA,EACVoK,YAAW3K,EAAO,OAAS,IAC3BF,UAAWA,EACXG,MAAOA,EAOP3B,MAAOA,EACPkB,OAAQA,EACRlC,IAAKA,EACLoN,IAAKA,EACLvK,OAAQ,AAACmI,IAEPb,EADYa,EAAMQ,UAEhBpB,GAF6B,CAG7BrH,EACAsH,EACAC,EACAC,EACA5K,EACA6K,EAEJ,EACAyC,QAAS,AAACjC,IAERgC,GAAe,GACXjK,AAAgB,SAAS,IAE3BwH,GAAgB,GAEd0C,GACFA,EAAQjC,EAEZ,EAHe,CAMrB,GAGF,SAASsC,EAAa,CAMrB,EANqB,GAAA,aACpBC,CAAW,eACX3F,CAAa,CAId,CANqB,EAOd1D,EAAgC,CACpCsJ,GAAI,QACJC,YAAa7F,EAAc1F,MAAM,CACjCtD,WAAYgJ,EAAc5G,KAAK,CAC/B0M,YAAa9F,EAAc8F,WAAW,CACtCC,eAAgB/F,EAAc+F,cAAc,CAC5C,GAAGjB,EAAgB9E,EAAc5E,aAAa,CAAC,AACjD,SAEA,AAAIuK,GAAeK,EAAAA,OAAQ,CAACC,OAAO,EAAE,AACnCD,EAAAA,OAAQ,CAACC,OAAO,CAACjG,EAAc5H,GAAG,CAAEkE,GAC7B,MAIP,CAAA,EAAA,EAAA,GAAA,EAAC1G,EAAAA,OAAI,CAAA,UACH,CAAA,EAAA,EAAA,GAAA,EAACsQ,EAAD,KAACA,CAOCC,IAAI,UAMJ/F,KAAMJ,EAAc1F,MAAM,MAAG1C,EAAYoI,EAAc5H,GAAG,CACzD,GAAGkE,CAAI,EAZN,UACA0D,EAAc5H,GAAG,CACjB4H,EAAc1F,MAAM,CACpB0F,EAAc5G,KAAK,GAa7B,CAOO,IAAM8I,EAAQgD,CAAAA,EAAAA,EAAAA,CAARhD,SAAQgD,AAAU,EAAlBhD,AACX,CAACxR,EAAOyU,KACN,IAAMiB,EAActQ,CAAAA,EAAAA,EAAAA,UAAU,AAAVA,EAAWyK,EAAAA,aAAa,EAItC8F,EAAgBvQ,CAAAA,EAAAA,EAAAA,UAAAA,AAAU,EAACwK,EAAAA,kBAAkB,EAC7ClG,EAASkM,CAAAA,EAAAA,EAAAA,OAAAA,AAAO,EAAC,SAIH7Q,EAHlB,IAAMA,EAAI0M,GAAakE,GAAiBvP,EAAAA,kBAAkB,CACpDuC,EAAW,IAAI5D,EAAEsB,WAAW,IAAKtB,EAAEuB,UAAU,CAAC,CAAC+E,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClElF,EAActB,EAAEsB,WAAW,CAACgF,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAC/CnE,EAAuB,AAAvBA,OAAYrC,EAAAA,EAAEqC,SAAAA,AAAS,EAAA,KAAA,EAAXrC,EAAasG,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAClD,MAAO,CAAE,GAAGxG,CAAC,UAAE4D,cAAUtC,YAAae,CAAU,CAClD,EAAG,CAACuO,EAAc,EAEZ,QAAEpL,CAAM,CAAEC,mBAAiB,CAAE,CAAGxK,EAChC+R,EAAYf,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAACzG,GAEzBxK,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KACRgS,EAAUZ,OAAO,CAAG5G,CACtB,EAAG,CAACA,EAAO,EAEX,IAAMyH,EAAuBhB,CAAAA,EAAAA,EAAAA,MAAAA,AAAM,EAACxG,GAEpCzK,CAAAA,EAAAA,EAAAA,SAAAA,AAAS,EAAC,KACRiS,EAAqBb,OAAO,CAAG3G,CACjC,EAAG,CAACA,EAAkB,EAEtB,GAAM,CAACW,EAAc8G,EAAgB,CAAG4D,GAAAA,EAAAA,QAAAA,AAAQ,GAAC,GAC3C,CAAC3K,EAAawJ,EAAe,CAAGmB,CAAAA,EAAAA,EAAAA,QAAAA,AAAQ,GAAC,GAEzC,CAAE7V,MAAOsP,CAAa,CAAE7M,KAAMqT,CAAO,CAAE,CAAGxO,CAAAA,EAAAA,EAAAA,WAAAA,AAAW,EAACtH,EAAO,CACjEoL,cAAAA,EAAAA,OAAa,CACbH,QAASvB,eACTyB,cACAD,CACF,GAEA,MACE,CADF,EACE,EAAA,IAAA,EAAA,CADF,CACE,QAAA,CAAA,WAEI,CAAA,EAAA,EAAA,GAAA,EAACqJ,EAAAA,CACE,GAAGjF,CAAa,CACjBjI,YAAayO,EAAQzO,WAAW,CAChCoD,YAAaqL,EAAQrL,WAAW,CAChCL,KAAM0L,EAAQ1L,IAAI,CAClB2H,UAAWA,EACXC,qBAAsBA,EACtBC,gBAAiBA,EACjByC,eAAgBA,EAChBxC,WAAYlS,EAAM0I,KAAK,CACvBoM,IAAKL,IAGRqB,EAAQ9L,QAAQ,CACf,CAAA,CADe,CACf,EAAA,GAAA,EAACgL,EAAAA,CADc,AAEbC,YApDY,CAACS,AAoDAT,EACb3F,cAAeA,IAEf,OAGV", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}