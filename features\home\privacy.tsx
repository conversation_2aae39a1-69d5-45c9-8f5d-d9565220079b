import {
  ArrowUp<PERSON>ightI<PERSON>,
  LockKeyholeIcon,
  ShieldCheckIcon,
  ShieldUserIcon,
} from 'lucide-react';

const privacyData = [
  {
    icon: LockKeyholeIcon,
    title: 'Encrypted',
    description:
      'Your messages and chats are fully encrypted during transit and at rest.',
  },
  {
    icon: ShieldCheckIcon,
    title: 'Enterprise-Grade',
    description:
      'Our infrastructure is built to the highest standards of reliability and security.',
  },
  {
    icon: ShieldUserIcon,
    title: 'Privacy-First',
    description:
      'We will never train on or sell your data - your information stays with you.',
  },
];
export default function Privacy() {
  return (
    <section className="mb-40">
      <div className="flex h-auto flex-col items-center justify-center gap-8 rounded-[20px] p-8 font-aeonik">
        <h2 className="mx-auto max-w-[320px] whitespace-pre-wrap text-center font-medium text-3xl leading-tight sm:max-w-2xl sm:text-4xl md:max-w-none md:text-4xl lg:text-5xl">
          We're serious about privacy.
        </h2>
        <p className="mx-auto w-full max-w-[280px] text-center font-normal text-lg text-muted-foreground sm:max-w-xl sm:text-xl">
          Highlight is built with privacy at its core. Experience powerful AI
          assistance without compromising on your privacy.
        </p>
        <a
          className="group font-medium text-brand-500/75 transition-all duration-300 hover:text-brand-500"
          href="https://docs.highlightai.com/privacy"
          rel="noopener"
          target="_blank"
        >
          <span className="group-hover:-translate-x-2 inline-flex translate-x-2 items-center transition-transform duration-300">
            Our Privacy Policy
            <ArrowUpRightIcon className="lucide lucide-arrow-up-right ml-1 h-4 w-4 origin-left scale-0 transition-all duration-300 group-hover:scale-100" />
          </span>
        </a>
        <div className="w-full max-w-4xl sm:p-8">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            {privacyData.map((item) => (
              <div
                className="flex flex-shrink-0 flex-col justify-between rounded-xl border border-muted/5 bg-muted/20 p-6 px-7 shadow-sm"
                key={item.title}
              >
                <div className="relative z-10 mb-8">
                  <item.icon className="size-11 text-muted-foreground" />
                </div>
                <span>
                  <h3 className="mb-2 font-medium font-sans text-white text-xl">
                    {item.title}
                  </h3>
                  <p className="w-full max-w-lg text-start font-normal text-md text-muted-foreground">
                    {item.description}
                  </p>
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
