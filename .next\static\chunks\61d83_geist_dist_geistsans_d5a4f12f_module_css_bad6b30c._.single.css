/* [project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistsans_d5a4f12f.module.css [app-client] (css) */
@font-face {
  font-family: GeistSans;
  src: url("../media/Geist_Variable-s.p.f19e4721.woff2") format("woff2");
  font-display: swap;
  font-weight: 100 900;
}

@font-face {
  font-family: GeistSans Fallback;
  src: local(Arial);
  ascent-override: 85.83%;
  descent-override: 20.53%;
  line-gap-override: 9.33%;
  size-adjust: 107.19%;
}

.geistsans_d5a4f12f-module__tBZO7G__className {
  font-family: GeistSans, GeistSans Fallback;
}

.geistsans_d5a4f12f-module__tBZO7G__variable {
  --font-geist-sans: "GeistSans", "GeistSans Fallback";
}

/*# sourceMappingURL=61d83_geist_dist_geistsans_d5a4f12f_module_css_bad6b30c._.single.css.map*/