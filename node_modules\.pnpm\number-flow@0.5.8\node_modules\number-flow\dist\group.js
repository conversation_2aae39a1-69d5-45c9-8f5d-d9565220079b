"use strict";const n=require("./lite-DyshwcGq.js"),t=require("./index.js");class i extends n.ServerSafeHTMLElement{constructor(){super(...arguments),this._flows=new Set,this._addDescendant=e=>{e.batched=!0,this._flows.add(e)},this._removeDescendant=e=>{e.batched=!1,this._flows.delete(e)},this._onDescendantConnected=e=>{this._addDescendant(e.target)},this._updating=!1,this._onDescendantUpdate=()=>{this._updating||(this._updating=!0,this._flows.forEach(e=>{e.created&&(e.willUpdate(),queueMicrotask(()=>{e.connected&&e.didUpdate()}))}),queueMicrotask(()=>{this._updating=!1}))}}connectedCallback(){this.querySelectorAll("number-flow").forEach(e=>{this._addDescendant(e)}),this.addEventListener(t.CONNECT_EVENT,this._onDescendantConnected),this.addEventListener(t.UPDATE_EVENT,this._onDescendantUpdate),this._mutationObserver??(this._mutationObserver=new MutationObserver(e=>{e.forEach(r=>{r.removedNodes.forEach(s=>{s instanceof t.default&&this._removeDescendant(s)})})})),this._mutationObserver.observe(this,{childList:!0,subtree:!0})}disconnectedCallback(){var e;this.removeEventListener(t.CONNECT_EVENT,this._onDescendantConnected),this.removeEventListener(t.UPDATE_EVENT,this._onDescendantUpdate),(e=this._mutationObserver)==null||e.disconnect()}}n.define("number-flow-group",i);module.exports=i;
