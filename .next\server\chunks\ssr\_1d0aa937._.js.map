{"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistmono_157ca88a.module.css [app-rsc] (css module)", "turbopack:///[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistsans_d5a4f12f.module.css [app-rsc] (css module)", "turbopack:///[project]/app/aeonikbold_b98b5370.module.css [app-rsc] (css module)", "turbopack:///[project]/app/aeonikregular_756171f0.module.css [app-rsc] (css module)", "turbopack:///[project]/components/ui/sonner.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistmono_157ca88a.js", "turbopack:///[project]/node_modules/.pnpm/geist@1.4.2_next@15.5.2_rea_62f3a0f29926db72aebc49356c7913ca/node_modules/geist/dist/geistsans_d5a4f12f.js", "turbopack:///[project]/app/aeonikbold_b98b5370.js", "turbopack:///[project]/app/aeonikregular_756171f0.js", "turbopack:///[project]/app/layout.tsx"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geistmono_157ca88a-module__WVqpCG__className\",\n  \"variable\": \"geistmono_157ca88a-module__WVqpCG__variable\",\n});\n", "__turbopack_context__.v({\n  \"className\": \"geistsans_d5a4f12f-module__tBZO7G__className\",\n  \"variable\": \"geistsans_d5a4f12f-module__tBZO7G__variable\",\n});\n", "__turbopack_context__.v({\n  \"className\": \"aeonikbold_b98b5370-module__HbNZGa__className\",\n  \"variable\": \"aeonikbold_b98b5370-module__HbNZGa__variable\",\n});\n", "__turbopack_context__.v({\n  \"className\": \"aeonikregular_756171f0-module__It3Qua__className\",\n  \"variable\": \"aeonikregular_756171f0-module__It3Qua__variable\",\n});\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n", "import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22mono.js%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/geist-mono/GeistMono-Variable.woff2%22,%22variable%22:%22--font-geist-mono%22,%22adjustFontFallback%22:false,%22fallback%22:[%22ui-monospace%22,%22SFMono-Regular%22,%22Roboto%20Mono%22,%22Menlo%22,%22Monaco%22,%22Liberation%20Mono%22,%22DejaVu%20Sans%20Mono%22,%22Courier%20New%22,%22monospace%22],%22weight%22:%22100%20900%22}],%22variableName%22:%22GeistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22sans.js%22,%22import%22:%22%22,%22arguments%22:[{%22src%22:%22./fonts/geist-sans/Geist-Variable.woff2%22,%22variable%22:%22--font-geist-sans%22,%22weight%22:%22100%20900%22}],%22variableName%22:%22GeistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'GeistSans', 'GeistSans Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22%22,%22arguments%22:[{%22variable%22:%22--font-aeonik-bold%22,%22src%22:%22./fonts/fonnts.com-Aeonik-Bold.ttf%22}],%22variableName%22:%22aeonikBold%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'aeonikBold', 'aeonikBold Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import cssModule from \"@vercel/turbopack-next/internal/font/local/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22%22,%22arguments%22:[{%22variable%22:%22--font-aeonik-regular%22,%22src%22:%22./fonts/fonnts.com-Aeonik-Regular.ttf%22}],%22variableName%22:%22aeonikRegular%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'aeonikRegular', 'aeonikRegular Fallback'\",\n        \n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import { GeistMono } from 'geist/font/mono';\nimport { GeistSans } from 'geist/font/sans';\nimport type { Metadata } from 'next';\nimport './globals.css';\nimport localFont from 'next/font/local';\nimport { Toaster } from '@/components/ui/sonner';\n\nconst aeonikBold = localFont({\n  variable: '--font-aeonik-bold',\n  src: './fonts/fonnts.com-Aeonik-Bold.ttf',\n});\nconst aeonikRegular = localFont({\n  variable: '--font-aeonik-regular',\n  src: './fonts/fonnts.com-Aeonik-Regular.ttf',\n});\nexport const metadata: Metadata = {\n  title: 'Create Next App',\n  description: 'Generated by create next app',\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html\n      className={`${GeistSans.variable} ${GeistMono.variable} ${aeonikBold.variable} ${aeonikRegular.variable}`}\n      lang=\"en\"\n      suppressHydrationWarning\n    >\n      <head />\n      <body>\n        {children}\n        <Toaster />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": "0BAAA,EAAA,CAAA,CAAA,CACA,UAAA,+CACA,SAAA,6CACA,cCHA,EAAA,CAAA,CAAA,CACA,UAAA,+CACA,SAAA,6CACA,cCHA,EAAA,CAAA,CAAA,CACA,UAAA,gDACA,SAAA,8CACA,aCHA,EAAA,CAAA,CAAA,CACA,UAAA,mDACA,SAAA,iDACA,kDCDO,IAAM,EAAU,CAAA,EADvB,AACuB,EADvB,CAAA,CAAA,MACuB,uBAAA,AAAuB,EAC1C,WAAa,MAAM,AAAI,MAAM,4NAA8N,EAC3P,yDACA,0DAHG,IAAM,EAAU,CAAA,EADvB,AACuB,EADvB,CAAA,CAAA,MACuB,uBAAA,AAAuB,EAC1C,WAAa,MAAU,AAAJ,MAAU,4NAA8N,EAC3P,qCACA,4JCLJ,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,kIAEhB,CACJ,CAEI,AAAsB,MAAM,GAA5B,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECV1C,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,mCAEhB,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECV1C,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,qCAEhB,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECV1C,IAAA,EAAA,EAAA,CAAA,CAAA,MACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,2CAEhB,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECL1C,IAAA,EAAA,EAAA,CAAA,CAAA,MAUO,IAAM,EAAqB,CAChC,MAAO,kBACP,YAAa,8BACf,EAEe,SAAS,EAAW,CACjC,UAAQ,CAGR,EACA,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CACC,UAAW,CAAA,EHdF,AGcK,EAAU,QAAQ,CAAC,CAAC,EAAE,AJd3B,EIcqC,QAAQ,CAAC,CAAC,EFd/C,AEciD,EAAW,QAAQ,CAAC,CAAC,EDdtE,ACcwE,EAAc,QAAQ,CAAA,CAAE,CACzG,KAAK,KACL,wBAAwB,CAAA,CAAA,YAExB,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAA,GACD,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,WACE,EACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAO,CAAA,CAAA,QAIhB", "ignoreList": [0, 1, 4, 5, 6]}