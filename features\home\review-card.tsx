import Image from 'next/image';
import { cn } from '@/lib/utils';

export const ReviewCard = ({
  img,
  name,
  username,
  body,
}: {
  img: string;
  name: string;
  username: string;
  body: string;
}) => {
  return (
    <figure
      className={cn(
        "group flex max-h-72 w-fit flex-shrink-0 flex-col justify-between gap-8 rounded-xl bg-muted/40 p-4 px-5 opacity-60 shadow-sm transition-all duration-300 hover:bg-[url('/gradient.webp')] hover:bg-center hover:bg-cover hover:bg-no-repeat hover:opacity-100 hover:shadow-md md:w-[28rem] md:p-6 md:px-7 [&:hover_.avatar-ring]:ring-brandYellow [&:hover_.external-link-icon]:opacity-100 [&:hover_.social-icon-border]:border-brand-600"
      )}
    >
      <blockquote className="mt-2 font-medium text-md text-primary tracking-normal">
        {body}
        One of the most delightful, inventive, powerful new interfaces I've
        tried in years. Actually feels like an AI native computer.
      </blockquote>
      <div className="relative flex w-full items-center gap-3 text-left transition-all duration-300 hover:scale-[98%]">
        <Image
          alt=""
          className="size-12 rounded-full"
          height="48"
          src={img}
          width="48"
        />

        <div className="flex-1">
          <figcaption className="font-aeonik font-medium text-lg text-primary/80 tracking-normal">
            {name}
          </figcaption>
          <p className="font-aeonik font-normal text-md text-primary/50 tracking-tight">
            {username}
          </p>
        </div>
      </div>
    </figure>
  );
};
