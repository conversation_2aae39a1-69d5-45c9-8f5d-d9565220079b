'use client';

import Cal, { getCalApi } from '@calcom/embed-react';
import { useEffect } from 'react';
export default function CalBook() {
  useEffect(() => {
    (async () => {
      const cal = await getCalApi({ namespace: 'better-flow' });
      cal('ui', {
        theme: 'dark',
        cssVarsPerTheme: { dark: { 'cal-brand': '#ebff0a' } },
        hideEventTypeDetails: false,
        layout: 'month_view',
      });
    })();
  }, []);
  return (
    <Cal
      calLink="rathon-webdev/better-flow"
      config={{ layout: 'month_view', theme: 'dark' }}
      namespace="better-flow"
      style={{ width: '100%', height: '100%', overflow: 'scroll' }}
    />
  );
}
