import { ArrowUpRightIcon } from 'lucide-react';
import { ReviewCards } from './review-cards';
import ReviewCardsMobile from './review-cards-mobile';

export default function Testimonials() {
  return (
    <section className="mb-14">
      <div className="mx-auto mb-14 max-w-6xl px-0 md:px-10 2xl:max-w-7xl">
        <h2 className="mx-auto mb-6 max-w-[320px] whitespace-pre-wrap text-center font-aeonik-bold font-medium text-3xl leading-tight sm:max-w-2xl sm:text-4xl md:max-w-none md:text-4xl lg:text-5xl">
          Hear from our users
        </h2>
        <p className="mx-auto mb-8 max-w-[280px] text-center font-aeonik text-lg text-muted-foreground sm:max-w-xl sm:text-xl">
          Highlight is loved by Mac and Windows users around the world. Become a
          part of our community today by joining our Discord!
        </p>
        <div className="flex justify-center">
          <a
            className="group font-medium font-sans text-brand-600 transition-all duration-300 hover:text-brand-600/80"
            href="/discord"
            rel="noopener noreferrer"
            target="_blank"
          >
            <span className="group-hover:-translate-x-2 inline-flex translate-x-2 items-center transition-transform duration-300">
              Join our Community
              <ArrowUpRightIcon className="lucide lucide-arrow-up-right ml-1 h-4 w-4 origin-left scale-0 transition-all duration-300 group-hover:scale-100" />
            </span>
          </a>
        </div>
      </div>
      <ReviewCards />
      <ReviewCardsMobile />
    </section>
  );
}
