const f = (e, n) => e == null ? n : n == null ? e : Math.max(e, n), i = /* @__PURE__ */ new WeakMap(), l = {
  onUpdate(e, n, o) {
    if (i.set(o, void 0), !o.computedTrend)
      return;
    const s = n.integer.concat(n.fraction).filter((t) => t.type === "integer" || t.type === "fraction"), r = e.integer.concat(e.fraction).filter((t) => t.type === "integer" || t.type === "fraction"), u = s.find((t) => !r.find((c) => c.pos === t.pos && c.value === t.value)), a = r.find((t) => !s.find((c) => t.pos === c.pos && t.value === c.value));
    i.set(o, f(u == null ? void 0 : u.pos, a == null ? void 0 : a.pos));
  },
  getDelta(e, n, o) {
    const s = e - n, r = i.get(o.flow);
    if (!s && r != null && r >= o.pos)
      return o.length * o.flow.computedTrend;
  }
};
export {
  l as continuous
};
