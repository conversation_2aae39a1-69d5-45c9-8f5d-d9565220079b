{"name": "react-medium-image-zoom", "version": "5.3.0", "license": "BSD-3-<PERSON><PERSON>", "description": "Accessible medium.com-style image zoom for React", "type": "module", "main": "./dist/index.js", "exports": {".": "./dist/index.js", "./dist/styles.css": "./dist/styles.css"}, "types": "./dist/index.d.ts", "repository": {"type": "git", "url": "git+ssh://**************/rpearce/react-medium-image-zoom.git"}, "homepage": "https://github.com/rpearce/react-medium-image-zoom", "bugs": "https://github.com/rpearce/react-medium-image-zoom/issues", "author": "<PERSON> <<EMAIL>> (https://robertwpearce.com)", "contributors": ["<PERSON> <<EMAIL>> (https://robertwpearce.com)", "<PERSON> (https://github.com/cbothner)", "ismay <<EMAIL>> (https://www.ismaywolff.nl)", "<PERSON> <jeremy<PERSON><PERSON>@gmail.com> (https://jbini.com)", "<PERSON> (http://www.joshsloat.com)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/HippoDippo)", "<PERSON> (https://github.com/ludwigfrank)", "<PERSON><PERSON> <<EMAIL>> (http://rahulgaba.com)", "<PERSON> (https://github.com/spencerfdavis)", "dnlnvl (https://github.com/dnlnvl)", "<PERSON> <<EMAIL>> (https://dougg0k.js.org)", "<PERSON> (https://sunknudsen.com)", "<PERSON><PERSON> <<EMAIL>> (https://rokoroku.github.io)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://joshcena.com)", "<PERSON><PERSON> <<EMAIL>>", "eych", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON> (https://github.com/edlerd)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://zohaib.is-a.dev)"], "funding": [{"type": "github", "url": "https://github.com/sponsors/rpearce"}], "keywords": ["react", "medium", "image", "zoom", "image-zoom", "modal", "react-component"], "tags": ["react", "medium", "image", "zoom", "image-zoom", "modal", "react-component"], "sideEffects": ["**/*.css"], "files": ["LICENSE", "dist/"], "devDependencies": {"@changesets/cli": "^2.29.5", "@eslint/compat": "^1.3.1", "@eslint/js": "^9.31.0", "@rollup/plugin-typescript": "^12.1.4", "@storybook/addon-a11y": "^9.0.16", "@storybook/addon-docs": "^9.0.16", "@storybook/addon-webpack5-compiler-swc": "^3.0.0", "@storybook/react-webpack5": "^9.0.16", "@storybook/test-runner": "^0.23.0", "@types/eslint__js": "^9.14.0", "@types/node": "^24.0.13", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "all-contributors-cli": "^6.26.1", "concurrently": "^9.2.0", "eslint": "^9.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^6.0.0", "neostandard": "^0.12.2", "react": "^19.1.0", "react-dom": "^19.1.0", "rollup": "^4.45.0", "rollup-plugin-dts": "^6.2.1", "storybook": "^9.0.16", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "eslint-plugin-storybook": "9.0.16"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "scripts": {"build": "rm -rf ./dist && rollup -c rollup.config.js && cp ./source/styles.css ./dist/styles.css", "build:docs": "rm -rf ./docs && mkdir ./docs && storybook build --quiet -o docs", "changeset": "changeset", "ci": "concurrently npm:lint npm:build npm:build:docs", "contributors:add": "all-contributors add", "contributors:generate": "all-contributors generate", "lint": "eslint .", "release": "changeset publish", "start": "NODE_OPTIONS=--openssl-legacy-provider storybook dev -p 6006", "storybook": "storybook dev -p 6006"}}