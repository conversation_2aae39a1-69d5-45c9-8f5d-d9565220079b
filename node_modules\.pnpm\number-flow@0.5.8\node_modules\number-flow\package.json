{"name": "number-flow", "publishConfig": {"access": "public"}, "version": "0.5.8", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://barvian.me"}, "description": "A component to transition and format numbers.", "license": "MIT", "homepage": "https://number-flow.barvian.me/vanilla", "repository": {"type": "git", "url": "https://github.com/barvian/number-flow", "directory": "src"}, "bugs": {"url": "https://github.com/barvian/number-flow/issues"}, "keywords": ["accessible", "odometer", "animation", "number-format", "number-animation", "animated-number"], "files": ["dist", "README.md"], "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./lite": {"types": "./dist/lite.d.ts", "import": "./dist/lite.mjs", "require": "./dist/lite.js"}, "./group": {"types": "./dist/group.d.ts", "import": "./dist/group.mjs", "require": "./dist/group.js"}, "./plugins": {"types": "./dist/plugins/index.d.ts", "import": "./dist/plugins.mjs", "require": "./dist/plugins.js"}}, "devDependencies": {"@rollup/plugin-typescript": "^12.1.0", "@testing-library/dom": "^10.4.0", "@vitest/browser": "^2.1.2", "babel-plugin-styled-components": "^2.1.4", "magic-string": "^0.30.11", "parse-literals": "^1.2.1", "playwright": "^1.48.0", "rollup-plugin-minify-html-literals-v3": "^1.3.4", "tslib": "^2.7.0", "typescript": "^5.6.2", "vite": "^5.4.3", "vitest": "^2.1.2"}, "dependencies": {"esm-env": "^1.1.4"}, "scripts": {"build": "vite build --mode production", "dev": "vite build --mode development --watch", "test": "pnpm -r --workspace-concurrency 1 --filter=\"./test/apps/*\" test"}}