export default function Featurea({
  title,
  description,
  videoSrc,
}: {
  title: string;
  description: string;
  videoSrc: string;
}) {
  return (
    <section className="mb-40">
      <div className="mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl">
        {/* Desktop */}
        <h2
          className="mb-6 text-center font-aeonik font-medium text-3xl leading-tight sm:text-4xl md:text-4xl lg:text-5xl"
          id="feature-title"
        >
          <span className="mx-auto block max-w-[320px] sm:max-w-2xl md:max-w-4xl">
            {title}
          </span>
        </h2>
        <div className="mb-12 text-center md:mb-16">
          <p className="mx-auto mb-8 max-w-[280px] font-aeonik text-lg text-muted-foreground sm:max-w-2xl sm:text-xl md:max-w-3xl">
            {description}
          </p>
        </div>

        <div className="relative flex aspect-square w-full items-center justify-center overflow-hidden rounded-[20px] bg-[#303030] md:aspect-auto">
          <video
            aria-label="Demo video 1"
            autoPlay
            className="h-full w-full scale-125 rounded-[20px] object-cover md:h-auto md:scale-110 md:object-contain"
            loop
            muted
            playsInline
            src={videoSrc}
          >
            <track kind="captions" label="English captions" srcLang="en" />
          </video>
        </div>
      </div>
    </section>
  );
}
