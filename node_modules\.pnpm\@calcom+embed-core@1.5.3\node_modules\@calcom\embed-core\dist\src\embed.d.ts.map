{"version": 3, "file": "embed.d.ts", "sourceRoot": "", "sources": ["../../src/embed.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,2BAA2B,EAAE,MAAM,gBAAgB,CAAC;AAE5G,OAAO,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAC;AACxD,OAAO,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAEpE,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAIxC,YAAY,EAAE,2BAA2B,EAAE,MAAM,gBAAgB,CAAC;AAGlE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAGvD,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,GAAG,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACjF,MAAM,MAAM,OAAO,GAAG;IACpB,UAAU,EAAE,MAAM,CAAC;IACnB,MAAM,EAAE,MAAM,mBAAmB,CAAC;IAClC,GAAG,EAAE,mBAAmB,CAAC,MAAM,mBAAmB,CAAC,CAAC;CACrD,CAAC;AAWF,OAAO,QAAQ,OAAO,CAAC;AACvB,KAAK,SAAS,GAAG,MAAM,CAAC;AACxB,KAAK,UAAU,GAAG;IAChB,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,CAAC,EAAE,OAAO,CAAC;IAChB,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB,CAAC;AAEF,KAAK,aAAa,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG;IACzC,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,KAAK,aAAa,GAAG;KAClB,CAAC,IAAI,MAAM,OAAO,mBAAmB,GAAG;QACvC,MAAM,EAAE,CAAC,CAAC;QACV,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC,OAAO,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;KACtD;CACF,CAAC,MAAM,OAAO,mBAAmB,CAAC,CAAC;AA4FpC,KAAK,8BAA8B,GAAG;KACnC,CAAC,IAAI,MAAM,YAAY,GAAG;QACzB,MAAM,EAAE,CAAC,CAAC;QACV,QAAQ,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;KACrD;CACF,CAAC,MAAM,YAAY,CAAC,CAAC;AAEtB,KAAK,oBAAoB,GAAG;IAC1B,EAAE,EAAE,CAAC,IAAI,EAAE,8BAA8B,CAAC,CAAC;IAC3C,GAAG,EAAE,CAAC,KAAK,EAAE,8BAA8B,CAAC,CAAC;CAC9C,GAAG;KACD,CAAC,IAAI,OAAO,CAAC,MAAM,MAAM,EAAE,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI,EAAE,KAAK,EAAE,KAAK,IAAI,GACpF,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAC7B,KAAK;CACV,CAAC;AAEF,KAAK,iBAAiB,GAAG,oBAAoB,CAAC,MAAM,oBAAoB,CAAC,CAAC;AAE1E,MAAM,MAAM,WAAW,GAAG,iBAAiB,GAAG,iBAAiB,EAAE,CAAC;AAClE,MAAM,MAAM,gBAAgB,GAAG,WAAW,EAAE,CAAC;AAoB7C,KAAK,oCAAoC,GAAG,2BAA2B,GAAG;IACxE,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,CAAC;CAC3B,CAAC;AAEF,KAAK,kDAAkD,GAAG,oCAAoC,GAAG;IAC/F,iBAAiB,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACnC,CAAC;AAEF,qBAAa,GAAG;IACd,MAAM,CAAC,EAAE,iBAAiB,CAAC;IAE3B,QAAQ,EAAE,UAAU,CAAC;IAErB,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEnB,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEnB,SAAS,EAAE,MAAM,CAAC;IAElB,aAAa,EAAE,gBAAgB,CAAC;IAEhC,WAAW,EAAG,OAAO,CAAC;IAEtB,aAAa,EAAE,aAAa,EAAE,CAAM;IAEpC,GAAG,EAAE,MAAM,CAAC;IAEZ,aAAa,CAAC,EAAE,OAAO,CAAC;IAExB,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;IAE5D,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,2BAA2B;IASzD,kBAAkB,CAAC,iBAAiB,EAAE,UAAU,GAAG,WAAW;IA4B9D,YAAY,CAAC,KAAK,EAAE,KAAK;IAczB;;OAEG;IACH,YAAY,CAAC,EACX,OAAO,EACP,MAAW,EACX,SAAS,GACV,EAAE;QACD,OAAO,EAAE,MAAM,CAAC;QAChB,MAAM,CAAC,EAAE,kDAAkD,CAAC;QAC5D,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;KAC1B;IAgDD,aAAa;IAIb,UAAU,CAAC,aAAa,EAAE,aAAa;gBAkB3B,SAAS,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK;IAsFvC,OAAO,CAAC,YAAY;IAIpB,OAAO,CAAC,sBAAsB;IAM9B,OAAO,CAAC,wBAAwB;CAoBjC;AAED,cAAM,MAAM;IACV,GAAG,EAAE,GAAG,CAAC;IACT,MAAM,CAAC,qBAAqB,WAAkB;IAC9C,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,iBAAiB,CAAC,EAAE,MAAM,CAAC;gBACf,GAAG,EAAE,GAAG;IAIpB;;;OAGG;IACH,IAAI,CAAC,iBAAiB,CAAC,EAAE,MAAM,GAAG,aAAa,EAAE,MAAM,gBAAsB;IAuB7E;;;OAGG;IACH,aAAa,CAAC,SAAS,EAAE,MAAM;IAK/B;;OAEG;IACH,MAAM,CAAC,EACL,OAAO,EACP,iBAAiB,EACjB,MAAM,GACP,EAAE;QACD,OAAO,EAAE,MAAM,CAAC;QAChB,iBAAiB,EAAE,MAAM,GAAG,WAAW,CAAC;QACxC,MAAM,CAAC,EAAE,2BAA2B,CAAC;KACtC;IAyED,cAAc,CAAC,EACb,OAAO,EACP,UAA0B,EAC1B,cAAsB,EACtB,UAAU,EACV,cAA+B,EAC/B,WAA4B,EAC5B,eAAsC,EACtC,SAAS,EACT,MAAM,GACP,EAAE;QACD,OAAO,EAAE,MAAM,CAAC;QAChB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,UAAU,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,EAAE,MAAM,CAAC,CAAC;QAClE,cAAc,CAAC,EAAE,OAAO,CAAC;QACzB,cAAc,CAAC,EAAE,aAAa,GAAG,cAAc,CAAC;QAChD,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,eAAe,CAAC,EAAE,MAAM,CAAC;QACzB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,MAAM,CAAC,EAAE,2BAA2B,CAAC;KACtC;IA0CD,KAAK,CAAC,EACJ,OAAO,EACP,MAAW,EACX,SAAS,EACT,WAAmB,GACpB,EAAE;QACD,OAAO,EAAE,MAAM,CAAC;QAChB,MAAM,CAAC,EAAE,2BAA2B,CAAC;QACrC,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,WAAW,CAAC,EAAE,OAAO,CAAC;KACvB;IAqFD,OAAO,CAAC,WAAW;IAOnB,EAAE,CAAC,CAAC,SAAS,MAAM,YAAY,EAAE,EAC/B,MAAM,EACN,QAAQ,GACT,EAAE;QACD,MAAM,EAAE,CAAC,CAAC;QACV,QAAQ,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;KACrD;IAkBD,GAAG,CAAC,CAAC,SAAS,MAAM,YAAY,EAAE,EAChC,MAAM,EACN,QAAQ,GACT,EAAE;QACD,MAAM,EAAE,CAAC,CAAC;QACV,QAAQ,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;KACrD;IAGD;;;;;;OAMG;IACH,OAAO,CAAC,EACN,OAAO,EACP,IAAI,EACJ,OAAY,GACb,EAAE;QACD,OAAO,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE,OAAO,GAAG,gBAAgB,CAAC;QAClC,OAAO,CAAC,EAAE;YACR,eAAe,CAAC,EAAE,OAAO,CAAC;SAC3B,CAAC;KACH;IAwDD,SAAS,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE;QAAE,OAAO,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,OAAO,GAAG,gBAAgB,CAAA;KAAE;IAOlF,EAAE,CAAC,QAAQ,EAAE,QAAQ;CAiBtB;AAQD,MAAM,MAAM,KAAK,GAAG,GAAG,EAAE,CAAC;AAC1B,KAAK,YAAY,GAAG;IAClB,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC9B,CAAC;AAEF,KAAK,yBAAyB,GAAG,MAAM,oBAAoB,CAAC;AAE5D,MAAM,WAAW,kBAAkB;IACjC,CAAC,CAAC,SAAS,yBAAyB,EAAE,UAAU,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;IAClG,mEAAmE;IACnE,MAAM,CAAC,EAAE,OAAO,CAAC;IACjB,4DAA4D;IAC5D,CAAC,EAAE,KAAK,CAAC;IACT,sEAAsE;IACtE,QAAQ,CAAC,EAAE,GAAG,CAAC;IACf,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,UAAU,CAAC,EAAE,OAAO,EAAE,CAAC;IACvB,MAAM,CAAC,EAAE,YAAY,CAAC;CACvB;AAGD,KAAK,eAAe,GAAG,kBAAkB,GAAG;IAC1C,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;CACxC,CAAC;AAEF,MAAM,MAAM,SAAS,GAAG,eAAe,CAAC;AAExC,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,MAAM;QACd,GAAG,EAAE,SAAS,CAAC;KAChB;CACF;AAED,MAAM,WAAW,SAAU,SAAQ,MAAM;IACvC,GAAG,EAAE,SAAS,CAAC;CAChB"}