import { ArmchairIcon } from 'lucide-react';
import Link from 'next/link';
import { links } from '@/config/docs';
import { siteConfig } from '@/config/site';
import { Separator } from '../ui/separator';
import FooterSubForm from './footer-subscribe-form';

export function SiteFooter() {
  return (
    <footer className="border-grid border-t bg-my-background pt-16" id="footer">
      <div className="container relative">
        <div className="grid grid-cols-2 gap-10 md:grid-cols-4 md:px-4 lg:grid-cols-12">
          <div className="col-span-2 flex flex-col justify-between xl:col-span-3">
            <Link
              aria-label="go home"
              className="flex size-fit items-center gap-1"
              href="/"
            >
              <ArmchairIcon className="block size-6 text-brand-600" />
              <span>Better Flow</span>
            </Link>
            <div className="my-5 w-fit font-normal text-md text-muted-foreground/50">
              Highlight AI lets models understand your desktop activity. Get
              stuff done faster.
            </div>
            <div className="flex gap-2">
              <Link
                className="text-muted-foreground transition-colors duration-150 hover:text-primary"
                href={siteConfig.links.youtube}
                rel="noreferrer"
                target="_blank"
              >
                <svg
                  className="size-5 fill-foreground"
                  height="24px"
                  viewBox="0 0 72 72"
                  width="24px"
                  x="0px"
                  xmlns="http://www.w3.org/2000/svg"
                  y="0px"
                >
                  <title>Youtube</title>
                  <path d="M61.115,18.856C63.666,21.503,64,25.709,64,36s-0.334,14.497-2.885,17.144C58.563,55.791,55.906,56,36,56	s-22.563-0.209-25.115-2.856C8.334,50.497,8,46.291,8,36s0.334-14.497,2.885-17.144S16.094,16,36,16S58.563,16.209,61.115,18.856z M31.464,44.476l13.603-8.044l-13.603-7.918V44.476z" />
                </svg>
              </Link>
              <Separator orientation="vertical" />
              <Link
                className="text-muted-foreground transition-colors duration-150 hover:text-primary"
                href={siteConfig.links.instagram}
                rel="noreferrer"
                target="_blank"
              >
                <svg
                  className="size-5 fill-foreground"
                  height="24px"
                  viewBox="0 0 64 64"
                  width="24px"
                  x="0px"
                  xmlns="http://www.w3.org/2000/svg"
                  y="0px"
                >
                  <title>Instagram</title>
                  <path d="M 21.580078 7 C 13.541078 7 7 13.544938 7 21.585938 L 7 42.417969 C 7 50.457969 13.544938 57 21.585938 57 L 42.417969 57 C 50.457969 57 57 50.455062 57 42.414062 L 57 21.580078 C 57 13.541078 50.455062 7 42.414062 7 L 21.580078 7 z M 47 15 C 48.104 15 49 15.896 49 17 C 49 18.104 48.104 19 47 19 C 45.896 19 45 18.104 45 17 C 45 15.896 45.896 15 47 15 z M 32 19 C 39.17 19 45 24.83 45 32 C 45 39.17 39.169 45 32 45 C 24.83 45 19 39.169 19 32 C 19 24.831 24.83 19 32 19 z M 32 23 C 27.029 23 23 27.029 23 32 C 23 36.971 27.029 41 32 41 C 36.971 41 41 36.971 41 32 C 41 27.029 36.971 23 32 23 z" />
                </svg>
              </Link>

              <Separator orientation="vertical" />
              <Link
                className="text-muted-foreground transition-colors duration-150 hover:text-primary"
                href={siteConfig.links.twitter}
                rel="noreferrer"
                target="_blank"
              >
                <svg
                  className="size-5 fill-foreground"
                  height="24px"
                  viewBox="0 0 24 24"
                  width="24px"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>Twitter</title>
                  <path d="M 2.3671875 3 L 9.4628906 13.140625 L 2.7402344 21 L 5.3808594 21 L 10.644531 14.830078 L 14.960938 21 L 21.871094 21 L 14.449219 10.375 L 20.740234 3 L 18.140625 3 L 13.271484 8.6875 L 9.2988281 3 L 2.3671875 3 z M 6.2070312 5 L 8.2558594 5 L 18.033203 19 L 16.001953 19 L 6.2070312 5 z" />
                </svg>
              </Link>
            </div>
          </div>

          {links.map((link) => (
            <div
              className="space-y-4 text-sm md:ml-5 lg:col-span-2"
              key={link.group}
            >
              <span className="block font-medium">{link.group}</span>
              <ul className="space-y-3">
                {link.items.map((item) => (
                  <li key={item.title}>
                    <Link
                      href={item.href}
                      {...(item.external
                        ? {
                            target: '_blank',
                            rel: 'noopener noreferrer',
                          }
                        : {})}
                      className="block text-muted-foreground text-sm leading-5 duration-150 hover:text-primary"
                    >
                      {item.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}

          <div className="col-span-2 space-y-4 text-sm lg:col-span-3">
            <span className="block font-medium">
              Subscribe to our newsletter
            </span>
            <div>
              <p className="text-balance text-muted-foreground text-sm leading-5">
                Stay updated on new releases and features, guides, and case
                studies.
              </p>
            </div>
            <FooterSubForm />
          </div>
        </div>
        <div className="my-12 flex items-center justify-between gap-5">
          <div className="space-y-3">
            <p className="text-balance text-muted-foreground text-sm leading-5">
              © {new Date().getFullYear()} Rathon, agency.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
