{"name": "better", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "format": "biome format --write"}, "dependencies": {"@calcom/embed-react": "^1.5.3", "@hookform/resolvers": "^5.2.1", "@number-flow/react": "^0.5.10", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@react-email/components": "^0.5.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "geist": "^1.4.2", "lucide-react": "^0.542.0", "motion": "^12.23.12", "next": "15.5.2", "next-themes": "^0.4.6", "prettier": "^3.6.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-medium-image-zoom": "^5.3.0", "react-use-measure": "^2.1.7", "resend": "^6.0.2", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.1.5"}, "devDependencies": {"@biomejs/biome": "2.2.2", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5", "ultracite": "5.2.9"}, "packageManager": "pnpm@10.15.0+sha512.486ebc259d3e999a4e8691ce03b5cac4a71cbeca39372a9b762cb500cfdf0873e2cb16abe3d951b1ee2cf012503f027b98b6584e4df22524e0c7450d9ec7aa7b"}