module.exports=[70381,a=>{a.v({className:"geistmono_157ca88a-module__WVqpCG__className",variable:"geistmono_157ca88a-module__WVqpCG__variable"})},93034,a=>{a.v({className:"geistsans_d5a4f12f-module__tBZO7G__className",variable:"geistsans_d5a4f12f-module__tBZO7G__variable"})},44211,a=>{a.v({className:"aeonikbold_b98b5370-module__HbNZGa__className",variable:"aeonikbold_b98b5370-module__HbNZGa__variable"})},6601,a=>{a.v({className:"aeonikregular_756171f0-module__It3Qua__className",variable:"aeonikregular_756171f0-module__It3Qua__variable"})},82170,a=>{"use strict";a.s(["Toaster",()=>b]);let b=(0,a.i(62e3).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/sonner.tsx <module evaluation>","Toaster")},52605,a=>{"use strict";a.s(["Toaster",()=>b]);let b=(0,a.i(62e3).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/components/ui/sonner.tsx","Toaster")},5968,a=>{"use strict";a.i(82170);var b=a.i(52605);a.n(b)},33290,a=>{"use strict";a.s(["default",()=>m,"metadata",()=>l],33290);var b=a.i(38470),c=a.i(70381);let d={className:c.default.className,style:{fontFamily:"'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace"}};null!=c.default.variable&&(d.variable=c.default.variable);var e=a.i(93034);let f={className:e.default.className,style:{fontFamily:"'GeistSans', 'GeistSans Fallback'"}};null!=e.default.variable&&(f.variable=e.default.variable);var g=a.i(44211);let h={className:g.default.className,style:{fontFamily:"'aeonikBold', 'aeonikBold Fallback'"}};null!=g.default.variable&&(h.variable=g.default.variable);var i=a.i(6601);let j={className:i.default.className,style:{fontFamily:"'aeonikRegular', 'aeonikRegular Fallback'"}};null!=i.default.variable&&(j.variable=i.default.variable);var k=a.i(5968);let l={title:"Create Next App",description:"Generated by create next app"};function m({children:a}){return(0,b.jsxs)("html",{className:`${f.variable} ${d.variable} ${h.variable} ${j.variable}`,lang:"en",suppressHydrationWarning:!0,children:[(0,b.jsx)("head",{}),(0,b.jsxs)("body",{children:[a,(0,b.jsx)(k.Toaster,{})]})]})}}];

//# sourceMappingURL=_1d0aa937._.js.map