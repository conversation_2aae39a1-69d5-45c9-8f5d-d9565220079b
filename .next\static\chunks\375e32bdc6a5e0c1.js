(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,44051,36650,t=>{"use strict";t.s(["Check",()=>i],44051);var e=t.i(86981);let i=(0,e.default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);t.s(["X",()=>n],36650);let n=(0,e.default)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},90946,t=>{"use strict";t.s(["default",()=>a]);var e=t.i(65830),i=t.i(44051),n=t.i(36650),r=t.i(94710),s=t.i(47163);let o=["Highlight","ChatGPT","Claude","Raycast","Notion"];function a(){return(0,e.jsx)("div",{className:"no-scrollbar hidden w-full overflow-x-auto md:block",children:(0,e.jsxs)("table",{className:"w-full min-w-[700px] border-collapse border-spacing-0",style:{borderSpacing:"0px"},children:[(0,e.jsx)("thead",{children:(0,e.jsxs)("tr",{children:[(0,e.jsx)("th",{className:"sticky top-0 left-0 z-40 w-[200px] min-w-[200px] bg-[#090909] text-left font-normal text-sm md:w-[260px] md:min-w-[260px] md:text-base",scope:"col",children:(0,e.jsx)("div",{className:"flex flex-row items-center gap-3 rounded-t-xl py-2",children:(0,e.jsx)("span",{className:"font-medium font-sans text-lg text-white",children:"Features"})})}),o.map((t,i)=>(0,e.jsx)("th",{className:"sticky top-0 left-[200px] z-30 w-[120px] min-w-[120px] bg-[#090909] px-0 text-center font-normal text-sm md:left-auto md:text-base",children:(0,e.jsx)("div",{className:(0,s.cn)("flex flex-col items-center font-medium font-sans",0===i?"rounded-t-xl py-2 text-primary":"p-4 text-muted-foreground"),children:(0,e.jsx)("div",{className:"flex items-center gap-1.5",children:(0,e.jsx)("a",{className:"flex items-center gap-1.5",href:"/",children:(0,e.jsx)("span",{className:"pb-[1.5px] font-medium text-lg",children:t})})})})},t))]})}),(0,e.jsx)("tbody",{children:r.featuresCompare.map((t,r)=>(0,e.jsxs)("tr",{className:"border-0.5 border-muted/60 border-b",children:[(0,e.jsx)("td",{className:"sticky left-0 z-30 w-[200px] min-w-[200px] bg-[#090909] py-3 pr-2 align-middle md:w-[260px] md:min-w-[260px] md:pr-8",children:(0,e.jsx)("span",{className:"text-nowrap font-medium text-base text-muted-foreground leading-tight",children:t.feature})}),o.map((r,o)=>(0,e.jsx)("td",{className:(0,s.cn)("w-[120px] min-w-[120px] p-4 text-center align-middle",0===o&&"sticky left-[200px] z-20 bg-[#090909] md:static"),children:(0,e.jsx)("div",{className:(0,s.cn)("mx-auto flex size-6 items-center justify-center rounded-lg text-black md:size-7",t[r]?"bg-brand-400":"bg-brand-100"),children:t[r]?(0,e.jsx)(i.Check,{className:"mx-auto size-4 text-black"}):(0,e.jsx)(n.X,{className:"mx-auto size-4 text-brand-600"})})},r))]},r))})]})})}},95540,t=>{"use strict";t.s(["default",()=>a]);var e=t.i(65830),i=t.i(44051),n=t.i(36650),r=t.i(94710),s=t.i(47163);let o=["Highlight","ChatGPT","Claude","Raycast","Notion"];function a(){return(0,e.jsx)("div",{className:"no-scrollbar block w-full overflow-x-auto md:hidden",children:(0,e.jsxs)("table",{className:"w-max min-w-[700px] border-collapse border-spacing-0",style:{borderSpacing:"0px"},children:[(0,e.jsx)("thead",{children:(0,e.jsxs)("tr",{children:[(0,e.jsx)("th",{className:"sticky top-0 left-0 z-50 w-[120px] min-w-[120px] bg-[#090909] text-left"}),r.featuresCompare.map((t,i)=>(0,e.jsx)("th",{className:"sticky top-0 z-40 w-[120px] min-w-[120px] bg-[#090909] text-center font-normal text-muted-foreground text-sm",children:(0,e.jsx)("span",{className:"block px-3 py-2 text-base leading-tight",children:t.feature})},i))]})}),(0,e.jsx)("tbody",{children:o.map((t,o)=>(0,e.jsxs)("tr",{className:"border-0.5 border-muted/60 border-b",children:[(0,e.jsx)("td",{className:"sticky left-0 z-40 w-[120px] min-w-[120px] bg-[#090909] px-3 py-3 pr-4 align-middle",children:(0,e.jsx)("span",{className:"text-nowrap font-medium font-sans text-base text-primary leading-tight",children:t})}),r.featuresCompare.map((r,o)=>(0,e.jsx)("td",{className:(0,s.cn)("w-[120px] min-w-[120px] p-4 text-center align-middle"),children:(0,e.jsx)("div",{className:(0,s.cn)("mx-auto flex size-6 items-center justify-center rounded-lg text-black md:size-7",r[t]?"bg-brand-400":"bg-brand-100"),children:r[t]?(0,e.jsx)(i.Check,{className:"mx-auto size-4 text-black"}):(0,e.jsx)(n.X,{className:"mx-auto size-4 text-brand-600"})})},o))]},o))})]})})}},75533,(t,e,i)=>{"use strict";Object.defineProperty(i,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(i,{default:function(){return l},getImageProps:function(){return a}});let n=t.r(81258),r=t.r(16783),s=t.r(38235),o=n._(t.r(98475));function a(t){let{props:e}=(0,r.getImgProps)(t,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[t,i]of Object.entries(e))void 0===i&&delete e[t];return{props:e}}let l=s.Image},57034,(t,e,i)=>{e.exports=t.r(75533)},85161,t=>{"use strict";t.s(["default",()=>A],85161);var e=t.i(65830),i=t.i(57034),n=t.i(6943),r=n,s=t.i(14931);let o=(t,e)=>{var i,n;return t===(null==e||null==(n=e.tagName)||null==(i=n.toUpperCase)?void 0:i.call(n))},a=t=>o("DIV",t)||o("SPAN",t),l=t=>o("IMG",t),u=t=>t.complete&&0!==t.naturalHeight,h=t=>o("SVG",t),d=t=>{let{height:e,offset:i,width:n}=t;return Math.min((window.innerWidth-2*i)/n,(window.innerHeight-2*i)/e)},c=t=>{let{containerHeight:e,containerWidth:i,hasScalableSrc:n,offset:r,targetHeight:s,targetWidth:o}=t;return e&&i?!n&&s&&o?(t=>{let{containerHeight:e,containerWidth:i,offset:n,targetHeight:r,targetWidth:s}=t,o=d({height:r,offset:n,width:s}),a=s>r?s/i:r/e;return o>1?a:o*a})({containerHeight:e,containerWidth:i,offset:r,targetHeight:s,targetWidth:o}):d({height:e,offset:r,width:i}):1},m=/url(?:\(['"]?)(.*?)(?:['"]?\))/,p=t=>{if(t){if(l(t))return t.currentSrc;else if(a(t)){let i=window.getComputedStyle(t).backgroundImage;if(i){var e;return null==(e=m.exec(i))?void 0:e[1]}}}},f=t=>{let{position:e,relativeNum:i}=t,n=parseFloat(e);return e.endsWith("%")?i*n/100:n},g=/\.svg$/i,v=t=>{if(!t)return{};if(!h(t))return{height:t.offsetHeight,left:t.offsetLeft,width:t.offsetWidth,top:t.offsetTop};{let e=t.parentElement,i=t.getBoundingClientRect();if(!e)return{height:i.height,left:i.left,width:i.width,top:i.top};{let t=e.getBoundingClientRect();return{height:i.height,left:t.left-i.left,top:t.top-i.top,width:i.width}}}},y=["img","svg",'[role="img"]',"[data-zoom]"].map(t=>"".concat(t,':not([aria-hidden="true"])')).join(","),x={overflow:"",width:""};function w(t){return r.default.createElement(b,{...t})}class b extends r.default.Component{render(){let{handleBtnUnzoomClick:t,handleDialogCancel:e,handleDialogClick:i,handleDialogClose:n,handleUnzoom:o,handleZoom:u,imgEl:d,props:{a11yNameButtonUnzoom:m,a11yNameButtonZoom:v,children:y,classDialog:x,IconUnzoom:w,IconZoom:b,isZoomed:E,wrapElement:S,ZoomContent:T,zoomImg:A,zoomMargin:P},refContent:M,refDialog:k,refModalContent:C,refModalImg:j,refWrap:D,state:{id:z,isZoomImgLoaded:L,loadedImgEl:V,modalState:N,shouldRefresh:R,styleGhost:I}}=this,O="rmiz-modal-".concat(z),F="rmiz-modal-img-".concat(z),B=a(d),U=l(d),_=h(d),W=(t=>{if(t){var e,i;return l(t)?null!=(e=t.alt)?e:void 0:null!=(i=t.getAttribute("aria-label"))?i:void 0}})(d),H=p(d),Y=U?d.sizes:void 0,G=U?d.srcset:void 0,X=U?d.crossOrigin:void 0,q=!!(null==A?void 0:A.src),Z=this.hasImage(),K=W?"".concat(v,": ").concat(W):v,$="LOADING"===N||"LOADED"===N,J=Z?"found":"not-found",Q="UNLOADED"===N||"UNLOADING"===N?"hidden":"visible";this.styleModalImg=Z?(t=>{var e;let{hasZoomImg:i,imgSrc:n,isSvg:r,isZoomed:s,loadedImgEl:o,offset:l,shouldRefresh:u,targetEl:h}=t,d=r||(null==n||null==(e=n.slice)?void 0:e.call(n,0,18))==="data:image/svg+xml"||i||!!(n&&g.test(n)),m=h.getBoundingClientRect(),p=window.getComputedStyle(h),v=null!=o&&a(h),y=null!=o&&!v,x=(t=>{let{containerHeight:e,containerLeft:i,containerTop:n,containerWidth:r,hasScalableSrc:s,offset:o,targetHeight:a,targetWidth:l}=t,u=c({containerHeight:e,containerWidth:r,hasScalableSrc:s,offset:o,targetHeight:a,targetWidth:l});return{top:n,left:i,width:r*u,height:e*u,transform:"translate(0,0) scale(".concat(1/u,")")}})({containerHeight:m.height,containerLeft:m.left,containerTop:m.top,containerWidth:m.width,hasScalableSrc:d,offset:l,targetHeight:(null==o?void 0:o.naturalHeight)||m.height,targetWidth:(null==o?void 0:o.naturalWidth)||m.width}),w=Object.assign({},x,y?(t=>{let{containerHeight:e,containerLeft:i,containerTop:n,containerWidth:r,hasScalableSrc:s,objectFit:o,objectPosition:a,offset:l,targetHeight:u,targetWidth:h}=t;if("scale-down"===o&&(o=h<=r&&u<=e?"none":"contain"),"cover"===o||"contain"===o){let t=r/h,d=e/u,m="cover"===o?Math.max(t,d):Math.min(t,d),[p="50%",g="50%"]=a.split(" "),v=f({position:p,relativeNum:r-h*m}),y=f({position:g,relativeNum:e-u*m}),x=c({containerHeight:u*m,containerWidth:h*m,hasScalableSrc:s,offset:l,targetHeight:u,targetWidth:h});return{top:n+y,left:i+v,width:h*m*x,height:u*m*x,transform:"translate(0,0) scale(".concat(1/x,")")}}if("none"===o){let[t="50%",o="50%"]=a.split(" "),d=f({position:t,relativeNum:r-h}),m=f({position:o,relativeNum:e-u}),p=c({containerHeight:u,containerWidth:h,hasScalableSrc:s,offset:l,targetHeight:u,targetWidth:h});return{top:n+m,left:i+d,width:h*p,height:u*p,transform:"translate(0,0) scale(".concat(1/p,")")}}if("fill"!==o)return{};{let t=Math.max(r/h,e/u),i=c({containerHeight:u*t,containerWidth:h*t,hasScalableSrc:s,offset:l,targetHeight:u,targetWidth:h});return{width:r*i,height:e*i,transform:"translate(0,0) scale(".concat(1/i,")")}}})({containerHeight:m.height,containerLeft:m.left,containerTop:m.top,containerWidth:m.width,hasScalableSrc:d,objectFit:p.objectFit,objectPosition:p.objectPosition,offset:l,targetHeight:(null==o?void 0:o.naturalHeight)||m.height,targetWidth:(null==o?void 0:o.naturalWidth)||m.width}):void 0,v?(t=>{let{backgroundPosition:e,backgroundSize:i,containerHeight:n,containerLeft:r,containerTop:s,containerWidth:o,hasScalableSrc:a,offset:l,targetHeight:u,targetWidth:h}=t;if("cover"===i||"contain"===i){let t=o/h,d=n/u,m="cover"===i?Math.max(t,d):Math.min(t,d),[p="50%",g="50%"]=e.split(" "),v=f({position:p,relativeNum:o-h*m}),y=f({position:g,relativeNum:n-u*m}),x=c({containerHeight:u*m,containerWidth:h*m,hasScalableSrc:a,offset:l,targetHeight:u,targetWidth:h});return{top:s+y,left:r+v,width:h*m*x,height:u*m*x,transform:"translate(0,0) scale(".concat(1/x,")")}}if("auto"===i){let[t="50%",i="50%"]=e.split(" "),d=f({position:t,relativeNum:o-h}),m=f({position:i,relativeNum:n-u}),p=c({containerHeight:u,containerWidth:h,hasScalableSrc:a,offset:l,targetHeight:u,targetWidth:h});return{top:s+m,left:r+d,width:h*p,height:u*p,transform:"translate(0,0) scale(".concat(1/p,")")}}{let[t="50%",d="50%"]=i.split(" "),m=f({position:t,relativeNum:o}),p=Math.min(m/h,f({position:d,relativeNum:n})/u),[g="50%",v="50%"]=e.split(" "),y=f({position:g,relativeNum:o-h*p}),x=f({position:v,relativeNum:n-u*p}),w=c({containerHeight:u*p,containerWidth:h*p,hasScalableSrc:a,offset:l,targetHeight:u,targetWidth:h});return{top:s+x,left:r+y,width:h*p*w,height:u*p*w,transform:"translate(0,0) scale(".concat(1/w,")")}}})({backgroundPosition:p.backgroundPosition,backgroundSize:p.backgroundSize,containerHeight:m.height,containerLeft:m.left,containerTop:m.top,containerWidth:m.width,hasScalableSrc:d,offset:l,targetHeight:(null==o?void 0:o.naturalHeight)||m.height,targetWidth:(null==o?void 0:o.naturalWidth)||m.width}):void 0);if(s){let t=window.innerWidth/2,e=window.innerHeight/2,i=parseFloat(String(w.left||0))+parseFloat(String(w.width||0))/2,n=parseFloat(String(w.top||0))+parseFloat(String(w.height||0))/2;u&&(w.transitionDuration="0.01ms"),w.transform="translate(".concat(t-i,"px,").concat(e-n,"px) scale(1)")}return w})({hasZoomImg:q,imgSrc:H,isSvg:_,isZoomed:E&&$,loadedImgEl:V,offset:P,shouldRefresh:R,targetEl:d}):{};let tt=null;if(Z){let e=U||B?r.default.createElement("img",{alt:W,crossOrigin:X,sizes:Y,src:H,srcSet:G,...L&&"LOADED"===N?A:{},"data-rmiz-modal-img":"",height:this.styleModalImg.height||void 0,id:F,ref:j,style:this.styleModalImg,width:this.styleModalImg.width||void 0}):_?r.default.createElement("div",{"data-rmiz-modal-img":!0,ref:j,style:this.styleModalImg}):null,i=r.default.createElement("button",{"aria-label":m,"data-rmiz-btn-unzoom":"",onClick:t,type:"button"},r.default.createElement(w,null));tt=T?r.default.createElement(T,{buttonUnzoom:i,modalState:N,img:e,isZoomImgLoaded:L,onUnzoom:o}):r.default.createElement(r.default.Fragment,null,e,i)}return r.default.createElement(S,{"aria-owns":O,"data-rmiz":"",ref:D},r.default.createElement(S,{"data-rmiz-content":J,ref:M,style:{visibility:"UNLOADED"===N?"visible":"hidden"}},y),Z&&r.default.createElement(S,{"data-rmiz-ghost":"",style:I},r.default.createElement("button",{"aria-label":K,"data-rmiz-btn-zoom":"",onClick:u,type:"button"},r.default.createElement(b,null))),Z&&s.default.createPortal(r.default.createElement("dialog",{"aria-labelledby":F,"aria-modal":"true",className:x,"data-rmiz-modal":"",id:O,onClick:i,onClose:n,onCancel:e,ref:k,role:"dialog"},r.default.createElement("div",{"data-rmiz-modal-overlay":Q}),r.default.createElement("div",{"data-rmiz-modal-content":"",ref:C},tt)),this.getDialogContainer()))}componentDidMount(){this.setId(),this.setAndTrackImg(),this.handleImgLoad(),this.UNSAFE_handleSvg()}componentWillUnmount(){var t,e,i,n,r,s,o,a,l,u,h,d;"UNLOADED"!==this.state.modalState&&this.bodyScrollEnable(),null==(e=this.contentChangeObserver)||null==(t=e.disconnect)||t.call(e),null==(n=this.contentNotFoundChangeObserver)||null==(i=n.disconnect)||i.call(n),null==(s=this.imgElResizeObserver)||null==(r=s.disconnect)||r.call(s),null==(a=this.imgEl)||null==(o=a.removeEventListener)||o.call(a,"load",this.handleImgLoad),null==(u=this.imgEl)||null==(l=u.removeEventListener)||l.call(u,"click",this.handleZoom),null==(d=this.refModalImg.current)||null==(h=d.removeEventListener)||h.call(d,"transitionend",this.handleImgTransitionEnd),window.removeEventListener("wheel",this.handleWheel),window.removeEventListener("touchstart",this.handleTouchStart),window.removeEventListener("touchmove",this.handleTouchMove),window.removeEventListener("touchend",this.handleTouchEnd),window.removeEventListener("touchcancel",this.handleTouchCancel),window.removeEventListener("resize",this.handleResize),document.removeEventListener("keydown",this.handleKeyDown,!0)}componentDidUpdate(t,e){this.handleModalStateChange(e.modalState),this.UNSAFE_handleSvg(),this.handleIfZoomChanged(t.isZoomed)}constructor(){super(...arguments),this.state={id:"",isZoomImgLoaded:!1,loadedImgEl:void 0,modalState:"UNLOADED",shouldRefresh:!1,styleGhost:{}},this.refContent=r.default.createRef(),this.refDialog=r.default.createRef(),this.refModalContent=r.default.createRef(),this.refModalImg=r.default.createRef(),this.refWrap=r.default.createRef(),this.imgEl=null,this.isScaling=!1,this.prevBodyAttrs=x,this.styleModalImg={},this.handleModalStateChange=t=>{let{modalState:e}=this.state;if("LOADING"!==t&&"LOADING"===e)this.loadZoomImg(),window.addEventListener("resize",this.handleResize,{passive:!0}),window.addEventListener("touchstart",this.handleTouchStart,{passive:!0}),window.addEventListener("touchmove",this.handleTouchMove,{passive:!0}),window.addEventListener("touchend",this.handleTouchEnd,{passive:!0}),window.addEventListener("touchcancel",this.handleTouchCancel,{passive:!0}),document.addEventListener("keydown",this.handleKeyDown,!0);else if("LOADED"!==t&&"LOADED"===e)window.addEventListener("wheel",this.handleWheel,{passive:!0});else if("UNLOADING"!==t&&"UNLOADING"===e)this.ensureImgTransitionEnd(),window.removeEventListener("wheel",this.handleWheel),window.removeEventListener("touchstart",this.handleTouchStart),window.removeEventListener("touchmove",this.handleTouchMove),window.removeEventListener("touchend",this.handleTouchEnd),window.removeEventListener("touchcancel",this.handleTouchCancel),document.removeEventListener("keydown",this.handleKeyDown,!0);else if("UNLOADED"!==t&&"UNLOADED"===e){var i,n,r,s;this.bodyScrollEnable(),window.removeEventListener("resize",this.handleResize),null==(n=this.refModalImg.current)||null==(i=n.removeEventListener)||i.call(n,"transitionend",this.handleImgTransitionEnd),null==(s=this.refDialog.current)||null==(r=s.close)||r.call(s)}},this.getDialogContainer=()=>{let t=document.querySelector("[data-rmiz-portal]");return null==t&&((t=document.createElement("div")).setAttribute("data-rmiz-portal",""),document.body.appendChild(t)),t},this.setId=()=>{let t=()=>Math.random().toString(16).slice(-4);this.setState({id:t()+t()+t()})},this.setAndTrackImg=()=>{let t=this.refContent.current;if(t)if(this.imgEl=t.querySelector(y),this.imgEl){var e,i;null==(i=this.contentNotFoundChangeObserver)||null==(e=i.disconnect)||e.call(i),this.imgEl.addEventListener("load",this.handleImgLoad),this.imgEl.addEventListener("click",this.handleZoom),this.state.loadedImgEl||this.handleImgLoad(),this.imgElResizeObserver=new ResizeObserver(t=>{let e=t[0];(null==e?void 0:e.target)&&(this.imgEl=e.target,this.setState({styleGhost:v(this.imgEl)}))}),this.imgElResizeObserver.observe(this.imgEl),this.contentChangeObserver||(this.contentChangeObserver=new MutationObserver(()=>{this.setState({styleGhost:v(this.imgEl)})}),this.contentChangeObserver.observe(t,{attributes:!0,childList:!0,subtree:!0}))}else this.contentNotFoundChangeObserver||(this.contentNotFoundChangeObserver=new MutationObserver(this.setAndTrackImg),this.contentNotFoundChangeObserver.observe(t,{childList:!0,subtree:!0}))},this.handleIfZoomChanged=t=>{let{isZoomed:e}=this.props;!t&&e?this.zoom():t&&!e&&this.unzoom()},this.handleImgLoad=()=>{let t=p(this.imgEl);if(!t)return;let e=new Image;l(this.imgEl)&&(e.sizes=this.imgEl.sizes,e.srcset=this.imgEl.srcset,e.crossOrigin=this.imgEl.crossOrigin),e.src=t;let i=()=>{this.setState({loadedImgEl:e,styleGhost:v(this.imgEl)})};e.decode().then(i).catch(()=>{if(u(e))return void i();e.onload=i})},this.handleZoom=()=>{if(!this.props.isDisabled&&this.hasImage()){var t,e;null==(t=(e=this.props).onZoomChange)||t.call(e,!0)}},this.handleUnzoom=()=>{if(!this.props.isDisabled){var t,e;null==(t=(e=this.props).onZoomChange)||t.call(e,!1)}},this.handleBtnUnzoomClick=t=>{t.preventDefault(),t.stopPropagation(),this.handleUnzoom()},this.handleDialogCancel=t=>{t.preventDefault()},this.handleDialogClick=t=>{(t.target===this.refModalContent.current||t.target===this.refModalImg.current)&&(t.stopPropagation(),this.handleUnzoom())},this.handleDialogClose=t=>{t.stopPropagation(),this.handleUnzoom()},this.handleKeyDown=t=>{("Escape"===t.key||27===t.keyCode)&&(t.preventDefault(),t.stopPropagation(),this.handleUnzoom())},this.handleWheel=t=>{t.ctrlKey||(t.stopPropagation(),queueMicrotask(()=>{this.handleUnzoom()}))},this.handleTouchStart=t=>{if(t.touches.length>1){this.isScaling=!0;return}1===t.changedTouches.length&&t.changedTouches[0]&&(this.touchYStart=t.changedTouches[0].screenY)},this.handleTouchMove=t=>{var e,i;let n=null!=(i=null==(e=window.visualViewport)?void 0:e.scale)?i:1;this.props.canSwipeToUnzoom&&!this.isScaling&&n<=1&&null!=this.touchYStart&&t.changedTouches[0]&&(this.touchYEnd=t.changedTouches[0].screenY,Math.abs(Math.max(this.touchYStart,this.touchYEnd)-Math.min(this.touchYStart,this.touchYEnd))>this.props.swipeToUnzoomThreshold&&(this.touchYStart=void 0,this.touchYEnd=void 0,this.handleUnzoom()))},this.handleTouchEnd=()=>{this.isScaling=!1,this.touchYStart=void 0,this.touchYEnd=void 0},this.handleTouchCancel=()=>{this.isScaling=!1,this.touchYStart=void 0,this.touchYEnd=void 0},this.handleResize=()=>{this.setState({shouldRefresh:!0})},this.hasImage=()=>this.imgEl&&(this.state.loadedImgEl||h(this.imgEl))&&"none"!==window.getComputedStyle(this.imgEl).display,this.zoom=()=>{var t,e,i,n;this.bodyScrollDisable(),null==(e=this.refDialog.current)||null==(t=e.showModal)||t.call(e),null==(n=this.refModalImg.current)||null==(i=n.addEventListener)||i.call(n,"transitionend",this.handleImgTransitionEnd),this.setState({modalState:"LOADING"})},this.unzoom=()=>{this.setState({modalState:"UNLOADING"})},this.handleImgTransitionEnd=()=>{clearTimeout(this.timeoutTransitionEnd),"LOADING"===this.state.modalState?this.setState({modalState:"LOADED"}):"UNLOADING"===this.state.modalState&&this.setState({shouldRefresh:!1,modalState:"UNLOADED"})},this.ensureImgTransitionEnd=()=>{if(this.refModalImg.current){let t=window.getComputedStyle(this.refModalImg.current).transitionDuration,e=parseFloat(t);if(e){let i=e*(t.endsWith("ms")?1:1e3)+50;this.timeoutTransitionEnd=setTimeout(this.handleImgTransitionEnd,i)}}},this.bodyScrollDisable=()=>{this.prevBodyAttrs={overflow:document.body.style.overflow,width:document.body.style.width};let t=document.body.clientWidth;document.body.style.overflow="hidden",document.body.style.width="".concat(t,"px")},this.bodyScrollEnable=()=>{document.body.style.width=this.prevBodyAttrs.width,document.body.style.overflow=this.prevBodyAttrs.overflow,this.prevBodyAttrs=x},this.loadZoomImg=()=>{let{props:{zoomImg:t}}=this,e=null==t?void 0:t.src;if(e){var i,n,r;let s=new Image;s.sizes=null!=(i=null==t?void 0:t.sizes)?i:"",s.srcset=null!=(n=null==t?void 0:t.srcSet)?n:"",s.crossOrigin=null!=(r=null==t?void 0:t.crossOrigin)?r:void 0,s.src=e;let o=()=>{this.setState({isZoomImgLoaded:!0})};s.decode().then(o).catch(()=>{if(u(s))return void o();s.onload=o})}},this.UNSAFE_handleSvg=()=>{let{imgEl:t,refModalImg:e,styleModalImg:i}=this;if(h(t)){var n,r,s,o,a;let l=t.cloneNode(!0);(t=>{let e="-zoom",i=["clip-path","fill","mask","marker-start","marker-mid","marker-end"],n=new Map;if(t.hasAttribute("id")){let i=t.id,r=i+e;n.set(i,r),t.id=r}t.querySelectorAll("[id]").forEach(t=>{let i=t.id,r=i+e;n.set(i,r),t.id=r}),n.forEach((e,n)=>{let r="url(#".concat(n,")"),s="url(#".concat(e,")"),o=i.map(t=>"[".concat(t,'="').concat(r,'"]')).join(", ");t.querySelectorAll(o).forEach(t=>{i.forEach(e=>{t.getAttribute(e)===r&&t.setAttribute(e,s)})})}),t.querySelectorAll("style").forEach(t=>{n.forEach((e,i)=>{t.textContent&&(t.textContent=t.textContent.replaceAll("#".concat(i),"#".concat(e)))})})})(l),l.style.width="".concat(i.width||0,"px"),l.style.height="".concat(i.height||0,"px"),l.addEventListener("click",this.handleUnzoom),null==(s=e.current)||null==(r=s.firstChild)||null==(n=r.remove)||n.call(r),null==(a=e.current)||null==(o=a.appendChild)||o.call(a,l)}}}}function E(t){let[e,i]=r.default.useState(!1);return r.default.createElement(w,{...t,isZoomed:e,onZoomChange:i})}b.defaultProps={a11yNameButtonUnzoom:"Minimize image",a11yNameButtonZoom:"Expand image",canSwipeToUnzoom:!0,IconUnzoom:function(){return r.default.createElement("svg",{"aria-hidden":"true","data-rmiz-btn-unzoom-icon":!0,fill:"currentColor",focusable:"false",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},r.default.createElement("path",{d:"M 14.144531 1.148438 L 9 6.292969 L 9 3 L 8 3 L 8 8 L 13 8 L 13 7 L 9.707031 7 L 14.855469 1.851563 Z M 8 8 L 3 8 L 3 9 L 6.292969 9 L 1.148438 14.144531 L 1.851563 14.855469 L 7 9.707031 L 7 13 L 8 13 Z"}))},IconZoom:function(){return r.default.createElement("svg",{"aria-hidden":"true","data-rmiz-btn-zoom-icon":!0,fill:"currentColor",focusable:"false",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},r.default.createElement("path",{d:"M 9 1 L 9 2 L 12.292969 2 L 2 12.292969 L 2 9 L 1 9 L 1 14 L 6 14 L 6 13 L 2.707031 13 L 13 2.707031 L 13 6 L 14 6 L 14 1 Z"}))},isDisabled:!1,swipeToUnzoomThreshold:10,wrapElement:"div",zoomMargin:0};var S=t.i(47163);let T=t=>{let{className:i,backdropClassName:n,...r}=t;return(0,e.jsx)("div",{className:(0,S.cn)("relative","[&_[data-rmiz-ghost]]:pointer-events-none [&_[data-rmiz-ghost]]:absolute","[&_[data-rmiz-btn-zoom]]:m-0 [&_[data-rmiz-btn-zoom]]:size-10 [&_[data-rmiz-btn-zoom]]:touch-manipulation [&_[data-rmiz-btn-zoom]]:appearance-none [&_[data-rmiz-btn-zoom]]:rounded-[50%] [&_[data-rmiz-btn-zoom]]:border-none [&_[data-rmiz-btn-zoom]]:bg-foreground/70 [&_[data-rmiz-btn-zoom]]:p-2 [&_[data-rmiz-btn-zoom]]:text-background [&_[data-rmiz-btn-zoom]]:outline-offset-2","[&_[data-rmiz-btn-unzoom]]:m-0 [&_[data-rmiz-btn-unzoom]]:size-10 [&_[data-rmiz-btn-unzoom]]:touch-manipulation [&_[data-rmiz-btn-unzoom]]:appearance-none [&_[data-rmiz-btn-unzoom]]:rounded-[50%] [&_[data-rmiz-btn-unzoom]]:border-none [&_[data-rmiz-btn-unzoom]]:bg-foreground/70 [&_[data-rmiz-btn-unzoom]]:p-2 [&_[data-rmiz-btn-unzoom]]:text-background [&_[data-rmiz-btn-unzoom]]:outline-offset-2","[&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:pointer-events-none [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:absolute [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:size-px [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:overflow-hidden [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:whitespace-nowrap [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:[clip-path:inset(50%)] [&_[data-rmiz-btn-zoom]:not(:focus):not(:active)]:[clip:rect(0_0_0_0)]","[&_[data-rmiz-btn-zoom]]:absolute [&_[data-rmiz-btn-zoom]]:top-2.5 [&_[data-rmiz-btn-zoom]]:right-2.5 [&_[data-rmiz-btn-zoom]]:bottom-auto [&_[data-rmiz-btn-zoom]]:left-auto [&_[data-rmiz-btn-zoom]]:cursor-zoom-in","[&_[data-rmiz-btn-unzoom]]:absolute [&_[data-rmiz-btn-unzoom]]:top-5 [&_[data-rmiz-btn-unzoom]]:right-5 [&_[data-rmiz-btn-unzoom]]:bottom-auto [&_[data-rmiz-btn-unzoom]]:left-auto [&_[data-rmiz-btn-unzoom]]:z-[1] [&_[data-rmiz-btn-unzoom]]:cursor-zoom-out",'[&_[data-rmiz-content="found"]_img]:cursor-zoom-in','[&_[data-rmiz-content="found"]_svg]:cursor-zoom-in','[&_[data-rmiz-content="found"]_[role="img"]]:cursor-zoom-in','[&_[data-rmiz-content="found"]_[data-zoom]]:cursor-zoom-in',i),children:(0,e.jsx)(E,{classDialog:(0,S.cn)("[&::backdrop]:hidden","[&[open]]:fixed [&[open]]:m-0 [&[open]]:h-dvh [&[open]]:max-h-none [&[open]]:w-dvw [&[open]]:max-w-none [&[open]]:overflow-hidden [&[open]]:border-0 [&[open]]:bg-transparent [&[open]]:p-0","[&_[data-rmiz-modal-overlay]]:absolute [&_[data-rmiz-modal-overlay]]:inset-0 [&_[data-rmiz-modal-overlay]]:transition-all",'[&_[data-rmiz-modal-overlay="hidden"]]:bg-transparent','[&_[data-rmiz-modal-overlay="visible"]]:bg-background/80 [&_[data-rmiz-modal-overlay="visible"]]:backdrop-blur-md',"[&_[data-rmiz-modal-content]]:relative [&_[data-rmiz-modal-content]]:size-full","[&_[data-rmiz-modal-img]]:absolute [&_[data-rmiz-modal-img]]:origin-top-left [&_[data-rmiz-modal-img]]:cursor-zoom-out [&_[data-rmiz-modal-img]]:transition-transform","motion-reduce:[&_[data-rmiz-modal-img]]:transition-none motion-reduce:[&_[data-rmiz-modal-overlay]]:transition-none",n),...r})})};function A(){let[t,r]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let t=()=>{r(window.innerWidth>=1024)};return t(),window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[]),(0,e.jsx)("section",{className:"mb-40",children:(0,e.jsx)("div",{className:"mx-auto max-w-6xl px-0 md:px-10 2xl:max-w-7xl",children:(0,e.jsxs)("div",{className:"flex flex-col items-center gap-[80px] font-aeonik",children:[(0,e.jsxs)("div",{className:"flex w-full flex-col items-start gap-[60px]",children:[(0,e.jsxs)("div",{className:"flex w-full flex-col items-start gap-5",children:[(0,e.jsx)("div",{className:"font-medium text-brand-500 text-xl",children:"Desktop Intelligence"}),(0,e.jsx)("h2",{className:"max-w-[320px] font-aeonik font-medium text-3xl text-white/80 leading-none tracking-[-1px] sm:max-w-2xl md:max-w-none md:text-[42px]",children:"Highlight understands what you see and hear."})]}),(0,e.jsx)("a",{className:"group relative flex aspect-square w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030] md:aspect-auto",href:"/chat",children:(0,e.jsx)(i.default,{alt:"Intelligence Hero",className:"h-full w-full rounded-[20px] object-cover transition-transform duration-300 group-hover:scale-105 md:h-auto md:object-contain",height:"622",src:"/desktop.webp",width:"1200"})})]}),(0,e.jsxs)("div",{className:"grid w-full grid-cols-1 items-start gap-16 lg:grid-cols-2",children:[(0,e.jsxs)("div",{className:"flex flex-1 flex-col gap-[50px]",children:[(0,e.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,e.jsx)("h3",{className:"font-aeonik font-medium text-2xl text-white leading-none tracking-[-1.2px] md:text-[30px]",children:"Record Meetings"}),(0,e.jsx)("p",{className:"mx-auto mb-2 max-w-3xl font-aeonik text-lg text-muted-foreground sm:text-xl",children:(0,e.jsx)("span",{children:"Record meetings, lessons, interviews, and more. Instant transcripts & summaries."})}),(0,e.jsxs)("a",{className:"flex items-center gap-2 font-medium text-[16px] text-brand-500 transition-colors hover:text-brand-500/80",href:"/meetings",children:["Learn More",(0,e.jsxs)("svg",{className:"lucide lucide-chevron-right h-4 w-4",fill:"none",height:"24",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg",children:[(0,e.jsx)("title",{children:"Right Arrow"}),(0,e.jsx)("path",{d:"m9 18 6-6-6-6"})]})]})]}),(0,e.jsxs)("div",{className:"flex flex-col gap-2 md:gap-[25px]",children:[(0,e.jsx)("div",{className:"group relative flex h-[450px] w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030]",children:(0,e.jsx)(T,{backdropClassName:(0,S.cn)('[&_[data-rmiz-modal-overlay="visible"]]:bg-brand-100/80'),className:"relative aspect-square h-full w-full overflow-hidden rounded-lg",zoomMargin:100*!!t,children:(0,e.jsx)(i.default,{alt:"Recording interface",className:"h-full w-full object-cover transition-transform duration-300 group-hover:scale-105",height:"450",loading:"lazy",sizes:"100vw",src:"/desktop.webp",style:{position:"absolute",height:"100%",width:"100%",inset:"0px",color:"transparent"},width:"450"})})}),(0,e.jsx)("div",{className:"flex flex-col gap-3 px-3 py-2 md:flex-row md:items-center md:justify-between md:gap-0",children:(0,e.jsx)("span",{className:"font-aeonik font-medium text-base text-white/50 tracking-[-0.4px] md:text-[20px]",children:"Works with all your meetings platforms."})})]})]}),(0,e.jsxs)("div",{className:"flex flex-1 flex-col gap-[50px]",children:[(0,e.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,e.jsx)("h3",{className:"font-aeonik font-medium text-2xl text-white leading-none tracking-[-1.2px] md:text-[30px]",children:"Record Meetings"}),(0,e.jsx)("p",{className:"mx-auto mb-2 max-w-3xl font-aeonik text-lg text-muted-foreground sm:text-xl",children:(0,e.jsx)("span",{children:"Record meetings, lessons, interviews, and more. Instant transcripts & summaries."})}),(0,e.jsxs)("a",{className:"flex items-center gap-2 font-medium text-[16px] text-brand-500 transition-colors hover:text-brand-500/80",href:"/meetings",children:["Learn More",(0,e.jsxs)("svg",{className:"lucide lucide-chevron-right h-4 w-4",fill:"none",height:"24",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",width:"24",xmlns:"http://www.w3.org/2000/svg",children:[(0,e.jsx)("title",{children:"Right Arrow"}),(0,e.jsx)("path",{d:"m9 18 6-6-6-6"})]})]})]}),(0,e.jsxs)("div",{className:"flex flex-col gap-2 md:gap-[25px]",children:[(0,e.jsx)("div",{className:"group relative flex h-[450px] w-full cursor-pointer items-center justify-center overflow-hidden rounded-[20px] bg-[#303030]",children:(0,e.jsx)(T,{backdropClassName:(0,S.cn)('[&_[data-rmiz-modal-overlay="visible"]]:bg-brand-100/80'),className:"relative aspect-square h-full w-full overflow-hidden rounded-lg",zoomMargin:100*!!t,children:(0,e.jsx)(i.default,{alt:"Recording interface",className:"h-full w-full object-cover transition-transform duration-300 group-hover:scale-105",height:"450",loading:"lazy",sizes:"100vw",src:"/desktop.webp",style:{position:"absolute",height:"100%",width:"100%",inset:"0px",color:"transparent"},width:"450"})})}),(0,e.jsx)("div",{className:"flex flex-col gap-3 px-3 py-2 md:flex-row md:items-center md:justify-between md:gap-0",children:(0,e.jsx)("span",{className:"font-aeonik font-medium text-base text-white/50 tracking-[-0.4px] md:text-[20px]",children:"Works with all your meetings platforms."})})]})]})]})]})})})}},33639,67327,58616,48367,37307,76650,47113,3083,10337,5331,33160,20085,25578,10867,13219,78072,55968,17161,27920,35088,39191,90776,42229,97834,48499,6831,t=>{"use strict";let e;function i(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}t.s(["addUniqueItem",()=>i,"removeItem",()=>n],33639),t.s(["spring",()=>y],37307),t.s(["millisecondsToSeconds",()=>s,"secondsToMilliseconds",()=>r],67327);let r=t=>1e3*t,s=t=>t/1e3,o=(t,e,i)=>i>e?e:i<t?t:i,a=function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return"linear(".concat(n.substring(0,n.length-2),")")};function l(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function u(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,i=arguments.length>2?arguments[2]:void 0,n=i({...t,keyframes:[0,e]}),r=Math.min(l(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:s(r)}}function h(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}t.s(["createGeneratorEasing",()=>u],58616);let d={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};t.s(["invariant",()=>m,"warning",()=>c],48367),t.i(16416);let c=()=>{},m=()=>{};function p(t,e){return t*Math.sqrt(1-e*e)}let f=["duration","bounce"],g=["stiffness","damping","mass"];function v(t,e){return e.some(e=>void 0!==t[e])}function y(){let t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:d.visualDuration,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:d.bounce,n="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:i}:e,{restSpeed:u,restDelta:m}=n,y=n.keyframes[0],x=n.keyframes[n.keyframes.length-1],w={done:!1,value:y},{stiffness:b,damping:E,mass:S,duration:T,velocity:A,isResolvedFromDuration:P}=function(t){let e={velocity:d.velocity,stiffness:d.stiffness,damping:d.damping,mass:d.mass,isResolvedFromDuration:!1,...t};if(!v(t,g)&&v(t,f))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*o(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:d.mass,stiffness:n,damping:r}}else{let i=function(t){let e,i,{duration:n=d.duration,bounce:a=d.bounce,velocity:l=d.velocity,mass:u=d.mass}=t;c(n<=r(d.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let h=1-a;h=o(d.minDamping,d.maxDamping,h),n=o(d.minDuration,d.maxDuration,s(n)),h<1?(e=t=>{let e=t*h,i=e*n;return .001-(e-l)/p(t,h)*Math.exp(-i)},i=t=>{let i=t*h*n,r=Math.pow(h,2)*Math.pow(t,2)*n,s=Math.exp(-i),o=p(Math.pow(t,2),h);return(i*l+l-r)*s*(-e(t)+.001>0?-1:1)/o}):(e=t=>-.001+Math.exp(-t*n)*((t-l)*n+1),i=t=>n*n*(l-t)*Math.exp(-t*n));let m=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(e,i,5/n);if(n=r(n),isNaN(m))return{stiffness:d.stiffness,damping:d.damping,duration:n};{let t=Math.pow(m,2)*u;return{stiffness:t,damping:2*h*Math.sqrt(u*t),duration:n}}}(t);(e={...e,...i,mass:d.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-s(n.velocity||0)}),M=A||0,k=E/(2*Math.sqrt(b*S)),C=x-y,j=s(Math.sqrt(b/S)),D=5>Math.abs(C);if(u||(u=D?d.restSpeed.granular:d.restSpeed.default),m||(m=D?d.restDelta.granular:d.restDelta.default),k<1){let e=p(j,k);t=t=>x-Math.exp(-k*j*t)*((M+k*j*C)/e*Math.sin(e*t)+C*Math.cos(e*t))}else if(1===k)t=t=>x-Math.exp(-j*t)*(C+(M+j*C)*t);else{let e=j*Math.sqrt(k*k-1);t=t=>{let i=Math.exp(-k*j*t),n=Math.min(e*t,300);return x-i*((M+k*j*C)*Math.sinh(n)+e*C*Math.cosh(n))/e}}let z={calculatedDuration:P&&T||null,next:e=>{let i=t(e);if(P)w.done=e>=T;else{let n=0===e?M:0;k<1&&(n=0===e?r(M):h(t,e,i));let s=Math.abs(x-i)<=m;w.done=Math.abs(n)<=u&&s}return w.value=w.done?x:i,w},toString:()=>{let t=Math.min(l(z),2e4),e=a(e=>z.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return z}y.applyToOptions=t=>{let e=u(t,100,y);return t.ease=e.ease,t.duration=r(e.duration),t.type="keyframes",t},t.s(["isMotionValue",()=>x],76650);let x=t=>!!(t&&t.getVelocity);t.s(["defaultOffset",()=>S],5331),t.s(["fillOffset",()=>E],10337),t.s(["progress",()=>w],47113);let w=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};t.s(["mixNumber",()=>b],3083);let b=(t,e,i)=>t+(e-t)*i;function E(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=w(0,e,n);t.push(b(i,1,r))}}function S(t){let e=[0];return E(e,t.length-1),e}function T(t){return"function"==typeof t&&"applyToOptions"in t}t.s(["isGenerator",()=>T],33160),t.s(["isEasingArray",()=>A],20085);let A=t=>Array.isArray(t)&&"number"!=typeof t[0];function P(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){var n;let r=document;e&&(r=e.current);let s=null!=(n=null==i?void 0:i[t])?n:r.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}t.s(["resolveElements",()=>P],25578),t.s(["visualElementStore",()=>M],10867);let M=new WeakMap;function k(t,e){var i,n;return null!=(n=null!=(i=null==t?void 0:t[e])?i:null==t?void 0:t.default)?n:t}t.s(["animateTarget",()=>eQ],78072);let C=t=>t,j={},D=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],z={value:null,addProjectionMetrics:null};function L(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=D.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&z.value&&z.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:h,update:d,preRender:c,render:m,postRender:p}=o,f=()=>{let s=j.useManualTiming?r.timestamp:performance.now();i=!1,j.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),h.process(r),d.process(r),c.process(r),m.process(r),p.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(f))};return{schedule:D.reduce((e,s)=>{let a=o[s];return e[s]=function(e){let s=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return!i&&(i=!0,n=!0,r.isProcessing||t(f)),a.schedule(e,s,o)},e},{}),cancel:t=>{for(let e=0;e<D.length;e++)o[D[e]].cancel(t)},state:r,steps:o}}let{schedule:V,cancel:N,state:R,steps:I}=L("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:C,!0),O=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],F=new Set(O),B=new Set(["width","height","top","left","right","bottom",...O]);t.s(["motionValue",()=>G],13219);class U{add(t){return i(this.subscriptions,t),()=>n(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}constructor(){this.subscriptions=[]}}function _(){e=void 0}let W={now:()=>(void 0===e&&W.set(R.isProcessing||j.useManualTiming?R.timestamp:performance.now()),e),set:t=>{e=t,queueMicrotask(_)}},H={current:void 0};class Y{setCurrent(t){this.current=t,this.updatedAt=W.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.current;this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new U);let i=this.events[t].add(e);return"change"===t?()=>{i(),V.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var t;null==(t=this.events.change)||t.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return H.current&&H.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=W.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var t,e;null==(t=this.dependents)||t.clear(),null==(e=this.events.destroy)||e.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{let e=W.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev){var i;if(null==(i=this.events.change)||i.notify(this.current),this.dependents)for(let t of this.dependents)t.dirty()}},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}}function G(t,e){return new Y(t,e)}let X=t=>Array.isArray(t);function q(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function Z(t,e,i,n){if("function"==typeof e){let[r,s]=q(n);e=e(void 0!==i?i:t.custom,r,s)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,s]=q(n);e=e(void 0!==i?i:t.custom,r,s)}return e}function K(t,e,i){let n=t.getProps();return Z(n,e,void 0!==i?i:n.custom,t)}function $(t,e){let i=t.getValue("willChange");if(x(i)&&i.add)return i.add(e);if(!i&&j.WillChange){let i=new j.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let J=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Q="data-"+J("framerAppearId");function tt(t){t.duration=0,t.type}let te=(t,e)=>i=>e(t(i)),ti=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.reduce(te)},tn={layout:0,mainThread:0,waapi:0},tr=t=>e=>"string"==typeof e&&e.startsWith(t),ts=tr("--"),to=tr("var(--"),ta=t=>!!to(t)&&tl.test(t.split("/*")[0].trim()),tl=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,tu={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},th={...tu,transform:t=>o(0,1,t)},td={...tu,default:1},tc=t=>Math.round(1e5*t)/1e5,tm=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tp=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tf=(t,e)=>i=>!!("string"==typeof i&&tp.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tg=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(tm);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tv={...tu,transform:t=>Math.round(o(0,255,t))},ty={test:tf("rgb","red"),parse:tg("red","green","blue"),transform:t=>{let{red:e,green:i,blue:n,alpha:r=1}=t;return"rgba("+tv.transform(e)+", "+tv.transform(i)+", "+tv.transform(n)+", "+tc(th.transform(r))+")"}},tx={test:tf("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:ty.transform},tw=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>"".concat(e).concat(t)}),tb=tw("deg"),tE=tw("%"),tS=tw("px"),tT=tw("vh"),tA=tw("vw"),tP={...tE,parse:t=>tE.parse(t)/100,transform:t=>tE.transform(100*t)},tM={test:tf("hsl","hue"),parse:tg("hue","saturation","lightness"),transform:t=>{let{hue:e,saturation:i,lightness:n,alpha:r=1}=t;return"hsla("+Math.round(e)+", "+tE.transform(tc(i))+", "+tE.transform(tc(n))+", "+tc(th.transform(r))+")"}},tk={test:t=>ty.test(t)||tx.test(t)||tM.test(t),parse:t=>ty.test(t)?ty.parse(t):tM.test(t)?tM.parse(t):tx.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ty.transform(t):tM.transform(t),getAnimatableNone:t=>{let e=tk.parse(t);return e.alpha=0,tk.transform(e)}},tC=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tj="number",tD="color",tz=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tL(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tz,t=>(tk.test(t)?(n.color.push(s),r.push(tD),i.push(tk.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(tj),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tV(t){return tL(t).values}function tN(t){let{split:e,types:i}=tL(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===tj?r+=tc(t[s]):e===tD?r+=tk.transform(t[s]):r+=t[s]}return r}}let tR=t=>"number"==typeof t?0:tk.test(t)?tk.getAnimatableNone(t):t,tI={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(tm))?void 0:e.length)||0)+((null==(i=t.match(tC))?void 0:i.length)||0)>0},parse:tV,createTransformer:tN,getAnimatableNone:function(t){let e=tV(t);return tN(t)(e.map(tR))}};function tO(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tF(t,e){return i=>i>0?e:t}let tB=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tU=[tx,ty,tM];function t_(t){let e=tU.find(e=>e.test(t));if(c(!!e,"'".concat(t,"' is not an animatable color. Use the equivalent color code instead."),"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===tM&&(i=function(t){let{hue:e,saturation:i,lightness:n,alpha:r}=t;e/=360,n/=100;let s=0,o=0,a=0;if(i/=100){let t=n<.5?n*(1+i):n+i-n*i,r=2*n-t;s=tO(r,t,e+1/3),o=tO(r,t,e),a=tO(r,t,e-1/3)}else s=o=a=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*a),alpha:r}}(i)),i}let tW=(t,e)=>{let i=t_(t),n=t_(e);if(!i||!n)return tF(t,e);let r={...i};return t=>(r.red=tB(i.red,n.red,t),r.green=tB(i.green,n.green,t),r.blue=tB(i.blue,n.blue,t),r.alpha=b(i.alpha,n.alpha,t),ty.transform(r))},tH=new Set(["none","hidden"]);function tY(t,e){return i=>b(t,e,i)}function tG(t){return"number"==typeof t?tY:"string"==typeof t?ta(t)?tF:tk.test(t)?tW:tZ:Array.isArray(t)?tX:"object"==typeof t?tk.test(t)?tW:tq:tF}function tX(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tG(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tq(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tG(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tZ=(t,e)=>{let i=tI.createTransformer(e),n=tL(t),r=tL(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tH.has(t)&&!r.values.length||tH.has(e)&&!n.values.length?function(t,e){return tH.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):ti(tX(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){var r;let o=e.types[s],a=t.indexes[o][n[o]],l=null!=(r=t.values[a])?r:0;i[s]=l,n[o]++}return i}(n,r),r.values),i):(c(!0,"Complex values '".concat(t,"' and '").concat(e,"' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition."),"complex-values-different"),tF(t,e))};function tK(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?b(t,e,i):tG(t)(t,e)}let t$=t=>{let e=e=>{let{timestamp:i}=e;return t(i)};return{start:function(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return V.update(e,t)},stop:()=>N(e),now:()=>R.isProcessing?R.timestamp:W.now()}};function tJ(t){let e,i,{keyframes:n,velocity:r=0,power:s=.8,timeConstant:o=325,bounceDamping:a=10,bounceStiffness:l=500,modifyTarget:u,min:d,max:c,restDelta:m=.5,restSpeed:p}=t,f=n[0],g={done:!1,value:f},v=s*r,x=f+v,w=void 0===u?x:u(x);w!==x&&(v=w-f);let b=t=>-v*Math.exp(-t/o),E=t=>w+b(t),S=t=>{let e=b(t),i=E(t);g.done=Math.abs(e)<=m,g.value=g.done?w:i},T=t=>{let n;if(n=g.value,void 0!==d&&n<d||void 0!==c&&n>c){var r;e=t,i=y({keyframes:[g.value,(r=g.value,void 0===d?c:void 0===c||Math.abs(d-r)<Math.abs(c-r)?d:c)],velocity:h(E,t,g.value),damping:a,stiffness:l,restDelta:m,restSpeed:p})}};return T(0),{calculatedDuration:null,next:t=>{let n=!1;return(i||void 0!==e||(n=!0,S(t),T(t)),void 0!==e&&t>=e)?i.next(t-e):(n||S(t),g)}}}let tQ=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function t0(t,e,i,n){return t===e&&i===n?C:r=>0===r||1===r?r:tQ(function(t,e,i,n,r){let s,o,a=0;do(s=tQ(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12)return o}(r,0,1,t,i),e,n)}let t1=t0(.42,0,1,1),t2=t0(0,0,.58,1),t3=t0(.42,0,.58,1),t5=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t4=t=>e=>1-t(1-e),t6=t0(.33,1.53,.69,.99),t9=t4(t6),t8=t5(t9),t7=t=>(t*=2)<1?.5*t9(t):.5*(2-Math.pow(2,-10*(t-1))),et=t=>1-Math.sin(Math.acos(t)),ee=t4(et),ei=t5(et),en=t=>Array.isArray(t)&&"number"==typeof t[0],er={linear:C,easeIn:t1,easeInOut:t3,easeOut:t2,circIn:et,circInOut:ei,circOut:ee,backIn:t9,backInOut:t8,backOut:t6,anticipate:t7},es=t=>{if(en(t)){m(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,n,r]=t;return t0(e,i,n,r)}return"string"==typeof t?(m(void 0!==er[t],"Invalid easing type '".concat(t,"'"),"invalid-easing-type"),er[t]):t};function eo(t){var e;let{duration:i=300,keyframes:n,times:r,ease:s="easeInOut"}=t,a=A(s)?s.map(es):es(s),l={done:!1,value:n[0]},u=function(t,e){let{clamp:i=!0,ease:n,mixer:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=t.length;if(m(s===e.length,"Both input and output ranges must be the same length","range-length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let l=function(t,e,i){let n=[],r=i||j.mix||tK,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=ti(Array.isArray(e)?e[i]||C:e,s)),n.push(s)}return n}(e,n,r),u=l.length,h=i=>{if(a&&i<t[0])return e[0];let n=0;if(u>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=w(t[n],t[n+1],i);return l[n](r)};return i?e=>h(o(t[0],t[s-1],e)):h}((e=r&&r.length===n.length?r:S(n),e.map(t=>t*i)),n,{ease:Array.isArray(a)?a:n.map(()=>a||t3).splice(0,n.length-1)});return{calculatedDuration:i,next:t=>(l.value=u(t),l.done=t>=i,l)}}let ea=t=>null!==t;function el(t,e,i){let{repeat:n,repeatType:r="loop"}=e,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,o=t.filter(ea),a=s<0||n&&"loop"!==r&&n%2==1?0:o.length-1;return a&&void 0!==i?i:o[a]}let eu={decay:tJ,inertia:tJ,tween:eo,keyframes:eo,spring:y};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ed{get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}constructor(){this.updateFinished()}}let ec=t=>t/100;class em extends ed{initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=ti(ec,tK(o[0],o[1])),o=[0,100]);let u=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===u.calculatedDuration&&(u.calculatedDuration=l(u));let{calculatedDuration:h}=u;this.calculatedDuration=h,this.resolvedDuration=h+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=u}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);let{delay:u=0,keyframes:h,repeat:d,repeatType:c,repeatDelay:m,type:p,onUpdate:f,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let v=this.currentTime-u*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?v<0:v>n;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let x=this.currentTime,w=i;if(d){let t=Math.min(this.currentTime,n)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===c?(i=1-i,m&&(i-=m/a)):"mirror"===c&&(w=s)),x=o(0,1,i)*a}let b=y?{done:!1,value:h[0]}:w.next(x);r&&(b.value=r(b.value));let{done:E}=b;y||null===l||(E=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let S=null===this.holdTime&&("finished"===this.state||"running"===this.state&&E);return S&&p!==tJ&&(b.value=el(h,this.options,g,this.speed)),f&&f(b.value),S&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return s(this.calculatedDuration)}get time(){return s(this.currentTime)}set time(t){var e;t=r(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),null==(e=this.driver)||e.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(W.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=s(this.currentTime))}play(){var t,e;if(this.isStopped)return;let{driver:i=t$,startTime:n}=this.options;this.driver||(this.driver=i(t=>this.tick(t))),null==(t=(e=this.options).onPlay)||t.call(e);let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=null!=n?n:r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(W.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){var t,e;this.notifyFinished(),this.teardown(),this.state="finished",null==(t=(e=this.options).onComplete)||t.call(e)}cancel(){var t,e;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),null==(t=(e=this.options).onCancel)||t.call(e)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,tn.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){var e;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),null==(e=this.driver)||e.stop(),t.observe(this)}constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var t,e;let{motionValue:i}=this.options;i&&i.updatedAt!==W.now()&&this.tick(W.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),null==(t=(e=this.options).onStop)||t.call(e))},tn.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}}let ep=t=>180*t/Math.PI,ef=t=>ev(ep(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ef,rotateZ:ef,skewX:t=>ep(Math.atan(t[1])),skewY:t=>ep(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ev=t=>((t%=360)<0&&(t+=360),t),ey=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ex=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ew={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ey,scaleY:ex,scale:t=>(ey(t)+ex(t))/2,rotateX:t=>ev(ep(Math.atan2(t[6],t[5]))),rotateY:t=>ev(ep(Math.atan2(-t[2],t[0]))),rotateZ:ef,rotate:ef,skewX:t=>ep(Math.atan(t[4])),skewY:t=>ep(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function eb(t){return+!!t.includes("scale")}function eE(t,e){let i,n;if(!t||"none"===t)return eb(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=ew,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eg,n=e}if(!n)return eb(e);let s=i[e],o=n[1].split(",").map(eS);return"function"==typeof s?s(o):o[s]}function eS(t){return parseFloat(t.trim())}let eT=t=>t===tu||t===tS,eA=new Set(["x","y","z"]),eP=O.filter(t=>!eA.has(t)),eM={width:(t,e)=>{let{x:i}=t,{paddingLeft:n="0",paddingRight:r="0"}=e;return i.max-i.min-parseFloat(n)-parseFloat(r)},height:(t,e)=>{let{y:i}=t,{paddingTop:n="0",paddingBottom:r="0"}=e;return i.max-i.min-parseFloat(n)-parseFloat(r)},top:(t,e)=>{let{top:i}=e;return parseFloat(i)},left:(t,e)=>{let{left:i}=e;return parseFloat(i)},bottom:(t,e)=>{let{y:i}=t,{top:n}=e;return parseFloat(n)+(i.max-i.min)},right:(t,e)=>{let{x:i}=t,{left:n}=e;return parseFloat(n)+(i.max-i.min)},x:(t,e)=>{let{transform:i}=e;return eE(i,"x")},y:(t,e)=>{let{transform:i}=e;return eE(i,"y")}};eM.translateX=eM.x,eM.translateY=eM.y;let ek=new Set,eC=!1,ej=!1,eD=!1;function ez(){if(ej){let t=Array.from(ek).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eP.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(e=>{var i;let[n,r]=e;null==(i=t.getValue(n))||i.set(r)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ej=!1,eC=!1,ek.forEach(t=>t.complete(eD)),ek.clear()}function eL(){ek.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ej=!0)})}class eV{scheduleResolve(){this.state="scheduled",this.isAsync?(ek.add(this),eC||(eC=!0,V.read(eL),V.resolveKeyframes(ez))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=null==n?void 0:n.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)null!=t[e]||(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),ek.delete(this)}cancel(){"scheduled"===this.state&&(ek.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}}function eN(t){let e;return()=>(void 0===e&&(e=t()),e)}let eR=eN(()=>void 0!==window.ScrollTimeline),eI={},eO=function(t,e){let i=eN(t);return()=>{var t;return null!=(t=eI[e])?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eF=t=>{let[e,i,n,r]=t;return"cubic-bezier(".concat(e,", ").concat(i,", ").concat(n,", ").concat(r,")")},eB={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eF([0,.65,.55,1]),circOut:eF([.55,0,1,.45]),backIn:eF([.31,.01,.66,-.59]),backOut:eF([.33,1.53,.69,.99])};class eU extends ed{play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){var t,e;null==(t=(e=this.animation).finish)||t.call(e)}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){if(!this.isPseudoElement){var t,e;null==(t=(e=this.animation).commitStyles)||t.call(e)}}get duration(){var t,e;return s(Number((null==(e=this.animation.effect)||null==(t=e.getComputedTiming)?void 0:t.call(e).duration)||0))}get time(){return s(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=r(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline(t){let{timeline:e,observe:i}=t;if(this.allowFlatten){var n;null==(n=this.animation.effect)||n.updateTiming({easing:"linear"})}return(this.animation.onfinish=null,e&&eR())?(this.animation.timeline=e,C):i(this)}constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:l}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,m("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let u=function(t){let{type:e,...i}=t;return T(e)&&eO()?e.applyToOptions(i):(null!=i.duration||(i.duration=300),null!=i.ease||(i.ease="easeOut"),i)}(t);this.animation=function(t,e,i){let{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:l="easeOut",times:u}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},h=arguments.length>4&&void 0!==arguments[4]?arguments[4]:void 0,d={[e]:i};u&&(d.offset=u);let c=function t(e,i){if(e)return"function"==typeof e?eO()?a(e,i):"ease-out":en(e)?eF(e):Array.isArray(e)?e.map(e=>t(e,i)||eB.easeOut):eB[e]}(l,r);Array.isArray(c)&&(d.easing=c),z.value&&tn.waapi++;let m={delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};h&&(m.pseudoElement=h);let p=t.animate(d,m);return z.value&&p.finished.finally(()=>{tn.waapi--}),p}(e,i,n,u,r),!1===u.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e.startsWith("--")?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}null==l||l(),this.notifyFinished()}}}let e_={anticipate:t7,backInOut:t8,circInOut:ei};class eW extends eU{updateMotionValue(t){var e;let{motionValue:i,onUpdate:n,onComplete:s,element:o,...a}=this.options;if(!i)return;if(void 0!==t)return void i.set(t);let l=new em({...a,autoplay:!1}),u=r(null!=(e=this.finishedTime)?e:this.time);i.setWithVelocity(l.sample(u-10).value,l.sample(u).value,10),l.stop()}constructor(t){!function(t){"string"==typeof t.ease&&t.ease in e_&&(t.ease=e_[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}}let eH=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tI.test(t)||"0"===t)&&!t.startsWith("url(")),eY=new Set(["opacity","clipPath","filter","transform"]),eG=eN(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eX extends ed{onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=W.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eH(r,e),a=eH(s,e);return c(o===a,"You are trying to animate ".concat(e,' from "').concat(r,'" to "').concat(s,'". "').concat(o?s:r,'" is not an animatable value.'),"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||T(i))&&n)}(t,r,s,o)&&((j.instantAnimations||!a)&&(null==u||u(el(t,i,e))),t[0]=t[t.length-1],tt(i),i.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){var e;let{motionValue:i,name:n,repeatDelay:r,repeatType:s,damping:o,type:a}=t;if(!((null==i||null==(e=i.owner)?void 0:e.current)instanceof HTMLElement))return!1;let{onUpdate:l,transformTemplate:u}=i.owner.getProps();return eG()&&n&&eY.has(n)&&("transform"!==n||!u)&&!l&&!r&&"mirror"!==s&&0!==o&&"inertia"!==a}(h)?new eW({...h,element:h.motionValue.owner.current}):new em(h);d.finished.then(()=>this.notifyFinished()).catch(C),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){if(!this._animation){var t;null==(t=this.keyframeResolver)||t.resume(),eD=!0,eL(),ez(),eD=!1}return this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var t;this._animation&&this.animation.cancel(),null==(t=this.keyframeResolver)||t.cancel()}constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){var d;super(),this.stop=()=>{var t,e;this._animation&&(this._animation.stop(),null==(e=this.stopTimeline)||e.call(this)),null==(t=this.keyframeResolver)||t.cancel()},this.createdAt=W.now();let c={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},m=(null==u?void 0:u.KeyframeResolver)||eV;this.keyframeResolver=new m(o,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,l,u),null==(d=this.keyframeResolver)||d.scheduleResolve()}}let eq=t=>null!==t,eZ={type:"spring",stiffness:500,damping:25,restSpeed:10},eK={type:"keyframes",duration:.8},e$={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},eJ=function(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0;return a=>{let l=k(n,t)||{},u=l.delay||n.delay||0,{elapsed:h=0}=n;h-=r(u);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-h,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:o?void 0:s};!function(t){let{when:e,delay:i,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:o,repeatType:a,repeatDelay:l,from:u,elapsed:h,...d}=t;return!!Object.keys(d).length}(l)&&Object.assign(d,((t,e)=>{let{keyframes:i}=e;return i.length>2?eK:F.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===i[1]?2*Math.sqrt(550):30,restSpeed:10}:eZ:e$})(t,d)),d.duration&&(d.duration=r(d.duration)),d.repeatDelay&&(d.repeatDelay=r(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let c=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(tt(d),0===d.delay&&(c=!0)),(j.instantAnimations||j.skipAnimations)&&(c=!0,tt(d),d.delay=0),d.allowFlatten=!l.type&&!l.ease,c&&!o&&void 0!==e.get()){let t=function(t,e,i){let{repeat:n,repeatType:r="loop"}=e,s=t.filter(eq),o=n&&"loop"!==r&&n%2==1?0:s.length-1;return s[o]}(d.keyframes,l);if(void 0!==t)return void V.update(()=>{d.onUpdate(t),d.onComplete()})}return l.isSync?new em(d):new eX(d)}};function eQ(t,e){let{delay:i=0,transitionOverride:n,type:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=e;n&&(s=n);let l=[],u=r&&t.animationState&&t.animationState.getState()[r];for(let e in a){var h;let n=t.getValue(e,null!=(h=t.latestValues[e])?h:null),r=a[e];if(void 0===r||u&&function(t,e){let{protectedKeys:i,needsAnimating:n}=t,r=i.hasOwnProperty(e)&&!0!==n[e];return n[e]=!1,r}(u,e))continue;let o={delay:i,...k(s||{},e)},d=n.get();if(void 0!==d&&!n.isAnimating&&!Array.isArray(r)&&r===d&&!o.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[Q];if(i){let t=window.MotionHandoffAnimation(i,e,V);null!==t&&(o.startTime=t,c=!0)}}$(t,e),n.start(eJ(e,n,r,t.shouldReduceMotion&&B.has(e)?{type:!1}:o,t,c));let m=n.animation;m&&l.push(m)}return o&&Promise.all(l).then(()=>{V.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=K(t,e)||{};for(let e in r={...r,...i}){var s;let i=X(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,G(i))}}(t,o)})}),l}function e0(t){return"object"==typeof t&&null!==t}function e1(t){return e0(t)&&"ownerSVGElement"in t}function e2(t){return e1(t)&&"svg"===t.tagName}function e3(t){let{top:e,left:i,right:n,bottom:r}=t;return{x:{min:i,max:n},y:{min:e,max:r}}}function e5(t){return void 0===t||1===t}function e4(t){let{scale:e,scaleX:i,scaleY:n}=t;return!e5(e)||!e5(i)||!e5(n)}function e6(t){return e4(t)||e9(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function e9(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}t.s(["isSVGElement",()=>e1],55968),t.s(["isSVGSVGElement",()=>e2],17161),t.s(["HTMLVisualElement",()=>iJ],39191);function e8(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function e7(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3?arguments[3]:void 0,r=arguments.length>4?arguments[4]:void 0;t.min=e8(t.min,e,i,n,r),t.max=e8(t.max,e,i,n,r)}function it(t,e){let{x:i,y:n}=e;e7(t.x,i.translate,i.scale,i.originPoint),e7(t.y,n.translate,n.scale,n.originPoint)}function ie(t,e){t.min=t.min+e,t.max=t.max+e}function ii(t,e,i,n){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.5,s=b(t.min,t.max,r);e7(t,e,i,s,n)}function ir(t,e){ii(t.x,e.x,e.scaleX,e.scale,e.originX),ii(t.y,e.y,e.scaleY,e.scale,e.originY)}function is(t,e){return e3(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let io=t=>e=>e.test(t),ia=[tu,tS,tE,tb,tA,tT,{test:t=>"auto"===t,parse:t=>t}],il=t=>ia.find(io(t)),iu=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ih=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,id=t=>/^0[^.\s]+$/u.test(t),ic=new Set(["brightness","contrast","saturate","opacity"]);function im(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(tm)||[];if(!n)return t;let r=i.replace(n,""),s=+!!ic.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let ip=/\b([a-z-]*)\(.*?\)/gu,ig={...tI,getAnimatableNone:t=>{let e=t.match(ip);return e?e.map(im).join(" "):t}},iv={...tu,transform:Math.round},iy={borderWidth:tS,borderTopWidth:tS,borderRightWidth:tS,borderBottomWidth:tS,borderLeftWidth:tS,borderRadius:tS,radius:tS,borderTopLeftRadius:tS,borderTopRightRadius:tS,borderBottomRightRadius:tS,borderBottomLeftRadius:tS,width:tS,maxWidth:tS,height:tS,maxHeight:tS,top:tS,right:tS,bottom:tS,left:tS,padding:tS,paddingTop:tS,paddingRight:tS,paddingBottom:tS,paddingLeft:tS,margin:tS,marginTop:tS,marginRight:tS,marginBottom:tS,marginLeft:tS,backgroundPositionX:tS,backgroundPositionY:tS,rotate:tb,rotateX:tb,rotateY:tb,rotateZ:tb,scale:td,scaleX:td,scaleY:td,scaleZ:td,skew:tb,skewX:tb,skewY:tb,distance:tS,translateX:tS,translateY:tS,translateZ:tS,x:tS,y:tS,z:tS,perspective:tS,transformPerspective:tS,opacity:th,originX:tP,originY:tP,originZ:tS,zIndex:iv,fillOpacity:th,strokeOpacity:th,numOctaves:iv},ix={...iy,color:tk,backgroundColor:tk,outlineColor:tk,fill:tk,stroke:tk,borderColor:tk,borderTopColor:tk,borderRightColor:tk,borderBottomColor:tk,borderLeftColor:tk,filter:ig,WebkitFilter:ig},iw=t=>ix[t];function ib(t,e){let i=iw(t);return i!==ig&&(i=tI),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let iE=new Set(["auto","none","0"]);class iS extends eV{readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&ta(n=n.trim())){let r=function t(e,i){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;m(n<=4,'Max CSS variable fallback depth detected in property "'.concat(e,'". This may indicate a circular fallback dependency.'),"max-css-var-depth");let[r,s]=function(t){let e=ih.exec(t);if(!e)return[,];let[,i,n,r]=e;return["--".concat(null!=i?i:n),r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return iu(t)?parseFloat(t):t}return ta(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!B.has(i)||2!==t.length)return;let[n,r]=t,s=il(n),o=il(r);if(s!==o)if(eT(s)&&eT(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eM[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||id(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!iE.has(e)&&tL(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=ib(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eM[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let r=e.getValue(i);r&&r.jump(this.measuredOrigin,!1);let s=n.length-1,o=n[s];n[s]=eM[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null==(t=this.removedTransforms)?void 0:t.length)&&this.removedTransforms.forEach(t=>{let[i,n]=t;e.getValue(i).set(n)}),this.resolveNoneKeyframes()}constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}}t.s(["VisualElement",()=>i_],35088);let iT=[...ia,tk,tI],{schedule:iA}=L(queueMicrotask,!1),iP={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iM={};for(let t in iP)iM[t]={isEnabled:e=>iP[t].some(t=>!!e[t])};t.s(["createBox",()=>iD,"createDelta",()=>iC],27920);let ik=()=>({translate:0,scale:1,origin:0,originPoint:0}),iC=()=>({x:ik(),y:ik()}),ij=()=>({min:0,max:0}),iD=()=>({x:ij(),y:ij()}),iz="undefined"!=typeof window,iL={current:null},iV={current:!1};function iN(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function iR(t){return"string"==typeof t||Array.isArray(t)}let iI=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],iO=["initial",...iI];function iF(t){return iN(t.animate)||iO.some(e=>iR(t[e]))}function iB(t){return!!(iF(t)||t.variants)}let iU=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class i_{scrapeMotionValuesFromProps(t,e,i){return{}}mount(t){var e;this.current=t,M.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),iV.current||function(){if(iV.current=!0,iz)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>iL.current=t.matches;t.addEventListener("change",e),e()}else iL.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||iL.current),null==(e=this.parent)||e.addChild(this),this.update(this.props,this.presenceContext)}unmount(){var t;for(let e in this.projection&&this.projection.unmount(),N(this.notifyUpdate),N(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),null==(t=this.parent)||t.removeChild(this),this.events)this.events[e].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),null!=this.enteringChildren||(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=F.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&V.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in iM){let e=iM[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iD()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<iU.length;e++){let i=iU[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(x(r))t.addValue(n,r);else if(x(s))t.addValue(n,G(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,G(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=G(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let n=void 0===this.latestValues[t]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,t))?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=n){if("string"==typeof n&&(iu(n)||id(n)))n=parseFloat(n);else{let i;i=n,!iT.find(io(i))&&tI.test(e)&&(n=ib(t,e))}this.setBaseTarget(t,x(n)?n.get():n)}return x(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){var n;let r=Z(this.props,i,null==(n=this.presenceContext)?void 0:n.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||x(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new U),this.events[t].add(e)}notify(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];this.events[t]&&this.events[t].notify(...i)}scheduleRenderMicrotask(){iA.render(this.render)}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eV,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=W.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,V.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=iF(e),this.isVariantNode=iB(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&x(e)&&e.set(a[t])}}}class iW extends i_{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,e){let{vars:i,style:n}=e;delete i[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;x(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent="".concat(t))}))}constructor(){super(...arguments),this.KeyframeResolver=iS}}let iH=(t,e)=>e&&"number"==typeof t?e.transform(t):t,iY={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iG=O.length;function iX(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(F.has(t)){o=!0;continue}if(ts(t)){r[t]=i;continue}{let e=iH(i,iy[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<iG;s++){let o=O[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=iH(a,iy[o]);if(!l){r=!1;let e=iY[o]||o;n+="".concat(e,"(").concat(t,") ")}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin="".concat(t," ").concat(e," ").concat(i)}}function iq(t,e,i,n){let r,{style:s,vars:o}=e,a=t.style;for(r in s)a[r]=s[r];for(r in null==n||n.applyProjectionStyles(a,i),o)a.setProperty(r,o[r])}let iZ={};function iK(t,e){let{layout:i,layoutId:n}=e;return F.has(t)||t.startsWith("origin")||(i||void 0!==n)&&(!!iZ[t]||"opacity"===t)}function i$(t,e,i){let{style:n}=t,r={};for(let o in n){var s;(x(n[o])||e.style&&x(e.style[o])||iK(o,t)||(null==i||null==(s=i.getValue(o))?void 0:s.liveStyle)!==void 0)&&(r[o]=n[o])}return r}class iJ extends iW{readValueFromInstance(t,e){var i;if(F.has(e))return(null==(i=this.projection)?void 0:i.isProjecting)?eb(e):((t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eE(i,e)})(t,e);{let i=window.getComputedStyle(t),n=(ts(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,e){let{transformPagePoint:i}=e;return is(t,i)}build(t,e,i){iX(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return i$(t,e,i)}constructor(){super(...arguments),this.type="html",this.renderInstance=iq}}t.s(["SVGVisualElement",()=>i4],90776);let iQ={offset:"stroke-dashoffset",array:"stroke-dasharray"},i0={offset:"strokeDashoffset",array:"strokeDasharray"};function i1(t,e,i,n,r){var s,o;let{attrX:a,attrY:l,attrScale:u,pathLength:h,pathSpacing:d=1,pathOffset:c=0,...m}=e;if(iX(t,m,n),i){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:p,style:f}=t;p.transform&&(f.transform=p.transform,delete p.transform),(f.transform||p.transformOrigin)&&(f.transformOrigin=null!=(s=p.transformOrigin)?s:"50% 50%",delete p.transformOrigin),f.transform&&(f.transformBox=null!=(o=null==r?void 0:r.transformBox)?o:"fill-box",delete p.transformBox),void 0!==a&&(p.x=a),void 0!==l&&(p.y=l),void 0!==u&&(p.scale=u),void 0!==h&&function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=!(arguments.length>4)||void 0===arguments[4]||arguments[4];t.pathLength=1;let s=r?iQ:i0;t[s.offset]=tS.transform(-n);let o=tS.transform(e),a=tS.transform(i);t[s.array]="".concat(o," ").concat(a)}(p,h,d,c,!1)}let i2=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),i3=t=>"string"==typeof t&&"svg"===t.toLowerCase();function i5(t,e,i){let n=i$(t,e,i);for(let i in t)(x(t[i])||x(e[i]))&&(n[-1!==O.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}class i4 extends iW{getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(F.has(e)){let t=iw(e);return t&&t.default||0}return e=i2.has(e)?e:J(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return i5(t,e,i)}build(t,e,i){i1(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in iq(t,e,void 0,n),e.attrs)t.setAttribute(i2.has(i)?i:J(i),e.attrs[i])}mount(t){this.isSVGTag=i3(t.tagName),super.mount(t)}constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iD}}function i6(t,e,i){let n=x(t)?t:G(t);return n.start(eJ("",n,e,i)),n.animation}t.s(["animateSingleValue",()=>i6],42229),t.s(["motion",()=>sd],6831);var i9=t.i(6943);let i8=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function i7(t){if("string"!=typeof t||t.includes("-"));else if(i8.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var nt=t.i(65830);let ne=(0,i9.createContext)({}),ni=(0,i9.createContext)({strict:!1});t.s(["MotionConfigContext",()=>nn],97834);let nn=(0,i9.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),nr=(0,i9.createContext)({});function ns(t){return Array.isArray(t)?t.join(" "):t}let no=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function na(t,e,i){for(let n in e)x(e[n])||iK(n,i)||(t[n]=e[n])}let nl=()=>({...no(),attrs:{}}),nu=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nh(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nu.has(t)}let nd=t=>!nh(t);try{!function(t){"function"==typeof t&&(nd=e=>e.startsWith("on")?!nh(e):t(e))}((()=>{let t=Error("Cannot find module '@emotion/is-prop-valid'");throw t.code="MODULE_NOT_FOUND",t})().default)}catch(t){}let nc=(0,i9.createContext)(null);function nm(t){let e=(0,i9.useRef)(null);return null===e.current&&(e.current=t()),e.current}function np(t){return x(t)?t.get():t}t.s(["useConstant",()=>nm],48499);let nf=t=>(e,i)=>{let n=(0,i9.useContext)(nr),r=(0,i9.useContext)(nc),s=()=>(function(t,e,i,n){let{scrapeMotionValuesFromProps:r,createRenderState:s}=t;return{latestValues:function(t,e,i,n){let r={},s=n(t,{});for(let t in s)r[t]=np(s[t]);let{initial:o,animate:a}=t,l=iF(t),u=iB(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!i&&!1===i.initial,d=(h=h||!1===o)?a:o;if(d&&"boolean"!=typeof d&&!iN(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let n=Z(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(e,i,n,r),renderState:s()}})(t,e,n,r);return i?s():nm(s)},ng=nf({scrapeMotionValuesFromProps:i$,createRenderState:no}),nv=nf({scrapeMotionValuesFromProps:i5,createRenderState:nl}),ny=Symbol.for("motionComponentSymbol");function nx(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let nw=(0,i9.createContext)({}),nb=iz?i9.useLayoutEffect:i9.useEffect;function nE(t){var e,i;let{forwardMotionProps:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,s=arguments.length>3?arguments[3]:void 0;r&&function(t){for(let e in t)iM[e]={...iM[e],...t[e]}}(r);let o=i7(t)?nv:ng;function a(e,i){var r;let a,l={...(0,i9.useContext)(nn),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,i9.useContext)(ne).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:u}=l,h=function(t){let{initial:e,animate:i}=function(t,e){if(iF(t)){let{initial:e,animate:i}=t;return{initial:!1===e||iR(e)?e:void 0,animate:iR(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,i9.useContext)(nr));return(0,i9.useMemo)(()=>({initial:e,animate:i}),[ns(e),ns(i)])}(e),d=o(e,u);if(!u&&iz){(0,i9.useContext)(ni).strict;let e=function(t){let{drag:e,layout:i}=iM;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(l);a=e.MeasureLayout,h.visualElement=function(t,e,i,n,r){var s,o,a,l;let{visualElement:u}=(0,i9.useContext)(nr),h=(0,i9.useContext)(ni),d=(0,i9.useContext)(nc),c=(0,i9.useContext)(nn).reducedMotion,m=(0,i9.useRef)(null);n=n||h.renderer,!m.current&&n&&(m.current=n(t,{visualState:e,parent:u,props:i,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:c}));let p=m.current,f=(0,i9.useContext)(nw);p&&!p.projection&&r&&("html"===p.type||"svg"===p.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&nx(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(m.current,i,r,f);let g=(0,i9.useRef)(!1);(0,i9.useInsertionEffect)(()=>{p&&g.current&&p.update(i,d)});let v=i[Q],y=(0,i9.useRef)(!!v&&!(null==(s=(o=window).MotionHandoffIsComplete)?void 0:s.call(o,v))&&(null==(a=(l=window).MotionHasOptimisedAnimation)?void 0:a.call(l,v)));return nb(()=>{p&&(g.current=!0,window.MotionIsMounted=!0,p.updateFeatures(),p.scheduleRenderMicrotask(),y.current&&p.animationState&&p.animationState.animateChanges())}),(0,i9.useEffect)(()=>{p&&(!y.current&&p.animationState&&p.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var t,e;null==(t=(e=window).MotionHandoffMarkAsComplete)||t.call(e,v)}),y.current=!1),p.enteringChildren=void 0)}),p}(t,d,l,s,e.ProjectionNode)}return(0,nt.jsxs)(nr.Provider,{value:h,children:[a&&h.visualElement?(0,nt.jsx)(a,{visualElement:h.visualElement,...l}):null,function(t,e,i,n,r){let{latestValues:s}=n,o=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a=(i7(t)?function(t,e,i,n){let r=(0,i9.useMemo)(()=>{let i=nl();return i1(i,e,i3(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};na(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return na(n,i,t),Object.assign(n,function(t,e){let{transformTemplate:i}=t;return(0,i9.useMemo)(()=>{let t=no();return iX(t,e,i),Object.assign({},t.vars,t.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":"pan-".concat("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(e,s,r,t),l=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(nd(r)||!0===i&&nh(r)||!e&&!nh(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(e,"string"==typeof t,o),u=t!==i9.Fragment?{...l,...a,ref:i}:{},{children:h}=e,d=(0,i9.useMemo)(()=>x(h)?h.get():h,[h]);return(0,i9.createElement)(t,{...u,children:d})}(t,e,(r=h.visualElement,(0,i9.useCallback)(t=>{t&&d.onMount&&d.onMount(t),r&&(t?r.mount(t):r.unmount()),i&&("function"==typeof i?i(t):nx(i)&&(i.current=t))},[r])),d,u,n)]})}a.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(i=null!=(e=t.displayName)?e:t.name)?i:"",")"));let l=(0,i9.forwardRef)(a);return l[ny]=t,l}function nS(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,s=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),o=t.size,a=(o-1)*n;return"function"==typeof i?i(s,o):1===r?s*n:a-s*n}function nT(t,e){var i;let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=K(t,e,"exit"===n.type?null==(i=t.presenceContext)?void 0:i.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(s=n.transitionOverride);let o=r?()=>Promise.all(eQ(t,r,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?function(){let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1,o=arguments.length>6?arguments[6]:void 0,a=[];for(let l of t.variantChildren)l.notify("AnimationStart",e),a.push(nT(l,e,{...o,delay:i+("function"==typeof n?0:n)+nS(t.variantChildren,l,n,r,s)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(a)}(t,e,i,r,o,a,n)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([o(),a(n.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function nA(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}let nP=iO.length,nM=[...iI].reverse(),nk=iI.length;function nC(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nj(){return{animate:nC(!0),whileInView:nC(),whileHover:nC(),whileTap:nC(),whileDrag:nC(),whileFocus:nC(),exit:nC()}}class nD{update(){}constructor(t){this.isMounted=!1,this.node=t}}let nz=0,nL={x:!1,y:!1};function nV(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let nN=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function nR(t){return{point:{x:t.pageX,y:t.pageY}}}function nI(t,e,i,n){return nV(t,e,t=>nN(t)&&i(t,nR(t)),n)}function nO(t){return t.max-t.min}function nF(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;t.origin=n,t.originPoint=b(e.min,e.max,t.origin),t.scale=nO(i)/nO(e),t.translate=b(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function nB(t,e,i,n){nF(t.x,e.x,i.x,n?n.originX:void 0),nF(t.y,e.y,i.y,n?n.originY:void 0)}function nU(t,e,i){t.min=i.min+e.min,t.max=t.min+nO(e)}function n_(t,e,i){t.min=e.min-i.min,t.max=t.min+nO(e)}function nW(t,e,i){n_(t.x,e.x,i.x),n_(t.y,e.y,i.y)}function nH(t){return[t("x"),t("y")]}let nY=t=>{let{current:e}=t;return e?e.ownerDocument.defaultView:null},nG=(t,e)=>Math.abs(t-e);class nX{updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),N(this.updatePoint)}constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:r=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=nK(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(nG(t.x,e.x)**2+nG(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=R;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=nq(e,this.transformPagePoint),V.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=nK("pointercancel"===t.type?this.lastMoveEventInfo:nq(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!nN(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=n||window;let o=nq(nR(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=R;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,nK(o,this.history)),this.removeListeners=ti(nI(this.contextWindow,"pointermove",this.handlePointerMove),nI(this.contextWindow,"pointerup",this.handlePointerUp),nI(this.contextWindow,"pointercancel",this.handlePointerUp))}}function nq(t,e){return e?{point:e(t.point)}:t}function nZ(t,e){return{x:t.x-e.x,y:t.y-e.y}}function nK(t,e){let{point:i}=t;return{point:i,delta:nZ(i,n$(e)),offset:nZ(i,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,o=n$(t);for(;i>=0&&(n=t[i],!(o.timestamp-n.timestamp>r(.1)));)i--;if(!n)return{x:0,y:0};let a=s(o.timestamp-n.timestamp);if(0===a)return{x:0,y:0};let l={x:(o.x-n.x)/a,y:(o.y-n.y)/a};return l.x===1/0&&(l.x=0),l.y===1/0&&(l.y=0),l}(e,.1)}}function n$(t){return t[t.length-1]}function nJ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function nQ(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function n0(t,e,i){return{min:n1(t,e),max:n1(t,i)}}function n1(t,e){return"number"==typeof t?t:t[e]||0}let n2=new WeakMap;class n3{start(t){let{snapToCursor:e=!1,distanceThreshold:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let r=t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(nR(t).point)},s=(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(nL[t])return null;else return nL[t]=!0,()=>{nL[t]=!1};return nL.x||nL.y?null:(nL.x=nL.y=!0,()=>{nL.x=nL.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nH(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tE.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=nO(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&V.postRender(()=>r(t,e)),$(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},o=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},a=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>nH(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new nX(t,{onSessionStart:r,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:i,contextWindow:nY(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!n||!i)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&V.postRender(()=>o(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!n5(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,e,i){let{min:n,max:r}=e;return void 0!==n&&t<n?t=i?b(n,t,i.min):Math.max(t,n):void 0!==r&&t>r&&(t=i?b(r,t,i.max):Math.min(t,r)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,r=this.constraints;e&&nx(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,e){let{top:i,left:n,bottom:r,right:s}=e;return{x:nJ(t.x,n,s),y:nJ(t.y,i,r)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:.35;return!1===t?t=0:!0===t&&(t=.35),{x:n0(t,"left","right"),y:n0(t,"top","bottom")}}(i),r!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&nH(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!nx(e))return!1;let n=e.current;m(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=is(t,i),{scroll:r}=e;return r&&(ie(n.x,r.offset.x),ie(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:nQ(t.x,s.x),y:nQ(t.y,s.y)});if(i){let t=i(function(t){let{x:e,y:i}=t;return{top:i.min,right:e.max,bottom:i.max,left:e.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=e3(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(nH(o=>{if(!n5(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return $(this.visualElement,t),i.start(eJ(t,i,0,e,this.visualElement,!1))}stopAnimation(){nH(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){nH(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e="_drag".concat(t.toUpperCase()),i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){nH(e=>{let{drag:i}=this.getProps();if(!n5(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-b(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!nx(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};nH(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=nO(t),r=nO(e);return r>n?i=w(e.min,e.max-n,t.min):n>r&&(i=w(t.min,t.max-r,e.min)),o(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),nH(e=>{if(!n5(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(b(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;n2.set(this.visualElement,this);let t=nI(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();nx(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),V.read(e);let r=nV(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:i}=t;this.isDragging&&i&&(nH(t=>{let i=this.getAxisMotionValue(t);i&&(this.originPoint[t]+=e[t].translate,i.set(i.get()+e[t].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iD(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}}function n5(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}let n4=t=>(e,i)=>{t&&V.postRender(()=>t(e,i))};var n6=i9;let n9={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function n8(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let n7={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tS.test(t))return t;else t=parseFloat(t);let i=n8(t,e.target.x),n=n8(t,e.target.y);return"".concat(i,"% ").concat(n,"%")}},rt=!1;class re extends n6.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in rn)iZ[t]=rn[t],ts(t)&&(iZ[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),rt&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),n9.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,rt=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||V.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),iA.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;rt=!0,n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ri(t){let[e,i]=function(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0],e=(0,i9.useContext)(nc);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:n,register:r}=e,s=(0,i9.useId)();(0,i9.useEffect)(()=>{if(t)return r(s)},[t]);let o=(0,i9.useCallback)(()=>t&&n&&n(s),[s,n,t]);return!i&&n?[!1,o]:[!0]}(),n=(0,n6.useContext)(ne);return(0,nt.jsx)(re,{...t,layoutGroup:n,switchLayoutGroup:(0,n6.useContext)(nw),isPresent:e,safeToRemove:i})}let rn={borderRadius:{...n7,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:n7,borderTopRightRadius:n7,borderBottomLeftRadius:n7,borderBottomRightRadius:n7,boxShadow:{correct:(t,e)=>{let{treeScale:i,projectionDelta:n}=e,r=tI.parse(t);if(r.length>5)return t;let s=tI.createTransformer(t),o=+("number"!=typeof r[0]),a=n.x.scale*i.x,l=n.y.scale*i.y;r[0+o]/=a,r[1+o]/=l;let u=b(a,l,.5);return"number"==typeof r[2+o]&&(r[2+o]/=u),"number"==typeof r[3+o]&&(r[3+o]/=u),s(r)}}},rr=(t,e)=>t.depth-e.depth;class rs{add(t){i(this.children,t),this.isDirty=!0}remove(t){n(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rr),this.isDirty=!1,this.children.forEach(t)}constructor(){this.children=[],this.isDirty=!1}}let ro=["TopLeft","TopRight","BottomLeft","BottomRight"],ra=ro.length,rl=t=>"string"==typeof t?parseFloat(t):t,ru=t=>"number"==typeof t||tS.test(t);function rh(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rd=rm(0,.5,ee),rc=rm(.5,.95,C);function rm(t,e,i){return n=>n<t?0:n>e?1:i(w(t,e,n))}function rp(t,e){t.min=e.min,t.max=e.max}function rf(t,e){rp(t.x,e.x),rp(t.y,e.y)}function rg(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rv(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function ry(t,e,i,n,r){let[s,o,a]=i;!function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,r=arguments.length>4?arguments[4]:void 0,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:t,o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:t;if(tE.test(e)&&(e=parseFloat(e),e=b(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=b(s.min,s.max,n);t===s&&(a-=e),t.min=rv(t.min,e,i,a,r),t.max=rv(t.max,e,i,a,r)}(t,e[s],e[o],e[a],e.scale,n,r)}let rx=["x","scaleX","originX"],rw=["y","scaleY","originY"];function rb(t,e,i,n){ry(t.x,e,rx,i?i.x:void 0,n?n.x:void 0),ry(t.y,e,rw,i?i.y:void 0,n?n.y:void 0)}function rE(t){return 0===t.translate&&1===t.scale}function rS(t){return rE(t.x)&&rE(t.y)}function rT(t,e){return t.min===e.min&&t.max===e.max}function rA(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rP(t,e){return rA(t.x,e.x)&&rA(t.y,e.y)}function rM(t){return nO(t.x)/nO(t.y)}function rk(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rC{add(t){i(this.members,t),t.scheduleRender()}remove(t){if(n(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}constructor(){this.members=[]}}let rj={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rD=["","X","Y","Z"],rz=0;function rL(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function rV(t){let{attachResizeListener:e,defaultParent:i,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}=t;return class{addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new U),this.eventHandlers.get(t).add(e)}notifyListeners(t){for(var e=arguments.length,i=Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];let r=this.eventHandlers.get(t);r&&r.notify(...i)}hasListeners(t){return this.eventHandlers.has(t)}mount(t){if(this.instance)return;this.isSVG=e1(t)&&!e2(t),this.instance=t;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),e){let i,n=0,r=()=>this.root.updateBlockedByResize=!1;V.read(()=>{n=window.innerWidth}),e(t,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=W.now(),n=e=>{let{timestamp:r}=e,s=r-i;s>=250&&(N(n),t(s-250))};return V.setup(n,!0),()=>N(n)}(r,250),n9.hasAnimatedSinceResize&&(n9.hasAnimatedSinceResize=!1,this.nodes.forEach(rW)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",t=>{let{delta:e,hasLayoutChanged:i,hasRelativeLayoutChanged:n,layout:s}=t;if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||r.getDefaultTransition()||r$,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=r.getProps(),u=!this.targetLayout||!rP(this.targetLayout,s),h=!i&&n;if(this.options.layoutRoot||this.resumeFrom||h||i&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let t={...k(o,"layout"),onPlay:a,onComplete:l};(r.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t),this.setAnimationOrigin(e,h)}else i||rW(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),N(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rG),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[Q];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",V,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rB);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(rU);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(r_),this.nodes.forEach(rN),this.nodes.forEach(rR)):this.nodes.forEach(rU),this.clearAllSnapshots();let t=W.now();R.delta=o(0,1e3/60,t-R.timestamp),R.timestamp=t,R.isProcessing=!0,I.update.process(R),I.preRender.process(R),I.render.process(R),R.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,iA.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rF),this.sharedNodes.forEach(rX)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||nO(this.snapshot.measuredBox.x)||nO(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iD(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rS(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||e6(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],i=this.measurePageBox(),n=this.removeElementScroll(i);return e&&(n=this.removeTransform(n)),r0((t=n).x),r0(t.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return iD();let i=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(r2))){let{scroll:t}=this.root;t&&(ie(i.x,t.offset.x),ie(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=iD();if(rf(i,t),null==(e=this.scroll)?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let n=this.path[e],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&rf(i,t),ie(i.x,r.offset.x),ie(i.y,r.offset.y))}return i}applyTransform(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=iD();rf(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&ir(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),e6(n.latestValues)&&ir(i,n.latestValues)}return e6(this.latestValues)&&ir(i,this.latestValues),i}removeTransform(t){let e=iD();rf(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!e6(i.latestValues))continue;e4(i.latestValues)&&i.updateSnapshot();let n=iD();rf(n,i.measurePageBox()),rb(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return e6(this.latestValues)&&rb(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==R.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){var t,e,i,n;let r=arguments.length>0&&void 0!==arguments[0]&&arguments[0],s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(r||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=R.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),nW(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rf(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iD(),this.targetWithTransforms=iD()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),e=this.target,i=this.relativeTarget,n=this.relativeParent.target,nU(e.x,i.x,n.x),nU(e.y,i.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rf(this.target,this.layout.layoutBox),it(this.target,this.targetDelta)):rf(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),nW(this.relativeTargetOrigin,this.target,t.target),rf(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}z.value&&rj.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||e4(this.parent.latestValues)||e9(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(n=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===R.timestamp&&(n=!1),n)return;let{layout:r,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||s))return;rf(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i){let n,r,s=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&ir(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,it(t,r)),s&&e6(n.latestValues)&&ir(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iD());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rg(this.prevProjectionDelta.x,this.projectionDelta.x),rg(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nB(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&rk(this.projectionDelta.x,this.prevProjectionDelta.x)&&rk(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),z.value&&rj.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){var t;let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(null==(t=this.options.visualElement)||t.scheduleRender(),e){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iC(),this.projectionDelta=iC(),this.projectionDeltaWithTransform=iC()}setAnimationOrigin(t){let e,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iC();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!i;let a=iD(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(rK));this.animationProgress=0,this.mixTargetDelta=i=>{let n=i/1e3;if(rq(o.x,t.x,n),rq(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,m,p,f,g;nW(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),m=this.relativeTarget,p=this.relativeTargetOrigin,f=a,g=n,rZ(m.x,p.x,f.x,g),rZ(m.y,p.y,f.y,g),e&&(u=this.relativeTarget,c=e,rT(u.x,c.x)&&rT(u.y,c.y))&&(this.isProjectionDirty=!1),e||(e=iD()),rf(e,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){var o,a,l,u;r?(t.opacity=b(0,null!=(o=i.opacity)?o:1,rd(n)),t.opacityExit=b(null!=(a=e.opacity)?a:1,0,rc(n))):s&&(t.opacity=b(null!=(l=e.opacity)?l:1,null!=(u=i.opacity)?u:1,n));for(let r=0;r<ra;r++){let s="border".concat(ro[r],"Radius"),o=rh(e,s),a=rh(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||ru(o)===ru(a)?(t[s]=Math.max(b(rl(o),rl(a),n),0),(tE.test(a)||tE.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=b(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){var e,i,n;this.notifyListeners("animationStart"),null==(e=this.currentAnimation)||e.stop(),null==(n=this.resumingFrom)||null==(i=n.currentAnimation)||i.stop(),this.pendingAnimation&&(N(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.update(()=>{n9.hasAnimatedSinceResize=!0,tn.layout++,this.motionValue||(this.motionValue=G(0)),this.currentAnimation=i6(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{tn.layout--},onComplete:()=>{tn.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&r1(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iD();let e=nO(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=nO(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}rf(e,i),ir(e,r),nB(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rC),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote(){let{needsReset:t,transition:e,preserveFollowOpacity:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&rL("z",t,n,this.animationValues);for(let e=0;e<rD.length;e++)rL("rotate".concat(rD[e]),t,n,this.animationValues),rL("skew".concat(rD[e]),t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=np(null==e?void 0:e.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=np(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!e6(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let r=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((r||s||o)&&(n="translate3d(".concat(r,"px, ").concat(s,"px, ").concat(o,"px) ")),(1!==e.x||1!==e.y)&&(n+="scale(".concat(1/e.x,", ").concat(1/e.y,") ")),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n="perspective(".concat(t,"px) ").concat(n)),e&&(n+="rotate(".concat(e,"deg) ")),r&&(n+="rotateX(".concat(r,"deg) ")),s&&(n+="rotateY(".concat(s,"deg) ")),o&&(n+="skewX(".concat(o,"deg) ")),a&&(n+="skewY(".concat(a,"deg) "))}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+="scale(".concat(a,", ").concat(l,")")),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(s=i(r,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;if(t.transformOrigin="".concat(100*o.origin,"% ").concat(100*a.origin,"% 0"),n.animationValues){var l,u;t.opacity=n===this?null!=(u=null!=(l=r.opacity)?l:this.latestValues.opacity)?u:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit}else t.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(let e in iZ){if(void 0===r[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=iZ[e],l="none"===s?r[e]:i(r[e],n);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?np(null==e?void 0:e.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(rB),this.root.sharedNodes.clear()}constructor(t={},e=null==i?void 0:i()){this.id=rz++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,z.value&&(rj.nodes=rj.calculatedTargetDeltas=rj.calculatedProjections=0),this.nodes.forEach(rI),this.nodes.forEach(rH),this.nodes.forEach(rY),this.nodes.forEach(rO),z.addProjectionMetrics&&z.addProjectionMetrics(rj)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=e?e.root||e:this,this.path=e?[...e.path,e]:[],this.parent=e,this.depth=e?e.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rs)}}}function rN(t){t.updateLayout()}function rR(t){var e;let i=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:r}=t.options,s=i.source!==t.layout.source;"size"===r?nH(t=>{let n=s?i.measuredBox[t]:i.layoutBox[t],r=nO(n);n.min=e[t].min,n.max=n.min+r}):r1(r,i.layoutBox,e)&&nH(n=>{let r=s?i.measuredBox[n]:i.layoutBox[n],o=nO(e[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iC();nB(o,e,i.layoutBox);let a=iC();s?nB(a,t.applyTransform(n,!0),i.measuredBox):nB(a,e,i.layoutBox);let l=!rS(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=iD();nW(o,i.layoutBox,r.layoutBox);let a=iD();nW(a,e,s.layoutBox),rP(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function rI(t){z.value&&rj.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rO(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function rF(t){t.clearSnapshot()}function rB(t){t.clearMeasurements()}function rU(t){t.isLayoutDirty=!1}function r_(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function rW(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function rH(t){t.resolveTargetDelta()}function rY(t){t.calcProjection()}function rG(t){t.resetSkewAndRotation()}function rX(t){t.removeLeadSnapshot()}function rq(t,e,i){t.translate=b(e.translate,0,i),t.scale=b(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function rZ(t,e,i,n){t.min=b(e.min,i.min,n),t.max=b(e.max,i.max,n)}function rK(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let r$={duration:.45,ease:[.4,0,.1,1]},rJ=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rQ=rJ("applewebkit/")&&!rJ("chrome/")?Math.round:C;function r0(t){t.min=rQ(t.min),t.max=rQ(t.max)}function r1(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rM(e)-rM(i)))}function r2(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let r3=rV({attachResizeListener:(t,e)=>nV(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),r5={current:void 0},r4=rV({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!r5.current){let t=new r3({});t.mount(window),t.setOptions({layoutScroll:!0}),r5.current=t}return r5.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function r6(t,e){let i=P(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function r9(t){return!("touch"===t.pointerType||nL.x||nL.y)}function r8(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&V.postRender(()=>r(e,nR(e)))}let r7=(t,e)=>!!e&&(t===e||r7(t,e.parentElement)),st=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),se=new WeakSet;function si(t){return e=>{"Enter"===e.key&&t(e)}}function sn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function sr(t){return nN(t)&&!(nL.x||nL.y)}function ss(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&V.postRender(()=>r(e,nR(e)))}let so=new WeakMap,sa=new WeakMap,sl=t=>{let e=so.get(t.target);e&&e(t)},su=t=>{t.forEach(sl)},sh={some:0,all:1},sd=function(t,e){if("undefined"==typeof Proxy)return nE;let i=new Map,n=(i,n)=>nE(i,n,t,e);return new Proxy((t,e)=>n(t,e),{get:(r,s)=>"create"===s?n:(i.has(s)||i.set(s,nE(s,void 0,t,e)),i.get(s))})}({animation:{Feature:class extends nD{updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();iN(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(e=>{let{animation:i,options:n}=e;return function(t,e){let i,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>nT(t,e,n)));else if("string"==typeof e)i=nT(t,e,n);else{let r="function"==typeof e?K(t,e,n.custom):e;i=Promise.all(eQ(t,r,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}(t,i,n)})),i=nj(),n=!0,r=e=>(i,n)=>{var r;let s=K(t,n,"exit"===e?null==(r=t.presenceContext)?void 0:r.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function s(s){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<nP;t++){let n=iO[t],r=e.props[n];(iR(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},l=[],u=new Set,h={},d=1/0;for(let e=0;e<nk;e++){var c,m;let p=nM[e],f=i[p],g=void 0!==o[p]?o[p]:a[p],v=iR(g),y=p===s?f.isActive:null;!1===y&&(d=e);let x=g===a[p]&&g!==o[p]&&v;if(x&&n&&t.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...h},!f.isActive&&null===y||!g&&!f.prevProp||iN(g)||"boolean"==typeof g)continue;let w=(c=f.prevProp,"string"==typeof(m=g)?m!==c:!!Array.isArray(m)&&!nA(m,c)),b=w||p===s&&f.isActive&&!x&&v||e>d&&v,E=!1,S=Array.isArray(g)?g:[g],T=S.reduce(r(p),{});!1===y&&(T={});let{prevResolvedValues:A={}}=f,P={...A,...T},M=e=>{b=!0,u.has(e)&&(E=!0,u.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in P){let e=T[t],i=A[t];if(!h.hasOwnProperty(t))(X(e)&&X(i)?nA(e,i):e===i)?void 0!==e&&u.has(t)?M(t):f.protectedKeys[t]=!0:null!=e?M(t):u.add(t)}f.prevProp=g,f.prevResolvedValues=T,f.isActive&&(h={...h,...T}),n&&t.blockInitialAnimation&&(b=!1);let k=x&&w,C=!k||E;b&&C&&l.push(...S.map(e=>{let i={type:p};if("string"==typeof e&&n&&!k&&t.manuallyAnimateOnMount&&t.parent){let{parent:n}=t,r=K(n,e);if(n.enteringChildren&&r){let{delayChildren:e}=r.transition||{};i.delay=nS(n.enteringChildren,t,e)}}return{animation:e,options:i}}))}if(u.size){let e={};if("boolean"!=typeof o.initial){let i=K(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}u.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=null!=n?n:null}),l.push({animation:e})}let p=!!l.length;return n&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(p=!1),n=!1,p?e(l):Promise.resolve()}return{animateChanges:s,setActive:function(e,n){var r;if(i[e].isActive===n)return Promise.resolve();null==(r=t.variantChildren)||r.forEach(t=>{var i;return null==(i=t.animationState)?void 0:i.setActive(e,n)}),i[e].isActive=n;let o=s(e);for(let t in i)i[t].protectedKeys={};return o},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=nj(),n=!0}}}(t))}}},exit:{Feature:class extends nD{update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}constructor(){super(...arguments),this.id=nz++}}},inView:{Feature:class extends nD{startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:sh[n]},o=t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)};var a=this.node.current;let l=function(t){let{root:e,...i}=t,n=e||document;sa.has(n)||sa.set(n,{});let r=sa.get(n),s=JSON.stringify(i);return r[s]||(r[s]=new IntersectionObserver(su,{root:e,...i})),r[s]}(s);return so.set(a,o),l.observe(a),()=>{so.delete(a),l.unobserve(a)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function(t){let{viewport:e={}}=t,{viewport:i={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t=>e[t]!==i[t]}(t,e))&&this.startObserver()}unmount(){}constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}}},tap:{Feature:class extends nD{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[n,r,s]=r6(t,i),o=t=>{let n=t.currentTarget;if(!sr(t))return;se.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),se.has(n)&&se.delete(n),sr(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||r7(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),e0(t)&&"offsetHeight"in t&&(t.addEventListener("focus",t=>((t,e)=>{let i=t.currentTarget;if(!i)return;let n=si(()=>{if(se.has(i))return;sn(i,"down");let t=si(()=>{sn(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>sn(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)})(t,r)),st.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(ss(this.node,e,"Start"),(t,e)=>{let{success:i}=e;return ss(this.node,t,i?"End":"Cancel")}),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends nD{onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ti(nV(this.node.current,"focus",()=>this.onFocus()),nV(this.node.current,"blur",()=>this.onBlur()))}unmount(){}constructor(){super(...arguments),this.isActive=!1}}},hover:{Feature:class extends nD{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[n,r,s]=r6(t,i),o=t=>{if(!r9(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{r9(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(r8(this.node,e,"Start"),t=>r8(this.node,t,"End"))))}unmount(){}}},pan:{Feature:class extends nD{onPointerDown(t){this.session=new nX(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nY(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:n4(t),onStart:n4(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&V.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=nI(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}constructor(){super(...arguments),this.removePointerDownListener=C}}},drag:{Feature:class extends nD{mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||C}unmount(){this.removeGroupControls(),this.removeListeners()}constructor(t){super(t),this.removeGroupControls=C,this.removeListeners=C,this.controls=new n3(t)}},ProjectionNode:r4,MeasureLayout:ri},layout:{ProjectionNode:r4,MeasureLayout:ri}},(t,e)=>i7(t)?new i4(e):new iJ(e,{allowProjection:t!==i9.Fragment}))},56848,t=>{"use strict";t.s(["InfiniteSlider",()=>q],56848);var e=t.i(65830);class i{get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t){let e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}}class n extends i{then(t,e){return this.finished.finally(t).then(()=>{})}}var r=t.i(33639),s=t.i(37307),o=t.i(76650),a=t.i(5331),l=t.i(33160),u=t.i(58616),h=t.i(10337),d=t.i(47113),c=t.i(67327),m=t.i(48367),p=t.i(20085);function f(t,e){return(0,p.isEasingArray)(t)?t[((t,e,i)=>{let n=e-t;return((i-t)%n+n)%n+t})(0,t.length,e)]:t}var g=t.i(25578);function v(t){return"object"==typeof t&&!Array.isArray(t)}function y(t,e,i,n){return"string"==typeof t&&v(e)?(0,g.resolveElements)(t,i,n):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function x(t,e,i,n){var r;return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?i:e.startsWith("<")?Math.max(0,i+parseFloat(e.slice(1))):null!=(r=n.get(e))?r:t}var w=t.i(3083);function b(t,e){return t.at!==e.at?t.at-e.at:null===t.value?1:null===e.value?-1:0}function E(t,e){return e.has(t)||e.set(t,{}),e.get(t)}function S(t,e){return e[t]||(e[t]=[]),e[t]}let T=t=>"number"==typeof t,A=t=>t.every(T);var P=t.i(10867),M=t.i(78072),k=t.i(55968),C=t.i(17161),j=t.i(39191),D=t.i(27920),z=t.i(35088);class L extends z.VisualElement{readValueFromInstance(t,e){if(e in t){let i=t[e];if("string"==typeof i||"number"==typeof i)return i}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return(0,D.createBox)()}build(t,e){Object.assign(t.output,e)}renderInstance(t,e){let{output:i}=e;Object.assign(t,i)}sortInstanceNodePosition(){return 0}constructor(){super(...arguments),this.type="object"}}var V=t.i(90776);function N(t){let e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},i=(0,k.isSVGElement)(t)&&!(0,C.isSVGSVGElement)(t)?new V.SVGVisualElement(e):new j.HTMLVisualElement(e);i.mount(t),P.visualElementStore.set(t,i)}function R(t){let e=new L({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),P.visualElementStore.set(t,e)}var I=t.i(42229);function O(t,e,i,n){let r=[];if((0,o.isMotionValue)(t)||"number"==typeof t||"string"==typeof t&&!v(e))r.push((0,I.animateSingleValue)(t,v(e)&&e.default||e,i&&i.default||i));else{let s=y(t,e,n),o=s.length;(0,m.invariant)(!!o,"No valid elements provided.","no-valid-elements");for(let t=0;t<o;t++){let n=s[t];(0,m.invariant)(null!==n,"You're trying to perform an animation on null. Ensure that selectors are correctly finding elements and refs are correctly hydrated.","animate-null");let a=n instanceof Element?N:R;P.visualElementStore.has(n)||a(n);let l=P.visualElementStore.get(n),u={...i};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,o)),r.push(...(0,M.animateTarget)(l,{...e,transition:u},{}))}}return r}let F=function(t){return function(e,i,p){let g=[],v=new n(g=Array.isArray(e)&&e.some(Array.isArray)?function(t,e,i){let n=[];return(function(t){let{defaultTransition:e={},...i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,s=arguments.length>3?arguments[3]:void 0,p=e.duration||.3,g=new Map,v=new Map,T={},P=new Map,M=0,k=0,C=0;for(let i=0;i<t.length;i++){let d=t[i];if("string"==typeof d){P.set(d,k);continue}if(!Array.isArray(d)){P.set(d.name,x(k,d.at,M,P));continue}let[g,b,z={}]=d;void 0!==z.at&&(k=x(k,z.at,M,P));let L=0,V=function(t,i,n){var o;let d=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,g=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0,v=Array.isArray(o=t)?o:[o],{delay:y=0,times:x=(0,a.defaultOffset)(v),type:b="keyframes",repeat:E,repeatType:S,repeatDelay:T=0,...P}=i,{ease:M=e.ease||"easeOut",duration:j}=i,D="function"==typeof y?y(d,g):y,z=v.length,V=(0,l.isGenerator)(b)?b:null==s?void 0:s[b||"keyframes"];if(z<=2&&V){let t=100;2===z&&A(v)&&(t=Math.abs(v[1]-v[0]));let e={...P};void 0!==j&&(e.duration=(0,c.secondsToMilliseconds)(j));let i=(0,u.createGeneratorEasing)(e,t,V);M=i.ease,j=i.duration}null!=j||(j=p);let N=k+D;1===x.length&&0===x[0]&&(x[1]=1);let R=x.length-v.length;if(R>0&&(0,h.fillOffset)(x,R),1===v.length&&v.unshift(null),E){(0,m.invariant)(E<20,"Repeat count too high, must be less than 20","repeat-count-high");j*=E+1;let t=[...v],e=[...x],i=[...M=Array.isArray(M)?[...M]:[M]];for(let n=0;n<E;n++){v.push(...t);for(let r=0;r<t.length;r++)x.push(e[r]+(n+1)),M.push(0===r?"linear":f(i,r-1))}for(let t=0;t<x.length;t++)x[t]=x[t]/(E+1)}let I=N+j;!function(t,e,i,n,s,o){for(let e=0;e<t.length;e++){let i=t[e];i.at>s&&i.at<o&&((0,r.removeItem)(t,i),e--)}for(let r=0;r<e.length;r++)t.push({value:e[r],at:(0,w.mixNumber)(s,o,n[r]),easing:f(i,r)})}(n,v,M,x,N,I),L=Math.max(D+j,L),C=Math.max(I,C)};if((0,o.isMotionValue)(g))V(b,z,S("default",E(g,v)));else{let t=y(g,b,n,T),e=t.length;for(let i=0;i<e;i++){let n=E(t[i],v);for(let t in b){var j,D;V(b[t],(j=z,D=t,j&&j[D]?{...j,...j[D]}:{...j}),S(t,n),i,e)}}}M=k,k+=L}return v.forEach((t,n)=>{for(let r in t){let s=t[r];s.sort(b);let o=[],a=[],l=[];for(let t=0;t<s.length;t++){let{at:e,value:i,easing:n}=s[t];o.push(i),a.push((0,d.progress)(0,C,e)),l.push(n||"easeOut")}0!==a[0]&&(a.unshift(0),o.unshift(o[0]),l.unshift("easeInOut")),1!==a[a.length-1]&&(a.push(1),o.push(null)),g.has(n)||g.set(n,{keyframes:{},transition:{}});let u=g.get(n);u.keyframes[r]=o,u.transition[r]={...e,duration:C,ease:l,times:a,...i}}}),g})(t,e,i,{spring:s.spring}).forEach((t,e)=>{let{keyframes:i,transition:r}=t;n.push(...O(e,i,r))}),n}(e,i,void 0):O(e,i,p,t));return t&&(t.animations.push(v),v.finished.then(()=>{(0,r.removeItem)(t.animations,v)})),v}}();var B=t.i(6831),U=t.i(13219),_=t.i(6943),W=t.i(97834),H=t.i(48499);function Y(t,e){let i;return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];window.clearTimeout(i),i=window.setTimeout(()=>t(...r),e)}}let G=["x","y","top","bottom","left","right","width","height"];var X=t.i(47163);function q(t){let{children:i,gap:n=16,speed:r=100,speedOnHover:s,direction:o="horizontal",reverse:a=!1,className:l}=t,[u,h]=(0,_.useState)(r),[d,{width:c,height:m}]=function(){var t,e,i;let{debounce:n,scroll:r,polyfill:s,offsetSize:o}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{debounce:0,scroll:!1,offsetSize:!1},a=s||("undefined"==typeof window?class{}:window.ResizeObserver);if(!a)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[l,u]=(0,_.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),h=(0,_.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:l,orientationHandler:null}),d=n?"number"==typeof n?n:n.scroll:null,c=n?"number"==typeof n?n:n.resize:null,m=(0,_.useRef)(!1);(0,_.useEffect)(()=>(m.current=!0,()=>void(m.current=!1)));let[p,f,g]=(0,_.useMemo)(()=>{let t=()=>{let t,e;if(!h.current.element)return;let{left:i,top:n,width:r,height:s,bottom:a,right:l,x:d,y:c}=h.current.element.getBoundingClientRect(),p={left:i,top:n,width:r,height:s,bottom:a,right:l,x:d,y:c};h.current.element instanceof HTMLElement&&o&&(p.height=h.current.element.offsetHeight,p.width=h.current.element.offsetWidth),Object.freeze(p),m.current&&(t=h.current.lastBounds,e=p,!G.every(i=>t[i]===e[i]))&&u(h.current.lastBounds=p)};return[t,c?Y(t,c):t,d?Y(t,d):t]},[u,o,d,c]);function v(){h.current.scrollContainers&&(h.current.scrollContainers.forEach(t=>t.removeEventListener("scroll",g,!0)),h.current.scrollContainers=null),h.current.resizeObserver&&(h.current.resizeObserver.disconnect(),h.current.resizeObserver=null),h.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",h.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",h.current.orientationHandler))}function y(){h.current.element&&(h.current.resizeObserver=new a(g),h.current.resizeObserver.observe(h.current.element),r&&h.current.scrollContainers&&h.current.scrollContainers.forEach(t=>t.addEventListener("scroll",g,{capture:!0,passive:!0})),h.current.orientationHandler=()=>{g()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",h.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",h.current.orientationHandler))}return t=g,e=!!r,(0,_.useEffect)(()=>{if(e)return window.addEventListener("scroll",t,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",t,!0)},[t,e]),i=f,(0,_.useEffect)(()=>(window.addEventListener("resize",i),()=>void window.removeEventListener("resize",i)),[i]),(0,_.useEffect)(()=>{v(),y()},[r,g,f]),(0,_.useEffect)(()=>v,[]),[t=>{t&&t!==h.current.element&&(v(),h.current.element=t,h.current.scrollContainers=function t(e){let i=[];if(!e||e===document.body)return i;let{overflow:n,overflowX:r,overflowY:s}=window.getComputedStyle(e);return[n,r,s].some(t=>"auto"===t||"scroll"===t)&&i.push(e),[...i,...t(e.parentElement)]}(t),y())},l,p]}(),p=function(t){let e=(0,H.useConstant)(()=>(0,U.motionValue)(0)),{isStatic:i}=(0,_.useContext)(W.MotionConfigContext);if(i){let[,t]=(0,_.useState)(0);(0,_.useEffect)(()=>e.on("change",t),[])}return e}(0),[f,g]=(0,_.useState)(!1),[v,y]=(0,_.useState)(0);(0,_.useEffect)(()=>{let t,e=("horizontal"===o?c:m)+n,i=a?-e/2:0,r=a?0:-e/2,s=Math.abs(r-i);if(f){let e=Math.abs(p.get()-r);t=F(p,[p.get(),r],{ease:"linear",duration:e/u,onComplete:()=>{g(!1),y(t=>t+1)}})}else t=F(p,[i,r],{ease:"linear",duration:s/u,repeat:1/0,repeatType:"loop",repeatDelay:0,onRepeat:()=>{p.set(i)}});return null==t?void 0:t.stop},[v,p,u,c,m,n,f,o,a]);let x=s?{onHoverStart:()=>{g(!0),h(s)},onHoverEnd:()=>{g(!0),h(r)}}:{};return(0,e.jsx)("div",{className:(0,X.cn)("overflow-hidden",l),children:(0,e.jsxs)(B.motion.div,{className:"flex w-max",ref:d,style:{..."horizontal"===o?{x:p}:{y:p},gap:"".concat(n,"px"),flexDirection:"horizontal"===o?"row":"column"},...x,children:[i,i]})})}},72806,t=>{"use strict";t.s(["GRADIENT_ANGLES",()=>r,"ProgressiveBlur",()=>s]);var e=t.i(65830),i=t.i(6831),n=t.i(47163);let r={top:0,right:90,bottom:180,left:270};function s(t){let{direction:s="bottom",blurLayers:o=8,className:a,blurIntensity:l=.25,...u}=t,h=Math.max(o,2),d=1/(o+1);return(0,e.jsx)("div",{className:(0,n.cn)("relative",a),children:Array.from({length:h}).map((t,n)=>{let o=r[s],a=[n*d,(n+1)*d,(n+2)*d,(n+3)*d].map((t,e)=>"rgba(255, 255, 255, ".concat(+(1===e||2===e),") ").concat(100*t,"%")),h="linear-gradient(".concat(o,"deg, ").concat(a.join(", "),")");return(0,e.jsx)(i.motion.div,{className:"pointer-events-none absolute inset-0 rounded-[inherit]",style:{maskImage:h,WebkitMaskImage:h,backdropFilter:"blur(".concat(n*l,"px)"),WebkitBackdropFilter:"blur(".concat(n*l,"px)")},...u},n)})})}},3808,t=>{"use strict";t.s(["default",()=>U],3808);var e=t.i(65830),i=t.i(6943);function n(t){return"[object Object]"===Object.prototype.toString.call(t)||Array.isArray(t)}function r(t,e){let i=Object.keys(t),s=Object.keys(e);return i.length===s.length&&JSON.stringify(Object.keys(t.breakpoints||{}))===JSON.stringify(Object.keys(e.breakpoints||{}))&&i.every(i=>{let s=t[i],o=e[i];return"function"==typeof s?"".concat(s)==="".concat(o):n(s)&&n(o)?r(s,o):s===o})}function s(t){return t.concat().sort((t,e)=>t.name>e.name?1:-1).map(t=>t.options)}function o(t){return"number"==typeof t}function a(t){return"string"==typeof t}function l(t){return"boolean"==typeof t}function u(t){return"[object Object]"===Object.prototype.toString.call(t)}function h(t){return Math.abs(t)}function d(t){return Math.sign(t)}function c(t){return g(t).map(Number)}function m(t){return t[p(t)]}function p(t){return Math.max(0,t.length-1)}function f(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Array.from(Array(t),(t,i)=>e+i)}function g(t){return Object.keys(t)}function v(t,e){return void 0!==e.MouseEvent&&t instanceof e.MouseEvent}function y(){let t=[],e={add:function(i,n,r){let s,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return"addEventListener"in i?(i.addEventListener(n,r,o),s=()=>i.removeEventListener(n,r,o)):(i.addListener(r),s=()=>i.removeListener(r)),t.push(s),e},clear:function(){t=t.filter(t=>t())}};return e}function x(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=h(t-e);function n(i){return i<t||i>e}return{length:i,max:e,min:t,constrain:function(i){return n(i)?i<t?t:e:i},reachedAny:n,reachedMax:function(t){return t>e},reachedMin:function(e){return e<t},removeOffset:function(t){return i?t-i*Math.ceil((t-e)/i):t}}}function w(t){let e=t;function i(t){return o(t)?t:t.get()}return{get:function(){return e},set:function(t){e=i(t)},add:function(t){e+=i(t)},subtract:function(t){e-=i(t)}}}function b(t,e){let i="x"===t.scroll?function(t){return"translate3d(".concat(t,"px,0px,0px)")}:function(t){return"translate3d(0px,".concat(t,"px,0px)")},n=e.style,r=null,s=!1;return{clear:function(){!s&&(n.transform="",e.getAttribute("style")||e.removeAttribute("style"))},to:function(e){if(s)return;let o=Math.round(100*t.direction(e))/100;o!==r&&(n.transform=i(o),r=o)},toggleActive:function(t){s=!t}}}let E={align:"center",axis:"x",container:null,slides:null,containScroll:"trimSnaps",direction:"ltr",slidesToScroll:1,inViewThreshold:0,breakpoints:{},dragFree:!1,dragThreshold:10,loop:!1,skipSnaps:!1,duration:25,startIndex:0,active:!0,watchDrag:!0,watchResize:!0,watchSlides:!0,watchFocus:!0};function S(t,e,i){let n,r,s,T,A,P=t.ownerDocument,M=P.defaultView,k=function(t){function e(t,e){return function t(e,i){return[e,i].reduce((e,i)=>(g(i).forEach(n=>{let r=e[n],s=i[n],o=u(r)&&u(s);e[n]=o?t(r,s):s}),e),{})}(t,e||{})}return{mergeOptions:e,optionsAtMedia:function(i){let n=i.breakpoints||{},r=g(n).filter(e=>t.matchMedia(e).matches).map(t=>n[t]).reduce((t,i)=>e(t,i),{});return e(i,r)},optionsMediaQueries:function(e){return e.map(t=>g(t.breakpoints||{})).reduce((t,e)=>t.concat(e),[]).map(t.matchMedia)}}}(M),C=(A=[],{init:function(t,e){return(A=e.filter(t=>{let{options:e}=t;return!1!==k.optionsAtMedia(e).active})).forEach(e=>e.init(t,k)),e.reduce((t,e)=>Object.assign(t,{[e.name]:e}),{})},destroy:function(){A=A.filter(t=>t.destroy())}}),j=y(),D=function(){let t,e={},i={init:function(e){t=e},emit:function(n){return(e[n]||[]).forEach(e=>e(t,n)),i},off:function(t,n){return e[t]=(e[t]||[]).filter(t=>t!==n),i},on:function(t,n){return e[t]=(e[t]||[]).concat([n]),i},clear:function(){e={}}};return i}(),{mergeOptions:z,optionsAtMedia:L,optionsMediaQueries:V}=k,{on:N,off:R,emit:I}=D,O=!1,F=z(E,S.globalOptions),B=z(F),U=[];function _(e,i){if(O)return;B=L(F=z(F,e)),U=i||U;let{container:u,slides:E}=B;s=(a(u)?t.querySelector(u):u)||t.children[0];let S=a(E)?s.querySelectorAll(E):E;T=[].slice.call(S||s.children),n=function e(i){let n=function(t,e,i,n,r,s,u){let E,S,{align:T,axis:A,direction:P,startIndex:M,loop:k,duration:C,dragFree:j,dragThreshold:D,inViewThreshold:z,slidesToScroll:L,skipSnaps:V,containScroll:N,watchResize:R,watchSlides:I,watchDrag:O,watchFocus:F}=s,B={measure:function(t){let{offsetTop:e,offsetLeft:i,offsetWidth:n,offsetHeight:r}=t;return{top:e,right:i+n,bottom:e+r,left:i,width:n,height:r}}},U=B.measure(e),_=i.map(B.measure),W=function(t,e){let i="rtl"===e,n="y"===t,r=!n&&i?-1:1;return{scroll:n?"y":"x",cross:n?"x":"y",startEdge:n?"top":i?"right":"left",endEdge:n?"bottom":i?"left":"right",measureSize:function(t){let{height:e,width:i}=t;return n?e:i},direction:function(t){return t*r}}}(A,P),H=W.measureSize(U),Y={measure:function(t){return t/100*H}},G=function(t,e){let i={start:function(){return 0},center:function(t){return(e-t)/2},end:function(t){return e-t}};return{measure:function(n,r){return a(t)?i[t](n):t(e,n,r)}}}(T,H),X=!k&&!!N,{slideSizes:q,slideSizesWithGaps:Z,startGap:K,endGap:$}=function(t,e,i,n,r,s){let{measureSize:o,startEdge:a,endEdge:l}=t,u=i[0]&&r,d=function(){if(!u)return 0;let t=i[0];return h(e[a]-t[a])}(),c=u?parseFloat(s.getComputedStyle(m(n)).getPropertyValue("margin-".concat(l))):0,f=i.map(o),g=i.map((t,e,i)=>{let n=e===p(i);return e?n?f[e]+c:i[e+1][a]-t[a]:f[e]+d}).map(h);return{slideSizes:f,slideSizesWithGaps:g,startGap:d,endGap:c}}(W,U,_,i,k||!!N,r),J=function(t,e,i,n,r,s,a,l,u){let{startEdge:d,endEdge:f,direction:g}=t,v=o(i);return{groupSlides:function(t){return v?c(t).filter(t=>t%i==0).map(e=>t.slice(e,e+i)):t.length?c(t).reduce((i,o,u)=>{let c=m(i)||0,v=o===p(t),y=r[d]-s[c][d],x=r[d]-s[o][f],w=n||0!==c?0:g(a),b=h(x-(!n&&v?g(l):0)-(y+w));return u&&b>e+2&&i.push(o),v&&i.push(t.length),i},[]).map((e,i,n)=>{let r=Math.max(n[i-1]||0);return t.slice(r,e)}):[]}}}(W,H,L,k,U,_,K,$,0),{snaps:Q,snapsAligned:tt}=function(t,e,i,n,r){let{startEdge:s,endEdge:o}=t,{groupSlides:a}=r,l=a(n).map(t=>m(t)[o]-t[0][s]).map(h).map(e.measure),u=n.map(t=>i[s]-t[s]).map(t=>-h(t)),d=a(u).map(t=>t[0]).map((t,e)=>t+l[e]);return{snaps:u,snapsAligned:d}}(W,G,U,_,J),te=-m(Q)+m(Z),{snapsContained:ti,scrollContainLimit:tn}=function(t,e,i,n,r){let s=x(-e+t,0),o=i.map((t,e)=>{let{min:n,max:r}=s,o=s.constrain(t),a=e===p(i);return e?a||function(t,e){return 1>=h(t-e)}(n,o)?n:function(t,e){return 1>=h(t-e)}(r,o)?r:o:r}).map(t=>parseFloat(t.toFixed(3))),a=function(){let t=o[0],e=m(o);return x(o.lastIndexOf(t),o.indexOf(e)+1)}();return{snapsContained:function(){if(e<=t+2)return[s.max];if("keepSnaps"===n)return o;let{min:i,max:r}=a;return o.slice(i,r)}(),scrollContainLimit:a}}(H,te,tt,N,0),tr=X?ti:tt,{limit:ts}=function(t,e,i){let n=e[0];return{limit:x(i?n-t:m(e),n)}}(te,tr,k),to=function t(e,i,n){let{constrain:r}=x(0,e),s=e+1,o=a(i);function a(t){return n?h((s+t)%s):r(t)}function l(){return t(e,o,n)}let u={get:function(){return o},set:function(t){return o=a(t),u},add:function(t){return l().set(o+t)},clone:l};return u}(p(tr),M,k),ta=to.clone(),tl=c(i),tu=function(t,e,i,n){let r=y(),s=1e3/60,o=null,a=0,l=0;function u(t){if(!l)return;o||(o=t,i(),i());let r=t-o;for(o=t,a+=r;a>=s;)i(),a-=s;n(a/s),l&&(l=e.requestAnimationFrame(u))}function h(){e.cancelAnimationFrame(l),o=null,a=0,l=0}return{init:function(){r.add(t,"visibilitychange",()=>{t.hidden&&(o=null,a=0)})},destroy:function(){h(),r.clear()},start:function(){l||(l=e.requestAnimationFrame(u))},stop:h,update:i,render:n}}(n,r,()=>(t=>{let{dragHandler:e,scrollBody:i,scrollBounds:n,options:{loop:r}}=t;r||n.constrain(e.pointerDown()),i.seek()})(tS),t=>((t,e)=>{let{scrollBody:i,translate:n,location:r,offsetLocation:s,previousLocation:o,scrollLooper:a,slideLooper:l,dragHandler:u,animation:h,eventHandler:d,scrollBounds:c,options:{loop:m}}=t,p=i.settled(),f=!c.shouldConstrain(),g=m?p:p&&f,v=g&&!u.pointerDown();v&&h.stop();let y=r.get()*e+o.get()*(1-e);s.set(y),m&&(a.loop(i.direction()),l.loop()),n.to(s.get()),v&&d.emit("settle"),g||d.emit("scroll")})(tS,t)),th=tr[to.get()],td=w(th),tc=w(th),tm=w(th),tp=w(th),tf=function(t,e,i,n,r,s){let o=0,a=0,l=r,u=.68,c=t.get(),m=0;function p(t){return l=t,g}function f(t){return u=t,g}let g={direction:function(){return a},duration:function(){return l},velocity:function(){return o},seek:function(){let e=n.get()-t.get(),r=0;return l?(i.set(t),o+=e/l,o*=u,c+=o,t.add(o),r=c-m):(o=0,i.set(n),t.set(n),r=e),a=d(r),m=c,g},settled:function(){return .001>h(n.get()-e.get())},useBaseFriction:function(){return f(.68)},useBaseDuration:function(){return p(r)},useFriction:f,useDuration:p};return g}(td,tm,tc,tp,C,.68),tg=function(t,e,i,n,r){let{reachedAny:s,removeOffset:o,constrain:a}=n;function l(t){return t.concat().sort((t,e)=>h(t)-h(e))[0]}function u(e,n){let r=[e,e+i,e-i];if(!t)return e;if(!n)return l(r);let s=r.filter(t=>d(t)===n);return s.length?l(s):m(r)-i}return{byDistance:function(i,n){let l=r.get()+i,{index:d,distance:c}=function(i){let n=t?o(i):a(i),{index:r}=e.map((t,e)=>({diff:u(t-n,0),index:e})).sort((t,e)=>h(t.diff)-h(e.diff))[0];return{index:r,distance:n}}(l),m=!t&&s(l);if(!n||m)return{index:d,distance:i};let p=i+u(e[d]-c,0);return{index:d,distance:p}},byIndex:function(t,i){let n=u(e[t]-r.get(),i);return{index:t,distance:n}},shortcut:u}}(k,tr,te,ts,tp),tv=function(t,e,i,n,r,s,o){function a(r){let a=r.distance,l=r.index!==e.get();s.add(a),a&&(n.duration()?t.start():(t.update(),t.render(1),t.update())),l&&(i.set(e.get()),e.set(r.index),o.emit("select"))}return{distance:function(t,e){a(r.byDistance(t,e))},index:function(t,i){let n=e.clone().set(t);a(r.byIndex(n.get(),i))}}}(tu,to,ta,tf,tg,tp,u),ty=function(t){let{max:e,length:i}=t;return{get:function(t){return i?-((t-e)/i):0}}}(ts),tx=y(),tw=function(t,e,i,n){let r,s={},o=null,a=null,l=!1;return{init:function(){r=new IntersectionObserver(t=>{l||(t.forEach(t=>{s[e.indexOf(t.target)]=t}),o=null,a=null,i.emit("slidesInView"))},{root:t.parentElement,threshold:n}),e.forEach(t=>r.observe(t))},destroy:function(){r&&r.disconnect(),l=!0},get:function(){let t=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(t&&o)return o;if(!t&&a)return a;let e=g(s).reduce((e,i)=>{let n=parseInt(i),{isIntersecting:r}=s[n];return(t&&r||!t&&!r)&&e.push(n),e},[]);return t&&(o=e),t||(a=e),e}}}(e,i,u,z),{slideRegistry:tb}=function(t,e,i,n,r,s){let{groupSlides:o}=r,{min:a,max:l}=n;return{slideRegistry:function(){let n=o(s);return 1===i.length?[s]:t&&"keepSnaps"!==e?n.slice(a,l).map((t,e,i)=>{let n=e===p(i);return e?n?f(p(s)-m(i)[0]+1,m(i)[0]):t:f(m(i[0])+1)}):n}()}}(X,N,tr,tn,J,tl),tE=function(t,e,i,n,r,s,a,u){let h={passive:!0,capture:!0},d=0;function c(t){"Tab"===t.code&&(d=new Date().getTime())}return{init:function(m){u&&(s.add(document,"keydown",c,!1),e.forEach((e,c)=>{s.add(e,"focus",e=>{(l(u)||u(m,e))&&function(e){if(new Date().getTime()-d>10)return;a.emit("slideFocusStart"),t.scrollLeft=0;let s=i.findIndex(t=>t.includes(e));o(s)&&(r.useDuration(0),n.index(s,0),a.emit("slideFocus"))}(c)},h)}))}}}(t,i,tb,tv,tf,tx,u,F),tS={ownerDocument:n,ownerWindow:r,eventHandler:u,containerRect:U,slideRects:_,animation:tu,axis:W,dragHandler:function(t,e,i,n,r,s,o,a,u,c,m,p,f,g,w,b,E,S,T){let{cross:A,direction:P}=t,M=["INPUT","SELECT","TEXTAREA"],k={passive:!1},C=y(),j=y(),D=x(50,225).constrain(g.measure(20)),z={mouse:300,touch:400},L={mouse:500,touch:600},V=w?43:25,N=!1,R=0,I=0,O=!1,F=!1,B=!1,U=!1;function _(t){if(!v(t,n)&&t.touches.length>=2)return W(t);let e=s.readPoint(t),i=s.readPoint(t,A),o=h(e-R),l=h(i-I);if(!F&&!U&&(!t.cancelable||!(F=o>l)))return W(t);let u=s.pointerMove(t);o>b&&(B=!0),c.useFriction(.3).useDuration(.75),a.start(),r.add(P(u)),t.preventDefault()}function W(t){let e=m.byDistance(0,!1).index!==p.get(),i=s.pointerUp(t)*(w?L:z)[U?"mouse":"touch"],n=function(t,e){let i=p.add(-1*d(t)),n=m.byDistance(t,!w).distance;return w||h(t)<D?n:E&&e?.5*n:m.byIndex(i.get(),0).distance}(P(i),e),r=function(t,e){var i,n;if(0===t||0===e||h(t)<=h(e))return 0;let r=(i=h(t),n=h(e),h(i-n));return h(r/t)}(i,n);F=!1,O=!1,j.clear(),c.useDuration(V-10*r).useFriction(.68+r/50),u.distance(n,!w),U=!1,f.emit("pointerUp")}function H(t){B&&(t.stopPropagation(),t.preventDefault(),B=!1)}return{init:function(t){T&&C.add(e,"dragstart",t=>t.preventDefault(),k).add(e,"touchmove",()=>void 0,k).add(e,"touchend",()=>void 0).add(e,"touchstart",a).add(e,"mousedown",a).add(e,"touchcancel",W).add(e,"contextmenu",W).add(e,"click",H,!0);function a(a){(l(T)||T(t,a))&&function(t){let a=v(t,n);if((U=a,B=w&&a&&!t.buttons&&N,N=h(r.get()-o.get())>=2,!a||0===t.button)&&!function(t){let e=t.nodeName||"";return M.includes(e)}(t.target)){O=!0,s.pointerDown(t),c.useFriction(0).useDuration(0),r.set(o);let n=U?i:e;j.add(n,"touchmove",_,k).add(n,"touchend",W).add(n,"mousemove",_,k).add(n,"mouseup",W),R=s.readPoint(t),I=s.readPoint(t,A),f.emit("pointerDown")}}(a)}},destroy:function(){C.clear(),j.clear()},pointerDown:function(){return O}}}(W,t,n,r,tp,function(t,e){let i,n;function r(t){return t.timeStamp}function s(i,n){let r=n||t.scroll;return(v(i,e)?i:i.touches[0])["client".concat("x"===r?"X":"Y")]}return{pointerDown:function(t){return i=t,n=t,s(t)},pointerMove:function(t){let e=s(t)-s(n),o=r(t)-r(i)>170;return n=t,o&&(i=t),e},pointerUp:function(t){if(!i||!n)return 0;let e=s(n)-s(i),o=r(t)-r(i),a=r(t)-r(n)>170,l=e/o;return o&&!a&&h(l)>.1?l:0},readPoint:s}}(W,r),td,tu,tv,tf,tg,to,u,Y,j,D,V,0,O),eventStore:tx,percentOfView:Y,index:to,indexPrevious:ta,limit:ts,location:td,offsetLocation:tm,previousLocation:tc,options:s,resizeHandler:function(t,e,i,n,r,s,o){let a,u,d=[t].concat(n),c=[],m=!1;function p(t){return r.measureSize(o.measure(t))}return{init:function(r){s&&(u=p(t),c=n.map(p),a=new ResizeObserver(i=>{(l(s)||s(r,i))&&function(i){for(let s of i){if(m)return;let i=s.target===t,o=n.indexOf(s.target),a=i?u:c[o];if(h(p(i?t:n[o])-a)>=.5){r.reInit(),e.emit("resize");break}}}(i)}),i.requestAnimationFrame(()=>{d.forEach(t=>a.observe(t))}))},destroy:function(){m=!0,a&&a.disconnect()}}}(e,u,r,i,W,R,B),scrollBody:tf,scrollBounds:function(t,e,i,n,r){let s=r.measure(10),o=r.measure(50),a=x(.1,.99),l=!1;function u(){return!l&&!!t.reachedAny(i.get())&&!!t.reachedAny(e.get())}return{shouldConstrain:u,constrain:function(r){if(!u())return;let l=t.reachedMin(e.get())?"min":"max",d=h(t[l]-e.get()),c=i.get()-e.get(),m=a.constrain(d/o);i.subtract(c*m),!r&&h(c)<s&&(i.set(t.constrain(i.get())),n.useDuration(25).useBaseFriction())},toggleActive:function(t){l=!t}}}(ts,tm,tp,tf,Y),scrollLooper:function(t,e,i,n){let{reachedMin:r,reachedMax:s}=x(e.min+.1,e.max+.1);return{loop:function(e){if(!(1===e?s(i.get()):-1===e&&r(i.get())))return;let o=-1*e*t;n.forEach(t=>t.add(o))}}}(te,ts,tm,[td,tm,tc,tp]),scrollProgress:ty,scrollSnapList:tr.map(ty.get),scrollSnaps:tr,scrollTarget:tg,scrollTo:tv,slideLooper:function(t,e,i,n,r,s,o,a,l){let u=c(r),h=c(r).reverse(),d=f(p(h,o[0]),i,!1).concat(f(p(u,e-o[0]-1),-i,!0));function m(t,e){return t.reduce((t,e)=>t-r[e],e)}function p(t,e){return t.reduce((t,i)=>m(t,e)>0?t.concat([i]):t,[])}function f(r,o,u){let h=s.map((t,i)=>({start:t-n[i]+.5+o,end:t+e-.5+o}));return r.map(e=>{let n=u?0:-i,r=u?i:0,s=h[e][u?"end":"start"];return{index:e,loopPoint:s,slideLocation:w(-1),translate:b(t,l[e]),target:()=>a.get()>s?n:r}})}return{canLoop:function(){return d.every(t=>{let{index:i}=t;return .1>=m(u.filter(t=>t!==i),e)})},clear:function(){d.forEach(t=>t.translate.clear())},loop:function(){d.forEach(t=>{let{target:e,translate:i,slideLocation:n}=t,r=e();r!==n.get()&&(i.to(r),n.set(r))})},loopPoints:d}}(W,H,te,q,Z,Q,tr,tm,i),slideFocus:tE,slidesHandler:(S=!1,{init:function(t){I&&(E=new MutationObserver(e=>{!S&&(l(I)||I(t,e))&&function(e){for(let i of e)if("childList"===i.type){t.reInit(),u.emit("slidesChanged");break}}(e)})).observe(e,{childList:!0})},destroy:function(){E&&E.disconnect(),S=!0}}),slidesInView:tw,slideIndexes:tl,slideRegistry:tb,slidesToScroll:J,target:tp,translate:b(W,e)};return tS}(t,s,T,P,M,i,D);return i.loop&&!n.slideLooper.canLoop()?e(Object.assign({},i,{loop:!1})):n}(B),V([F,...U.map(t=>{let{options:e}=t;return e})]).forEach(t=>j.add(t,"change",W)),B.active&&(n.translate.to(n.location.get()),n.animation.init(),n.slidesInView.init(),n.slideFocus.init(X),n.eventHandler.init(X),n.resizeHandler.init(X),n.slidesHandler.init(X),n.options.loop&&n.slideLooper.loop(),s.offsetParent&&T.length&&n.dragHandler.init(X),r=C.init(X,U))}function W(t,e){let i=G();H(),_(z({startIndex:i},t),e),D.emit("reInit")}function H(){n.dragHandler.destroy(),n.eventStore.clear(),n.translate.clear(),n.slideLooper.clear(),n.resizeHandler.destroy(),n.slidesHandler.destroy(),n.slidesInView.destroy(),n.animation.destroy(),C.destroy(),j.clear()}function Y(t,e,i){B.active&&!O&&(n.scrollBody.useBaseFriction().useDuration(!0===e?0:B.duration),n.scrollTo.index(t,i||0))}function G(){return n.index.get()}let X={canScrollNext:function(){return n.index.add(1).get()!==G()},canScrollPrev:function(){return n.index.add(-1).get()!==G()},containerNode:function(){return s},internalEngine:function(){return n},destroy:function(){O||(O=!0,j.clear(),H(),D.emit("destroy"),D.clear())},off:R,on:N,emit:I,plugins:function(){return r},previousScrollSnap:function(){return n.indexPrevious.get()},reInit:W,rootNode:function(){return t},scrollNext:function(t){Y(n.index.add(1).get(),t,-1)},scrollPrev:function(t){Y(n.index.add(-1).get(),t,1)},scrollProgress:function(){return n.scrollProgress.get(n.offsetLocation.get())},scrollSnapList:function(){return n.scrollSnapList},scrollTo:Y,selectedScrollSnap:G,slideNodes:function(){return T},slidesInView:function(){return n.slidesInView.get()},slidesNotInView:function(){return n.slidesInView.get(!1)}};return _(e,i),setTimeout(()=>D.emit("init"),0),X}function T(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=(0,i.useRef)(t),o=(0,i.useRef)(e),[a,l]=(0,i.useState)(),[u,h]=(0,i.useState)(),d=(0,i.useCallback)(()=>{a&&a.reInit(n.current,o.current)},[a]);return(0,i.useEffect)(()=>{r(n.current,t)||(n.current=t,d())},[t,d]),(0,i.useEffect)(()=>{!function(t,e){if(t.length!==e.length)return!1;let i=s(t),n=s(e);return i.every((t,e)=>r(t,n[e]))}(o.current,e)&&(o.current=e,d())},[e,d]),(0,i.useEffect)(()=>{if("undefined"!=typeof window&&window.document&&window.document.createElement&&u){S.globalOptions=T.globalOptions;let t=S(u,n.current,o.current);return l(t),()=>t.destroy()}l(void 0)},[u,l]),[h,a]}S.globalOptions=void 0,T.globalOptions=void 0;var A=t.i(86981);let P=(0,A.default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),M=(0,A.default)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var k=t.i(67881),C=t.i(47163);let j=i.default.createContext(null);function D(){let t=i.default.useContext(j);if(!t)throw Error("useCarousel must be used within a <Carousel />");return t}function z(t){let{orientation:n="horizontal",opts:r,setApi:s,plugins:o,className:a,children:l,...u}=t,[h,d]=T({...r,axis:"horizontal"===n?"x":"y"},o),[c,m]=i.default.useState(!1),[p,f]=i.default.useState(!1),g=i.default.useCallback(t=>{t&&(m(t.canScrollPrev()),f(t.canScrollNext()))},[]),v=i.default.useCallback(()=>{null==d||d.scrollPrev()},[d]),y=i.default.useCallback(()=>{null==d||d.scrollNext()},[d]),x=i.default.useCallback(t=>{"ArrowLeft"===t.key?(t.preventDefault(),v()):"ArrowRight"===t.key&&(t.preventDefault(),y())},[v,y]);return i.default.useEffect(()=>{d&&s&&s(d)},[d,s]),i.default.useEffect(()=>{if(d)return g(d),d.on("reInit",g),d.on("select",g),()=>{null==d||d.off("select",g)}},[d,g]),(0,e.jsx)(j.Provider,{value:{carouselRef:h,api:d,opts:r,orientation:n||((null==r?void 0:r.axis)==="y"?"vertical":"horizontal"),scrollPrev:v,scrollNext:y,canScrollPrev:c,canScrollNext:p},children:(0,e.jsx)("div",{"aria-roledescription":"carousel",className:(0,C.cn)("relative",a),"data-slot":"carousel",onKeyDownCapture:x,role:"region",...u,children:l})})}function L(t){let{className:i,...n}=t,{carouselRef:r,orientation:s}=D();return(0,e.jsx)("div",{className:"overflow-hidden","data-slot":"carousel-content",ref:r,children:(0,e.jsx)("div",{className:(0,C.cn)("flex","horizontal"===s?"-ml-4":"-mt-4 flex-col",i),...n})})}function V(t){let{className:i,...n}=t,{orientation:r}=D();return(0,e.jsx)("div",{"aria-roledescription":"slide",className:(0,C.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===r?"pl-4":"pt-4",i),"data-slot":"carousel-item",role:"group",...n})}function N(t){let{className:i,variant:n="outline",size:r="icon",...s}=t,{orientation:o,scrollPrev:a,canScrollPrev:l}=D();return(0,e.jsxs)(k.Button,{className:(0,C.cn)("absolute size-8 rounded-full","horizontal"===o?"-left-12 -translate-y-1/2 top-1/2":"-top-12 -translate-x-1/2 left-1/2 rotate-90",i),"data-slot":"carousel-previous",disabled:!l,onClick:a,size:r,variant:n,...s,children:[(0,e.jsx)(P,{}),(0,e.jsx)("span",{className:"sr-only",children:"Previous slide"})]})}function R(t){let{className:i,variant:n="outline",size:r="icon",...s}=t,{orientation:o,scrollNext:a,canScrollNext:l}=D();return(0,e.jsxs)(k.Button,{className:(0,C.cn)("absolute size-8 rounded-full","horizontal"===o?"-right-12 -translate-y-1/2 top-1/2":"-bottom-12 -translate-x-1/2 left-1/2 rotate-90",i),"data-slot":"carousel-next",disabled:!l,onClick:a,size:r,variant:n,...s,children:[(0,e.jsx)(M,{}),(0,e.jsx)("span",{className:"sr-only",children:"Next slide"})]})}var I=t.i(94710),O=t.i(57034);let F=t=>{let{img:i,name:n,username:r,body:s}=t;return(0,e.jsxs)("figure",{className:(0,C.cn)("group flex max-h-72 w-fit flex-shrink-0 flex-col justify-between gap-8 rounded-xl bg-muted/40 p-4 px-5 opacity-60 shadow-sm transition-all duration-300 hover:bg-[url('/gradient.webp')] hover:bg-center hover:bg-cover hover:bg-no-repeat hover:opacity-100 hover:shadow-md md:w-[28rem] md:p-6 md:px-7 [&:hover_.avatar-ring]:ring-brandYellow [&:hover_.external-link-icon]:opacity-100 [&:hover_.social-icon-border]:border-brand-600"),children:[(0,e.jsxs)("blockquote",{className:"mt-2 font-medium text-md text-primary tracking-normal",children:[s,"One of the most delightful, inventive, powerful new interfaces I've tried in years. Actually feels like an AI native computer."]}),(0,e.jsxs)("div",{className:"relative flex w-full items-center gap-3 text-left transition-all duration-300 hover:scale-[98%]",children:[(0,e.jsx)(O.default,{alt:"",className:"size-12 rounded-full",height:"48",src:i,width:"48"}),(0,e.jsxs)("div",{className:"flex-1",children:[(0,e.jsx)("figcaption",{className:"font-aeonik font-medium text-lg text-primary/80 tracking-normal",children:n}),(0,e.jsx)("p",{className:"font-aeonik font-normal text-md text-primary/50 tracking-tight",children:r})]})]})]})};function B(){let{api:t}=D(),[n,r]=(0,i.useState)(0),[s,o]=(0,i.useState)(0);return(0,i.useEffect)(()=>{if(!t)return;o(t.scrollSnapList().length);let e=()=>{r(t.selectedScrollSnap())};return t.on("select",e),e(),()=>{t.off("select",e)}},[t]),(0,e.jsx)("div",{className:"mt-4 flex justify-center gap-2",children:Array.from({length:s}).map((i,r)=>(0,e.jsx)("button",{className:"h-2 rounded-full transition-all ".concat(r===n?"w-8 bg-brand-600":"w-2 bg-muted"),onClick:()=>null==t?void 0:t.scrollTo(r),type:"button"},r))})}function U(){return(0,e.jsx)("div",{className:"md:hidden",children:(0,e.jsxs)(z,{children:[(0,e.jsx)(L,{children:I.reviews.map(t=>(0,e.jsx)(V,{children:(0,e.jsx)(F,{...t})},t.name))}),(0,e.jsx)(N,{}),(0,e.jsx)(R,{}),(0,e.jsx)(B,{})]})})}}]);