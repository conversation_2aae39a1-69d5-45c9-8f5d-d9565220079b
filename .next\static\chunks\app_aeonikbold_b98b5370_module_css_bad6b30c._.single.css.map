{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/aeonikbold_b98b5370.module.css"], "sourcesContent": ["@font-face {\n    font-family: 'aeonikBold';\n    src: url('@vercel/turbopack-next/internal/font/local/font?{%22path%22:%22./fonts/fonnts.com-Aeonik-Bold.ttf%22,%22preload%22:true,%22has_size_adjust%22:true}') format('truetype');\n    font-display: swap;\n    \n}\n\n@font-face {\n    font-family: 'aeonikBold Fallback';\n    src: local(\"Arial\");\n    ascent-override: 93.63%;\ndescent-override: 18.73%;\nline-gap-override: 0.00%;\nsize-adjust: 106.80%;\n\n}\n\n.className {\n    font-family: 'aeonikBold', 'aeonikBold Fallback';\n    \n}\n.variable {\n    --font-aeonik-bold: 'aeonikBold', 'aeonikBold Fallback';\n}\n\n"], "names": [], "mappings": "AAAA;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA"}}]}