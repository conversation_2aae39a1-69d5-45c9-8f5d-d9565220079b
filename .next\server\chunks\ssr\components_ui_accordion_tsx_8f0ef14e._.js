module.exports=[86547,a=>{"use strict";a.s(["Accordion",()=>ac,"AccordionContent",()=>af,"AccordionItem",()=>ad,"AccordionTrigger",()=>ae],86547);var b=a.i(69720),c=a.i(29611),d=a.i(21380),e=a.i(97836),f=a.i(39003),g=a.i(59653),h=a.i(9806),i=a.i(53834),j=a.i(71120),k=a.i(91802),l=a.i(29239),m="Collapsible",[n,o]=(0,d.createContextScope)(m),[p,q]=n(m),r=c.forwardRef((a,d)=>{let{__scopeCollapsible:e,open:f,defaultOpen:g,disabled:j,onOpenChange:k,...n}=a,[o,q]=(0,h.useControllableState)({prop:f,defaultProp:g??!1,onChange:k,caller:m});return(0,b.jsx)(p,{scope:e,disabled:j,contentId:(0,l.useId)(),open:o,onOpenToggle:c.useCallback(()=>q(a=>!a),[q]),children:(0,b.jsx)(i.Primitive.div,{"data-state":x(o),"data-disabled":j?"":void 0,...n,ref:d})})});r.displayName=m;var s="CollapsibleTrigger",t=c.forwardRef((a,c)=>{let{__scopeCollapsible:d,...e}=a,f=q(s,d);return(0,b.jsx)(i.Primitive.button,{type:"button","aria-controls":f.contentId,"aria-expanded":f.open||!1,"data-state":x(f.open),"data-disabled":f.disabled?"":void 0,disabled:f.disabled,...e,ref:c,onClick:(0,g.composeEventHandlers)(a.onClick,f.onOpenToggle)})});t.displayName=s;var u="CollapsibleContent",v=c.forwardRef((a,c)=>{let{forceMount:d,...e}=a,f=q(u,a.__scopeCollapsible);return(0,b.jsx)(k.Presence,{present:d||f.open,children:({present:a})=>(0,b.jsx)(w,{...e,ref:c,present:a})})});v.displayName=u;var w=c.forwardRef((a,d)=>{let{__scopeCollapsible:e,present:g,children:h,...k}=a,l=q(u,e),[m,n]=c.useState(g),o=c.useRef(null),p=(0,f.useComposedRefs)(d,o),r=c.useRef(0),s=r.current,t=c.useRef(0),v=t.current,w=l.open||m,y=c.useRef(w),z=c.useRef(void 0);return c.useEffect(()=>{let a=requestAnimationFrame(()=>y.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,j.useLayoutEffect)(()=>{let a=o.current;if(a){z.current=z.current||{transitionDuration:a.style.transitionDuration,animationName:a.style.animationName},a.style.transitionDuration="0s",a.style.animationName="none";let b=a.getBoundingClientRect();r.current=b.height,t.current=b.width,y.current||(a.style.transitionDuration=z.current.transitionDuration,a.style.animationName=z.current.animationName),n(g)}},[l.open,g]),(0,b.jsx)(i.Primitive.div,{"data-state":x(l.open),"data-disabled":l.disabled?"":void 0,id:l.contentId,hidden:!w,...k,ref:p,style:{"--radix-collapsible-content-height":s?`${s}px`:void 0,"--radix-collapsible-content-width":v?`${v}px`:void 0,...a.style},children:w&&h})});function x(a){return a?"open":"closed"}var y=a.i(38442),z="Accordion",A=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[B,C,D]=(0,e.createCollection)(z),[E,F]=(0,d.createContextScope)(z,[D,o]),G=o(),H=c.default.forwardRef((a,c)=>{let{type:d,...e}=a;return(0,b.jsx)(B.Provider,{scope:a.__scopeAccordion,children:"multiple"===d?(0,b.jsx)(N,{...e,ref:c}):(0,b.jsx)(M,{...e,ref:c})})});H.displayName=z;var[I,J]=E(z),[K,L]=E(z,{collapsible:!1}),M=c.default.forwardRef((a,d)=>{let{value:e,defaultValue:f,onValueChange:g=()=>{},collapsible:i=!1,...j}=a,[k,l]=(0,h.useControllableState)({prop:e,defaultProp:f??"",onChange:g,caller:z});return(0,b.jsx)(I,{scope:a.__scopeAccordion,value:c.default.useMemo(()=>k?[k]:[],[k]),onItemOpen:l,onItemClose:c.default.useCallback(()=>i&&l(""),[i,l]),children:(0,b.jsx)(K,{scope:a.__scopeAccordion,collapsible:i,children:(0,b.jsx)(Q,{...j,ref:d})})})}),N=c.default.forwardRef((a,d)=>{let{value:e,defaultValue:f,onValueChange:g=()=>{},...i}=a,[j,k]=(0,h.useControllableState)({prop:e,defaultProp:f??[],onChange:g,caller:z}),l=c.default.useCallback(a=>k((b=[])=>[...b,a]),[k]),m=c.default.useCallback(a=>k((b=[])=>b.filter(b=>b!==a)),[k]);return(0,b.jsx)(I,{scope:a.__scopeAccordion,value:j,onItemOpen:l,onItemClose:m,children:(0,b.jsx)(K,{scope:a.__scopeAccordion,collapsible:!0,children:(0,b.jsx)(Q,{...i,ref:d})})})}),[O,P]=E(z),Q=c.default.forwardRef((a,d)=>{let{__scopeAccordion:e,disabled:h,dir:j,orientation:k="vertical",...l}=a,m=c.default.useRef(null),n=(0,f.useComposedRefs)(m,d),o=C(e),p="ltr"===(0,y.useDirection)(j),q=(0,g.composeEventHandlers)(a.onKeyDown,a=>{if(!A.includes(a.key))return;let b=a.target,c=o().filter(a=>!a.ref.current?.disabled),d=c.findIndex(a=>a.ref.current===b),e=c.length;if(-1===d)return;a.preventDefault();let f=d,g=e-1,h=()=>{(f=d+1)>g&&(f=0)},i=()=>{(f=d-1)<0&&(f=g)};switch(a.key){case"Home":f=0;break;case"End":f=g;break;case"ArrowRight":"horizontal"===k&&(p?h():i());break;case"ArrowDown":"vertical"===k&&h();break;case"ArrowLeft":"horizontal"===k&&(p?i():h());break;case"ArrowUp":"vertical"===k&&i()}let j=f%e;c[j].ref.current?.focus()});return(0,b.jsx)(O,{scope:e,disabled:h,direction:j,orientation:k,children:(0,b.jsx)(B.Slot,{scope:e,children:(0,b.jsx)(i.Primitive.div,{...l,"data-orientation":k,ref:n,onKeyDown:h?void 0:q})})})}),R="AccordionItem",[S,T]=E(R),U=c.default.forwardRef((a,c)=>{let{__scopeAccordion:d,value:e,...f}=a,g=P(R,d),h=J(R,d),i=G(d),j=(0,l.useId)(),k=e&&h.value.includes(e)||!1,m=g.disabled||a.disabled;return(0,b.jsx)(S,{scope:d,open:k,disabled:m,triggerId:j,children:(0,b.jsx)(r,{"data-orientation":g.orientation,"data-state":_(k),...i,...f,ref:c,disabled:m,open:k,onOpenChange:a=>{a?h.onItemOpen(e):h.onItemClose(e)}})})});U.displayName=R;var V="AccordionHeader",W=c.default.forwardRef((a,c)=>{let{__scopeAccordion:d,...e}=a,f=P(z,d),g=T(V,d);return(0,b.jsx)(i.Primitive.h3,{"data-orientation":f.orientation,"data-state":_(g.open),"data-disabled":g.disabled?"":void 0,...e,ref:c})});W.displayName=V;var X="AccordionTrigger",Y=c.default.forwardRef((a,c)=>{let{__scopeAccordion:d,...e}=a,f=P(z,d),g=T(X,d),h=L(X,d),i=G(d);return(0,b.jsx)(B.ItemSlot,{scope:d,children:(0,b.jsx)(t,{"aria-disabled":g.open&&!h.collapsible||void 0,"data-orientation":f.orientation,id:g.triggerId,...i,...e,ref:c})})});Y.displayName=X;var Z="AccordionContent",$=c.default.forwardRef((a,c)=>{let{__scopeAccordion:d,...e}=a,f=P(z,d),g=T(Z,d),h=G(d);return(0,b.jsx)(v,{role:"region","aria-labelledby":g.triggerId,"data-orientation":f.orientation,...h,...e,ref:c,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...a.style}})});function _(a){return a?"open":"closed"}$.displayName=Z;var aa=a.i(81533),ab=a.i(97895);function ac({...a}){return(0,b.jsx)(H,{"data-slot":"accordion",...a})}function ad({className:a,...c}){return(0,b.jsx)(U,{className:(0,ab.cn)("border-b last:border-b-0",a),"data-slot":"accordion-item",...c})}function ae({className:a,children:c,...d}){return(0,b.jsx)(W,{className:"flex",children:(0,b.jsxs)(Y,{className:(0,ab.cn)("flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left font-medium text-sm outline-none transition-all hover:underline focus-visible:border-ring focus-visible:ring-[3px] focus-visible:ring-ring/50 disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",a),"data-slot":"accordion-trigger",...d,children:[c,(0,b.jsx)(aa.ChevronDownIcon,{className:"pointer-events-none size-4 shrink-0 translate-y-0.5 text-muted-foreground transition-transform duration-200"})]})})}function af({className:a,children:c,...d}){return(0,b.jsx)($,{className:"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down","data-slot":"accordion-content",...d,children:(0,b.jsx)("div",{className:(0,ab.cn)("pt-0 pb-4",a),children:c})})}}];

//# sourceMappingURL=components_ui_accordion_tsx_8f0ef14e._.js.map