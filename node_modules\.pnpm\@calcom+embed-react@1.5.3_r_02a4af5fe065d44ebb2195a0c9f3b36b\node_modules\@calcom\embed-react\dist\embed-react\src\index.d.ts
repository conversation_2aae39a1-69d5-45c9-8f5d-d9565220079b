import type { GlobalCal, GlobalCalWithoutNs } from "@calcom/embed-core";
import Cal from "./Cal";
export type { EmbedEvent } from "@calcom/embed-core";
export declare function getCalApi(options?: {
    embedJsUrl?: string;
    namespace?: string;
}): Promise<GlobalCal | GlobalCalWithoutNs>;
export declare function getCalApi(embedJsUrl: string): Promise<GlobalCal | GlobalCalWithoutNs>;
export default Cal;
//# sourceMappingURL=index.d.ts.map