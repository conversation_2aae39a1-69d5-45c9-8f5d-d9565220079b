import Image from 'next/image';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import team from '@/public/team.webp';

export default function AboutHero() {
  return (
    <section className="my-12">
      <div className="mx-auto flex max-w-6xl flex-col items-start px-4 md:px-10 2xl:max-w-7xl">
        <p className="mb-8 font-aeonik-bold font-medium text-md text-muted-foreground/80 uppercase">
          OUR TEAM
        </p>
        <h2 className="mb-12 max-w-4xl font-medium font-sans text-2xl text-light leading-tight md:text-4xl lg:text-5xl lg:tracking-tight">
          We're a passionate group of engineers, designers, and researchers
          building your favorite desktop AI assistant.
        </h2>
        <div className="mb-6 flex w-full flex-col justify-center">
          <AspectRatio className="w-full rounded-lg bg-muted" ratio={16 / 9}>
            <Image
              alt="The Highlight AI team in New York"
              className="h-full w-full rounded-lg object-cover transition-all duration-700 ease-out"
              fill
              src={team}
            />
          </AspectRatio>
          <p className="mt-4 text-start font-medium text-md text-muted-foreground/80">
            On the steps where it all started. New York, 2024
          </p>
        </div>
      </div>
    </section>
  );
}
