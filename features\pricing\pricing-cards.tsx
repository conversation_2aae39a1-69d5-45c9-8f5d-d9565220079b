'use client';
import NumberFlow from '@number-flow/react';
import { BadgeCheck } from 'lucide-react';
import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { plans } from '@/config/docs';
import { cn } from '@/lib/utils';

export default function PricingCards() {
  const [frequency, setFrequency] = useState<string>('monthly');
  return (
    <div className="mb-40 flex flex-col gap-16 text-center">
      <div className="flex flex-col items-center justify-center gap-6">
        <Tabs defaultValue={frequency} onValueChange={setFrequency}>
          <TabsList className="rounded-full bg-my-background p-4 py-6">
            <TabsTrigger
              className="rounded-full p-4 dark:data-[state=active]:bg-muted"
              value="monthly"
            >
              Monthly
            </TabsTrigger>
            <TabsTrigger
              className="rounded-full p-4 dark:data-[state=active]:bg-muted"
              value="yearly"
            >
              Yearly
              <Badge variant="secondary">20% off</Badge>
            </TabsTrigger>
          </TabsList>
        </Tabs>
        <div className="grid w-full grid-cols-1 gap-[15px] overflow-visible sm:grid-cols-2 sm:px-4 md:px-0 lg:grid-cols-3">
          {plans.map((plan) => (
            <Card
              className={cn(
                'relative w-full text-left',
                plan.popular && 'ring-2 ring-primary'
              )}
              key={plan.id}
            >
              {plan.popular && (
                <Badge className="-translate-x-1/2 -translate-y-1/2 absolute top-0 left-1/2 rounded-full">
                  Popular
                </Badge>
              )}
              <CardHeader>
                <CardTitle className="font-medium text-xl">
                  {plan.name}
                </CardTitle>
                <CardDescription>
                  <p className="mb-4">{plan.description}</p>
                  {typeof plan.price[frequency as keyof typeof plan.price] ===
                  'number' ? (
                    <NumberFlow
                      className="font-medium text-foreground text-xl lg:text-3xl"
                      format={{
                        style: 'currency',
                        currency: 'USD',
                        maximumFractionDigits: 0,
                      }}
                      suffix={'/seat'}
                      value={
                        plan.price[
                          frequency as keyof typeof plan.price
                        ] as number
                      }
                    />
                  ) : (
                    <span className="font-medium text-foreground text-xl lg:text-2xl">
                      {plan.price[frequency as keyof typeof plan.price]}.
                    </span>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent className="grid gap-2">
                {plan.features.map((feature, index) => (
                  <div
                    className="flex items-center gap-2 text-muted-foreground text-sm"
                    key={index}
                  >
                    <BadgeCheck className="size-4 text-brand-500" />
                    {feature}
                  </div>
                ))}
              </CardContent>
              <CardFooter className="mt-auto">
                <Button
                  className={cn(
                    'w-full rounded-full',
                    plan.popular ? 'bg-brand-600 hover:bg-brand-500' : ''
                  )}
                  size={'lg'}
                  variant={plan.popular ? 'default' : 'secondary'}
                >
                  {plan.cta}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
