"use strict";Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const e=require("./lite-DyshwcGq.js"),l=require("./plugins.js"),n="number-flow-connect",s="number-flow-update",f=(r,{locales:t,format:o,numberPrefix:a,numberSuffix:u}={})=>{const c=e.formatToData(r,new Intl.NumberFormat(t,o),a,u);return e.renderInnerHTML(c)};class i extends e.NumberFlowLite{constructor(){super(...arguments),this.connected=!1}connectedCallback(){this.connected=!0,this.dispatchEvent(new Event(n,{bubbles:!0}))}disconnectedCallback(){this.connected=!1}get value(){return this._value}update(t){(!this._formatter||this._prevFormat!==this.format||this._prevLocales!==this.locales)&&(this._formatter=new Intl.NumberFormat(this.locales,this.format),this._prevFormat=this.format,this._prevLocales=this.locales),t!=null&&(this._value=t),this.dispatchEvent(new Event(s,{bubbles:!0})),this.data=e.formatToData(this._value,this._formatter,this.numberPrefix,this.numberSuffix)}}e.define("number-flow",i);exports.Digit=e.Digit;exports.canAnimate=e.canAnimate;exports.define=e.define;exports.formatToData=e.formatToData;exports.prefersReducedMotion=e.prefersReducedMotion;exports.continuous=l.continuous;exports.CONNECT_EVENT=n;exports.UPDATE_EVENT=s;exports.default=i;exports.renderInnerHTML=f;
